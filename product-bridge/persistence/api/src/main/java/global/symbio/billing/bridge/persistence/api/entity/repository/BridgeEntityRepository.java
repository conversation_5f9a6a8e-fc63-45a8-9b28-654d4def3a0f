package global.symbio.billing.bridge.persistence.api.entity.repository;

import global.symbio.billing.bridge.persistence.api.entity.BridgeEntityDataAccessObject;
import io.micronaut.context.annotation.Executable;
import io.micronaut.data.repository.CrudRepository;
import jakarta.annotation.Nonnull;

import java.util.Optional;
import java.util.UUID;

public interface BridgeEntityRepository<T extends BridgeEntityDataAccessObject> extends CrudRepository<T, UUID> {

    @Nonnull
    @Executable
    Optional<T> findByTypeEqualsAndRefEqualsAndSystemEquals(@Nonnull String type, @Nonnull String ref, @Nonnull String system);
}
