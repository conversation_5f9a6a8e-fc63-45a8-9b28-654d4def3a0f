package global.symbio.billing.bridge.persistence.api.entity;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.persistence.api.DataAccessObject;
import jakarta.annotation.Nonnull;

import java.time.ZonedDateTime;
import java.util.Objects;
import java.util.UUID;

public abstract class BridgeEntityDataAccessObject implements DataAccessObject<UUID, BridgeEntityDataAccessObject> {

    @Nonnull
    public abstract String getType();

    @Nonnull
    public abstract BridgeEntityDataAccessObject type(@Nonnull String type);

    @Nonnull
    public abstract String getRef();

    @Nonnull
    public abstract BridgeEntityDataAccessObject ref(@Nonnull String ref);

    @Nonnull
    public abstract String getSystem();

    @Nonnull
    public abstract BridgeEntityDataAccessObject system(@Nonnull String system);

    @Nonnull
    public abstract String getPayload();

    @Nonnull
    public abstract BridgeEntityDataAccessObject payload(@Nonnull String payload);

    @Nonnull
    public abstract ZonedDateTime getTimestamp();

    @Nonnull
    public abstract String getCountry();

    @Nonnull
    public abstract BridgeEntityDataAccessObject country(@Nonnull String country);

    @Nonnull
    public abstract BridgeEntityDataAccessObject timestamp(@Nonnull ZonedDateTime timestamp);

    @Override
    @Nonnull
    public BridgeEntity entity() {
        return new BridgeEntity(this);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof BridgeEntityDataAccessObject entity)) return false;
        return Objects.equals(getIdentifier(), entity.getIdentifier())
            && Objects.equals(getType(), entity.getType())
            && Objects.equals(getRef(), entity.getRef())
            && Objects.equals(getSystem(), entity.getSystem())
            && Objects.equals(getPayload(), entity.getPayload())
            && Objects.equals(getCountry(), entity.getCountry())
            && Objects.equals(getTimestamp(), entity.getTimestamp());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getIdentifier(), getType(), getRef(), getSystem(), getPayload(), getCountry(), getTimestamp());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("identifier", getIdentifier())
            .add("type", getType())
            .add("ref", getRef())
            .add("system", getSystem())
            .add("payload", getPayload())
            .add("country", getCountry())
            .add("timestamp", getTimestamp())
            .toString();
    }
}
