package global.symbio.billing.bridge.persistence.api.entity;

import global.symbio.billing.core.persistence.api.Entity;
import jakarta.annotation.Nonnull;

import java.time.ZonedDateTime;
import java.util.UUID;

public class BridgeEntity extends Entity<UUID, BridgeEntityDataAccessObject> {

    public BridgeEntity(@Nonnull BridgeEntityDataAccessObject data) {
        super(data);
    }

    @Nonnull
    public String getType() {
        return data().getType();
    }

    @Nonnull
    public String getRef() {
        return data().getRef();
    }

    @Nonnull
    public String getSystem() {
        return data().getSystem();
    }

    @Nonnull
    public String getPayload() {
        return data().getPayload();
    }

    @Nonnull
    public ZonedDateTime getTimestamp() {
        return data().getTimestamp();
    }

    @Nonnull
    public String getCountry() {
        return data().getCountry();
    }
}
