package global.symbio.billing.bridge.persistence.api.entity.transaction;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.persistence.api.DataAccessObject;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Objects;
import java.util.UUID;

public abstract class TransactionEntityDataAccessObject implements DataAccessObject<UUID, TransactionEntityDataAccessObject> {

    @Nullable
    public abstract Integer getCurrency();

    @Nonnull
    public abstract TransactionEntityDataAccessObject currency(@Nullable Integer currency);

    @Nullable
    public abstract Integer getCategory();

    @Nonnull
    public abstract TransactionEntityDataAccessObject category(@Nullable Integer category);

    @Nullable
    public abstract BigDecimal getAmount();

    @Nonnull
    public abstract TransactionEntityDataAccessObject amount(@Nullable BigDecimal amount);

    @Nullable
    public abstract BigDecimal getTaxation();

    @Nonnull
    public abstract TransactionEntityDataAccessObject taxation(@Nullable BigDecimal taxation);

    @Nullable
    public abstract UUID getDebit();

    @Nonnull
    public abstract TransactionEntityDataAccessObject debit(@Nullable UUID account);

    @Nullable
    public abstract UUID getCredit();

    @Nonnull
    public abstract TransactionEntityDataAccessObject credit(@Nullable UUID account);

    @Nullable
    public abstract String getDescription();

    @Nonnull
    public abstract TransactionEntityDataAccessObject description(@Nullable String description);

    @Nullable
    public abstract String getRef();

    @Nonnull
    public abstract TransactionEntityDataAccessObject ref(@Nullable String plexid);

    @Nonnull
    public abstract String getCountry();

    @Nonnull
    public abstract TransactionEntityDataAccessObject country(@Nonnull String country);

    @Nonnull
    public abstract ZonedDateTime getTimestamp();

    @Nonnull
    public abstract TransactionEntityDataAccessObject timestamp(@Nonnull ZonedDateTime timestamp);

    @Override @Nonnull
    public TransactionEntity entity() {
        return new TransactionEntity(this);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof TransactionEntityDataAccessObject tx)) return false;
        return Objects.equals(getIdentifier(), tx.getIdentifier())
            && Objects.equals(getCurrency(), tx.getCurrency())
            && Objects.equals(getCategory(), tx.getCategory())
            && Objects.equals(getAmount(), tx.getAmount())
            && Objects.equals(getTaxation(), tx.getTaxation())
            && Objects.equals(getDebit(), tx.getDebit())
            && Objects.equals(getCredit(), tx.getCredit())
            && Objects.equals(getDescription(), tx.getDescription())
            && Objects.equals(getCountry(), tx.getCountry())
            && Objects.equals(getRef(), tx.getRef())
            && Objects.equals(getTimestamp(), tx.getTimestamp());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getIdentifier(), getCurrency(), getCategory(), getAmount(), getTaxation(), getDebit(), getCredit(), getDescription(), getCountry(), getRef(), getTimestamp());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("identifier", getIdentifier())
            .add("currency", getCurrency())
            .add("category", getCategory())
            .add("amount", getAmount())
            .add("taxation", getTaxation())
            .add("debit", getDebit())
            .add("credit", getCredit())
            .add("description", getDescription())
            .add("country", getCountry())
            .add("ref", getRef())
            .add("timestamp", getTimestamp())
            .toString();
    }
}
