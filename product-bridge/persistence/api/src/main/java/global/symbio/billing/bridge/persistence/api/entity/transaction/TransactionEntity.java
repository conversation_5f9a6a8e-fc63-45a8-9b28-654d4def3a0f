package global.symbio.billing.bridge.persistence.api.entity.transaction;

import global.symbio.billing.core.persistence.api.Entity;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.UUID;

public class TransactionEntity extends Entity<UUID, TransactionEntityDataAccessObject> {

    protected TransactionEntity(@Nonnull TransactionEntityDataAccessObject data) {
        super(data);
    }

    @Nullable
    public Integer getCurrency() {
        return data().getCurrency();
    }

    @Nullable
    public Integer getCategory() {
        return data().getCategory();
    }

    @Nullable
    public BigDecimal getAmount() {
        return data().getAmount();
    }

    @Nullable
    public BigDecimal getTaxation() {
        return data().getTaxation();
    }

    @Nullable
    public UUID getDebit() {
        return data().getDebit();
    }

    @Nullable
    public UUID getCredit() {
        return data().getCredit();
    }

    @Nullable
    public String getDescription() {
        return data().getDescription();
    }

    @Nullable
    public String getRef() {
        return data().getRef();
    }

    @Nonnull
    public String getCountry() {
        return data().getCountry();
    }

    @Nonnull
    public ZonedDateTime getTimestamp() {
        return data().getTimestamp();
    }
}
