package global.symbio.billing.bridge.persistence.spi.jdbc.jpa.entity;

import global.symbio.billing.bridge.persistence.api.entity.repository.BridgeEntityRepository;
import global.symbio.billing.core.persistence.api.Repositories;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

public class TestBridgeEntityRepository {

    @Test
    @DisplayName("ReadOnlyBridgeEntityRepository is connected to read-only datasource")
    public void test_read_only_repository() {
        Assertions.assertTrue(BridgeEntityRepository.class.isAssignableFrom(ReadOnlyJPABridgeEntityRepository.class));
        Assertions.assertTrue(Repositories.isReadOnlyRepository(ReadOnlyJPABridgeEntityRepository.class));
    }

    @Test
    @DisplayName("ReadWriteBridgeEntityRepository is connected to read-write datasource")
    public void test_read_write_repository() {
        Assertions.assertTrue(BridgeEntityRepository.class.isAssignableFrom(ReadWriteJPABridgeEntityRepository.class));
        Assertions.assertTrue(Repositories.isReadWriteRepository(ReadWriteJPABridgeEntityRepository.class));
    }
}