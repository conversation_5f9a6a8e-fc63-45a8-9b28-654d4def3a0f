package global.symbio.billing.bridge.persistence.spi.jdbc.jpa.entity.transaction;

import global.symbio.billing.bridge.persistence.api.entity.repository.TransactionEntityRepository;
import global.symbio.billing.core.persistence.api.Repositories;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

public class TestTransactionEntityRepository {

    @Test
    @DisplayName("ReadOnlyTransactionEntityRepository is connected to read-only datasource")
    public void test_read_only_repository() {
        Assertions.assertTrue(TransactionEntityRepository.class.isAssignableFrom(ReadOnlyJPATransactionEntityRepository.class));
        Assertions.assertTrue(Repositories.isReadOnlyRepository(ReadOnlyJPATransactionEntityRepository.class));
    }

    @Test
    @DisplayName("ReadWriteTransactionEntityRepository is connected to read-write datasource")
    public void test_read_write_repository() {
        Assertions.assertTrue(TransactionEntityRepository.class.isAssignableFrom(ReadWriteJPATransactionEntityRepository.class));
        Assertions.assertTrue(Repositories.isReadWriteRepository(ReadWriteJPATransactionEntityRepository.class));
    }
}