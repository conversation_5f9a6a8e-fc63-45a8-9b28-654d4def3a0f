package global.symbio.billing.bridge.persistence.spi.jdbc.jpa.entity;

import global.symbio.billing.bridge.persistence.api.entity.BridgeEntityDataAccessObject;
import io.hypersistence.utils.hibernate.type.json.JsonBinaryType;
import io.micronaut.core.annotation.Introspected;
import jakarta.annotation.Nonnull;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Type;

import java.time.ZonedDateTime;
import java.util.UUID;

@Entity
@Table(name = "entity", indexes = { @Index(columnList = "type, ref"), @Index(columnList = "system"), @Index(columnList = "timestamp") })
@Getter @Setter
@Introspected(includedAnnotations = Entity.class)
public class JPABridgeEntityDataAccessObject extends BridgeEntityDataAccessObject {

    @Id @Nonnull
    @Column(name = "id", unique = true, nullable = false, updatable = false)
    private UUID identifier;

    @Nonnull
    @Column(name = "type", nullable = false, updatable = false, length = 64)
    private String type;

    @Nonnull
    @Column(name = "ref", nullable = false, updatable = false, length = 128)
    private String ref;

    @Nonnull
    @Column(name = "system", nullable = false, updatable = false, length = 64)
    private String system;

    @Nonnull
    @Type(JsonBinaryType.class)
    @Column(name = "payload", nullable = false, updatable = false, length = Short.MAX_VALUE)
    private String payload;

    @Nonnull
    @Column(name = "country", nullable = false, updatable = false, length = 2)
    private String country;

    @Nonnull
    @Column(name = "timestamp", nullable = false, updatable = false, insertable = false)
    private ZonedDateTime timestamp;

    @Nonnull
    @Override
    public BridgeEntityDataAccessObject identifier(@Nonnull UUID identifier) {
        setIdentifier(identifier);
        return this;
    }

    @Nonnull
    @Override
    public BridgeEntityDataAccessObject type(@Nonnull String type) {
        setType(type);
        return this;
    }

    @Nonnull
    @Override
    public BridgeEntityDataAccessObject ref(@Nonnull String ref) {
        setRef(ref);
        return this;
    }

    @Nonnull
    @Override
    public BridgeEntityDataAccessObject system(@Nonnull String system) {
        setSystem(system);
        return this;
    }

    @Nonnull
    @Override
    public BridgeEntityDataAccessObject payload(@Nonnull String payload) {
        setPayload(payload);
        return this;
    }

    @Nonnull
    @Override
    public BridgeEntityDataAccessObject country(@Nonnull String country) {
        setCountry(country);
        return this;
    }

    @Nonnull
    @Override
    public BridgeEntityDataAccessObject timestamp(@Nonnull ZonedDateTime timestamp) {
        setTimestamp(timestamp);
        return this;
    }
}
