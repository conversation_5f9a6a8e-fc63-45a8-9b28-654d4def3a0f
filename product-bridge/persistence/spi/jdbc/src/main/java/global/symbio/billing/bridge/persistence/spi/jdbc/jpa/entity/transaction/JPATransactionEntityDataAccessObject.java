package global.symbio.billing.bridge.persistence.spi.jdbc.jpa.entity.transaction;

import global.symbio.billing.bridge.persistence.api.entity.transaction.TransactionEntityDataAccessObject;
import io.micronaut.core.annotation.Introspected;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.UUID;

@Entity
@Table(name = "transaction_entity", indexes = { @Index(columnList = "credit"), @Index(columnList = "debit"), @Index(columnList = "currency"), @Index(columnList = "category") })
@Getter @Setter
@Introspected(includedAnnotations = Entity.class)
public class JPATransactionEntityDataAccessObject extends TransactionEntityDataAccessObject {

    @Id @Nonnull
    @Column(name = "id", unique = true, nullable = false, updatable = false)
    private UUID identifier;

    @Nullable
    @Column(name = "currency", nullable = true, updatable = false)
    private Integer currency;

    @Nullable
    @Column(name = "category", nullable = true, updatable = false)
    private Integer category;

    @Nullable
    @Column(name = "amount", nullable = true, updatable = false, scale = 32, precision = 12)
    private BigDecimal amount;

    @Nullable
    @Column(name = "taxation", nullable = true, updatable = false, scale = 32, precision = 12)
    private BigDecimal taxation;

    @Nullable
    @Column(name = "debit", nullable = true, updatable = false)
    private UUID debit;

    @Nullable
    @Column(name = "credit", nullable = true, updatable = false)
    private UUID credit;

    @Nullable
    @Column(name = "description", nullable = true, updatable = false, length = 128)
    private String description;

    @Nullable
    @Column(name = "ref", nullable = true, updatable = false, length = 512)
    private String ref;

    @Nonnull
    @Column(name = "country", nullable = false, updatable = false, length = 2)
    private String country;

    @Nonnull
    @Column(name = "timestamp", nullable = false, updatable = false)
    private ZonedDateTime timestamp;

    @Nonnull
    @Override
    public TransactionEntityDataAccessObject identifier(@Nonnull UUID identifier) {
        setIdentifier(identifier);
        return this;
    }

    @Nonnull
    @Override
    public TransactionEntityDataAccessObject currency(@Nullable Integer currency) {
        setCurrency(currency);
        return this;
    }

    @Nonnull
    @Override
    public TransactionEntityDataAccessObject category(@Nullable Integer category) {
        setCategory(category);
        return this;
    }

    @Nonnull
    @Override
    public TransactionEntityDataAccessObject amount(@Nullable BigDecimal amount) {
        setAmount(amount);
        return this;
    }

    @Nonnull
    @Override
    public TransactionEntityDataAccessObject taxation(BigDecimal taxation) {
        setTaxation(taxation);
        return this;
    }

    @Nonnull
    @Override
    public TransactionEntityDataAccessObject debit(@Nullable UUID debit) {
        setDebit(debit);
        return this;
    }

    @Nonnull
    @Override
    public TransactionEntityDataAccessObject credit(@Nullable UUID credit) {
        setCredit(credit);
        return this;
    }

    @Nonnull
    @Override
    public TransactionEntityDataAccessObject description(@Nullable String description) {
        setDescription(description);
        return this;
    }

    @Nonnull
    @Override
    public TransactionEntityDataAccessObject ref(@Nullable String plexid) {
        setRef(plexid);
        return this;
    }

    @Nonnull
    @Override
    public TransactionEntityDataAccessObject country(@Nonnull String country) {
        setCountry(country);
        return this;
    }

    @Nonnull
    @Override
    public TransactionEntityDataAccessObject timestamp(@Nonnull ZonedDateTime timestamp) {
        setTimestamp(timestamp);
        return this;
    }
}