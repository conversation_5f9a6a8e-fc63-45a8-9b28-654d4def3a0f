package global.symbio.billing.bridge.persistence.spi.jdbc.jpa.entity;

import global.symbio.billing.bridge.persistence.api.entity.BridgeEntityDataAccessObject;
import global.symbio.billing.bridge.persistence.api.entity.BridgeEntityDataAccessObjectFactory;
import io.micronaut.context.annotation.Primary;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

@Primary
@Singleton
public class JPABridgeEntityDataAccessObjectFactory implements BridgeEntityDataAccessObjectFactory {

    @Override
    public @Nonnull Class<? extends BridgeEntityDataAccessObject> type() {
        return JPABridgeEntityDataAccessObject.class;
    }

    @Nonnull
    @Override
    public BridgeEntityDataAccessObject create() {
        return new JPABridgeEntityDataAccessObject();
    }
}