package global.symbio.billing.bridge.persistence.spi.jdbc.jpa.entity.transaction;

import global.symbio.billing.bridge.persistence.api.entity.transaction.TransactionEntityDataAccessObject;
import global.symbio.billing.bridge.persistence.api.entity.transaction.TransactionEntityDataAccessObjectFactory;
import io.micronaut.context.annotation.Primary;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

@Primary
@Singleton
public class JPATransactionEntityDataAccessObjectFactory implements TransactionEntityDataAccessObjectFactory {

    @Nonnull
    @Override
    public Class<? extends TransactionEntityDataAccessObject> type() {
        return JPATransactionEntityDataAccessObject.class;
    }

    @Nonnull
    @Override
    public TransactionEntityDataAccessObject create() {
        return new JPATransactionEntityDataAccessObject();
    }
}
