# Project-wide Gradle settings.

# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html

# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
org.gradle.jvmargs=-Xmx3072m

# When configured, <PERSON><PERSON><PERSON> will run in parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
org.gradle.parallel=true
org.gradle.caching=true

# Root project version
version=0.0.1-SNAPSHOT

# Micronaut version
micronautVersion=4.6.3

# JDBC / Liquibase configuration
jdbc_driver=org.postgresql:postgresql:42.7.7
jdbc_driver_class=org.postgresql.Driver
liquibase_runtime_version=4.29.2
liquibase_gradle_version=3.0.2
picocli_version=4.7.7

# Dependency version management
jmh_version=1.37
junit_version=5.13.4
mockito_version=5.15.2
slf4j_version=2.0.17
log4j2_version=2.25.1
jakarta_persistence_version=3.2.0
guava_version=33.4.0-jre
caffeine_version=3.2.2
commons_lang_version=3.18.0
commons_csv_version=1.14.1
lmax_disruptor_version=4.0.0.RC1
aws_sdk_version=2.32.16
java_uuid_generator_version=5.1.0
hypersistence_version=3.10.3
grpc_version=1.73.0
grpc_protoc_version=4.31.1
elastic_apm_version=1.55.0
