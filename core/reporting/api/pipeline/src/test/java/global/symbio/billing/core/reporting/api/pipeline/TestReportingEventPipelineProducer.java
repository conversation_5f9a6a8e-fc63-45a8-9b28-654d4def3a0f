package global.symbio.billing.core.reporting.api.pipeline;

import global.symbio.billing.core.pipeline.api.Pipeline;
import global.symbio.billing.core.pipeline.api.PipelineEngine;
import global.symbio.billing.core.pipeline.api.request.Request;
import global.symbio.billing.core.pipeline.api.request.router.RequestRouter;
import global.symbio.billing.core.reporting.api.ReportingEntityEvent;
import global.symbio.billing.core.reporting.api.ReportingEntityEventProvider;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import static java.time.Duration.ZERO;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestReportingEventPipelineProducer {

    private Pipeline<ReportingEntityEvent<String, String>, PipelineEngine<ReportingEntityEvent<String, String>>, RequestRouter<ReportingEntityEvent<String, String>>> pipeline;

    @Mock
    private PipelineEngine<ReportingEntityEvent<String, String>> engine;

    @Mock
    private ReportingEntityEventProvider<String, String> provider;
    private ReportingEventPipelineProducer<String, String> producer;

    @Captor
    private ArgumentCaptor<List<ReportingEntityEvent<String, String>>> captor;

    @BeforeEach
    public void setup() {
        pipeline = Pipeline.mono("TestReportingEventPipelineProducer", engine, RequestRouter.create());
        producer = new ReportingEventPipelineProducer<>(pipeline, provider, null);
    }

    @AfterEach
    public void teardown() {
        producer.stop(); // start and stop operations are idempotent
    }

    @Test
    @DisplayName("ReportingEventPipelineProducer::new rejects null constructor arguments")
    public void reporting_event_pipeline_producer_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new ReportingEventPipelineProducer<>(null, provider, ZERO));
        assertThrows(NullPointerException.class, () -> new ReportingEventPipelineProducer<>(pipeline, (ReportingEntityEventProvider<String, String>) null, ZERO));
        assertDoesNotThrow(() -> new ReportingEventPipelineProducer<>(pipeline, provider, null));
        assertThrows(NullPointerException.class, () -> new ReportingEventPipelineProducer<>(null, Set.of(provider), ZERO));
        assertThrows(NullPointerException.class, () -> new ReportingEventPipelineProducer<>(pipeline, (Set<ReportingEntityEventProvider<String, String>>) null, ZERO));
        assertDoesNotThrow(() -> new ReportingEventPipelineProducer<>(pipeline, Set.of(provider), null));
    }

    @Test
    @Timeout(value = 5, unit = TimeUnit.SECONDS, threadMode = Timeout.ThreadMode.SEPARATE_THREAD)
    @DisplayName("ReportingEventPipelineProducer receives messages from consumer and passes them to pipeline")
    public void reporting_event_pipeline_producer_message_passing() {
        final List<ReportingEntityEvent<String, String>> events = List.of(mock(ReportingEntityEvent.class), mock(ReportingEntityEvent.class));
        when(provider.events()).thenReturn(events);

        assertDoesNotThrow(producer::start);
        assertDoesNotThrow(producer::run);

        verify(engine, times(1)).receive(captor.capture());
        assertEquals(events, captor.getValue());
    }

    @Test
    @DisplayName("ReportingEventPipelineProducer::poll polls reporting entity event providers and returns results")
    public void reporting_event_pipeline_producer_poll_invokes_consumer_poll() {
        final List<ReportingEntityEvent<String, String>> events = List.of(mock(ReportingEntityEvent.class));
        when(provider.events()).thenReturn(events);

        final var requests = assertDoesNotThrow(() -> producer.poll());
        final var expected = Request.of(events);
        assertEquals(List.of(expected), requests.toList());
        verify(provider, times(1)).events();
    }

    @Test
    @DisplayName("ReportingEventPipelineProducer::poll returns empty stream if underlying event provider throws an exception")
    public void reporting_event_pipeline_producer_poll_returns_empty_stream_if_underlying_event_provider_throws_an_exception() {
        when(provider.events()).thenThrow(new RuntimeException("Unable to fetch events"));

        final var requests = assertDoesNotThrow(() -> producer.poll());
        assertEquals(List.of(), requests.toList());
        verify(provider, times(1)).events();
    }
}
