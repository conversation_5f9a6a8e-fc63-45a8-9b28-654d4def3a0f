package global.symbio.billing.core.reporting.api.pipeline;

import com.lmax.disruptor.*;
import com.lmax.disruptor.dsl.Disruptor;
import com.lmax.disruptor.dsl.ProducerType;
import global.symbio.billing.core.pipeline.api.Pipeline;
import global.symbio.billing.core.pipeline.api.PipelineEngine;
import global.symbio.billing.core.pipeline.api.request.batch.EngineRequestBatchingStrategy;
import global.symbio.billing.core.pipeline.api.request.handler.MultipleItemRequestHandler;
import global.symbio.billing.core.pipeline.api.request.handler.SingleItemRequestHandler;
import global.symbio.billing.core.pipeline.api.request.router.DefaultRequestRouter;
import global.symbio.billing.core.pipeline.api.request.router.RequestRouter;
import global.symbio.billing.core.pipeline.spi.disruptor.DisruptorPipelineEngine;
import global.symbio.billing.core.pipeline.spi.disruptor.event.batch.BatchConsumer;
import global.symbio.billing.core.pipeline.spi.disruptor.event.batch.BatchingEventHandler;
import global.symbio.billing.core.pipeline.spi.disruptor.event.value.ValueEvent;
import global.symbio.billing.core.pipeline.spi.disruptor.event.value.ValueEventRecycler;
import global.symbio.billing.core.reporting.api.ReportingEntityEvent;
import global.symbio.billing.core.reporting.api.pipeline.consumer.ReportingEventConsumers;
import jakarta.annotation.Nonnull;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.concurrent.TimeUnit;
import java.util.function.IntFunction;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ReportingEventPipeline {

    public static final String PIPELINE_NAME = "reporting-pipeline";
    public static final int DEFAULT_SIZE = 1024 * 8;

    @Nonnull
    public static <K, V, T extends ReportingEntityEvent<K, V>> Pipeline<T, PipelineEngine<T>, RequestRouter<T>> createReportingEventPipeline(
        @Nonnull ProducerType type,
        int size,
        @Nonnull BatchConsumer<T> emitter,
        @Nonnull BatchConsumer<T> acknowledger,
        @Nonnull IntFunction<T[]> allocator
    ) {
        return createReportingEventPipeline(type, size, emitter, acknowledger, ReportingEventConsumers.createEntityCategoriser(), allocator);
    }

    @Nonnull
    public static <K, V, T extends ReportingEntityEvent<K, V>> Pipeline<T, PipelineEngine<T>, RequestRouter<T>> createReportingEventPipeline(
        @Nonnull ProducerType type,
        int size,
        @Nonnull BatchConsumer<T> emitter,
        @Nonnull BatchConsumer<T> acknowledger,
        @Nonnull BatchConsumer<T> categoriser,
        @Nonnull IntFunction<T[]> allocator
    ) {
        final var backoff = PhasedBackoffWaitStrategy.withLock(60, 60, TimeUnit.SECONDS);

        final var factory = Thread.ofPlatform().daemon().name(PIPELINE_NAME + "-", 0L).factory();
        final Disruptor<ValueEvent<T>> disruptor = new Disruptor<>(ValueEvent::new, size, factory, type, backoff);

        final var chain = disruptor
            .handleEventsWith((buffer, sequences) -> create(buffer, sequences, emitter)) // emit reporting events to kafka
            .then((buffer, sequences) -> create(buffer, sequences, acknowledger)) // insert reporting event receipts into database
            .then((buffer, sequences) -> create(buffer, sequences, categoriser)) // categorise reporting events
            .then(ValueEventRecycler.getInstance()); // recycle value events

        final var engine = new DisruptorPipelineEngine<>(disruptor, allocator);
        final var router = DefaultRequestRouter.<T>builder().withSingle(SingleItemRequestHandler.create()).withMultiple(MultipleItemRequestHandler.create(EngineRequestBatchingStrategy.partitioned(size))).build();
        return Pipeline.mono(PIPELINE_NAME, engine, router);
    }

    @Nonnull
    private static <T> BatchEventProcessor<ValueEvent<T>> create(@Nonnull RingBuffer<ValueEvent<T>> buffer, @Nonnull Sequence[] sequences, @Nonnull BatchConsumer<T> consumer) {
        //TODO: refactor into utility class
        final var processor = new BatchEventProcessor<>(buffer, buffer.newBarrier(sequences), new BatchingEventHandler<>(consumer));
        processor.setRewindStrategy(new EventuallyGiveUpBatchRewindStrategy(5L)); //TODO: determine / implement new strategy
        processor.setExceptionHandler(new IgnoreExceptionHandler());
        return processor;
    }
}
