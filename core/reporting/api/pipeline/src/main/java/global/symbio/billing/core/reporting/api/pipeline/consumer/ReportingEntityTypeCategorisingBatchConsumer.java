package global.symbio.billing.core.reporting.api.pipeline.consumer;

import global.symbio.billing.core.pipeline.spi.disruptor.event.batch.BatchConsumer;
import global.symbio.billing.core.reporting.api.ReportingEntityEvent;
import jakarta.annotation.Nonnull;
import lombok.extern.slf4j.Slf4j;

import java.util.Collection;
import java.util.stream.Collectors;

@Slf4j
public record ReportingEntityTypeCategorisingBatchConsumer<K, V, T extends ReportingEntityEvent<K, V>>() implements BatchConsumer<T> {

    @Override
    public void consume(@Nonnull Collection<T> elements) throws Exception {
        final var categories = elements.stream().collect(Collectors.groupingBy(this::categorise, Collectors.counting()));
        categories.forEach((category, count) -> log.info("Reported events of type: {} - {}", category, count));
    }

    @Nonnull
    private String categorise(@Nonnull ReportingEntityEvent<K, V> event) {
        return event.qualifier().getSimpleName();
    }
}