package global.symbio.billing.core.reporting.api.pipeline;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.pipeline.api.Pipeline;
import global.symbio.billing.core.pipeline.api.PipelineEngine;
import global.symbio.billing.core.pipeline.api.producer.PipelineProducer;
import global.symbio.billing.core.pipeline.api.request.Request;
import global.symbio.billing.core.pipeline.api.request.router.RequestRouter;
import global.symbio.billing.core.reporting.api.ReportingEntityEvent;
import global.symbio.billing.core.reporting.api.ReportingEntityEventProvider;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Stream;

@Slf4j
public class ReportingEventPipelineProducer<K, V> extends PipelineProducer<ReportingEntityEvent<K, V>> {

    /**
     * The set of {@link ReportingEntityEventProvider}s to query for events.
     */
    @Nonnull
    private final Set<ReportingEntityEventProvider<K, V>> providers;

    public ReportingEventPipelineProducer(@Nonnull Pipeline<ReportingEntityEvent<K, V>, PipelineEngine<ReportingEntityEvent<K, V>>, RequestRouter<ReportingEntityEvent<K, V>>> pipeline, @Nonnull ReportingEntityEventProvider<K, V> provider, @Nullable Duration period) {
        this(pipeline, Set.of(provider), period);
    }

    public ReportingEventPipelineProducer(@Nonnull Pipeline<ReportingEntityEvent<K, V>, PipelineEngine<ReportingEntityEvent<K, V>>, RequestRouter<ReportingEntityEvent<K, V>>> pipeline, @Nonnull Set<ReportingEntityEventProvider<K, V>> providers, @Nullable Duration period) {
        super(pipeline, period);
        this.providers = Objects.requireNonNull(providers, "providers");
    }

    /**
     * Polls the {@link ReportingEntityEventProvider}s for events and maps each batch of records to a {@link Request}.
     *
     * @return A {@link Stream} of {@link Request} encoded {@link ReportingEntityEvent}s.
     * @see PipelineProducer#poll
     */
    @Override
    @Nonnull
    protected Stream<Request<ReportingEntityEvent<K, V>>> poll() {
        //TODO: consider parallelism
        return providers.stream().map(this::fetch).filter(Objects::nonNull);
    }

    /**
     * Safely poll the {@link ReportingEntityEventProvider} for events.
     * @param provider The provider to poll.
     * @return A {@link Request} of events or null in the case of an exception being thrown.
     */
    @Nullable
    private Request<ReportingEntityEvent<K, V>> fetch(@Nonnull ReportingEntityEventProvider<K, V> provider) {
        try {
            return Request.of(provider.events());
        } catch (Throwable cause) {
            log.warn("Exception fetching events from provider: {}", provider, cause);
            return null;
        }
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this).add("state", getState()).add("providers", providers).add("period", getPeriod()).add("pipeline", pipeline).toString();
    }
}