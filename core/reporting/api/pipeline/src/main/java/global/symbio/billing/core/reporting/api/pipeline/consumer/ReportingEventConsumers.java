package global.symbio.billing.core.reporting.api.pipeline.consumer;

import global.symbio.billing.core.exception.sdk.BillingEntityNotFoundException;
import global.symbio.billing.core.pipeline.spi.disruptor.consumer.kafka.KafkaEntityEmitterBatchConsumer;
import global.symbio.billing.core.pipeline.spi.disruptor.event.batch.BatchConsumer;
import global.symbio.billing.core.reporting.api.ReportingEntityEvent;
import global.symbio.billing.core.reporting.api.ReportingEntityEventProvider;
import io.micronaut.core.util.functional.ThrowingFunction;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.Producer;
import org.apache.kafka.common.header.Header;
import org.apache.kafka.common.header.internals.RecordHeader;

import java.time.ZonedDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.nio.charset.StandardCharsets.UTF_8;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ReportingEventConsumers {

    private static final String HEADER_COUNTRY_CODE = "countryCode";
    private static final String HEADER_SYSTEM_ID = "systemId";
    private static final String HEADER_MAJOR_VERSION = "majorVersion";
    private static final String HEADER_MAJOR_VERSION_VALUE = "1";

    @Nonnull
    public static <K, V> BatchConsumer<ReportingEntityEvent<K, V>> createEntityEmitter(
        @Nonnull String system,
        @Nonnull String country,
        @Nonnull Producer<String, V> producer,
        @Nullable Integer limit,
        boolean transactional
    ) {
        final var systemId = "billing-" + system;
        final Iterable<Header> memoizedHeaders = List.of(
            new RecordHeader(HEADER_COUNTRY_CODE, country.getBytes(UTF_8)),
            new RecordHeader(HEADER_SYSTEM_ID, systemId.getBytes(UTF_8)),
            new RecordHeader(HEADER_MAJOR_VERSION, HEADER_MAJOR_VERSION_VALUE.getBytes(UTF_8))
        );
        final ThrowingFunction<ReportingEntityEvent<K, V>, String, Exception> topic = ReportingEntityEvent::topic;
        final ThrowingFunction<ReportingEntityEvent<K, V>, Integer, Exception> partition = event -> null;
        final ThrowingFunction<ReportingEntityEvent<K, V>, String, Exception> key = ReportingEntityEvent::key;
        final ThrowingFunction<ReportingEntityEvent<K, V>, V, Exception> value = ReportingEntityEvent::entity;
        final ThrowingFunction<ReportingEntityEvent<K, V>, Iterable<Header>, Exception> headers = event -> memoizedHeaders;
        return new KafkaEntityEmitterBatchConsumer<>(producer, null, topic, partition, key, value, headers, transactional, limit);
    }

    @Nonnull
    public static <K, V, T extends ReportingEntityEvent<K, V>> BatchConsumer<T> createDeliveryAcknowledger(
        @Nonnull Set<ReportingEntityEventProvider<K, V>> providers,
        @Nullable Integer limit
    ) {
        final var entityTypeToReportingEventProviderMapping = providers.stream().collect(Collectors.toMap(ReportingEntityEventProvider::qualifier, Function.identity()));
        return new ReportingEventDeliveryAcknowledger<>(entityTypeToReportingEventProviderMapping, limit);
    }

    @Slf4j
    private record ReportingEventDeliveryAcknowledger<K, V, T extends ReportingEntityEvent<K, V>>(
        @Nonnull Map<Class<V>, ReportingEntityEventProvider<K, V>> providers,
        @Nullable Integer limit
    ) implements BatchConsumer<T> {

        private ReportingEventDeliveryAcknowledger {
            Objects.requireNonNull(providers, "providers");
        }

        @Override
        public boolean isRetryable(@Nonnull Throwable cause) {
            //TODO: determine which exceptions are retryable
            return true;
        }

        @Override
        public void consume(@Nonnull Collection<T> elements) {
            final var timestamp = ZonedDateTime.now();
            final var groups = elements.stream().collect(Collectors.groupingBy(ReportingEntityEvent::qualifier));
            for (final var group : groups.entrySet()) {
                final var qualifier = group.getKey();
                final var provider = Optional.ofNullable(providers.get(qualifier)).orElseThrow(() -> new BillingEntityNotFoundException(ReportingEntityEventProvider.class, qualifier.getSimpleName()));
                final var identifiers = group.getValue().stream().map(ReportingEntityEvent::identifier).collect(Collectors.toSet());
                final var count = provider.update(identifiers, timestamp);
                log.trace("Reported {} {} to data-warehousing", count, qualifier.getSimpleName());
            }
        }

        @Override
        public boolean isConsumable(@Nonnull Collection<T> elements) {
            return limit != null && limit <= elements.size();
        }
    }

    @Nonnull
    public static <K, V, T extends ReportingEntityEvent<K, V>> BatchConsumer<T> createEntityCategoriser() {
        return new ReportingEntityTypeCategorisingBatchConsumer<>();
    }
}