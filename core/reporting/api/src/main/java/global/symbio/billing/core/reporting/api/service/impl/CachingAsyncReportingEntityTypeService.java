package global.symbio.billing.core.reporting.api.service.impl;

import com.github.benmanes.caffeine.cache.AsyncLoadingCache;
import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.reporting.api.service.AsyncReportingEntityTypeService;
import global.symbio.billing.core.reporting.persistence.api.event.type.ReportingEntityType;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.util.Objects;
import java.util.concurrent.CompletableFuture;

@Singleton
@VisibleForTesting
record CachingAsyncReportingEntityTypeService(
    @Inject @Nonnull AsyncLoadingCache<Integer, ReportingEntityType> types
) implements AsyncReportingEntityTypeService {

    public CachingAsyncReportingEntityTypeService {
        Objects.requireNonNull(types, "types");
    }

    @Nonnull
    @Override
    public CompletableFuture<ReportingEntityType> lookup(@Nonnull Integer identifier) {
        return types.get(identifier);
    }
}
