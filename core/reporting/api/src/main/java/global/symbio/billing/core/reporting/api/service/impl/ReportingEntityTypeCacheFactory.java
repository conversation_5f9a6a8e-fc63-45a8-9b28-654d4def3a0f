package global.symbio.billing.core.reporting.api.service.impl;

import com.github.benmanes.caffeine.cache.AsyncLoadingCache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.reporting.persistence.api.event.type.ReportingEntityType;
import global.symbio.billing.core.reporting.persistence.api.event.type.ReportingEntityTypeDataAccessObject;
import global.symbio.billing.core.reporting.persistence.api.repository.ReportingEntityTypeRepository;
import io.micronaut.context.annotation.Factory;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.inject.Singleton;

import java.time.Duration;

@VisibleForTesting
@Factory
class ReportingEntityTypeCacheFactory {

    public static final String CACHE_NAME = "reporting-entity-types";
    private static final Duration REFRESH_INTERVAL = Duration.ofMinutes(5L);

    @Inject
    @Nonnull
    @Singleton
    @Named(CACHE_NAME)
    public AsyncLoadingCache<Integer, ReportingEntityType> create_cache(
        @Nonnull ReportingEntityTypeRepository<ReportingEntityTypeDataAccessObject> types
    ) {
        final var loader = new ReportingEntityTypeCacheLoader(types);
        return Caffeine.newBuilder().refreshAfterWrite(REFRESH_INTERVAL).buildAsync(loader);
    }
}