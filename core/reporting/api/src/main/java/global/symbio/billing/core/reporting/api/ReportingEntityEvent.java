package global.symbio.billing.core.reporting.api;

import jakarta.annotation.Nonnull;

import java.util.Objects;

public interface ReportingEntityEvent<K, V> {

    /**
     * The type discriminator / qualifier for the entity that we are reporting to DWH. Primarily used for aggregation.
     */
    @Nonnull
    Class<V> qualifier();

    /**
     * The primary key of the entity we are reporting to DWH.
     */
    @Nonnull
    K identifier();

    /**
     * The entity we are reporting to DWH.
     */
    @Nonnull
    V entity();

    /**
     * The DWH reporting Kafka topic we are delivering the entity to.
     */
    @Nonnull
    String topic();

    /**
     * The key of this entity within the DWH reporting Kafka topic.
     */
    @Nonnull
    String key();

    @Nonnull
    static <K, V> ReportingEntityEvent<K, V> create(
        @Nonnull Class<V> qualifier,
        @Nonnull K identifier,
        @Nonnull V entity,
        @Nonnull String topic,
        @Nonnull String key
    ) {
        return new ReportingEntityEventImpl<>(qualifier, identifier, entity, topic, key);
    }

    record ReportingEntityEventImpl<K, V>(
        @Nonnull Class<V> qualifier,
        @Nonnull K identifier,
        @Nonnull V entity,
        @Nonnull String topic,
        @Nonnull String key
    ) implements ReportingEntityEvent<K, V> {

        public ReportingEntityEventImpl {
            Objects.requireNonNull(qualifier, "qualifier");
            Objects.requireNonNull(identifier, "identifier");
            Objects.requireNonNull(entity, "entity");
            Objects.requireNonNull(topic, "topic");
            Objects.requireNonNull(key, "key");
        }
    }
}