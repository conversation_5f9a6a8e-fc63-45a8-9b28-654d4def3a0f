package global.symbio.billing.core.reporting.api;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.exception.sdk.BillingEntityNotFoundException;
import global.symbio.billing.core.reporting.api.service.ReportingEntityTypeService;
import global.symbio.billing.core.reporting.persistence.api.event.type.ReportingEntityType;
import io.micronaut.data.model.Pageable;
import jakarta.annotation.Nonnull;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
public abstract class ReportingEntityEventProviderAdapter<K, V> implements ReportingEntityEventProvider<K, V> {

    @Nonnull
    protected final Class<V> qualifier;

    @Nonnull
    protected final ReportingEntityTypeService types;

    protected final int type;

    public ReportingEntityEventProviderAdapter(
        @Nonnull Class<V> qualifier,
        @Nonnull ReportingEntityTypeService types,
        int type
    ) {
        this.qualifier = Objects.requireNonNull(qualifier, "qualifier");
        this.types = Objects.requireNonNull(types, "types");
        this.type = type;
    }

    @Nonnull
    @Override
    public Class<V> qualifier() {
        return qualifier;
    }

    @Nonnull
    @Override
    public List<ReportingEntityEvent<K, V>> events() {
        final var configuration = lookup();
        if (!configuration.getEnabled()) {
            log.trace("Reporting disabled for: {}", configuration);
            return List.of();
        }
        final var pagination = Pageable.from(0, configuration.getQuantity());
        return fetch(configuration, pagination);
    }

    @Nonnull
    protected abstract List<ReportingEntityEvent<K, V>> fetch(@Nonnull ReportingEntityType configuration, @Nonnull Pageable pagination);

    @Nonnull
    protected ReportingEntityType lookup() {
        return Optional.ofNullable(types.lookup(type)).orElseThrow(() -> new BillingEntityNotFoundException(ReportingEntityType.class, type));
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof ReportingEntityEventProviderAdapter<?, ?> adapter)) return false;
        return type == adapter.type && Objects.equals(qualifier, adapter.qualifier) && Objects.equals(types, adapter.types);
    }

    @Override
    public int hashCode() {
        return Objects.hash(qualifier, types, type);
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("qualifier", qualifier)
            .add("types", types)
            .add("type", type)
            .toString();
    }
}