package global.symbio.billing.core.reporting.api;

import jakarta.annotation.Nonnull;

import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.List;
import java.util.function.Function;

public interface ReportingEntityEventProvider<K, V> {

    /**
     * The type discriminator / qualifier for the entity that we are interested in reporting to DWH.
     * Corresponds to {@link ReportingEntityEvent#qualifier}.
     */
    @Nonnull
    Class<V> qualifier();

    /**
     * Retrieves a collection of events that require delivery to DWH.
     */
    @Nonnull
    List<ReportingEntityEvent<K, V>> events();

    /**
     * Marks the events denoted by their respective {@code identifiers} as being delivered to DWH at the given {@code timestamp}.
     *
     * @param identifiers The identifiers of the events to mark as delivered to DWH.
     * @param timestamp   The timestamp at which the events were delivered to DWH.
     */
    long update(@Nonnull Collection<K> identifiers, @Nonnull ZonedDateTime timestamp);

    @Nonnull
    static <K, V> List<ReportingEntityEvent<K, V>> transform(@Nonnull Class<V> qualifier, @Nonnull Collection<V> entities, @Nonnull String topic, @Nonnull Function<V, K> identifier, @Nonnull Function<V, String> key) {
        return entities.stream().map(entity -> ReportingEntityEvent.create(qualifier, identifier.apply(entity), entity, topic, key.apply(entity))).toList();
    }
}