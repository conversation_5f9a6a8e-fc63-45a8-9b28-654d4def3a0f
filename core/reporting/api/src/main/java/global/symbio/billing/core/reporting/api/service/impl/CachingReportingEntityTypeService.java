package global.symbio.billing.core.reporting.api.service.impl;

import com.github.benmanes.caffeine.cache.AsyncLoadingCache;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.reporting.api.service.ReportingEntityTypeService;
import global.symbio.billing.core.reporting.persistence.api.event.type.ReportingEntityType;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.util.Objects;

@Singleton
@VisibleForTesting
record CachingReportingEntityTypeService(
    @Inject @Nonnull LoadingCache<Integer, ReportingEntityType> types
) implements ReportingEntityTypeService {

    public CachingReportingEntityTypeService {
        Objects.requireNonNull(types, "types");
    }

    @Inject
    public CachingReportingEntityTypeService(@Nonnull AsyncLoadingCache<Integer, ReportingEntityType> types) {
        this(types.synchronous());
    }

    @Nullable
    @Override
    public ReportingEntityType lookup(@Nonnull Integer identifier) {
        return types.get(identifier);
    }
}
