package global.symbio.billing.core.reporting.api.service.impl;

import com.github.benmanes.caffeine.cache.CacheLoader;
import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.reporting.persistence.api.event.type.ReportingEntityType;
import global.symbio.billing.core.reporting.persistence.api.event.type.ReportingEntityTypeDataAccessObject;
import global.symbio.billing.core.reporting.persistence.api.repository.ReportingEntityTypeRepository;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.util.Objects;

@VisibleForTesting
record ReportingEntityTypeCacheLoader(
    @Nonnull ReportingEntityTypeRepository<ReportingEntityTypeDataAccessObject> types
) implements CacheLoader<Integer, ReportingEntityType> {

    public ReportingEntityTypeCacheLoader {
        Objects.requireNonNull(types, "types");
    }

    @Nullable
    @Override
    public ReportingEntityType load(Integer key) {
        return types
            .findById(key)
            .map(ReportingEntityTypeDataAccessObject::entity)
            .orElse(null);
    }
}