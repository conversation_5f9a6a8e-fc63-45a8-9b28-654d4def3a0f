package global.symbio.billing.core.reporting.api.service.impl;

import com.github.benmanes.caffeine.cache.AsyncLoadingCache;
import com.github.benmanes.caffeine.cache.LoadingCache;
import global.symbio.billing.core.reporting.api.service.ReportingEntityTypeService;
import global.symbio.billing.core.reporting.persistence.api.event.type.ReportingEntityType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestCachingReportingEntityTypeService {
    
    @Mock
    private LoadingCache<Integer, ReportingEntityType> types;
    
    private ReportingEntityTypeService service;

    @BeforeEach
    public void setup() {
        service = new CachingReportingEntityTypeService(types);
    }

    @Test
    @DisplayName("CachingReportingEntityTypeService::new rejects null constructor arguments")
    public void caching_reporting_entity_type_service_new_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new CachingReportingEntityTypeService((LoadingCache<Integer, ReportingEntityType>) null));
        assertThrows(NullPointerException.class, () -> new CachingReportingEntityTypeService((AsyncLoadingCache<Integer, ReportingEntityType>) null));
    }

    @Test
    @DisplayName("CachingReportingEntityTypeService::lookup returns reporting entity type from cache when type in the cache")
    public void caching_reporting_entity_type_service_lookup_returns_reporting_entity_type_from_cache_when_type_in_the_cache() {
        final var type = mock(ReportingEntityType.class);
        when(types.get(anyInt())).thenReturn(type);
        final var result = assertDoesNotThrow(() -> service.lookup(1337));
        assertSame(type, result);
        verify(types, times(1)).get(eq(1337));
    }

    @Test
    @DisplayName("CachingReportingEntityTypeService::lookup returns null when type not in the cache")
    public void caching_reporting_entity_type_service_lookup_returns_null_when_type_not_in_cache() {
        when(types.get(anyInt())).thenReturn(null);
        final var result = assertDoesNotThrow(() -> service.lookup(1337));
        assertNull(result);
        verify(types, times(1)).get(eq(1337));
    }

    @Test
    @DisplayName("CachingReportingEntityTypeService::lookup throws exception when cache hit throws exception")
    public void caching_reporting_entity_type_service_lookup_throws_exception_when_cache_hit_throws_exception() {
        final var failure = new RuntimeException("Underlying exception");
        when(types.get(anyInt())).thenThrow(failure);
        final var cause = assertThrows(RuntimeException.class, () -> service.lookup(1337));
        assertSame(failure, cause);
        verify(types, times(1)).get(eq(1337));
    }
}