package global.symbio.billing.core.reporting.api.service.impl;

import com.github.benmanes.caffeine.cache.AsyncLoadingCache;
import global.symbio.billing.core.reporting.api.service.AsyncReportingEntityTypeService;
import global.symbio.billing.core.reporting.persistence.api.event.type.ReportingEntityType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestCachingAsyncReportingEntityTypeService {

    @Mock
    private AsyncLoadingCache<Integer, ReportingEntityType> types;

    private AsyncReportingEntityTypeService service;

    @BeforeEach
    public void setup() {
        service = new CachingAsyncReportingEntityTypeService(types);
    }

    @Test
    @DisplayName("CachingAsyncReportingEntityTypeService::new rejects null constructor arguments")
    public void caching_async_reporting_entity_type_service_new_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new CachingAsyncReportingEntityTypeService(null));
    }

    @Test
    @DisplayName("CachingAsyncReportingEntityTypeService::lookup returns reporting entity type from cache when type in the cache")
    public void caching_async_reporting_entity_type_service_lookup_returns_reporting_entity_type_from_cache_when_type_in_the_cache() {
        final var type = mock(ReportingEntityType.class);
        when(types.get(anyInt())).thenReturn(CompletableFuture.completedFuture(type));
        final var future = assertDoesNotThrow(() -> service.lookup(1337));
        final var result = assertDoesNotThrow(future::join);
        assertSame(type, result);
        verify(types, times(1)).get(eq(1337));
    }

    @Test
    @DisplayName("CachingAsyncReportingEntityTypeService::lookup returns null when type not in the cache")
    public void caching_async_reporting_entity_type_service_lookup_returns_null_when_type_not_in_cache() {
        when(types.get(anyInt())).thenReturn(CompletableFuture.completedFuture(null));
        final var future = assertDoesNotThrow(() -> service.lookup(1337));
        final var result = assertDoesNotThrow(future::join);
        assertNull(result);
        verify(types, times(1)).get(eq(1337));
    }

    @Test
    @DisplayName("CachingAsyncReportingEntityTypeService::lookup throws exception when cache hit throws exception")
    public void caching_async_reporting_entity_type_service_lookup_throws_exception_when_cache_hit_throws_exception() {
        final var failure = new RuntimeException("Underlying exception");
        when(types.get(anyInt())).thenReturn(CompletableFuture.failedFuture(failure));
        final var future = assertDoesNotThrow(() -> service.lookup(1337));
        final var cause = assertThrows(CompletionException.class, future::join);
        assertSame(failure, cause.getCause());
        verify(types, times(1)).get(eq(1337));
    }
}
