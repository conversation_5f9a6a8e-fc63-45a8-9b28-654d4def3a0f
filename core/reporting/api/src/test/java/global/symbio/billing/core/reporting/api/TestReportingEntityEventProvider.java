package global.symbio.billing.core.reporting.api;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.function.Function;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class TestReportingEntityEventProvider {

    @Test
    @DisplayName("ReportingEntityEventProvider::transform constructs list of ReportingEntityEvents")
    public void reporting_event_provider_transform_constructs_list_of_reporting_entity_events() {
        final var qualifier = Integer.class;
        final var entities = List.of(1, 2, 3);
        final var topic = "a.b.c";
        final Function<Integer, Integer> identifier = (i) -> i * i;
        final Function<Integer, String> key = (i) -> "key: " + i;

        final var transformed = assertDoesNotThrow(() -> ReportingEntityEventProvider.transform(qualifier, entities, topic, identifier, key));
        final var expected = List.of(
            ReportingEntityEvent.create(qualifier, 1, 1, topic, "key: 1"),
            ReportingEntityEvent.create(qualifier, 4, 2, topic, "key: 2"),
            ReportingEntityEvent.create(qualifier, 9, 3, topic, "key: 3")
        );
        assertEquals(expected, transformed);
    }
}
