package global.symbio.billing.core.reporting.api.service.impl;

import com.github.benmanes.caffeine.cache.CacheLoader;
import global.symbio.billing.core.reporting.persistence.api.event.type.ReportingEntityType;
import global.symbio.billing.core.reporting.persistence.api.event.type.ReportingEntityTypeDataAccessObject;
import global.symbio.billing.core.reporting.persistence.api.repository.ReportingEntityTypeRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestReportingEntityTypeCacheLoader {

    @Mock
    private ReportingEntityTypeRepository<ReportingEntityTypeDataAccessObject> types;

    private CacheLoader<Integer, ReportingEntityType> loader;

    @BeforeEach
    public void setup() {
        loader = new ReportingEntityTypeCacheLoader(types);
    }

    @Test
    @DisplayName("ReportingEntityTypeCacheLoader::new rejects null constructor arguments")
    public void reporting_entity_type_cache_loader_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new ReportingEntityTypeCacheLoader(null));
    }

    @Test
    @DisplayName("ReportingEntityTypeCacheLoader::load invokes ReportingEntityTypeRepository lookup by identifier")
    public void reporting_entity_type_cache_loader_load_invokes_reporting_entity_type_repository_lookup() {
        final var type = mock(ReportingEntityTypeDataAccessObject.class, CALLS_REAL_METHODS);
        when(type.getIdentifier()).thenReturn(420);
        when(types.findById(anyInt())).thenReturn(Optional.of(type));
        final var result = assertDoesNotThrow(() -> loader.load(420));
        assertSame(type, result.data());
        verify(types, times(1)).findById(eq(420));
    }
}
