package global.symbio.billing.core.reporting.api;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static global.symbio.billing.core.reporting.api.ReportingEntityEvent.create;
import static org.junit.jupiter.api.Assertions.*;

public class TestReportingEntityEvent {

    @Test
    @DisplayName("ReportingEntityEvent::create rejects null arguments")
    public void reporting_entity_event_create_rejects_null_arguments() {
        assertThrows(NullPointerException.class, () -> create(null, "identifier", "entity", "topic", "key"));
        assertThrows(NullPointerException.class, () -> create(String.class, null, "entity", "topic", "key"));
        assertThrows(NullPointerException.class, () -> create(String.class, "identifier", null, "topic", "key"));
        assertThrows(NullPointerException.class, () -> create(String.class, "identifier", "entity", null, "key"));
        assertThrows(NullPointerException.class, () -> create(String.class, "identifier", "entity", "topic", null));
    }

    @Test
    @DisplayName("ReportingEntityEvent::create returns instanceof ReportingEntityEvent")
    public void reporting_entity_event_create_returns_instance_of_reporting_entity_event() {
        final var event = assertDoesNotThrow(() -> create(String.class, "identifier", "entity", "topic", "key"));
        assertInstanceOf(ReportingEntityEvent.class, event);
        assertInstanceOf(ReportingEntityEvent.ReportingEntityEventImpl.class, event);
    }
}