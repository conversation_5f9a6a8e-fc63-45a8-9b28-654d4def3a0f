package global.symbio.billing.core.reporting.api;

import global.symbio.billing.core.exception.sdk.BillingEntityNotFoundException;
import global.symbio.billing.core.reporting.api.service.ReportingEntityTypeService;
import global.symbio.billing.core.reporting.persistence.api.event.type.ReportingEntityType;
import io.micronaut.data.model.Pageable;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestReportingEntityEventProviderAdapter {

    @Mock
    private ReportingEntityTypeService types;

    private ReportingEntityEventProviderAdapter<Integer, Integer> adapter;

    @BeforeEach
    public void setup() {
        adapter = Mockito.spy(new IntegerReportingEntityEventProviderAdapter(types));
    }

    @Test
    @DisplayName("ReportingEntityEventProviderAdapter::new rejects null constructor arguments")
    public void reporting_entity_event_provider_adapter_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new IntegerReportingEntityEventProviderAdapter(null));
    }

    @Test
    @DisplayName("ReportingEntityEventProviderAdapter::type is -1")
    public void reporting_entity_event_provider_adapter_type_is_minus_one() {
        assertEquals(-1, adapter.type);
    }

    @Test
    @DisplayName("ReportingEntityEventProviderAdapter::qualifier is Integer")
    public void reporting_entity_event_provider_adapter_qualifier_is_integer() {
        assertSame(Integer.class, adapter.qualifier());
    }

    @Test
    @DisplayName("ReportingEntityEventProviderAdapter::lookup resolves ReportingEntityType configuration")
    public void reporting_entity_event_provider_adapter_lookup_resolves_reporting_entity_type_configuration() {
        final var configuration = mock(ReportingEntityType.class);
        when(types.lookup(anyInt())).thenReturn(configuration);
        final var result = assertDoesNotThrow(adapter::lookup);
        assertSame(configuration, result);
        verify(types, times(1)).lookup(adapter.type);
    }

    @Test
    @DisplayName("ReportingEntityEventProviderAdapter::lookup fails to resolve ReportingEntityType configuration")
    public void reporting_entity_event_provider_adapter_lookup_fails_to_resolve_reporting_entity_type_configuration() {
        when(types.lookup(anyInt())).thenReturn(null);
        final var cause = assertThrows(BillingEntityNotFoundException.class, adapter::lookup);
        assertEquals("Entity of type " + ReportingEntityType.class.getSimpleName() + " with identifier " + adapter.type + " could not be found.", cause.getMessage());
        verify(types, times(1)).lookup(adapter.type);
    }

    @Test
    @DisplayName("ReportingEntityEventProviderAdapter::events fetches no results when configuration is disabled")
    public void reporting_entity_event_provider_adapter_events_when_configuration_is_disabled_does_not_fetch_events() {
        final var configuration = mock(ReportingEntityType.class);
        when(configuration.getEnabled()).thenReturn(false);
        when(types.lookup(anyInt())).thenReturn(configuration);
        final var events = assertDoesNotThrow(adapter::events);
        assertEquals(List.of(), events);
        verify(adapter, never()).fetch(any(ReportingEntityType.class), any(Pageable.class));
    }

    @Test
    @DisplayName("ReportingEntityEventProviderAdapter::events fetches results when configuration is enabled")
    public void reporting_entity_event_provider_adapter_events_when_configuration_is_enabled_fetches_events() {
        final var configuration = mock(ReportingEntityType.class);
        when(configuration.getEnabled()).thenReturn(true);
        when(configuration.getQuantity()).thenReturn(123);
        when(types.lookup(anyInt())).thenReturn(configuration);
        final var events = assertDoesNotThrow(adapter::events);
        assertEquals(List.of(), events);
        verify(adapter, times(1)).fetch(any(ReportingEntityType.class), any(Pageable.class));
    }

    private static class IntegerReportingEntityEventProviderAdapter extends ReportingEntityEventProviderAdapter<Integer, Integer> {

        public IntegerReportingEntityEventProviderAdapter(
            @Nonnull ReportingEntityTypeService types
        ) {
            super(Integer.class, types, -1);
        }

        @Nonnull
        @Override
        protected List<ReportingEntityEvent<Integer, Integer>> fetch(@Nonnull ReportingEntityType configuration, @Nonnull Pageable pagination) {
            return List.of();
        }

        @Override
        public long update(@Nonnull Collection<Integer> identifiers, @Nonnull ZonedDateTime timestamp) {
            return 0;
        }
    }
}