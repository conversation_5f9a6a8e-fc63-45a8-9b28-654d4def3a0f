package global.symbio.billing.core.reporting.persistence.spi.jdbc.jpa.event.type;

import global.symbio.billing.core.reporting.persistence.api.event.type.ReportingEntityTypeDataAccessObject;
import io.micronaut.core.annotation.Introspected;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Immutable;

@Immutable @Entity
@Table(name = "reporting_entity_type")
@Getter @Setter
@Introspected(includedAnnotations = Entity.class)
public class JPAReportingEntityTypeDataAccessObject extends ReportingEntityTypeDataAccessObject {

    @Id
    @Nonnull
    @Column(name = "id", unique = true, nullable = false, updatable = false)
    private Integer identifier;

    @Nonnull
    @Column(name = "name", unique = true, nullable = false, length = 64)
    private String name;

    @Nonnull
    @Column(name = "quantity", nullable = false)
    private Integer quantity;

    @Nullable
    @Column(name = "backlog", nullable = true)
    private Integer backlog;

    @Nonnull
    @Column(name = "enabled", nullable = false)
    private Boolean enabled;

    @Nonnull
    @Override
    public JPAReportingEntityTypeDataAccessObject identifier(@Nonnull Integer identifier) {
        setIdentifier(identifier);
        return this;
    }

    @Nonnull
    @Override
    public JPAReportingEntityTypeDataAccessObject name(@Nonnull String name) {
        setName(name);
        return this;
    }

    @Nonnull
    @Override
    public ReportingEntityTypeDataAccessObject quantity(@Nonnull Integer quantity) {
        setQuantity(quantity);
        return this;
    }

    @Nonnull
    @Override
    public ReportingEntityTypeDataAccessObject backlog(@Nonnull Integer days) {
        setBacklog(days);
        return this;
    }

    @Nonnull
    @Override
    public ReportingEntityTypeDataAccessObject enabled(@Nonnull Boolean enabled) {
        setEnabled(enabled);
        return this;
    }
}
