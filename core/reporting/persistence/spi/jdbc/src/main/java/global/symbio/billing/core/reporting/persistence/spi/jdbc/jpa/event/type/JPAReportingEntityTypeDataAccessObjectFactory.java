package global.symbio.billing.core.reporting.persistence.spi.jdbc.jpa.event.type;

import global.symbio.billing.core.reporting.persistence.api.event.type.ReportingEntityTypeDataAccessObject;
import global.symbio.billing.core.reporting.persistence.api.event.type.ReportingEntityTypeDataAccessObjectFactory;
import jakarta.annotation.Nonnull;

public class JPAReportingEntityTypeDataAccessObjectFactory implements ReportingEntityTypeDataAccessObjectFactory {

    @Nonnull
    @Override
    public Class<? extends ReportingEntityTypeDataAccessObject> type() {
        return JPAReportingEntityTypeDataAccessObject.class;
    }

    @Nonnull
    @Override
    public ReportingEntityTypeDataAccessObject create() {
        return new JPAReportingEntityTypeDataAccessObject();
    }
}
