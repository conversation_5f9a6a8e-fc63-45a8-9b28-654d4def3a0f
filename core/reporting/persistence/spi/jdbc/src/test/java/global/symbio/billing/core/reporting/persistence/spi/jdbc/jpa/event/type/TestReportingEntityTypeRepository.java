package global.symbio.billing.core.reporting.persistence.spi.jdbc.jpa.event.type;

import global.symbio.billing.core.persistence.api.Repositories;
import global.symbio.billing.core.reporting.persistence.api.repository.ReportingEntityTypeRepository;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

public class TestReportingEntityTypeRepository {

    @Test
    @DisplayName("ReportingEntityTypeRepository is connected to read-only datasource")
    public void test_read_only_repository() {
        Assertions.assertTrue(ReportingEntityTypeRepository.class.isAssignableFrom(ReadOnlyJPAReportingEntityTypeRepository.class));
        Assertions.assertTrue(Repositories.isReadOnlyRepository(ReadOnlyJPAReportingEntityTypeRepository.class));
    }

    @Test
    @DisplayName("ReportingEntityTypeRepository is connected to read-write datasource")
    public void test_read_write_repository() {
        Assertions.assertTrue(ReportingEntityTypeRepository.class.isAssignableFrom(ReadWriteJPAReportingEntityTypeRepository.class));
        Assertions.assertTrue(Repositories.isReadWriteRepository(ReadWriteJPAReportingEntityTypeRepository.class));
    }
}