package global.symbio.billing.core.reporting.persistence.api.event.type;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ReportingEntityTypeIdentifiers {

    public static final int REPORTING_ENTITY_TYPE_INVOICE = 1;
    public static final int REPORTING_ENTITY_TYPE_TRANSACTION = 2;
    public static final int REPORTING_ENTITY_TYPE_INVOICE_TRANSACTION = 3;
}