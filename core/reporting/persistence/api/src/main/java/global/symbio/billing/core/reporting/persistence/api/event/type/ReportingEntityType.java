package global.symbio.billing.core.reporting.persistence.api.event.type;

import global.symbio.billing.core.persistence.api.Entity;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.time.Duration;
import java.time.ZonedDateTime;

public class ReportingEntityType extends Entity<Integer, ReportingEntityTypeDataAccessObject> {

    public ReportingEntityType(@Nonnull ReportingEntityTypeDataAccessObject data) {
        super(data);
    }

    @Nonnull
    public String getName() {
        return data().getName();
    }

    @Nonnull
    public Integer getQuantity() {
        return data().getQuantity();
    }

    @Nullable
    public Integer getBacklog() {
        return data().getBacklog();
    }

    @Nullable
    public ZonedDateTime getSearchTimestamp() {
        return getSearchTimestamp(ZonedDateTime.now());
    }

    @Nonnull
    public Boolean getEnabled() {
        return data().getEnabled();
    }

    @Nullable
    public ZonedDateTime getSearchTimestamp(@Nonnull ZonedDateTime timestamp) {
        final var backlog = getBacklog();
        if (backlog == null) {
            return null;
        }
        final var offset = Duration.ofDays(backlog);
        return timestamp.minus(offset);
    }
}
