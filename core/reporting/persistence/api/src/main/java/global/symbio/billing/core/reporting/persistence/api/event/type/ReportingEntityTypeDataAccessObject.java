package global.symbio.billing.core.reporting.persistence.api.event.type;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.persistence.api.DataAccessObject;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.util.Objects;

public abstract class ReportingEntityTypeDataAccessObject implements DataAccessObject<Integer, ReportingEntityTypeDataAccessObject> {

    @Nonnull
    public abstract String getName();

    @Nonnull
    public abstract ReportingEntityTypeDataAccessObject name(@Nonnull String name);

    @Nonnull
    public abstract Integer getQuantity();

    @Nonnull
    public abstract ReportingEntityTypeDataAccessObject quantity(@Nonnull Integer quantity);

    @Nullable
    public abstract Integer getBacklog();

    @Nonnull
    public abstract ReportingEntityTypeDataAccessObject backlog(@Nonnull Integer days);

    @Nonnull
    public abstract Boolean getEnabled();

    @Nonnull
    public abstract ReportingEntityTypeDataAccessObject enabled(@Nonnull Boolean enabled);

    @Nonnull
    @Override
    public ReportingEntityType entity() {
        return new ReportingEntityType(this);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (!(obj instanceof ReportingEntityTypeDataAccessObject type)) return false;
        return Objects.equals(getIdentifier(), type.getIdentifier()) && Objects.equals(getName(), type.getName()) && Objects.equals(getQuantity(), type.getQuantity()) && Objects.equals(getBacklog(), type.getBacklog()) && Objects.equals(getEnabled(), type.getEnabled());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getIdentifier(), getName(), getQuantity(), getBacklog(), getEnabled());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("identifier", getIdentifier())
            .add("name", getName())
            .add("quantity", getQuantity())
            .add("backlog", getBacklog())
            .add("enabled", getEnabled())
            .toString();
    }
}
