package global.symbio.billing.core.micronaut.authentication;

import io.micronaut.context.annotation.Property;
import io.micronaut.context.annotation.Requires;
import io.micronaut.context.env.Environment;
import io.micronaut.http.HttpRequest;
import io.micronaut.security.authentication.AuthenticationRequest;
import io.micronaut.security.authentication.AuthenticationResponse;
import io.micronaut.security.authentication.provider.ReactiveAuthenticationProvider;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import org.reactivestreams.Publisher;
import reactor.core.publisher.Flux;
import reactor.core.publisher.FluxSink;

import java.util.Map;
import java.util.Objects;

@Singleton
@Requires(env = Environment.CLOUD)
public class ServiceAuthenticationProvider implements ReactiveAuthenticationProvider<HttpRequest<?>, String, String> {

    @Nonnull
    private final Map<String, String> credentials;

    @Inject
    public ServiceAuthenticationProvider(@Nonnull @Property(name = "service-credentials") Map<String, String> credentials) {
        this.credentials = Objects.requireNonNull(credentials, "credentials");
    }

    @Override
    @Nonnull
    public Publisher<AuthenticationResponse> authenticate(@Nullable HttpRequest<?> http, @Nonnull AuthenticationRequest<String, String> auth) {
        return Flux.create(emitter -> {
            final var identity = auth.getIdentity();
            if (identity instanceof String username) {
                final var password = credentials.get(username);
                final var authenticated = password != null && password.equals(auth.getSecret());
                if (authenticated) {
                    emitter.next(AuthenticationResponse.success(username));
                    emitter.complete();
                } else {
                    emitter.error(AuthenticationResponse.exception());
                }
            } else {
                emitter.error(AuthenticationResponse.exception());
            }
        }, FluxSink.OverflowStrategy.ERROR);
    }
}