package global.symbio.billing.core.micronaut.environment;

import io.micronaut.context.annotation.ConfigurationProperties;
import io.micronaut.context.annotation.Context;
import jakarta.annotation.Nonnull;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Context
@Getter
@Setter
@ConfigurationProperties("validation.country-platform")
public class CountryPlatformValidationConfiguration {
    private boolean enabled;
    private Map<String, List<String>> rules;
    private Map<String, Boolean> validationCache;

    public CountryPlatformValidationConfiguration() {
        this.enabled = false;
        this.rules = new HashMap<>();
        this.validationCache = new ConcurrentHashMap<>();
    }

    public void setRules(Map<String, List<String>> rules) {
        this.rules = rules.entrySet().stream()
            .collect(Collectors.toMap(
                entry -> entry.getKey().toLowerCase(),
                entry -> entry.getValue().stream()
                    .map(String::toLowerCase)
                    .collect(Collectors.toList())
            ));
    }

    public boolean isEnabled(@Nonnull String country, @Nonnull String platform) {
        Objects.requireNonNull(country, "country must not be null");
        Objects.requireNonNull(platform, "platform must not be null");
        // If validation is disabled, always return true
        if (!enabled) {
            return true;
        }
        if (country.isEmpty() || platform.isEmpty()) {
            return false;
        }
        // If rules are empty/null, allow all countries and platforms
        if (rules.isEmpty()) {
            return true;
        }
        final var lowerCaseCountry = country.toLowerCase();
        final var lowerCasePlatform = platform.toLowerCase();
        final var cacheKey = lowerCaseCountry+"|"+lowerCasePlatform;
        return validationCache.computeIfAbsent(cacheKey, k -> validateCountryPlatform(lowerCaseCountry, lowerCasePlatform));
    }

    private boolean validateCountryPlatform(@Nonnull String country, @Nonnull String platform) {
        final var platforms = rules.get(country);
        if (platforms == null || platforms.isEmpty()) {
            return true;
        }
        // Check if any configured platform is contained in the platform input
        for (var p : platforms) {
            if (platform.contains(p)) {
                return true;
            }
        }
        return false;
    }
}