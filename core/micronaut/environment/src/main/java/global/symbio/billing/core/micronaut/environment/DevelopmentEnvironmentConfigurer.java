package global.symbio.billing.core.micronaut.environment;

import co.elastic.apm.attach.ElasticApmAttacher;
import io.micronaut.context.ApplicationContext;
import io.micronaut.context.ApplicationContextBuilder;
import io.micronaut.context.ApplicationContextConfigurer;
import io.micronaut.context.annotation.ContextConfigurer;
import io.micronaut.context.env.Environment;
import io.micronaut.core.annotation.NonNull;
import jakarta.annotation.Nonnull;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@ContextConfigurer
public class DevelopmentEnvironmentConfigurer implements ApplicationContextConfigurer {

    @Nonnull
    private static final String DEFAULT_ENVIRONMENT = Environment.DEVELOPMENT;

    @Override
    public void configure(ApplicationContextBuilder builder) {
        log.info("Configuring Micronaut with default environment: {}", DEFAULT_ENVIRONMENT);
        builder.defaultEnvironments(DEFAULT_ENVIRONMENT);
    }

    @Override
    public void configure(@NonNull ApplicationContext context) {
        if (context.getEnvironment().getActiveNames().contains(DEFAULT_ENVIRONMENT)) {
            log.info("Application running in {} environment - skipping attachment of APM Agent.", DEFAULT_ENVIRONMENT);
        } else {
            log.info("Attaching APM Agent.");
            ElasticApmAttacher.attach();
        }
    }
}
