package global.symbio.billing.core.micronaut.environment;

import com.google.common.base.MoreObjects;
import io.micronaut.context.annotation.Context;
import io.micronaut.context.annotation.Property;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import lombok.Getter;

import java.util.Objects;
import java.util.Set;

@Context
@Getter
public final class EnvironmentConfiguration {

    private static final String CONFIGURATION_ENVIRONMENT = "environment";
    private static final String CONFIGURATION_COUNTRIES = "countries";

    private static final String ENV_PRODUCTION = "prod";
    private static final String ENV_UAT = "uat";
    private static final String ENV_REVIEW = "review";
    private static final String ENV_DEVELOPMENT = "dev";

    public enum DeploymentEnvironment {
        PRODUCTION,
        UAT,
        REVIEW,
        DEVELOPMENT
        ;

        @Nonnull
        public static DeploymentEnvironment parse(@Nonnull String configuration) {
            if (configuration.startsWith(EnvironmentConfiguration.ENV_PRODUCTION)) {
                return PRODUCTION;
            } else if (configuration.startsWith(ENV_UAT)) {
                return UAT;
            } else if (configuration.startsWith(ENV_REVIEW)) {
                return REVIEW;
            } else if (configuration.startsWith(ENV_DEVELOPMENT)) {
                return DEVELOPMENT;
            } else {
                throw new IllegalStateException("Unknown deployment environment configuration: " + configuration);
            }
        }
    }

    @Nonnull
    private final DeploymentEnvironment environment;

    @Nonnull
    private final Set<String> countries;

    @Inject
    public EnvironmentConfiguration(
        @Nonnull @Property(name = CONFIGURATION_ENVIRONMENT) String environment,
        @Nonnull @Property(name = CONFIGURATION_COUNTRIES) Set<String> countries
    ) {
        this(DeploymentEnvironment.parse(environment), countries);
    }

    public EnvironmentConfiguration(
        @Nonnull DeploymentEnvironment environment,
        @Nonnull Set<String> countries
    ) {
        this.environment = Objects.requireNonNull(environment, "environment");
        this.countries = Objects.requireNonNull(Set.copyOf(countries), "countries");
    }

    public boolean isProduction() {
        return is(DeploymentEnvironment.PRODUCTION);
    }

    public boolean isUAT() {
        return is(DeploymentEnvironment.UAT);
    }

    public boolean isReview() {
        return is(DeploymentEnvironment.REVIEW);
    }

    public boolean isDevelopment() {
        return is(DeploymentEnvironment.DEVELOPMENT);
    }

    private boolean is(@Nonnull DeploymentEnvironment env) {
        return environment.equals(env);
    }

    public boolean inCountry(@Nonnull String country) {
        return countries.contains(country);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof EnvironmentConfiguration that)) return false;
        return getEnvironment() == that.getEnvironment() && Objects.equals(getCountries(), that.getCountries());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getEnvironment(), getCountries());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("environment", environment)
            .add("countries", countries)
            .toString();
    }
}
