package global.symbio.billing.core.micronaut.environment;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

public class TestCountryPlatformValidationConfiguration {

    @Test
    @DisplayName("CountryPlatformValidationConfiguration::new creates disabled config with empty rules")
    void country_platform_validation_configuration_new_creates_disabled_config_with_empty_rules() {
        var config = new CountryPlatformValidationConfiguration();
        config.setEnabled(true);
        allowAllCountriesAndPlatforms(config);
    }

    @Test
    @DisplayName("CountryPlatformValidationConfiguration::isEnabled validates based on configuration")
    void country_platform_validation_configuration_is_enabled_validates_based_on_configuration() {
        var config = new CountryPlatformValidationConfiguration();
        config.setEnabled(true);
        config.setRules(Map.of(
            "NZ", List.of("MVP04", "MVPCI", "MVP CI"),
            "au", List.of("mvpci", "MVP CI")
        ));

        // Verify all cases
        assertTrue(config.isEnabled("NZ", "Sonar_MVP04 sonar"));
        assertTrue(config.isEnabled("NZ", "Sonar_MVPCI sonar"));
        assertFalse(config.isEnabled("NZ", "Sonar_MVP21 sonar"));
        assertFalse(config.isEnabled("NZ", "Sonar_MVP02 sonar"));
        assertTrue(config.isEnabled("AU", "Sonar_MVPCI sonar"));
        assertTrue(config.isEnabled("AU", "Sonar_MVP CI sonar"));
        assertFalse(config.isEnabled("AU", "Sonar_MVP21 sonar"));
        assertFalse(config.isEnabled("AU", "Sonar_MVPAAA sonar"));
        assertFalse(config.isEnabled("AU", "Sonar_MVP04 sonar"));
        assertFalse(config.isEnabled("AU", "Sonar_Sonar MVP02 QA SG"));
        assertTrue(config.isEnabled("nz", "sonar_mvp04 sonar"));
        assertTrue(config.isEnabled("nz", "Sonar_mvpci sonar"));
        assertTrue(config.isEnabled("NZ", "Sonar_MVP CI sonar"));
        assertFalse(config.isEnabled("nz", "Sonar_mvp21 sonar"));
        assertFalse(config.isEnabled("nz", "Sonar_mvp02 sonar"));
        assertTrue(config.isEnabled("au", "Sonar_mvpci sonar"));
        assertFalse(config.isEnabled("au", "Sonar_mvp02 sonar"));
        assertFalse(config.isEnabled("au", "Sonar_mvpaaa sonar"));
        assertFalse(config.isEnabled("au", "Sonar_mvp04 sonar"));
        // Country not in rules
        assertTrue(config.isEnabled("SG", "Sonar_ENT sonar"));
        assertTrue(config.isEnabled("SG", "Sonar_MVPCI sonar"));
        assertTrue(config.isEnabled("SG", "Sonar_MVP21 sonar"));
        assertTrue(config.isEnabled("SG", "Sonar_MVP04 sonar"));
        assertTrue(config.isEnabled("MY", "Sonar_MVP04 sonar"));
        assertTrue(config.isEnabled("MY", "Sonar_MVPCI sonar"));
        assertTrue(config.isEnabled("MY", "ANY sonar"));
        assertTrue(config.isEnabled("HK", "ANY HK sonar"));
        assertTrue(config.isEnabled("JP", "ANY JP sonar"));
        assertTrue(config.isEnabled("TW", "ANY TW sonar"));
    }

    @Test
    @DisplayName("CountryPlatformValidationConfiguration::isEnabled rejects null and empty parameters")
    void country_platform_validation_configuration_is_enabled_rejects_null_and_empty_parameters() {
        var config = new CountryPlatformValidationConfiguration();
        config.setEnabled(true);
        config.setRules(Map.of());

        // Test null parameters
        assertThrows(NullPointerException.class, () -> config.isEnabled(null, "MVP04"));
        assertThrows(NullPointerException.class, () -> config.isEnabled("NZ", null));
        assertThrows(NullPointerException.class, () -> config.isEnabled(null, null));

        // Test empty parameters
        assertFalse(config.isEnabled("", "MVP04"));
        assertFalse(config.isEnabled("NZ", ""));
        assertFalse(config.isEnabled("", "mvp04"));
        assertFalse(config.isEnabled("nz", ""));
        assertFalse(config.isEnabled("", ""));
    }

    @Test
    @DisplayName("CountryPlatformValidationConfiguration::isEnabled respects enabled flag")
    void country_platform_validation_configuration_is_enabled_respects_enabled_flag() {
        var config = new CountryPlatformValidationConfiguration();
        config.setEnabled(false);
        config.setRules(Map.of(
            "NZ", List.of("MVP04", "MVPCI"),
            "AU", List.of("MVPCI")
        ));

        allowAllCountriesAndPlatforms(config);
    }

    private void allowAllCountriesAndPlatforms(CountryPlatformValidationConfiguration config) {
        // Verify all cases
        assertTrue(config.isEnabled("NZ", "Sonar_MVP04 sonar"));
        assertTrue(config.isEnabled("NZ", "Sonar_MVPCI sonar"));
        assertTrue(config.isEnabled("NZ", "Sonar_MVP21 sonar"));
        assertTrue(config.isEnabled("NZ", "Sonar_MVP02 sonar"));
        assertTrue(config.isEnabled("AU", "Sonar_MVPCI sonar"));
        assertTrue(config.isEnabled("AU", "Sonar_MVP21 sonar"));
        assertTrue(config.isEnabled("AU", "Sonar_MVPAAA sonar"));
        assertTrue(config.isEnabled("AU", "Sonar_MVP04 sonar"));
        assertTrue(config.isEnabled("HK", "any sonar"));
        assertTrue(config.isEnabled("JP", "any sonar"));
        assertTrue(config.isEnabled("SG", "Sonar_ENT sonar"));
        assertTrue(config.isEnabled("MY", "ANY sonar"));
        assertTrue(config.isEnabled("nz", "sonar_mvp04 sonar"));
        assertTrue(config.isEnabled("nz", "sonar_mvpci sonar"));
        assertTrue(config.isEnabled("nz", "sonar_mvp21 sonar"));
        assertTrue(config.isEnabled("nz", "sonar_mvp02 sonar"));
        assertTrue(config.isEnabled("au", "sonar_mvpci sonar"));
        assertTrue(config.isEnabled("au", "sonar_mvp21 sonar"));
        assertTrue(config.isEnabled("au", "sonar_mvpaaa sonar"));
        assertTrue(config.isEnabled("au", "sonar_mvp04 sonar"));
        assertTrue(config.isEnabled("hk", "any sonar"));
        assertTrue(config.isEnabled("jp", "any sonar"));
        assertTrue(config.isEnabled("sg", "sonar_ent sonar"));
        assertTrue(config.isEnabled("my", "any sonar"));
    }
}