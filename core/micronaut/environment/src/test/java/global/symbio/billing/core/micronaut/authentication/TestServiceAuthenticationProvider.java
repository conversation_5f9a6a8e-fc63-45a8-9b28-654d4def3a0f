package global.symbio.billing.core.micronaut.authentication;

import io.micronaut.security.authentication.AuthenticationRequest;
import io.micronaut.security.authentication.AuthenticationResponse;
import io.micronaut.security.authentication.UsernamePasswordCredentials;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.test.StepVerifier;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class TestServiceAuthenticationProvider {

    private static final String USERNAME = "username";
    private static final String PASSWORD = "password";
    private static final Map<String, String> CREDENTIALS = Map.of(USERNAME, PASSWORD);

    private ServiceAuthenticationProvider provider;

    @BeforeEach
    public void setup() {
        provider = new ServiceAuthenticationProvider(CREDENTIALS);
    }

    @Test
    @DisplayName("ServiceAuthenticationProvider::new rejects null constructor arguments")
    public void service_authentication_provider_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new ServiceAuthenticationProvider(null));
    }

    @ParameterizedTest
    @EnumSource(value = TestCase.class, names = { "CREDENTIALS_CORRECT", "CREDENTIALS_INCORRECT", "CREDENTIALS_MISSING", "CREDENTIALS_INVALID" })
    @DisplayName("ServiceAuthenticationProvider::authenticate")
    public void service_authentication_provider_authenticate_scenarios(final TestCase scenario) {
        final AuthenticationRequest<String, String> auth = switch (scenario) {
            case CREDENTIALS_CORRECT -> new UsernamePasswordCredentials("username", "password");
            case CREDENTIALS_INCORRECT -> new UsernamePasswordCredentials("username", "incorrect_password");
            case CREDENTIALS_MISSING -> new UsernamePasswordCredentials("???", "???");
            case CREDENTIALS_INVALID -> {
                final var req = mock(AuthenticationRequest.class);
                when(req.getIdentity()).thenReturn(1337L);
                yield req;
            }
        };
        final var publisher = assertDoesNotThrow(() -> provider.authenticate(null, auth));
        final StepVerifier verifier;

        if (scenario == TestCase.CREDENTIALS_CORRECT) {
            verifier = StepVerifier.create(publisher).expectNextMatches(AuthenticationResponse::isAuthenticated).expectComplete();
        } else {
            verifier = StepVerifier.create(publisher).expectError();
        }

        assertDoesNotThrow(() -> verifier.verify());
    }

    public enum TestCase {
        CREDENTIALS_CORRECT,
        CREDENTIALS_INCORRECT,
        CREDENTIALS_MISSING,
        CREDENTIALS_INVALID
    }
}