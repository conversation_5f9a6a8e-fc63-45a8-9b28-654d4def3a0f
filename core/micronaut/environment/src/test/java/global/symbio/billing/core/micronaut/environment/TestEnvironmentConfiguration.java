package global.symbio.billing.core.micronaut.environment;

import global.symbio.billing.core.micronaut.environment.EnvironmentConfiguration.DeploymentEnvironment;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;

import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

public class TestEnvironmentConfiguration {

    @Test
    @DisplayName("EnvironmentConfiguration::new rejects null constructor arguments")
    public void environment_configuration_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new EnvironmentConfiguration((DeploymentEnvironment) null, Set.of()));
        assertThrows(NullPointerException.class, () -> new EnvironmentConfiguration((String) null, Set.of()));
        assertThrows(IllegalStateException.class, () -> new EnvironmentConfiguration("environment", null));
    }

    @ParameterizedTest
    @CsvSource(value = {"PRODUCTION,prod", "UAT,uat", "REVIEW,review", "DEVELOPMENT,dev"})
    @DisplayName("EnvironmentConfiguration::DeploymentEnvironment::parse determines environment for slug")
    public void environment_configuration_deployment_environment_parse_determines_environment_for_slug(@Nonnull DeploymentEnvironment expected, @Nonnull String slug) {
        final var environment = assertDoesNotThrow(() -> DeploymentEnvironment.parse(slug));
        assertEquals(expected, environment);
    }

    @ParameterizedTest
    @ValueSource(strings = {"nonprod", "PrOd", "useracceptancetesting", "staging", "DEVELOP", ""})
    @DisplayName("EnvironmentConfiguration::DeploymentEnvironment::parse throws IllegalStateException for unknown environment slug")
    public void environment_configuration_deployment_environment_parse_throws_illegal_state_exception_for_unknown_slug(@Nonnull String slug) {
        final var cause = assertThrows(IllegalStateException.class, (() -> DeploymentEnvironment.parse(slug)));
        assertEquals("Unknown deployment environment configuration: " + slug, cause.getMessage());
    }

    @Test
    @DisplayName("EnvironmentConfiguration::inCountry returns true for a country in the list")
    public void environment_configuration_in_country_returns_true_for_valid_country() {
        final var countries = Set.of("MY", "SG", "AU");
        final var config = new EnvironmentConfiguration(EnvironmentConfiguration.DeploymentEnvironment.PRODUCTION, countries);
        assertTrue(config.inCountry("MY"));
    }

    @Test
    @DisplayName("EnvironmentConfiguration::inCountry returns false for a country not in the list")
    public void environment_configuration_in_country_returns_false_for_invalid_country() {
        final var countries = Set.of("MY", "SG", "AU");
        final var config = new EnvironmentConfiguration(EnvironmentConfiguration.DeploymentEnvironment.PRODUCTION, countries);
        assertFalse(config.inCountry("NZ"));
    }
}
