package global.symbio.billing.core.micronaut.http.plexus;

import io.micronaut.context.annotation.Context;
import io.micronaut.http.HttpRequest;
import io.micronaut.http.HttpStatus;
import io.micronaut.http.MutableHttpResponse;
import io.micronaut.http.annotation.Filter;
import io.micronaut.http.filter.HttpServerFilter;
import io.micronaut.http.filter.ServerFilterChain;
import jakarta.annotation.Nonnull;
import lombok.extern.slf4j.Slf4j;
import org.reactivestreams.Publisher;
import reactor.core.publisher.Flux;

@Slf4j
@Context
@Filter(patterns = "/v*/**")
public class PlexusAPIResponseHTTPFilter implements HttpServerFilter {

    private static final String[] IGNORED_ENDPOINTS = {
        "/v2/billing/accounts/*/invoices/*/download"
    };

    @Override
    public Publisher<MutableHttpResponse<?>> doFilter(HttpRequest<?> request, ServerFilterChain chain) {
        return Flux.from(chain.proceed(request)).doOnNext(response -> handle(request, response));
    }

    private void handle(@Nonnull HttpRequest<?> request, @Nonnull MutableHttpResponse<?> response) {
        final var requestPath = request.getUri().getPath();

        // Check if the request path matches any ignored endpoints
        if (isIgnoredEndpoint(requestPath)) {
            log.info("Skipping response modification for ignored endpoint: {}", requestPath);
            return;
        }

        // Apply response modification for non-ignored endpoints
        if (response.getStatus() == HttpStatus.OK) {
            final var body = response.getBody().orElse(null);
            response.body(new SuccessfulPlexusAPIResponse<>(body));
        }
    }

    private boolean isIgnoredEndpoint(@Nonnull String requestPath) {
        for (final var pattern : IGNORED_ENDPOINTS) {
            if (matchesPattern(requestPath, pattern)) {
                return true;
            }
        }
        return false;
    }

    private boolean matchesPattern(@Nonnull String requestPath, @Nonnull String pattern) {
        // Convert wildcard pattern to regex for matching
        String regex = pattern.replace("*", ".*");
        return requestPath.matches(regex);
    }
}