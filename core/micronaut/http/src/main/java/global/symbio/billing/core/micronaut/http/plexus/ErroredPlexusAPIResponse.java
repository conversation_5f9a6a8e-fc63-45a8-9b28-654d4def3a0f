package global.symbio.billing.core.micronaut.http.plexus;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.MoreObjects;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.Getter;

import java.util.Objects;

@Getter
@JsonInclude(JsonInclude.Include.ALWAYS)
@VisibleForTesting
final class ErroredPlexusAPIResponse<T> extends PlexusAPIResponse<T> {

    public record ErrorState(int code, @Nonnull String message, @Nullable String details) {}

    @Nonnull
    private final ErrorState error;

    public ErroredPlexusAPIResponse(@Nonnull ErrorState error) {
        super(false);
        this.error = Objects.requireNonNull(error, "error");
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof ErroredPlexusAPIResponse<?> that)) return false;
        return Objects.equals(getError(), that.getError());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getError());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("error", error)
            .add("success", success)
            .toString();
    }
}
