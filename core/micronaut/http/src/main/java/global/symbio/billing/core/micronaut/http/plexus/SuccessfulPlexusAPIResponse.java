package global.symbio.billing.core.micronaut.http.plexus;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.MoreObjects;
import jakarta.annotation.Nullable;
import lombok.Getter;

import java.util.Objects;

@Getter
@JsonInclude(JsonInclude.Include.ALWAYS)
@VisibleForTesting
final class SuccessfulPlexusAPIResponse<T> extends PlexusAPIResponse<T> {

    @Nullable
    private final T data;

    public SuccessfulPlexusAPIResponse(@Nullable T data) {
        super(true);
        this.data = data;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof SuccessfulPlexusAPIResponse<?> that)) return false;
        return Objects.equals(getData(), that.getData());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getData());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("data", data)
            .add("success", success)
            .toString();
    }
}