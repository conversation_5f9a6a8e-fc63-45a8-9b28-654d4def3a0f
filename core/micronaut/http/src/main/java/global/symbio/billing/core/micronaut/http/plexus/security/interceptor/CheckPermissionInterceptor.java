package global.symbio.billing.core.micronaut.http.plexus.security.interceptor;

import global.symbio.billing.core.micronaut.http.plexus.exception.PermissionValidationException;
import global.symbio.billing.core.micronaut.http.plexus.security.annotation.CheckPermission;
import global.symbio.billing.core.util.constants.APIConstants;
import io.micronaut.aop.InterceptorBean;
import io.micronaut.aop.MethodInterceptor;
import io.micronaut.aop.MethodInvocationContext;
import io.micronaut.core.annotation.AnnotationValue;
import io.micronaut.http.HttpRequest;
import io.micronaut.http.context.ServerRequestContext;
import io.micronaut.http.exceptions.HttpStatusException;
import io.micronaut.http.HttpStatus;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

import java.util.Arrays;
import java.util.Optional;

@Singleton
@InterceptorBean(CheckPermission.class)
public class CheckPermissionInterceptor implements MethodInterceptor<Object, Object> {

    @Override
    public Object intercept(MethodInvocationContext<Object, Object> context) {
        try {
            final var annotation = context.getAnnotation(CheckPermission.class);
            if (annotation == null) {
                return context.proceed();
            }
            final var requiredPermissionOpt = annotation.stringValue();
            if (requiredPermissionOpt.isEmpty()) {
                return context.proceed();
            }
            final var requiredPermission = requiredPermissionOpt.get();
            Optional<HttpRequest<Object>> currentRequestOpt = ServerRequestContext.currentRequest();
            if (currentRequestOpt.isEmpty()) {
                return context.proceed();
            }
            final var req = currentRequestOpt.get();
            final var permissionsOpt = req.getAttribute(APIConstants.PERMISSIONS);
            if (permissionsOpt.isEmpty()) {
                throw new PermissionValidationException("The user does not have any permissions.");
            }
            final var permissionsStr = permissionsOpt.get().toString();
            if (!matchPermissionIgnoreCase(permissionsStr, requiredPermission)) {
                throw new PermissionValidationException("The user does not have permission to access this resource.");
            }
            return context.proceed();
        } catch (PermissionValidationException e) {
            throw new HttpStatusException(HttpStatus.FORBIDDEN, e.getMessage());
        } catch (Exception e) {
            throw new HttpStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "An unexpected error occurred: " + e.getMessage());
        }
    }

    private boolean matchPermissionIgnoreCase(@Nonnull String permissions, @Nonnull String requiredPermission) {
        return Arrays.stream(permissions.split(","))
            .anyMatch(permission -> permission.equalsIgnoreCase(requiredPermission));
    }

}
