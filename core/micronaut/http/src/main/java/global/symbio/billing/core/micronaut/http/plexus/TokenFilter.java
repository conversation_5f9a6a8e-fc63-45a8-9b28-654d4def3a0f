package global.symbio.billing.core.micronaut.http.plexus;

import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.audit.api.AuditRequestAttribute;
import global.symbio.billing.core.audit.api.User;
import global.symbio.billing.core.util.constants.APIConstants;
import global.symbio.billing.core.util.mapper.MapperUtil;
import io.micronaut.context.annotation.Context;
import io.micronaut.http.HttpRequest;
import io.micronaut.http.HttpResponse;
import io.micronaut.http.HttpStatus;
import io.micronaut.http.annotation.Filter;
import io.micronaut.http.filter.FilterChain;
import io.micronaut.http.filter.FilterPatternStyle;
import io.micronaut.http.filter.HttpFilter;
import jakarta.annotation.Nonnull;
import lombok.extern.slf4j.Slf4j;
import org.reactivestreams.Publisher;
import reactor.core.publisher.Flux;

import java.util.Base64;
import java.util.Map;
import java.util.Objects;

import static io.micronaut.http.server.cors.CorsFilter.CORS_FILTER_ORDER;

@Slf4j
@Context
@Filter(patterns = "\\/v\\d+\\/.*", patternStyle = FilterPatternStyle.REGEX)
@VisibleForTesting
public class TokenFilter implements HttpFilter {

    private static final String AUTHORIZATION_HEADER = "Authorization";
    private static final String GIVEN_NAME = "given_name";
    private static final String FAMILY_NAME = "family_name";
    private static final String EMAIL = "email";
    private static final String REGEX = "\\.";
    private static final String PLEXUS_PERMISSIONS = "plexusPermissions";

    @Nonnull
    private final AuditRequestAttribute auditRequestAttribute;

    public TokenFilter(@Nonnull AuditRequestAttribute auditRequestAttribute) {
        this.auditRequestAttribute = Objects.requireNonNull(auditRequestAttribute, "auditRequestAttribute");
    }

    @Override
    public Publisher<? extends HttpResponse<?>> doFilter(HttpRequest<?> request, FilterChain chain) {
        try {
            final var token = request.getHeaders().get(AUTHORIZATION_HEADER);
            if (token == null) {
                return Flux.just(HttpResponse.status(HttpStatus.UNAUTHORIZED, "Missing Authorization Header"));
            }
            final var payloadMap = parsePayloadMap(token);
            auditRequestAttribute.setUser(getUser(payloadMap));
            if (payloadMap.containsKey(PLEXUS_PERMISSIONS)) {
                final var plexusPermissions = payloadMap.get(PLEXUS_PERMISSIONS).toString();
                request.setAttribute(APIConstants.PERMISSIONS, plexusPermissions);
            } else {
                log.warn("Missing plexusPermissions in token payload");
                return Flux.just(HttpResponse.status(HttpStatus.FORBIDDEN, "Missing plexusPermissions in token payload"));
            }
            return chain.proceed(request);
        } catch (Exception e) {
            log.error("Unexpected error occurred while processing request", e);
            return Flux.just(HttpResponse.status(HttpStatus.INTERNAL_SERVER_ERROR, "Internal Server Error"));
        }
    }

    private User getUser(@Nonnull Map<String, Object> payloadMap) {
        final var name = payloadMap.get(GIVEN_NAME) + " " + payloadMap.get(FAMILY_NAME);
        final var email = payloadMap.get(EMAIL).toString();
        return User.builder().name(name).email(email).build();
    }

    @Nonnull
    private Map<String, Object> parsePayloadMap(@Nonnull String token) {
        final var chunks = token.split(REGEX);
        final var decoder = Base64.getUrlDecoder();
        final var payload = new String(decoder.decode(chunks[1]));
        return MapperUtil.fromJson(payload, Map.class);
    }

    @Override
    public int getOrder() {
        return CORS_FILTER_ORDER + 1;
    }
}



