package global.symbio.billing.core.micronaut.http.plexus;

import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.util.constants.APIConstants;
import io.grpc.Status;
import io.micronaut.context.annotation.Context;
import io.micronaut.context.annotation.Primary;
import io.micronaut.http.HttpMethod;
import io.micronaut.http.MutableHttpResponse;
import io.micronaut.http.server.exceptions.response.ErrorContext;
import io.micronaut.http.server.exceptions.response.ErrorResponseProcessor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Primary
@Context
@VisibleForTesting
class PlexusAPIErrorResponseProcessor implements ErrorResponseProcessor<Object> {

    public PlexusAPIErrorResponseProcessor() {
        log.info("Created {}", PlexusAPIErrorResponseProcessor.class.getSimpleName());
    }

    @Override
    public MutableHttpResponse<Object> processResponse(ErrorContext context, MutableHttpResponse<?> response) {
        if (context.getRequest().getMethod() == HttpMethod.HEAD) {
            return (MutableHttpResponse<Object>) response;
        }
        var details = context.getRootCause().map(Throwable::getMessage).orElse(null);
        var code = response.code();
        var reason = response.reason();
        var httpStatus = response.status();

        if (details != null) {
            final var matcher = APIConstants.GRPC_ERROR_REGEX.matcher(details);

            if (matcher.find()) {
                final var status = matcher.group(APIConstants.GROUP_ERROR_STATUS);
                final var grpcStatus = Status.fromCode(Status.Code.valueOf(status.toUpperCase()));
                httpStatus = GrpcToHttpStatusMapper.convert(grpcStatus);
                code = httpStatus.getCode();
                reason = httpStatus.getReason();
                details = matcher.group(APIConstants.GROUP_ERROR_DETAILS);
            }
        }

        final var state = new ErroredPlexusAPIResponse.ErrorState(code, reason, details);
        return response.status(httpStatus).body(new ErroredPlexusAPIResponse<>(state));
    }
}