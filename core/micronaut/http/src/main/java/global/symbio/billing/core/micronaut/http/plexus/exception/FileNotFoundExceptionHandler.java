package global.symbio.billing.core.micronaut.http.plexus.exception;

import com.google.common.annotations.VisibleForTesting;
import io.micronaut.context.annotation.Requires;
import io.micronaut.http.HttpRequest;
import io.micronaut.http.HttpResponse;
import io.micronaut.http.HttpResponseFactory;
import io.micronaut.http.HttpStatus;
import io.micronaut.http.annotation.Produces;
import io.micronaut.http.server.exceptions.ExceptionHandler;
import io.micronaut.http.server.exceptions.response.ErrorContext;
import io.micronaut.http.server.exceptions.response.ErrorResponseProcessor;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import javax.annotation.Nonnull;
import java.io.FileNotFoundException;
import java.util.Objects;

@Produces
@Singleton
@Requires(classes = { FileNotFoundException.class, ExceptionHandler.class })
@VisibleForTesting
public class FileNotFoundExceptionHandler implements ExceptionHandler<FileNotFoundException, HttpResponse<?>> {

    @Nonnull
    private final ErrorResponseProcessor<?> processor;

    @Inject
    public FileNotFoundExceptionHandler(@Nonnull ErrorResponseProcessor<?> processor) {
        this.processor = Objects.requireNonNull(processor, "processor");
    }

    @Nonnull
    @Override
    public HttpResponse<?> handle(@Nonnull HttpRequest request, @Nonnull FileNotFoundException exception) {
        final var context = ErrorContext.builder(request).cause(exception).build();
        return processor.processResponse(context, HttpResponseFactory.INSTANCE.status(HttpStatus.NOT_FOUND));
    }
}
