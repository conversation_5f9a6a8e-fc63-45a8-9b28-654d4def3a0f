package global.symbio.billing.core.micronaut.http.plexus;

import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.util.metrics.MetricsUtil;
import io.micronaut.context.annotation.Context;
import io.micronaut.core.order.Ordered;
import io.micronaut.http.HttpAttributes;
import io.micronaut.http.HttpRequest;
import io.micronaut.http.HttpResponse;
import io.micronaut.http.HttpStatus;
import io.micronaut.http.annotation.Filter;
import io.micronaut.http.filter.FilterChain;
import io.micronaut.http.filter.HttpFilter;
import io.micronaut.web.router.DefaultUrlRouteInfo;
import io.micronaut.web.router.RouteInfo;
import lombok.extern.slf4j.Slf4j;
import org.reactivestreams.Publisher;
import reactor.core.publisher.Flux;

import java.time.Duration;
import java.time.Instant;
import java.util.Map;

import static global.symbio.billing.core.util.constants.MetricsConstants.*;

@Slf4j
@Context
@Filter(Filter.MATCH_ALL_PATTERN)
@VisibleForTesting
class MetricsTransactionFilter implements HttpFilter {

    private static final String HEALTH_PATH = "/health";
    private static final String SEPARATOR = "#";

    public MetricsTransactionFilter() {
        log.info("Created {}", MetricsTransactionFilter.class.getSimpleName());
    }

    @Override
    public Publisher<? extends HttpResponse<?>> doFilter(HttpRequest<?> request, FilterChain chain) {
        final var path = request.getPath();
        if (path.startsWith(HEALTH_PATH)) {
            return chain.proceed(request);
        }

        final var route = request.getAttribute(HttpAttributes.ROUTE_INFO, RouteInfo.class).orElse(null);
        if (!(route instanceof DefaultUrlRouteInfo<?, ?> info)) {
            return chain.proceed(request);
        }

        final var context = info.getTargetMethod().getExecutableMethod();
        final var type = context.getDeclaringType().getSimpleName();
        final var method = context.getMethodName();
        final var name = type + SEPARATOR + method;

        final var start = Instant.now();
        return Flux.from(chain.proceed(request))
            .doOnNext(response -> {
                var errorDetails = "";
                if (!HttpStatus.OK.equals(response.getStatus()) && response.getBody().isPresent()) {
                    if (response.getBody().get() instanceof ErroredPlexusAPIResponse<?> error) {
                        errorDetails = error.getError().details();
                    }
                }
                final var status = response.getStatus();
                final var duration = Duration.between(start, Instant.now());
                try (final var _ = MetricsUtil.create(Map.of(
                    CONTEXT_TAG_API_NAME, name,
                    CONTEXT_TAG_API_STATUS, status.toString(),
                    CONTEXT_TAG_API_STATUS_CODE, String.valueOf(status.getCode()),
                    CONTEXT_TAG_API_DURATION, String.valueOf(duration.toMillis()),
                    CONTEXT_TAG_API_ERROR_DETAILS, errorDetails))
                ) {
                    log.info("Transaction: {}, status: {}", name, status);
                }
            })
            .doOnError(throwable -> log.error("Error occurred in transaction: {}, {}", name, throwable.getMessage(), throwable));
    }

    @Override
    public int getOrder() {
        return Ordered.HIGHEST_PRECEDENCE;
    }
}