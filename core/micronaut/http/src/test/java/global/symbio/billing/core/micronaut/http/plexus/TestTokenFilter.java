package global.symbio.billing.core.micronaut.http.plexus;

import global.symbio.billing.core.audit.api.AuditRequestAttribute;
import global.symbio.billing.core.audit.api.User;
import global.symbio.billing.core.util.constants.APIConstants;
import io.micronaut.http.*;
import io.micronaut.http.filter.FilterChain;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

import java.net.URI;
import java.util.Base64;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

public class TestTokenFilter {
    private static final String AUTHORIZATION_HEADER = "Authorization";
    private static final String TEST_URI = "v2/billing/payment/";
    private static final String TEST_GIVEN_NAME = "Test";
    private static final String TEST_FAMILY_NAME = "Name";
    private static final String TEST_EMAIL = "<EMAIL>";
    private static final String TEST_PERMISSIONS = "address/create,payment/create,transfer/create,account/get,person/delete,invoice/get,customer/get,person/update,person/create,address/update,business/update,rate-plan/search,customer/update,api-user-manager/create,business/create,address/delete,payment/get,transfer/get,payment/update,rate-plan/get,payment/cancel";

    @Mock
    private AuditRequestAttribute auditRequestAttribute;
    private TokenFilter filter;

    @BeforeEach
    public void setup() {
        auditRequestAttribute = new AuditRequestAttribute();
        filter = new TokenFilter(auditRequestAttribute);
    }

    private String generateToken(String givenName, String familyName, String email, String plexusPermissions) {
        final var header = "eyJraWQiOiJLWGROSzZLdGpCNmxrY2xOd3pmdDd6VlloWVJPXC90RUU1aTE3ZUY1ZTNDMD0iLCJhbGciOiJSUzI1NiJ9";
        final var payload = new StringBuilder("{");

        if (givenName != null) {
            payload.append(String.format("\"given_name\":\"%s\"", givenName));
        }
        if (familyName != null) {
            if (payload.length() > 1) payload.append(",");
            payload.append(String.format("\"family_name\":\"%s\"", familyName));
        }
        if (email != null) {
            if (payload.length() > 1) payload.append(",");
            payload.append(String.format("\"email\":\"%s\"", email));
        }
        if (plexusPermissions != null) {
            if (payload.length() > 1) payload.append(",");
            payload.append(String.format("\"plexusPermissions\":\"%s\"", plexusPermissions));
        }
        payload.append("}");

        final var signature = "DQp9";
        return header + "." + Base64.getUrlEncoder().encodeToString(payload.toString().getBytes()) + "." + signature;
    }

    @Test
    @DisplayName("TokenFilter::doFilter returns Unauthorized if Authorization header is missing")
    public void token_filter_do_filter_returns_unauthorized_if_authorization_header_is_missing() {
        final var headers = mock(HttpHeaders.class);
        final var request = mock(HttpRequest.class);
        final var chain = mock(FilterChain.class);
        when(request.getHeaders()).thenReturn(headers);

        final var response = assertDoesNotThrow(() -> filter.doFilter(request, chain));
        final var verifier = StepVerifier
            .create(response)
            .expectNextMatches(httpResponse -> HttpStatus.UNAUTHORIZED.equals(httpResponse.getStatus()))
            .expectComplete();
        assertDoesNotThrow(() -> verifier.verify());
    }

    @Test
    @DisplayName("TokenFilter::doFilter returns Forbidden if token is malformed")
    public void token_filter_do_filter_returns_forbidden_if_token_is_malformed() {
        final var headers = mock(HttpHeaders.class);
        final var request = mock(HttpRequest.class);
        final var chain = mock(FilterChain.class);
        when(request.getHeaders()).thenReturn(headers);
        when(request.getHeaders().contains(AUTHORIZATION_HEADER)).thenReturn(true);
        when(request.getHeaders().get(anyString())).thenReturn("malformed.token");

        final var response = assertDoesNotThrow(() -> filter.doFilter(request, chain));
        final var verifier = StepVerifier
            .create(response)
            .expectNextMatches(httpResponse -> HttpStatus.INTERNAL_SERVER_ERROR.equals(httpResponse.getStatus()))
            .expectComplete();
        assertDoesNotThrow(() -> verifier.verify());
    }

    @Test
    @DisplayName("TokenFilter::doFilter sets User in the AuditRequestAttribute and permissions in the request attribute")
    public void token_filter_do_filter_sets_user_in_the_audit_request_attribute_and_permissions_in_the_request_attribute() {
        final var headers = mock(HttpHeaders.class);
        final var request = mock(HttpRequest.class);
        final var response = mock(HttpResponse.class);
        final var chain = mock(FilterChain.class);
        final var result = Flux.just(response);

        doReturn(result).when(chain).proceed(request);

        when(request.getHeaders()).thenReturn(headers);
        when(request.getHeaders().contains(AUTHORIZATION_HEADER)).thenReturn(true);
        when(request.getHeaders().get(anyString())).thenReturn(generateToken(TEST_GIVEN_NAME, TEST_FAMILY_NAME, TEST_EMAIL, TEST_PERMISSIONS));
        when(request.getUri()).thenReturn(URI.create(TEST_URI));
        when(request.getMethod()).thenReturn(HttpMethod.GET);

        final var user = new User(TEST_GIVEN_NAME + " " + TEST_FAMILY_NAME, TEST_EMAIL);

        assertDoesNotThrow(() -> filter.doFilter(request, chain));
        final var verifier = StepVerifier
            .create(result)
            .expectNext(response)
            .expectComplete();
        assertDoesNotThrow(() -> verifier.verify());
        assertEquals(user, auditRequestAttribute.getUser());
        verify(request, times(1)).setAttribute(eq(APIConstants.PERMISSIONS), eq(TEST_PERMISSIONS));
    }

    @Test
    @DisplayName("TokenFilter::doFilter returns Forbidden if plexusPermissions is missing in token payload")
    public void token_filter_do_filter_returns_forbidden_if_plexus_permissions_is_missing_in_token_payload() {
        final var headers = mock(HttpHeaders.class);
        final var request = mock(HttpRequest.class);
        final var chain = mock(FilterChain.class);
        final var response = mock(HttpResponse.class);
        final var result = Flux.just(response);


        doReturn(result).when(chain).proceed(request);

        when(request.getHeaders()).thenReturn(headers);
        when(request.getHeaders().contains(AUTHORIZATION_HEADER)).thenReturn(true);
        when(request.getHeaders().get(anyString())).thenReturn(generateToken(TEST_GIVEN_NAME, TEST_FAMILY_NAME, TEST_EMAIL, null));
        when(request.getUri()).thenReturn(URI.create(TEST_URI));
        when(request.getMethod()).thenReturn(HttpMethod.GET);

        final var responsePublisher = assertDoesNotThrow(() -> filter.doFilter(request, chain));
        final var verifier = StepVerifier
            .create(responsePublisher)
            .expectNextMatches(httpResponse -> HttpStatus.FORBIDDEN.equals(httpResponse.getStatus()))
            .expectComplete();
        assertDoesNotThrow(() -> verifier.verify());
    }
}
