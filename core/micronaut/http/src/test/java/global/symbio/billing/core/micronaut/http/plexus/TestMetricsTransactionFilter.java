package global.symbio.billing.core.micronaut.http.plexus;

import io.micronaut.http.HttpRequest;
import io.micronaut.http.HttpResponse;
import io.micronaut.http.HttpStatus;
import io.micronaut.http.filter.FilterChain;
import io.micronaut.inject.ExecutableMethod;
import io.micronaut.web.router.DefaultUrlRouteInfo;
import org.apache.logging.log4j.CloseableThreadContext;
import org.apache.logging.log4j.ThreadContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

import java.util.Map;
import java.util.Optional;
import java.util.stream.Stream;

import static global.symbio.billing.core.util.constants.MetricsConstants.*;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestMetricsTransactionFilter {

    private MetricsTransactionFilter filter;

    @BeforeEach
    public void setup() {
        filter = new MetricsTransactionFilter();
    }

    @Test
    @DisplayName("MetricsTransactionFilter::doFilter skips metrics creation when health endpoint")
    public void metrics_transaction_filter_do_filter_skips_metrics_create_when_health_endpoint() {
        final var request = mock(HttpRequest.class);
        final var routeInfo = mock(DefaultUrlRouteInfo.class);
        when(request.getPath()).thenReturn("/health");
        final var chain = mock(FilterChain.class);
        assertDoesNotThrow(() -> filter.doFilter(request, chain));
        verify(chain, times(1)).proceed(request);
        verify(routeInfo, never()).getTargetMethod();
    }

    @Test
    @DisplayName("MetricsTransactionFilter::doFilter skips metrics creation when routeInfo is null")
    public void metrics_transaction_filter_do_filter_skips_metrics_create_when_route_info_is_null() {
        final var request = mock(HttpRequest.class);
        final var routeInfo = mock(DefaultUrlRouteInfo.class);
        when(request.getPath()).thenReturn("/v1/my/path");
        when(request.getAttribute(any(), any())).thenReturn(null);
        when(request.getAttribute(any(), any())).thenReturn(Optional.empty());
        final var chain = mock(FilterChain.class);
        assertDoesNotThrow(() -> filter.doFilter(request, chain));
        verify(chain, times(1)).proceed(request);
        verify(routeInfo, never()).getTargetMethod();
    }

    @ParameterizedTest
    @MethodSource("scenarios")
    @DisplayName("MetricsTransactionFilter::doFilter creates metrics")
    public void metrics_transaction_filter_do_filter_creates_metrics(
        final String name,
        final HttpStatus status,
        final String duration,
        final String error,
        final HttpResponse response
    ) {

        final var request = mock(HttpRequest.class);
        final var routeInfo = mock(DefaultUrlRouteInfo.class, RETURNS_DEEP_STUBS);
        when(request.getPath()).thenReturn("/v1/my/path");
        when(request.getAttribute(any(), any())).thenReturn(Optional.ofNullable(routeInfo));

        final var executableMethod = mock(ExecutableMethod.class, RETURNS_DEEP_STUBS);
        final var clazz = mock(MetricsTransactionFilter.class).getClass();

        when(executableMethod.getDeclaringType()).thenReturn(clazz);
        when(executableMethod.getMethodName()).thenReturn("myMethod");
        when(routeInfo.getTargetMethod().getExecutableMethod()).thenReturn(executableMethod);

        final var chain = mock(FilterChain.class);
        final var result = Flux.just(response);
        doReturn(result).when(chain).proceed(request);

        final var filterResult = Flux.from(filter.doFilter(request, chain))
            .doOnNext(_ -> {
                final var params = Map.of(
                    CONTEXT_TAG_API_NAME, name,
                    CONTEXT_TAG_API_STATUS, status.toString(),
                    CONTEXT_TAG_API_STATUS_CODE, String.valueOf(status.getCode()),
                    CONTEXT_TAG_API_DURATION, duration,
                    CONTEXT_TAG_API_ERROR_DETAILS, error
                );
                try(CloseableThreadContext.Instance simulate = CloseableThreadContext.putAll(params)) {
                    assertEquals(name, ThreadContext.get(CONTEXT_TAG_API_NAME));
                    assertEquals(status.toString(), ThreadContext.get(CONTEXT_TAG_API_STATUS));
                    assertEquals(String.valueOf(status.getCode()), ThreadContext.get(CONTEXT_TAG_API_STATUS_CODE));
                    assertEquals(duration, ThreadContext.get(CONTEXT_TAG_API_DURATION));
                    assertEquals(error, ThreadContext.get(CONTEXT_TAG_API_ERROR_DETAILS));
                }
            });

        if (status != HttpStatus.OK) {
            StepVerifier.create(filterResult)
                .expectNextMatches(resp -> error.equals(((ErroredPlexusAPIResponse<?>)resp.body()).getError().details()))
                .expectComplete()
                .verify();
        } else {
            StepVerifier.create(filterResult)
                .expectNextMatches(resp -> name.equals(resp.body()))
                .expectComplete()
                .verify();
        }

        verify(executableMethod, times(1)).getMethodName();
        verify(chain, times(1)).proceed(request);
    }

    @Test
    @DisplayName("MetricsTransactionFilter::doFilter handles doOnError")
    public void metrics_transaction_filter_do_filter_handles_do_on_error() {
        final var request = mock(HttpRequest.class);
        final var routeInfo = mock(DefaultUrlRouteInfo.class, RETURNS_DEEP_STUBS);
        when(request.getPath()).thenReturn("/v1/my/path");
        when(request.getAttribute(any(), any())).thenReturn(Optional.ofNullable(routeInfo));

        final var executableMethod = mock(ExecutableMethod.class, RETURNS_DEEP_STUBS);
        final var clazz = mock(MetricsTransactionFilter.class).getClass();

        when(executableMethod.getDeclaringType()).thenReturn(clazz);
        when(executableMethod.getMethodName()).thenReturn("myMethod");
        when(routeInfo.getTargetMethod().getExecutableMethod()).thenReturn(executableMethod);

        final var chain = mock(FilterChain.class);
        final var result = Flux.error(Exception::new);
        doReturn(result).when(chain).proceed(request);

        assertDoesNotThrow(() -> filter.doFilter(request, chain));
        final var verifier = StepVerifier.create(result).expectError();
        assertDoesNotThrow(() -> verifier.verify());
        verify(executableMethod, times(1)).getMethodName();
        verify(chain, times(1)).proceed(request);
    }

    private static Stream<Arguments> scenarios() {
        return Stream.of(
            Arguments.of("OK", HttpStatus.OK, "1111", "", HttpResponse.ok("OK")),
            Arguments.of("INTERNAL_SERVER_ERROR", HttpStatus.INTERNAL_SERVER_ERROR, "2222", "Server Error", HttpResponse.serverError(new ErroredPlexusAPIResponse<>(new ErroredPlexusAPIResponse.ErrorState(400, "Internal Server Error", "Server Error")))),
            Arguments.of("NOT_FOUND", HttpStatus.NOT_FOUND, "3333", "Not Found", HttpResponse.notFound(new ErroredPlexusAPIResponse<>(new ErroredPlexusAPIResponse.ErrorState(404, "Not Found", "Not Found")))),
            Arguments.of("BAD_REQUEST", HttpStatus.BAD_REQUEST, "5555", "Bad Request", HttpResponse.badRequest(new ErroredPlexusAPIResponse<>(new ErroredPlexusAPIResponse.ErrorState(400, "Bad Request", "Bad Request"))))
        );
    }
}