package global.symbio.billing.core.micronaut.http.plexus;

import io.micronaut.http.HttpMethod;
import io.micronaut.http.HttpStatus;
import io.micronaut.http.MutableHttpResponse;
import io.micronaut.http.server.exceptions.response.ErrorContext;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Answers;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;
import java.util.stream.Stream;

import static io.micronaut.http.HttpStatus.INTERNAL_SERVER_ERROR;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestPlexusAPIErrorResponseProcessor {

    private static final String UNEXPECTED_EXCEPTION = "Unexpected exception";

    private PlexusAPIErrorResponseProcessor processor;

    @Captor
    private ArgumentCaptor<PlexusAPIResponse<?>> body;

    @BeforeEach
    public void setup() {
        processor = new PlexusAPIErrorResponseProcessor();
    }

    @Test
    @DisplayName("PlexusAPIErrorResponseProcessor::processResponse does not modify response if request method was HTTP HEAD")
    public void plexus_api_error_response_processor_process_response_does_not_modify_response_if_request_method_was_head() {
        final var context = mock(ErrorContext.class, Answers.RETURNS_DEEP_STUBS);
        final var response = mock(MutableHttpResponse.class);
        when(context.getRequest().getMethod()).thenReturn(HttpMethod.HEAD);
        final var result = assertDoesNotThrow(() -> processor.processResponse(context, response));
        assertSame(response, result);
        verify(context, never()).getRootCause();
        verify(response, never()).body(any());
    }

    @ParameterizedTest
    @EnumSource(value = HttpMethod.class, names = {"GET", "POST", "PUT", "DELETE"})
    @DisplayName("PlexusAPIErrorResponseProcessor::processResponse modifies response into an ErroredPlexusAPIResponse if request method was not HTTP HEAD")
    public void plexus_api_error_response_processor_process_response_modifies_response_into_an_errored_plexus_api_response_if_request_method_not_http_head(final HttpMethod method) {
        final var context = mock(ErrorContext.class, Answers.RETURNS_DEEP_STUBS);
        when(context.getRequest().getMethod()).thenReturn(method);
        when(context.getRootCause()).thenReturn(Optional.of(new Exception("Request method: " + method)));
        final var response = mock(MutableHttpResponse.class, Answers.RETURNS_DEEP_STUBS);
        final var status = INTERNAL_SERVER_ERROR;
        when(response.status()).thenReturn(status);
        when(response.code()).thenReturn(status.getCode());
        when(response.reason()).thenReturn(status.getReason());

        final var result = assertDoesNotThrow(() -> processor.processResponse(context, response));
        assertNotSame(response, result);

        verify(context, times(1)).getRootCause();
        verify(response, times(1)).code();
        verify(response, times(1)).reason();
        verify(response, times(1)).status();
        verify(response.status(any(HttpStatus.class)), times(1)).body(body.capture());

        final var state = new ErroredPlexusAPIResponse.ErrorState(INTERNAL_SERVER_ERROR.getCode(), INTERNAL_SERVER_ERROR.getReason(), "Request method: " + method);
        assertEquals(new ErroredPlexusAPIResponse<>(state), body.getValue());
    }

    @ParameterizedTest
    @MethodSource("scenarios")
    @DisplayName("PlexusAPIErrorResponseProcessor::processResponse maps GRPC error to HTTP")
    public void plexus_api_error_response_processor_process_response_maps_grpc_error_to_http(final String exception, final String detail, final HttpStatus expected) {
        final var isGrpc = !UNEXPECTED_EXCEPTION.equals(detail);
        final var context = mock(ErrorContext.class, Answers.RETURNS_DEEP_STUBS);
        when(context.getRequest().getMethod()).thenReturn(HttpMethod.POST);
        when(context.getRootCause()).thenReturn(Optional.of(new Exception(exception + detail)));

        final var response = mock(MutableHttpResponse.class, Answers.RETURNS_DEEP_STUBS);
        final var status = INTERNAL_SERVER_ERROR;
        when(response.status()).thenReturn(status);
        when(response.code()).thenReturn(status.getCode());
        when(response.reason()).thenReturn(status.getReason());

        assertDoesNotThrow(() -> processor.processResponse(context, response));
        verify(response.status(any(HttpStatus.class)), times(1)).body(body.capture());
        final var error = (ErroredPlexusAPIResponse) body.getValue();
        assertEquals(expected.getCode(), error.getError().code());
        assertEquals(expected.getReason(), error.getError().message());
        assertEquals(isGrpc ? detail : exception + detail, error.getError().details());
    }

    @Nonnull
    private static Stream<Arguments> scenarios() {
        return Stream.of(
            Arguments.of("java.util.concurrent.ExecutionException: io.grpc.StatusRuntimeException: NOT_FOUND:", "Entity of type PlexusAccount with identifier 0ec64160-d3c8-49de-aac0-059f8989cb8e could not be found.", HttpStatus.NOT_FOUND),
            Arguments.of("java.util.concurrent.ExecutionException: io.grpc.StatusRuntimeException: INVALID_ARGUMENT:", "Unable to create an allocation greater than the unallocated amount. Unallocated: 1000, Requested: 5000.", HttpStatus.BAD_REQUEST),
            Arguments.of("java.util.concurrent.ExecutionException: io.grpc.StatusRuntimeException: NOT_FOUND:", "No bean of type [global.symbio.billing.core.services.rest.plexus.service.PlexusService] exists for the given qualifier: @Named('AU').", HttpStatus.NOT_FOUND),
            Arguments.of("java.util.concurrent.ExecutionException: io.grpc.StatusRuntimeException: UNKNOWN:", "An exception occurred completing the request.", HttpStatus.INTERNAL_SERVER_ERROR),
            Arguments.of("java.lang.RuntimeException ", UNEXPECTED_EXCEPTION, HttpStatus.INTERNAL_SERVER_ERROR)
        );
    }
}