package global.symbio.billing.core.micronaut.http.plexus;

import io.micronaut.http.HttpRequest;
import io.micronaut.http.HttpStatus;
import io.micronaut.http.MutableHttpResponse;
import io.micronaut.http.filter.ServerFilterChain;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.junit.jupiter.MockitoExtension;
import reactor.core.publisher.Flux;
import reactor.test.StepVerifier;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestPlexusAPIResponseHTTPFilter {

    private PlexusAPIResponseHTTPFilter filter;

    @Captor
    private ArgumentCaptor<PlexusAPIResponse<?>> body;

    @BeforeEach
    public void setup() {
        filter = new PlexusAPIResponseHTTPFilter();
    }

    @ParameterizedTest
    @EnumSource(value = HttpStatus.class, names = { "OK", "INTERNAL_SERVER_ERROR" })
    @DisplayName("PlexusAPIResponseHTTPFilter::doFilter modifies response body to be SuccessfulPlexusAPIResponse when HTTP Response status is 200 OK")
    public void plexus_api_response_http_filter_do_filter_modifies_response_body_to_be_successful_plexus_api_response_when_status_code_is_ok(final HttpStatus status) {
        final var request = mock(HttpRequest.class, RETURNS_DEEP_STUBS);
        when(request.getUri().getPath()).thenReturn("/v2/billing");
        final var response = mock(MutableHttpResponse.class);
        when(response.getStatus()).thenReturn(status);
        if (status == HttpStatus.OK) {
            when(response.getBody()).thenReturn(Optional.of(12345));
        }
        final var chain = mock(ServerFilterChain.class);
        when(chain.proceed(any(HttpRequest.class))).thenReturn(Flux.just(response));

        final var publisher = assertDoesNotThrow(() -> filter.doFilter(request, chain));
        final var verifier = StepVerifier.create(publisher).expectNext(response).expectComplete();
        assertDoesNotThrow(() -> verifier.verify());

        if (status == HttpStatus.OK) {
            verify(response, times(1)).getBody();
            verify(response, times(1)).body(body.capture());
            final var state = new SuccessfulPlexusAPIResponse(12345);
            assertEquals(state, body.getValue());
        } else {
            verify(response, never()).getBody();
            verify(response, never()).body(body.capture());
        }
    }

    @Test
    @DisplayName("PlexusAPIResponseHTTPFilter::doFilter does not modify ignored endpoints")
    public void plexus_api_response_http_filter_do_filter_does_not_modify_ignored_endpoints() {
        final var request = mock(HttpRequest.class, RETURNS_DEEP_STUBS);
        when(request.getUri().getPath()).thenReturn("/v2/billing/accounts/mockAccountId/invoices/mockInvoiceId/download");
        final var response = mock(MutableHttpResponse.class);
        final var chain = mock(ServerFilterChain.class);
        when(chain.proceed(any(HttpRequest.class))).thenReturn(Flux.just(response));

        final var publisher = assertDoesNotThrow(() -> filter.doFilter(request, chain));
        final var verifier = StepVerifier.create(publisher).expectNext(response).expectComplete();
        assertDoesNotThrow(() -> verifier.verify());

        verify(response, never()).getBody();
        verify(response, never()).body(body.capture());
    }
}
