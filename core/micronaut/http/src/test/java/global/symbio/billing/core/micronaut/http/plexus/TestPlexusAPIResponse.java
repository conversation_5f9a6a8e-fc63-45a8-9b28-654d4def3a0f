package global.symbio.billing.core.micronaut.http.plexus;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.platform.commons.support.ModifierSupport;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class TestPlexusAPIResponse {

    @Test
    @DisplayName("PlexusAPIResponse is abstract")
    public void plexus_api_response_is_abstract() {
        assertTrue(ModifierSupport.isAbstract(PlexusAPIResponse.class));
    }

    @Test
    @DisplayName("PlexusAPIResponse is sealed")
    public void plexus_api_response_is_sealed() {
        assertTrue(PlexusAPIResponse.class.isSealed());
    }

    @Test
    @DisplayName("PlexusAPIResponse only permits SuccessfulPlexusAPIResponse and ErroredPlexusAPIResponse")
    public void plexus_api_response_only_permits_successful_response_and_errored_response() {
        final var permittedSubclasses = PlexusAPIResponse.class.getPermittedSubclasses();
        final var expectedSubclasses = new Class[]{ SuccessfulPlexusAPIResponse.class, ErroredPlexusAPIResponse.class };
        assertArrayEquals(permittedSubclasses, expectedSubclasses);
    }
}
