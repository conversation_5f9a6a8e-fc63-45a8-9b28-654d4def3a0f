package global.symbio.billing.core.micronaut.http.plexus.exception;

import global.symbio.billing.core.exception.sdk.BillingEntityConflictException;
import io.micronaut.http.HttpRequest;
import io.micronaut.http.HttpStatus;
import io.micronaut.http.MutableHttpResponse;
import io.micronaut.http.server.exceptions.response.ErrorContext;
import io.micronaut.http.server.exceptions.response.ErrorResponseProcessor;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestBillingEntityConflictExceptionHandler {

    @Mock
    private ErrorResponseProcessor<?> processor;

    private BillingEntityConflictExceptionHandler handler;

    @Captor
    private ArgumentCaptor<ErrorContext> context;

    @Captor
    private ArgumentCaptor<MutableHttpResponse<?>> response;

    @BeforeEach
    public void setup() {
        handler = new BillingEntityConflictExceptionHandler(processor);
    }

    @Test
    @DisplayName("BillingEntityConflictExceptionHandler::new rejects null constructor arguments")
    public void billing_entity_conflict_exception_handler_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new BillingEntityConflictExceptionHandler(null));
    }

    @Test
    @DisplayName("BillingEntityConflictExceptionHandler::handle creates ErrorContext with request and cause, and sets response status to CONFLICT")
    public void billing_entity_conflict_exception_handler_handle_creates_error_context_and_sets_response_status_to_conflict() {
        final var request = mock(HttpRequest.class);
        final var exception = mock(BillingEntityConflictException.class);
        assertDoesNotThrow(() -> handler.handle(request, exception));
        verify(processor, times(1)).processResponse(context.capture(), response.capture());

        assertTrue(context.getValue().getRootCause().isPresent());
        assertEquals(request, context.getValue().getRequest());
        assertEquals(exception, context.getValue().getRootCause().get());
        assertEquals(HttpStatus.CONFLICT, response.getValue().status());
    }
}