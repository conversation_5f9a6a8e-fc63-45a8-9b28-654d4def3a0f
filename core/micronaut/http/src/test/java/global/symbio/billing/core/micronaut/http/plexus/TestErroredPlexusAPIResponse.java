package global.symbio.billing.core.micronaut.http.plexus;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.mock;

public class TestErroredPlexusAPIResponse {

    @Test
    @DisplayName("ErroredPlexusAPIResponse::new rejects null constructor arguments")
    public void errored_plexus_api_response_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new ErroredPlexusAPIResponse(null));
    }

    @Test
    @DisplayName("ErroredPlexusAPIResponse::new always sets success to false")
    public void errored_plexus_api_response_always_sets_success_to_false() {
        final var state = mock(ErroredPlexusAPIResponse.ErrorState.class);
        final var response = new ErroredPlexusAPIResponse<>(state);
        assertFalse(response.isSuccess());
    }
}
