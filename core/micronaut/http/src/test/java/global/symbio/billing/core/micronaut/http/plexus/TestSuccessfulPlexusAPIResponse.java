package global.symbio.billing.core.micronaut.http.plexus;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class TestSuccessfulPlexusAPIResponse {

    @Test
    @DisplayName("SuccessfulPlexusAPIResponse::new permits null constructor arguments")
    public void successful_plexus_api_response_permits_null_constructor_arguments() {
        assertDoesNotThrow(() -> new SuccessfulPlexusAPIResponse(null));
    }

    @Test
    @DisplayName("SuccessfulPlexusAPIResponse::new always sets success to true")
    public void successful_plexus_api_response_always_sets_success_to_true() {
        final var response = new SuccessfulPlexusAPIResponse<>(12345);
        assertTrue(response.isSuccess());
    }
}
