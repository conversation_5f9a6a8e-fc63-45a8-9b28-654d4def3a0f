package global.symbio.billing.core.micronaut.http.plexus.security.interceptor;

import global.symbio.billing.core.micronaut.http.plexus.security.annotation.CheckPermission;
import global.symbio.billing.core.util.constants.APIConstants;
import io.micronaut.aop.MethodInvocationContext;
import io.micronaut.core.annotation.AnnotationValue;
import io.micronaut.core.annotation.NonNull;
import io.micronaut.http.HttpRequest;
import io.micronaut.http.context.ServerRequestContext;
import io.micronaut.http.exceptions.HttpStatusException;
import io.micronaut.test.extensions.junit5.annotation.MicronautTest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.Mockito;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

@MicronautTest
public class TestCheckPermissionInterceptor{

    private CheckPermissionInterceptor interceptor;
    private MethodInvocationContext<Object, Object> context;

    @BeforeEach
    void setUp() {
        interceptor = new CheckPermissionInterceptor();
        context = Mockito.mock(MethodInvocationContext.class);
    }

    @Test
    @DisplayName("CheckPermissionInterceptor::intercept handles null annotation")
    void check_permission_interceptor_intercept_handles_null_annotation() {
        when(context.getAnnotation(CheckPermission.class)).thenReturn(null);
        interceptor.intercept(context);
    }

    @Test
    @DisplayName("CheckPermissionInterceptor::intercept handles empty currentRequestOpt")
    void check_permission_interceptor_intercept_handles_empty_currentRequestOpt() {
        when(context.getAnnotation(CheckPermission.class)).thenReturn(
            AnnotationValue.builder(CheckPermission.class).value("somePermission").build()
        );
        ServerRequestContext.with(null, () -> {
            interceptor.intercept(context);
        });
    }

    @Test
    @DisplayName("CheckPermissionInterceptor::intercept handles empty requiredPermissionOpt")
    void check_permission_interceptor_intercept_handles_empty_requiredPermissionOpt() {
        when(context.getAnnotation(CheckPermission.class)).thenReturn(
            AnnotationValue.builder(CheckPermission.class).build()
        );
        interceptor.intercept(context);
    }

    @Test
    @DisplayName("CheckPermissionInterceptor::intercept handles empty permissionsOpt")
    void check_permission_interceptor_intercept_handles_empty_permissionsOpt() {
        when(context.getAnnotation(CheckPermission.class)).thenReturn(
            AnnotationValue.builder(CheckPermission.class).value("somePermission").build()
        );
        final var request = Mockito.mock(HttpRequest.class);
        ServerRequestContext.with(request, () -> {
            when(request.getAttribute(APIConstants.PERMISSIONS)).thenReturn(Optional.empty());
            assertThrows(HttpStatusException.class, () -> interceptor.intercept(context));
        });
    }

    @ParameterizedTest
    @CsvSource({
        "invoice/get, 'INVOICE/GET,ACCOUNT/GET', true",
        "invoice/get, 'invoice/get,ACCOUNT/GET', true",
        "payment/create, 'CUSTOMER/GET,ACCOUNT/GET', false",
        "invoice/get, 'invoice/delete,Invoice/create,invoice/update', false",
        "invoice/get, 'invoice', false",
        "invoice/get, '', false",
        "payment, 'PAYMENT/GET', false"
    })
    @DisplayName("CheckPermissionInterceptor::intercept handles different permission scenarios")
    void check_permission_interceptor_intercept_handles_different_permission_scenarios(@NonNull String requiredPermission, @NonNull String availablePermissions, @NonNull boolean shouldPass) {
        when(context.getAnnotation(CheckPermission.class)).thenReturn(
            AnnotationValue.builder(CheckPermission.class).value(requiredPermission).build()
        );
        final var request = Mockito.mock(HttpRequest.class);
        ServerRequestContext.with(request, () -> {
            when(request.getAttribute(APIConstants.PERMISSIONS)).thenReturn(Optional.of(availablePermissions));

            if (shouldPass) {
                interceptor.intercept(context);
            } else {
                assertThrows(HttpStatusException.class, () -> interceptor.intercept(context));
            }
        });
    }
}