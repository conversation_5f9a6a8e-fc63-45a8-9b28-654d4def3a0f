package global.symbio.billing.core.micronaut.http.plexus;

import io.grpc.Status;
import io.micronaut.http.HttpStatus;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class TestGrpcToHttpStatusMapper {

    @ParameterizedTest
    @MethodSource("codes")
    @DisplayName("GrpcToHttpStatusMapper::map maps GRPC to HTTP status")
    public void grpc_to_http_status_mapper_maps_grpc_to_http_status(final Status status, final HttpStatus httpStatus) {
        final var result = GrpcToHttpStatusMapper.convert(status);
        assertEquals(httpStatus, result);
    }

    @Nonnull
    private static Stream<Arguments> codes() {
        return Stream.of(
            Arguments.of(Status.OK, HttpStatus.OK),
            Arguments.of(Status.NOT_FOUND, HttpStatus.NOT_FOUND),
            Arguments.of(Status.INVALID_ARGUMENT, HttpStatus.BAD_REQUEST),
            Arguments.of(Status.UNKNOWN, HttpStatus.INTERNAL_SERVER_ERROR),
            Arguments.of(Status.FAILED_PRECONDITION, HttpStatus.BAD_REQUEST),
            Arguments.of(Status.OUT_OF_RANGE, HttpStatus.BAD_REQUEST),
            Arguments.of(Status.DEADLINE_EXCEEDED, HttpStatus.GATEWAY_TIMEOUT),
            Arguments.of(Status.ALREADY_EXISTS, HttpStatus.CONFLICT),
            Arguments.of(Status.PERMISSION_DENIED, HttpStatus.FORBIDDEN),
            Arguments.of(Status.RESOURCE_EXHAUSTED, HttpStatus.TOO_MANY_REQUESTS),
            Arguments.of(Status.UNIMPLEMENTED, HttpStatus.NOT_IMPLEMENTED),
            Arguments.of(Status.UNAVAILABLE, HttpStatus.SERVICE_UNAVAILABLE),
            Arguments.of(Status.UNAUTHENTICATED, HttpStatus.UNAUTHORIZED)
        );
    }
}
