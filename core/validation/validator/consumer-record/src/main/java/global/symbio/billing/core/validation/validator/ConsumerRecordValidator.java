package global.symbio.billing.core.validation.validator;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.annotation.Stateless;
import global.symbio.billing.core.validation.Validatable;
import global.symbio.billing.core.validation.Validator;
import jakarta.annotation.Nonnull;
import org.apache.kafka.clients.consumer.ConsumerRecord;

import java.util.Objects;
import java.util.function.Function;

@Stateless
public record ConsumerRecordValidator<T>(@Nonnull Function<T, ConsumerRecord<?, ?>> extractor) implements Validator<T> {

    public ConsumerRecordValidator {
        Objects.requireNonNull(extractor, "extractor");
    }

    @Override
    public void validate(@Nonnull Validatable<T> component) {
        final var record = extractor.apply(component.element());
        if (record == null) {
            component.invalidate("Unable to extract ConsumerRecord from component");
        } else if (record.key() == null) {
            component.invalidate(format("key", record));
        } else if (record.value() == null) {
            component.invalidate(format("value", record));
        }
    }

    @Nonnull
    private static String format(@Nonnull String type, @Nonnull ConsumerRecord<?, ?> record) {
        return "Unable to extract " + type + " for ConsumerRecord from topic '" + record.topic() + "' at offset '" + record.offset() + "' in partition '" + record.partition() + "'";
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this).toString();
    }
}