package global.symbio.billing.core.validation.validator;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.MoreObjects;
import global.symbio.billing.core.annotation.Stateless;
import global.symbio.billing.core.validation.Validatable;
import global.symbio.billing.core.validation.Validator;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;
import java.util.function.Function;

@Slf4j
@Stateless
public record JsonStructureValidator<T>(@Nonnull ObjectMapper mapper, @Nonnull Function<T, String> extractor, @Nullable Class<T> type) implements Validator<T> {

    public JsonStructureValidator {
        Objects.requireNonNull(mapper, "mapper");
        Objects.requireNonNull(extractor, "extractor");
    }

    @Override
    public void validate(@Nonnull Validatable<T> component) {
        try {
            final var json = extractor.apply(component.element());
            if (type == null) {
                mapper.readTree(json);
            } else {
                mapper.readValue(json, type);
            }
            log.trace("Component validated - {}.", component.element());
        } catch (Throwable cause) {
            component.invalidate(cause);
            log.warn("Component invalidated - {}.", component.element(), component.cause());
        }
    }

    @Override
    public String toString() {
        final var builder = MoreObjects.toStringHelper(this);
        if (type != null) {
            builder.add("type", type.getSimpleName());
        }
        return builder.toString();
    }
}
