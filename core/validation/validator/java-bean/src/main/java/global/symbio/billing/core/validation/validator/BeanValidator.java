package global.symbio.billing.core.validation.validator;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.annotation.Stateless;
import global.symbio.billing.core.validation.Validatable;
import global.symbio.billing.core.validation.Validator;
import jakarta.annotation.Nonnull;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;
import java.util.function.Function;

@Slf4j
@Stateless
public record BeanValidator<T>(@Nonnull jakarta.validation.Validator validator, @Nonnull Function<T, ?> extractor) implements Validator<T> {

    public BeanValidator {
        Objects.requireNonNull(validator, "validator");
        Objects.requireNonNull(extractor, "extractor");
    }

    @Override
    public void validate(@Nonnull Validatable<T> component) {
        final var bean = extractor.apply(component.element());
        try {
            final var violations = validator.validate(bean);
            if (!violations.isEmpty()) {
                throw new ConstraintViolationException(violations);
            }
            log.trace("Component validated - {}.", component.element());
        } catch (Throwable cause) {
            component.invalidate(cause);
            log.warn("Component invalidated - {}.", component.element(), component.cause());
        }
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this).add("validator", validator.getClass().getSimpleName()).toString();
    }
}
