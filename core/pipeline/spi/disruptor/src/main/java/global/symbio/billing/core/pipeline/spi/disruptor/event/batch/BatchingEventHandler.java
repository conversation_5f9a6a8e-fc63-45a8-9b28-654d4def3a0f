package global.symbio.billing.core.pipeline.spi.disruptor.event.batch;

import com.google.common.base.MoreObjects;
import com.lmax.disruptor.EventHandler;
import com.lmax.disruptor.RewindableException;
import com.lmax.disruptor.Sequence;
import global.symbio.billing.core.pipeline.spi.disruptor.event.value.ValueEvent;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.time.Instant;
import java.util.ArrayDeque;
import java.util.Objects;
import java.util.Queue;

@Slf4j
@Getter
public class BatchingEventHandler<T> implements EventHandler<ValueEvent<T>> {

    protected static final int DEFAULT_QUEUE_SIZE = 128;

    @Nonnull
    private final BatchConsumer<T> consumer;

    @Nonnull
    private final Queue<T> batch;

    @Nullable
    private Sequence sequence;

    public BatchingEventHandler(@Nonnull BatchConsumer<T> consumer) {
        this(consumer, DEFAULT_QUEUE_SIZE);
    }

    public BatchingEventHandler(@Nonnull BatchConsumer<T> consumer, int size) {
        this(consumer, new ArrayDeque<>(size));
    }

    public BatchingEventHandler(@Nonnull BatchConsumer<T> consumer, @Nonnull Queue<T> batch) {
        this.consumer = Objects.requireNonNull(consumer, "consumer");
        this.batch = Objects.requireNonNull(batch, "batch");
    }

    protected boolean interested(@Nonnull ValueEvent<T> event, long sequence) {
        return consumer.isInterested(event.getValue());
    }

    @Override
    public void onEvent(@Nonnull ValueEvent<T> event, long sequence, boolean complete) throws Exception {
        if (interested(event, sequence)) {
            batch.add(event.getValue());
        }

        if (complete || consumer.isConsumable(batch)) {
            handle();
            this.sequence.set(sequence);
        }
    }

    private void handle() throws Exception {
        if (log.isInfoEnabled()) {
            final var start = Instant.now();
            final var size = batch.size();
            consume();
            final var duration = Duration.between(start, Instant.now());
            log.info("Elements consumed - {}. Duration - {}. Consumer - {}", size, duration, consumer);
        } else {
            consume();
        }
    }

    private void consume() throws Exception {
        try {
            try {
                if (!batch.isEmpty()) {
                    consumer.consume(batch);
                }
            } finally {
                batch.clear();
            }
        } catch (Throwable cause) {
            if (consumer.isRetryable(cause)) {
                throw new RewindableException(cause);
            }
            throw cause;
        }
    }

    @Override
    public void setSequenceCallback(Sequence sequence) {
        this.sequence = Objects.requireNonNull(sequence, "sequence");
    }

    @Override
    public void onBatchStart(long size) {
        log.trace("Batch size - {}. Consumer - {}", size, consumer);
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this).add("consumer", consumer).toString();
    }
}
