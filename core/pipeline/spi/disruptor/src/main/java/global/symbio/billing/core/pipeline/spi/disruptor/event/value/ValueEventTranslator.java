package global.symbio.billing.core.pipeline.spi.disruptor.event.value;

import com.lmax.disruptor.EventTranslatorOneArg;

public record ValueEventTranslator<T>() implements EventTranslatorOneArg<ValueEvent<T>, T> {

    private static final ValueEventTranslator<?> INSTANCE = new ValueEventTranslator<>();

    @Override
    public void translateTo(ValueEvent<T> event, long sequence, T value) {
        event.setValue(value);
    }

    @SuppressWarnings("unchecked")
    public static <T> ValueEventTranslator<T> getInstance() {
        return (ValueEventTranslator<T>) INSTANCE;
    }
}
