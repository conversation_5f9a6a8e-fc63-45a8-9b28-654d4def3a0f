package global.symbio.billing.core.pipeline.spi.disruptor.event.batch;

import com.google.common.base.MoreObjects;
import com.google.common.base.Preconditions;
import global.symbio.billing.core.pipeline.spi.disruptor.event.value.ValueEvent;
import jakarta.annotation.Nonnull;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.function.Supplier;

@Slf4j
@Getter
public class ShardedBatchingEventHandler<T> extends BatchingEventHandler<T> {

    protected final int ordinal, total;

    protected ShardedBatchingEventHandler(@Nonnull BatchConsumer<T> consumer, int ordinal, int total) {
        super(consumer);
        this.ordinal = ordinal;
        this.total = total;
    }

    @Override
    protected boolean interested(@Nonnull ValueEvent<T> event, long sequence) {
        return sequence % total == ordinal && super.interested(event, sequence);
    }

    @SuppressWarnings("unchecked")
    public static <T> ShardedBatchingEventHandler<T>[] create(int total, Supplier<BatchConsumer<T>> consumer) {
        Preconditions.checkArgument(total > 1, "Must create more than one sharded batching event handler.");
        final var shards = new ShardedBatchingEventHandler[total];
        for (int ordinal = 0; ordinal < total; ordinal++) {
            shards[ordinal] = new ShardedBatchingEventHandler<>(consumer.get(), ordinal, total);
        }
        return (ShardedBatchingEventHandler<T>[]) shards;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this).add("consumer", getConsumer()).add("ordinal", ordinal).add("total", total).toString();
    }
}
