package global.symbio.billing.core.pipeline.spi.disruptor;

import com.google.common.base.MoreObjects;
import com.lmax.disruptor.EventTranslatorOneArg;
import com.lmax.disruptor.dsl.Disruptor;
import global.symbio.billing.core.pipeline.api.PipelineEngine;
import global.symbio.billing.core.pipeline.spi.disruptor.event.value.ValueEvent;
import global.symbio.billing.core.pipeline.spi.disruptor.event.value.ValueEventTranslator;
import jakarta.annotation.Nonnull;

import java.util.List;
import java.util.Objects;
import java.util.function.IntFunction;

public record DisruptorPipelineEngine<T>(@Nonnull Disruptor<ValueEvent<T>> disruptor, @Nonnull EventTranslatorOneArg<ValueEvent<T>, T> translator, @Nonnull IntFunction<T[]> converter) implements PipelineEngine<T> {

    public DisruptorPipelineEngine {
        Objects.requireNonNull(disruptor, "disruptor");
        Objects.requireNonNull(translator, "translator");
        Objects.requireNonNull(converter, "converter");
    }

    public DisruptorPipelineEngine(@Nonnull Disruptor<ValueEvent<T>> disruptor, @Nonnull IntFunction<T[]> converter) {
        this(disruptor, ValueEventTranslator.getInstance(), converter);
    }

    @Override
    public void receive(@Nonnull T element) {
        disruptor.publishEvent(translator, element);
    }

    @Override
    public void receive(@Nonnull List<T> elements) {
        final var events = elements.toArray(converter);
        disruptor.publishEvents(translator, events);
    }

    @Override
    public void start() {
        disruptor.start();
    }

    @Override
    public void stop() {
        disruptor.shutdown();
    }

    @Override
    public int capacity() {
        final var buffer = disruptor.getBufferSize();
        if (buffer <= 0 || buffer > Integer.MAX_VALUE) {
            return Integer.MIN_VALUE;
        }
        return (int) buffer;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this).add("buffer", disruptor.getBufferSize()).add("started", disruptor.hasStarted()).toString();
    }
}
