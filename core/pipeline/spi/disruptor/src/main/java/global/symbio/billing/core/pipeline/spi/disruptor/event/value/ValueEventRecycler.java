package global.symbio.billing.core.pipeline.spi.disruptor.event.value;

import com.lmax.disruptor.EventHandler;

public record ValueEventRecycler<T>() implements EventHandler<ValueEvent<T>> {

    private static final ValueEventRecycler<?> INSTANCE = new ValueEventRecycler<>();

    @Override
    public void onEvent(ValueEvent<T> event, long sequence, boolean complete) {
        event.clear();
    }

    @SuppressWarnings("unchecked")
    public static <T> ValueEventRecycler<T> getInstance() {
        return (ValueEventRecycler<T>) INSTANCE;
    }
}