package global.symbio.billing.core.pipeline.spi.disruptor;

import com.lmax.disruptor.dsl.Disruptor;
import global.symbio.billing.core.pipeline.api.PipelineEngine;
import global.symbio.billing.core.pipeline.spi.disruptor.event.value.ValueEvent;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestDisruptorPipelineEngine {

    @Mock
    private Disruptor<ValueEvent<Integer>> disruptor;

    private PipelineEngine<Integer> engine;

    @BeforeEach
    public void setup() {
        engine = new DisruptorPipelineEngine<>(disruptor, Integer[]::new);
    }

    @AfterEach
    public void teardown() {
        engine.stop();
    }

    @Test
    @DisplayName("DisruptorPipelineEngine life-cycle methods invoke the Disruptors life-cycle methods")
    public void verify_engine_lifecycle_maps_to_disruptor_lifecycle() {
        verify(disruptor, never()).start();
        verify(disruptor, never()).shutdown();
        engine.start();
        verify(disruptor, times(1)).start();
        verify(disruptor, never()).shutdown();
        engine.stop();
        verify(disruptor, times(1)).start();
        verify(disruptor, times(1)).shutdown();
    }

    @ParameterizedTest(name = "{index} DisruptorPipelineEngine.receive(List.of({0})) invokes Disruptor.publishEvents(translator, [{0}])")
    @ValueSource(ints = { 42, 69, 420, 1337 })
    @DisplayName("DisruptorPipelineEngine propagates list of events to Disruptor for publication")
    public void list_of_events_received_by_engine_is_published_to_disruptor(int value) {
        verify(disruptor, never()).publishEvents(any(), any());
        engine.receive(List.of(value));
        final Integer[] array = { value };
        verify(disruptor, times(1)).publishEvents(any(), eq(array));
    }

    @ParameterizedTest(name = "{index} DisruptorPipelineEngine.receive({0}) invokes Disruptor.publishEvent(translator, {0})")
    @ValueSource(ints = { 42, 69, 420, 1337 })
    @DisplayName("DisruptorPipelineEngine propagates single event to Disruptor for publication")
    public void single_event_received_by_engine_is_published_to_disruptor(int value) {
        verify(disruptor, never()).publishEvent(any(), any());
        engine.receive(value);
        verify(disruptor, times(1)).publishEvent(any(), eq(value));
    }
}
