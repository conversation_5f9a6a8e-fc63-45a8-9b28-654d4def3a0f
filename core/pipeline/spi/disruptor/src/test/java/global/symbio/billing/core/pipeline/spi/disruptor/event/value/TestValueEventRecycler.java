package global.symbio.billing.core.pipeline.spi.disruptor.event.value;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestValueEventRecycler {

    @Mock
    private ValueEvent<String> event;

    private ValueEventRecycler<String> recycler;

    @BeforeEach
    public void setup() {
        recycler = ValueEventRecycler.getInstance();
    }

    @Test
    @DisplayName("ValueEventRecycler invokes ValueEvent#clear")
    public void value_event_recycler_clears_events() {
        verify(event, never()).clear();
        recycler.onEvent(event, 0L, true);
        verify(event, times(1)).clear();
    }
}
