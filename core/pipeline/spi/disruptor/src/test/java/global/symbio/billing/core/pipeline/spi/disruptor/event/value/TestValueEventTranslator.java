package global.symbio.billing.core.pipeline.spi.disruptor.event.value;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestValueEventTranslator {

    @Mock
    private ValueEvent<Integer> event;

    private ValueEventTranslator<Integer> translator;

    @BeforeEach
    public void setup() {
        translator = ValueEventTranslator.getInstance();
    }

    @ParameterizedTest(name = "{index} ValeEventTranslator.translateTo(event, 0L, {0}) invokes ValueEvent.set({0})")
    @ValueSource(ints = { 42, 69, 420, 1337 })
    @DisplayName("ValueEventTranslator invokes ValueEvent#set")
    public void value_event_translator_sets_events_value(int value) {
        verify(event, never()).setValue(anyInt());
        translator.translateTo(event, 0L, value);
        verify(event, times(1)).setValue(value);
    }
}
