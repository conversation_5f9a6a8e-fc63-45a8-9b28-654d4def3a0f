package global.symbio.billing.core.pipeline.spi.disruptor;

import com.lmax.disruptor.BatchEventProcessor;
import com.lmax.disruptor.PhasedBackoffWaitStrategy;
import com.lmax.disruptor.dsl.Disruptor;
import com.lmax.disruptor.dsl.ProducerType;
import global.symbio.billing.core.pipeline.api.Pipeline;
import global.symbio.billing.core.pipeline.api.PipelineEngine;
import global.symbio.billing.core.pipeline.api.request.MultipleItemRequest;
import global.symbio.billing.core.pipeline.api.request.Request;
import global.symbio.billing.core.pipeline.api.request.SingleItemRequest;
import global.symbio.billing.core.pipeline.api.request.router.RequestRouter;
import global.symbio.billing.core.pipeline.spi.disruptor.event.batch.BatchConsumer;
import global.symbio.billing.core.pipeline.spi.disruptor.event.batch.BatchingEventHandler;
import global.symbio.billing.core.pipeline.spi.disruptor.event.batch.ShardedBatchingEventHandler;
import global.symbio.billing.core.pipeline.spi.disruptor.event.value.ValueEvent;
import jakarta.annotation.Nonnull;
import lombok.Getter;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;

public class TestDisruptorPipelineEngineE2E {

    @ParameterizedTest(name = "{index} Unstarted Disruptor pipeline does not process any requests [{0}]")
    @ValueSource(ints = {42, 69, 420, 1337})
    @DisplayName("Unstarted Disruptor pipeline does not process any requests")
    public void unstarted_disruptor_pipeline_does_not_receive_events(int value) {
        final var ctx = TestContext.create(1);

        assertFalse(ctx.disruptor.hasStarted());
        assertTrue(ctx.disruptor.getBufferSize() >= 1);
        assertDoesNotThrow(() -> ctx.pipeline.consume(Request.of(value)));

        assertDoesNotThrow(ctx.pipeline::stop);
        for (int index = 0; index < ctx.shards.length; index++) {
            final var expected = 0;
            final var actual = ctx.sum(index);
            assertEquals(expected, actual);
        }

        assertEquals(0, ctx.summation.getSum());
        assertEquals(0, ctx.count.getCount());
    }

    @ParameterizedTest(name = "{index} Disruptor pipeline rejects and does not process requests that can't be buffered [{0}]")
    @ValueSource(ints = {42, 69, 420, 1337})
    @DisplayName("Disruptor pipeline rejects requests that can't be buffered")
    @Timeout(value = 15, unit = TimeUnit.SECONDS, threadMode = Timeout.ThreadMode.SEPARATE_THREAD)
    public void disruptor_pipeline_throws_exception_when_unable_to_buffer_events(int value) {
        final var sum = value;
        final var count = 1;
        final var ctx = TestContext.create(1);

        assertFalse(ctx.disruptor.hasStarted());
        assertDoesNotThrow(ctx.pipeline::start);
        assertTrue(ctx.disruptor.hasStarted());

        final var single = assertInstanceOf(SingleItemRequest.class, Request.of(value));
        final var multiple = assertInstanceOf(MultipleItemRequest.class, Request.of(List.of(value, value, value, value)));

        assertTrue(ctx.disruptor.getBufferSize() >= 1);
        assertFalse(ctx.disruptor.getBufferSize() >= multiple.elements().size());

        assertDoesNotThrow(() -> ctx.pipeline.consume(single));
        assertThrows(IllegalArgumentException.class, () -> ctx.pipeline.consume(multiple));

        while (sum != ctx.summation.getSum() || count != ctx.count.getCount()) {
            Thread.onSpinWait();
        }

        assertDoesNotThrow(ctx.pipeline::stop);
        for (int index = 0; index < ctx.shards.length; index++) {
            // only the first request does not throw and is actually processed
            final var expected = index == 0 ? value : 0;
            final var actual = ctx.sum(index);
            assertEquals(expected, actual);
        }

        assertEquals(sum, ctx.summation.getSum());
        assertEquals(count, ctx.count.getCount());
    }

    @Test
    @DisplayName("Partitioned Disruptor pipeline receives distributed events across partitions and processes them end-to-end")
    @Timeout(value = 15, unit = TimeUnit.SECONDS, threadMode = Timeout.ThreadMode.SEPARATE_THREAD)
    public void partitioned_disruptor_pipeline_receives_and_processes_events_correctly_e2e() {
        final var sum = 75;
        final var count = 25;
        final var ctx = TestContext.create(32, 5);

        assertFalse(ctx.disruptor.hasStarted());
        assertDoesNotThrow(ctx.pipeline::start);
        assertTrue(ctx.disruptor.hasStarted());

        // each partition receives one column of events (P0 -> 5x1, P1 -> 5x2, ... etc)
        final var matrix = List.of(
            1, 2, 3, 4, 5,
            1, 2, 3, 4, 5,
            1, 2, 3, 4, 5,
            1, 2, 3, 4, 5,
            1, 2, 3, 4, 5
        );

        final var request = assertInstanceOf(MultipleItemRequest.class, Request.of(matrix));
        assertTrue(ctx.disruptor.getBufferSize() >= request.elements().size());
        assertDoesNotThrow(() -> ctx.pipeline.consume(request));

        while (sum != ctx.summation.getSum() || count != ctx.count.getCount()) {
            Thread.onSpinWait();
        }

        assertDoesNotThrow(ctx.pipeline::stop);
        for (int index = 0; index < ctx.shards.length; index++) {
            final var expected = 5 * (index + 1);
            final var actual = ctx.sum(index);
            assertEquals(expected, actual);
        }

        assertEquals(sum, ctx.summation.getSum()); // cumulative value of 75 for all events
        assertEquals(count, ctx.count.getCount()); // 25 events in total
    }

    @Test
    @DisplayName("SummingBatchConsumer sums the cumulative value of each element received")
    public void test_summing_batch_consumer_contract() {
        final var consumer = new SummingBatchConsumer();
        final var elements = List.of(1, 2, 3, 4, 5);
        assertDoesNotThrow(() -> consumer.consume(elements));
        assertEquals(15, consumer.getSum());
    }

    @Test
    @DisplayName("CountingBatchConsumer counts the total number of elements received")
    public void test_counting_batch_consumer_contract() {
        final var consumer = new CountingBatchConsumer();
        final var elements = List.of(1, 2, 3, 4, 5);
        assertDoesNotThrow(() -> consumer.consume(elements));
        assertEquals(5, consumer.getCount());
    }

    private record TestContext(
        @Nonnull Pipeline<Integer, PipelineEngine<Integer>, RequestRouter<Integer>> pipeline,
        @Nonnull Disruptor<ValueEvent<Integer>> disruptor,
        @Nonnull ShardedBatchingEventHandler<Integer>[] shards,
        @Nonnull SummingBatchConsumer summation,
        @Nonnull CountingBatchConsumer count
    ) {

        int sum(int index) {
            return ((SummingBatchConsumer) shards[index].getConsumer()).getSum();
        }

        static TestContext create(int size) {
            return create(size, 2);
        }

        static TestContext create(int size, int partitions) {
            final var disruptor = disruptor(size);
            final var buffer = disruptor.getRingBuffer();

            final var shards = ShardedBatchingEventHandler.create(partitions, SummingBatchConsumer::new);
            final var summation = new SummingBatchConsumer();
            final var count = new CountingBatchConsumer();

            final var processors = Arrays.stream(shards).map(shard -> new BatchEventProcessor<>(buffer, buffer.newBarrier(), shard)).toArray(BatchEventProcessor[]::new);
            disruptor.handleEventsWith(processors).then(new BatchingEventHandler<>(summation), new BatchingEventHandler<>(count));

            final var pipeline = pipeline(disruptor);

            return new TestContext(pipeline, disruptor, shards, summation, count);
        }

        private static Disruptor<ValueEvent<Integer>> disruptor(int buffer) {
            final var factory = Thread.ofPlatform().daemon().factory();
            return new Disruptor<>(ValueEvent::new, buffer, factory, ProducerType.SINGLE, PhasedBackoffWaitStrategy.withLock(60, 60, TimeUnit.SECONDS));
        }

        private static Pipeline<Integer, PipelineEngine<Integer>, RequestRouter<Integer>> pipeline(Disruptor<ValueEvent<Integer>> disruptor) {
            final var engine = new DisruptorPipelineEngine<>(disruptor, Integer[]::new);
            return Pipeline.mono("DisruptorPipelineE2E", engine, RequestRouter.create());
        }
    }

    @Getter
    private static class SummingBatchConsumer implements BatchConsumer<Integer> {

        private final AtomicInteger sum = new AtomicInteger(0);

        public int getSum() {
            return sum.get();
        }

        @Override
        public void consume(@Nonnull Collection<Integer> elements) {
            final var total = elements.stream().mapToInt(Integer::intValue).sum();
            sum.addAndGet(total);
        }
    }

    @Getter
    private static class CountingBatchConsumer implements BatchConsumer<Integer> {

        private final AtomicInteger count = new AtomicInteger(0);

        public int getCount() {
            return count.get();
        }

        @Override
        public void consume(@Nonnull Collection<Integer> elements) {
            count.addAndGet(elements.size());
        }
    }
}
