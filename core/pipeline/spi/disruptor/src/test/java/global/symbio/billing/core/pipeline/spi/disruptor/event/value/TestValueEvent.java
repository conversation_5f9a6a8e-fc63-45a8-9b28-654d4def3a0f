package global.symbio.billing.core.pipeline.spi.disruptor.event.value;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

public class TestValueEvent {

    @Test
    @DisplayName("ValueEvent no-arg constructor initialises value to null")
    public void value_event_default_constructor_null_value() {
        final var event = new ValueEvent<>();
        assertNull(event.getValue());
    }

    @ParameterizedTest(name = "{index} ValueEvent({0}) constructor initialises value to {0}")
    @ValueSource(ints = { 42, 69, 420, 1337 })
    @DisplayName("ValueEvent parameterised constructor initialises value to given value")
    public void value_event_parameterised_constructor_nonnull_value(int value) {
        final var event = new ValueEvent<>(value);
        assertEquals(value, event.getValue());
    }

    @ParameterizedTest(name = "{index} ValueEvent().set({0}).clear() creates a ValueEvent whose value is null, {0}, null")
    @ValueSource(ints = { 42, 69, 420, 1337 })
    @DisplayName("ValueEvent no-arg constructor and setter initialises value to given value and clears value to null")
    public void value_event_default_constructor_set_and_clear_value(int value) {
        final var event = new ValueEvent<>();
        assertNull(event.getValue());
        event.setValue(value);
        assertEquals(value, event.getValue());
        event.clear();
        assertNull(event.getValue());
    }
}
