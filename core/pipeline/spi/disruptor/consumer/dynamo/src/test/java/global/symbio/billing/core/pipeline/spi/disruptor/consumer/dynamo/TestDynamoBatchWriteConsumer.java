package global.symbio.billing.core.pipeline.spi.disruptor.consumer.dynamo;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.retries.api.BackoffStrategy;

import java.time.Duration;
import java.util.Arrays;
import java.util.function.Function;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.lenient;

@ExtendWith(MockitoExtension.class)
public class TestDynamoBatchWriteConsumer {

    @Mock
    private DynamoDbEnhancedClient dynamo;

    @Mock
    private DynamoDbTable<Integer> table;

    @Mock
    private BackoffStrategy backoff;

    private DynamoBatchWriteConsumer<Integer, Integer, Integer> consumer;

    @BeforeEach
    public void setup() {
        consumer = new DynamoBatchWriteConsumer<>(dynamo, table, Integer.class, Function.identity(), Function.identity(), backoff, 3);
        lenient().when(backoff.computeDelay(anyInt())).thenReturn(Duration.ofSeconds(1L));
    }

    @Test
    @DisplayName("DynamoBatchWriteConsumer::new rejects null constructor arguments")
    public void dynamo_batch_write_consumer_new_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new DynamoBatchWriteConsumer<>(null, table, Integer.class, Function.identity(), Function.identity(), backoff));
        assertThrows(NullPointerException.class, () -> new DynamoBatchWriteConsumer<>(dynamo, null, Integer.class, Function.identity(), Function.identity(), backoff));
        assertThrows(NullPointerException.class, () -> new DynamoBatchWriteConsumer<>(dynamo, table, null, Function.identity(), Function.identity(), backoff));
        assertThrows(NullPointerException.class, () -> new DynamoBatchWriteConsumer<>(dynamo, table, Integer.class, null, Function.identity(), backoff));
        assertThrows(NullPointerException.class, () -> new DynamoBatchWriteConsumer<>(dynamo, table, Integer.class, Function.identity(), null, backoff));
        assertThrows(NullPointerException.class, () -> new DynamoBatchWriteConsumer<>(dynamo, table, Integer.class, Function.identity(), Function.identity(), null));
    }

    @Test
    @DisplayName("DynamoBatchWriteConsumer::consume rejects null input")
    public void dynamo_batch_write_consumer_consume_null_collection() {
        assertThrows(NullPointerException.class, () -> consumer.consume(null));
    }

    @ParameterizedTest(name = "{index} DynamoBatchWriteConsumer with limit of {0} is consumable when given list of {1} elements == {2}")
    @CsvSource({"3,0,false", "3,1,false", "3,2,false", "3,3,true", "3,4,true", "3,5,true"})
    @DisplayName("DynamoBatchWriteConsumer is consumable given a limit and different sized collections of elements")
    public void dynamo_batch_write_consumer_limit_is_consumable(int limit, int size, boolean expected) {
        final var consumer = new DynamoBatchWriteConsumer<>(dynamo, table, Integer.class, Function.identity(), Function.identity(), backoff, limit);
        final var elements = Arrays.asList(new Integer[size]);
        assertEquals(expected, consumer.isConsumable(elements));
    }
}