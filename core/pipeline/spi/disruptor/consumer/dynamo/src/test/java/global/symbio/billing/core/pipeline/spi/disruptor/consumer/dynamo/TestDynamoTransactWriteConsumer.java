package global.symbio.billing.core.pipeline.spi.disruptor.consumer.dynamo;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.model.TransactWriteItemsEnhancedRequest;

import java.util.Arrays;
import java.util.List;
import java.util.function.Function;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class TestDynamoTransactWriteConsumer {

    @Mock
    private DynamoDbEnhancedClient dynamo;

    @Mock
    private DynamoDbTable<Integer> table;

    private DynamoTransactWriteConsumer<Integer, Integer, Integer> consumer;

    @BeforeEach
    public void setup() {
        consumer = new DynamoTransactWriteConsumer<>(dynamo, table, Function.identity(), Function.identity(), 3);
    }

    @Test
    @DisplayName("DynamoTransactWriteConsumer::new rejects null constructor arguments")
    public void dynamo_transact_write_consumer_new_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new DynamoTransactWriteConsumer<>(null, table, Function.identity(), Function.identity()));
        assertThrows(NullPointerException.class, () -> new DynamoTransactWriteConsumer<>(dynamo, null, Function.identity(), Function.identity()));
        assertThrows(NullPointerException.class, () -> new DynamoTransactWriteConsumer<>(dynamo, table, null, Function.identity()));
        assertThrows(NullPointerException.class, () -> new DynamoTransactWriteConsumer<>(dynamo, table, Function.identity(), null));
    }

    @Test
    @DisplayName("DynamoTransactWriteConsumer::consume rejects null input")
    public void dynamo_transact_write_consumer_consume_null_collection() {
        assertThrows(NullPointerException.class, () -> consumer.consume(null));
    }

    @ParameterizedTest(name = "{index} DynamoTransactWriteConsumer with limit of {0} is consumable when given list of {1} elements == {2}")
    @CsvSource({"3,0,false", "3,1,false", "3,2,false", "3,3,true", "3,4,true", "3,5,true"})
    @DisplayName("DynamoTransactWriteConsumer is consumable given a limit and different sized collections of elements")
    public void dynamo_transact_write_consumer_limit_is_consumable(int limit, int size, boolean expected) {
        final var consumer = new DynamoTransactWriteConsumer<>(dynamo, table, Function.identity(), Function.identity(), limit);
        final var elements = Arrays.asList(new Integer[size]);
        assertEquals(expected, consumer.isConsumable(elements));
    }

    @Test
    @DisplayName("DynamoTransactWriteConsumer::consume calls dynamo::transactWriteItems")
    public void dynamo_transact_write_consumer_consume_calls_transact_write_items() {
        assertDoesNotThrow(() -> consumer.consume(List.of()));
        verify(dynamo, times(1)).transactWriteItems(any(TransactWriteItemsEnhancedRequest.class));
    }
}