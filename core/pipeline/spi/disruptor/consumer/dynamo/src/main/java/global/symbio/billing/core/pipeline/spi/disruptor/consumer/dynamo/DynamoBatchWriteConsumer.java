package global.symbio.billing.core.pipeline.spi.disruptor.consumer.dynamo;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.MoreObjects;
import global.symbio.billing.core.pipeline.spi.disruptor.event.batch.BatchConsumer;
import jakarta.annotation.Nonnull;
import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.model.BatchWriteItemEnhancedRequest;
import software.amazon.awssdk.enhanced.dynamodb.model.BatchWriteResult;
import software.amazon.awssdk.enhanced.dynamodb.model.WriteBatch;
import software.amazon.awssdk.retries.api.BackoffStrategy;

import java.time.Duration;
import java.util.Collection;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class DynamoBatchWriteConsumer<T, K, E> implements BatchConsumer<T> {

    // Limitation as per: https://docs.aws.amazon.com/amazondynamodb/latest/APIReference/API_BatchWriteItem.html
    public static final int DEFAULT_MAX_BATCH_SIZE = 25;

    //TODO: accommodate for TTL field/mechanism...

    @Nonnull
    private final DynamoDbEnhancedClient dynamo;

    @Nonnull
    private final DynamoDbTable<E> table;

    @Nonnull
    private final Class<E> type;

    @Nonnull
    private final Function<T, K> key;

    @Nonnull
    private final Function<T, E> encoder;

    @Nonnull
    private final BackoffStrategy backoff;

    private final int limit;

    public DynamoBatchWriteConsumer(@Nonnull DynamoDbEnhancedClient dynamo, @Nonnull DynamoDbTable<E> table, @Nonnull Class<E> type, @Nonnull Function<T, K> key, @Nonnull Function<T, E> encoder, @Nonnull BackoffStrategy backoff) {
        this(dynamo, table, type, key, encoder, backoff, DEFAULT_MAX_BATCH_SIZE);
    }

    public DynamoBatchWriteConsumer(@Nonnull DynamoDbEnhancedClient dynamo, @Nonnull DynamoDbTable<E> table, @Nonnull Class<E> type, @Nonnull Function<T, K> key, @Nonnull Function<T, E> encoder, @Nonnull BackoffStrategy backoff, int limit) {
        this.dynamo = Objects.requireNonNull(dynamo, "dynamo");
        this.table = Objects.requireNonNull(table, "table");
        this.type = Objects.requireNonNull(type, "type");
        this.key = Objects.requireNonNull(key, "key");
        this.encoder = Objects.requireNonNull(encoder, "encoder");
        this.backoff = Objects.requireNonNull(backoff, "backoff");
        this.limit = limit;
    }

    @Override
    public void consume(@Nonnull Collection<T> elements) {
        write(elements);
    }

    private void write(@Nonnull Collection<T> elements) {
        final var items = elements.stream().collect(Collectors.toMap(key, encoder, (latest, _) -> latest));
        if (items.size() != elements.size()) {
            log.warn("Duplicate items detected in batch. Expected: {}, De-duplicated: {}.", elements.size(), items.size());
        }
        var attempts = 0;
        var pending = items.values();
        while (!pending.isEmpty()) {
            final var result = doWrite(pending);
            pending = result.unprocessedPutItemsForTable(table);
            if (pending.isEmpty()) {
                break;
            }
            // The number of attempts is post-incremented so that the first retry is our 0th.
            final var delay = computeDelayBeforeNextRetry(attempts++);
            log.warn("BatchWrite operation is being retried for {} items after {} attempts. Waiting for {}", pending.size(), attempts, delay);
            if (delay.isPositive()) {
                try {
                    Thread.sleep(delay);
                } catch (InterruptedException cause) {
                    log.warn("BatchWrite operation unable to be delayed: {}", delay, cause);
                }
            }
        }
    }

    @Nonnull
    @VisibleForTesting
    Duration computeDelayBeforeNextRetry(int attempts) {
        return backoff.computeDelay(attempts);
    }

    @Nonnull
    private BatchWriteResult doWrite(@Nonnull Collection<E> items) {
        final var builder = BatchWriteItemEnhancedRequest.builder();
        final var batch = WriteBatch.builder(type).mappedTableResource(table);
        for (final var item : items) {
            batch.addPutItem(item);
        }
        builder.addWriteBatch(batch.build());
        return dynamo.batchWriteItem(builder.build());
    }

    @Override
    public boolean isConsumable(@Nonnull Collection<T> elements) {
        return limit <= elements.size();
    }

    @Override
    public boolean isRetryable(@Nonnull Throwable cause) {
        //TODO: determine which exceptions are retryable
        return true;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this).add("table", table.tableName()).add("limit", limit).toString();
    }
}