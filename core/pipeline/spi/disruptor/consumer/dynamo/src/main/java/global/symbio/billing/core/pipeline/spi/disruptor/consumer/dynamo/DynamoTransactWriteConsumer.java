package global.symbio.billing.core.pipeline.spi.disruptor.consumer.dynamo;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.pipeline.spi.disruptor.event.batch.BatchConsumer;
import jakarta.annotation.Nonnull;
import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.model.TransactWriteItemsEnhancedRequest;

import java.util.Collection;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public class DynamoTransactWriteConsumer<T, K, E> implements BatchConsumer<T> {

    // Limitation as per: https://docs.aws.amazon.com/amazondynamodb/latest/APIReference/API_TransactWriteItems.html
    public static final int DEFAULT_MAX_BATCH_SIZE = 100;
    public static final int DEVELOPMENT_MAX_BATCH_SIZE = 25;

    //TODO: accommodate for TTL field/mechanism...

    @Nonnull
    private final DynamoDbEnhancedClient dynamo;

    @Nonnull
    private final DynamoDbTable<E> table;

    @Nonnull
    private final Function<T, K> key;

    @Nonnull
    private final Function<T, E> encoder;

    private final int limit;

    public DynamoTransactWriteConsumer(@Nonnull DynamoDbEnhancedClient dynamo, @Nonnull DynamoDbTable<E> table, @Nonnull Function<T, K> key, @Nonnull Function<T, E> encoder) {
        this(dynamo, table, key, encoder, DEFAULT_MAX_BATCH_SIZE);
    }

    public DynamoTransactWriteConsumer(@Nonnull DynamoDbEnhancedClient dynamo, @Nonnull DynamoDbTable<E> table, @Nonnull Function<T, K> key, @Nonnull Function<T, E> encoder, int limit) {
        this.dynamo = Objects.requireNonNull(dynamo, "dynamo");
        this.table = Objects.requireNonNull(table, "table");
        this.key = Objects.requireNonNull(key, "key");
        this.encoder = Objects.requireNonNull(encoder, "encoder");
        this.limit = limit;
    }

    @Override
    public void consume(@Nonnull Collection<T> elements) {
        write(elements);
    }

    private void write(@Nonnull Collection<T> elements) {
        final var items = elements.stream().collect(Collectors.toMap(key, encoder, (latest, _) -> latest));
        if (items.size() != elements.size()) {
            log.warn("Duplicate items detected in batch. Expected: {}, De-duplicated: {}.", elements.size(), items.size());
        }
        final var builder = TransactWriteItemsEnhancedRequest.builder();
        for (final var item : items.values()) {
            builder.addPutItem(table, item);
        }
        dynamo.transactWriteItems(builder.build());
    }

    @Override
    public boolean isConsumable(@Nonnull Collection<T> elements) {
        return limit <= elements.size();
    }

    @Override
    public boolean isRetryable(@Nonnull Throwable cause) {
        //TODO: determine which exceptions are retryable
        return true;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this).add("table", table.tableName()).add("limit", limit).toString();
    }
}
