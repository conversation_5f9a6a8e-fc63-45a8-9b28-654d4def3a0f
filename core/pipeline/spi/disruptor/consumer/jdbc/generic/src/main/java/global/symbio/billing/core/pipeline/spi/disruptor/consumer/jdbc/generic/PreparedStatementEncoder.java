package global.symbio.billing.core.pipeline.spi.disruptor.consumer.jdbc.generic;

import jakarta.annotation.Nonnull;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.UUID;

public interface PreparedStatementEncoder<T> {

    void encode(@Nonnull T entity, @Nonnull PreparedStatement statement) throws SQLException;

    UUID getIdentifier(@Nonnull T entity);

    default String entityToString(@Nonnull T entity) {
        return entity.toString();
    }
}
