package global.symbio.billing.core.pipeline.spi.disruptor.consumer.jdbc.generic;

import global.symbio.billing.core.pipeline.spi.disruptor.consumer.jdbc.TransactionalBiConsumer;
import global.symbio.billing.core.util.metrics.MetricsUtil;
import io.micronaut.transaction.SynchronousTransactionManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.List;
import java.util.UUID;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestGenericPreparedStatementBatchConsumer {

    private static final String QUERY = "INSERT";

    @Mock
    private SynchronousTransactionManager<Connection> transaction;

    @Mock
    private DataSource datasource;

    @Mock
    private PreparedStatementEncoder<String> encoder;

    @Mock
    private GeneratedKeysCallback<String> callback;

    @Mock
    private TransactionalBiConsumer<String> txStarted;

    @Mock
    private TransactionalBiConsumer<String> txFinished;

    private GenericPreparedStatementBatchConsumer<String> consumer;

    @BeforeEach
    public void setup() {
        consumer = new GenericPreparedStatementBatchConsumer<>(transaction, datasource, QUERY, encoder, null, null, null);
    }

    @Test
    @DisplayName("GenericPreparedStatementBatchConsumer::new rejects null constructor arguments")
    public void generic_prepared_statement_batch_consumer_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new GenericPreparedStatementBatchConsumer<>(null, datasource, QUERY, encoder, callback, txStarted, txFinished));
        assertThrows(NullPointerException.class, () -> new GenericPreparedStatementBatchConsumer<>(transaction, null, QUERY, encoder, callback, txStarted, txFinished));
        assertThrows(NullPointerException.class, () -> new GenericPreparedStatementBatchConsumer<>(transaction, datasource, null, encoder, callback, txStarted, txFinished));
        assertThrows(NullPointerException.class, () -> new GenericPreparedStatementBatchConsumer<>(transaction, datasource, QUERY, null, callback, txStarted, txFinished));
        assertDoesNotThrow(() -> new GenericPreparedStatementBatchConsumer<>(transaction, datasource, QUERY, encoder, null, txStarted, txFinished));
        assertDoesNotThrow(() -> new GenericPreparedStatementBatchConsumer<>(transaction, datasource, QUERY, encoder, callback, null, txFinished));
        assertDoesNotThrow(() -> new GenericPreparedStatementBatchConsumer<>(transaction, datasource, QUERY, encoder, callback, txStarted, null));
    }

    @Test
    @DisplayName("GenericPreparedStatementBatchConsumer::consume verify calls")
    public void generic_prepared_statement_batch_consumer_consume_verify_calls() throws Exception {
        final var connection = mock(Connection.class);
        final var statement = mock(PreparedStatement.class);
        final var statuses = new int[]{0,1};
        when(connection.prepareStatement(anyString())).thenReturn(statement);
        when(statement.executeBatch()).thenReturn(statuses);
        final var elements = List.of("foo", "bar");
        when(encoder.getIdentifier(anyString())).thenReturn(UUID.randomUUID());
        when(encoder.entityToString(anyString())).thenReturn(elements.getFirst());

        try (final var utilMockedStatic = mockStatic(MetricsUtil.class)) {
            assertDoesNotThrow(() -> consumer.consume(connection, elements));
            verify(encoder, times(2)).encode(anyString(), any(PreparedStatement.class));
            verify(encoder, times(2)).getIdentifier(anyString());
            verify(encoder, times(2)).entityToString(anyString());

            //only once since we only emit if status = 0
            utilMockedStatic.verify(() -> MetricsUtil.emitDuplicateTransaction(any(UUID.class), anyString()), times(1));
        }
    }

    @Test
    @DisplayName("GenericPreparedStatementBatchConsumer::consume verify calls non-insert query")
    public void generic_prepared_statement_batch_consumer_consume_verify_calls_non_insert_query() throws Exception {
        consumer = new GenericPreparedStatementBatchConsumer<>(transaction, datasource, "SELECT 1", encoder, null, null, null);
        final var connection = mock(Connection.class);
        final var statement = mock(PreparedStatement.class);
        final var statuses = new int[]{0,1};
        when(connection.prepareStatement(anyString())).thenReturn(statement);
        when(statement.executeBatch()).thenReturn(statuses);
        final var elements = List.of("foo", "bar");
        when(encoder.getIdentifier(anyString())).thenReturn(UUID.randomUUID());
        when(encoder.entityToString(anyString())).thenReturn(elements.getFirst());

        try (final var utilMockedStatic = mockStatic(MetricsUtil.class)) {
            assertDoesNotThrow(() -> consumer.consume(connection, elements));
            verify(encoder, times(2)).encode(anyString(), any(PreparedStatement.class));
            verify(encoder, times(2)).getIdentifier(anyString());
            verify(encoder, times(2)).entityToString(anyString());

            //never since query is SELECT
            utilMockedStatic.verify(() -> MetricsUtil.emitDuplicateTransaction(any(UUID.class), anyString()), never());
        }
    }

    @Test
    @DisplayName("GenericPreparedStatementBatchConsumer::consume verify calls for empty collection")
    public void generic_prepared_statement_batch_consumer_consume_verify_calls_for_empty_collection() throws Exception {
        final var connection = mock(Connection.class);
        final var statement = mock(PreparedStatement.class);
        final var statuses = new int[]{};
        when(connection.prepareStatement(anyString())).thenReturn(statement);
        when(statement.executeBatch()).thenReturn(statuses);

        try (final var utilMockedStatic = mockStatic(MetricsUtil.class)) {
            assertDoesNotThrow(() -> consumer.consume(connection, List.of()));
            verify(encoder, never()).encode(anyString(), any(PreparedStatement.class));
            verify(encoder, never()).getIdentifier(anyString());
            verify(encoder, never()).entityToString(anyString());

            utilMockedStatic.verify(() -> MetricsUtil.emitDuplicateTransaction(any(UUID.class), anyString()), never());
        }
    }

    @ParameterizedTest
    @MethodSource("scenarios")
    @DisplayName("GenericPreparedStatementBatchConsumer::consume verify calls callback not null")
    public void generic_prepared_statement_batch_consumer_consume_verify_calls_callback_not_null(final boolean hasKeys, final boolean next, final boolean invoke) throws Exception {
        consumer = new GenericPreparedStatementBatchConsumer<>(transaction, datasource, QUERY, encoder, callback, null, null);
        final var connection = mock(Connection.class);
        final var statement = mock(PreparedStatement.class);
        final var statuses = new int[]{0,1};
        when(connection.prepareStatement(anyString(), anyInt())).thenReturn(statement);
        when(statement.executeBatch()).thenReturn(statuses);
        final var elements = List.of("foo", "bar");
        when(encoder.getIdentifier(anyString())).thenReturn(UUID.randomUUID());
        when(encoder.entityToString(anyString())).thenReturn(elements.getFirst());

        final var resultSet = mock(ResultSet.class);
        when(statement.getGeneratedKeys()).thenReturn(resultSet);
        when(callback.hasKeys(anyInt())).thenReturn(hasKeys);
        lenient().when(resultSet.next()).thenReturn(next);

        try (final var utilMockedStatic = mockStatic(MetricsUtil.class)) {
            assertDoesNotThrow(() -> consumer.consume(connection, elements));
            verify(encoder, times(2)).encode(anyString(), any(PreparedStatement.class));
            verify(encoder, times(2)).getIdentifier(anyString());
            verify(encoder, times(2)).entityToString(anyString());
            verify(callback, invoke ? times(2) : never()).invoke(anyInt(), anyString(), any(ResultSet.class));

            //only once since we only emit if status = 0
            utilMockedStatic.verify(() -> MetricsUtil.emitDuplicateTransaction(any(UUID.class), anyString()), times(1));
        }
    }

    private static Stream<Arguments> scenarios() {
        return Stream.of(
            Arguments.of(true, true, true),
            Arguments.of(false, true, false),
            Arguments.of(true, false, false)
        );
    }
}
