package global.symbio.billing.core.pipeline.spi.disruptor.consumer.jdbc.generic;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.pipeline.spi.disruptor.consumer.jdbc.TransactionalBatchConsumer;
import global.symbio.billing.core.pipeline.spi.disruptor.consumer.jdbc.TransactionalBiConsumer;
import global.symbio.billing.core.util.metrics.MetricsUtil;
import io.micronaut.transaction.SynchronousTransactionManager;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.extern.slf4j.Slf4j;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Objects;
import java.util.UUID;

import static java.sql.Statement.RETURN_GENERATED_KEYS;

@Slf4j
public class GenericPreparedStatementBatchConsumer<T> extends TransactionalBatchConsumer<T> {

    private static final int DEFAULT_MAX_BATCH_SIZE = 512;
    private static final int ROW_NOT_UPDATED = 0;
    private static final String INSERT_QUERY = "INSERT";

    @Nonnull
    private final String query;

    @Nonnull
    private final PreparedStatementEncoder<T> encoder;

    @Nullable
    private final GeneratedKeysCallback<T> callback;

    public GenericPreparedStatementBatchConsumer(@Nonnull SynchronousTransactionManager<Connection> transaction, @Nonnull DataSource datasource, @Nonnull String query, @Nonnull PreparedStatementEncoder<T> encoder, @Nullable GeneratedKeysCallback<T> callback, @Nullable TransactionalBiConsumer<T> txStarted, @Nullable TransactionalBiConsumer<T> txFinished) {
        this(transaction, datasource, query, encoder, callback, txStarted, txFinished, DEFAULT_MAX_BATCH_SIZE);
    }

    public GenericPreparedStatementBatchConsumer(@Nonnull SynchronousTransactionManager<Connection> transaction, @Nonnull DataSource datasource, @Nonnull String query, @Nonnull PreparedStatementEncoder<T> encoder, @Nullable GeneratedKeysCallback<T> callback, @Nullable TransactionalBiConsumer<T> txStarted, @Nullable TransactionalBiConsumer<T> txFinished, @Nullable Integer limit) {
        super(transaction, datasource, txStarted, txFinished, limit);
        this.query = Objects.requireNonNull(query, "query");
        this.encoder = Objects.requireNonNull(encoder, "encoder");
        this.callback = callback;
    }

    @Override
    public void consume(@Nonnull Connection connection, @Nonnull Collection<T> elements) throws Exception {
        final var idList = new ArrayList<UUID>();
        final var detailsList = new ArrayList<String>();
        try (final var statement = prepare(connection)) {
            for (final var element : elements) {
                encoder.encode(element, statement);
                statement.addBatch();
                idList.add(encoder.getIdentifier(element));
                detailsList.add(encoder.entityToString(element));
            }

            final var statuses = statement.executeBatch();

            //check which elements were not inserted. order of statuses array is the same as the order they were added in the batch
            for (int index = 0; index < statuses.length; index++) {
                //currently, all prepared statements are insert statements with ON CONFLICT DO NOTHING
                //use contains instead of startsWith since not all queries start with 'INSERT' (e.g. BridgeEntityTransactionInserterFactory)
                if (statuses[index] == ROW_NOT_UPDATED && query.contains(INSERT_QUERY)) {
                    MetricsUtil.emitDuplicateTransaction(idList.get(index), detailsList.get(index));
                }
            }

            if (callback == null) {
                return;
            }

            try (final var keys = statement.getGeneratedKeys()) {
                final var entities = elements.iterator(); //TODO: iteration order must be consistent...

                for (int index = 0; index < statuses.length && entities.hasNext(); index++) {
                    final var status = statuses[index];
                    final var entity = entities.next();

                    if (!callback.hasKeys(status) || !keys.next()) {
                        log.info("Callback discarded - no keys: status = {}, entity = {}", status, entity);
                        continue;
                    }

                    callback.invoke(status, entity, keys);
                }
            }
        }
    }

    @Nonnull
    private PreparedStatement prepare(@Nonnull Connection connection) throws SQLException {
        if (callback == null) {
            return connection.prepareStatement(query);
        }
        return connection.prepareStatement(query, RETURN_GENERATED_KEYS);
    }

    @Override
    public boolean isRetryable(@Nonnull Throwable cause) {
        //TODO: determine which exceptions are retryable
        return true;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this).add("limit", limit).toString();
    }
}
