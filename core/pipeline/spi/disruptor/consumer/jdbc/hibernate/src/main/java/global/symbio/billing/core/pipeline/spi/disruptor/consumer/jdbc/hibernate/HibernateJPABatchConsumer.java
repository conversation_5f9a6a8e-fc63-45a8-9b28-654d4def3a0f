package global.symbio.billing.core.pipeline.spi.disruptor.consumer.jdbc.hibernate;

import global.symbio.billing.core.pipeline.spi.disruptor.consumer.jdbc.TransactionalBatchConsumer;
import global.symbio.billing.core.pipeline.spi.disruptor.consumer.jdbc.TransactionalBiConsumer;
import io.micronaut.data.repository.CrudRepository;
import io.micronaut.transaction.SynchronousTransactionManager;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import javax.sql.DataSource;
import java.sql.Connection;
import java.util.Collection;
import java.util.Objects;
import java.util.function.Function;

public class HibernateJPABatchConsumer<T, E> extends TransactionalBatchConsumer<T> {

    private static final int DEFAULT_MAX_BATCH_SIZE = 250;

    @Nonnull
    private final CrudRepository<E, ?> repository;

    @Nonnull
    private final Function<T, E> encoder;

    public HibernateJPABatchConsumer(@Nonnull SynchronousTransactionManager<Connection> transaction, @Nonnull DataSource datasource, @Nonnull CrudRepository<E, ?> repository, @Nonnull Function<T, E> encoder, @Nullable TransactionalBiConsumer<T> txStarted, @Nullable TransactionalBiConsumer<T> txFinished) {
        this(transaction, datasource, repository, encoder, txStarted, txFinished, DEFAULT_MAX_BATCH_SIZE);
    }

    public HibernateJPABatchConsumer(@Nonnull SynchronousTransactionManager<Connection> transaction, @Nonnull DataSource datasource, @Nonnull CrudRepository<E, ?> repository, @Nonnull Function<T, E> encoder, @Nullable TransactionalBiConsumer<T> txStarted, @Nullable TransactionalBiConsumer<T> txFinished, @Nullable Integer limit) {
        super(transaction, datasource, txStarted, txFinished, limit);
        this.repository = Objects.requireNonNull(repository, "repository");
        this.encoder = Objects.requireNonNull(encoder, "encoder");
    }

    @Override
    public void consume(@Nonnull Connection connection, @Nonnull Collection<T> elements) throws Exception {
        final var entities = elements.stream().map(encoder).toList();
        repository.saveAll(entities);
    }

    @Override
    public boolean isRetryable(@Nonnull Throwable cause) {
        //TODO: determine which exceptions are retryable
        return true;
    }
}
