package global.symbio.billing.core.pipeline.spi.disruptor.consumer.jdbc.postgres;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.pipeline.spi.disruptor.consumer.jdbc.TransactionalBatchConsumer;
import global.symbio.billing.core.pipeline.spi.disruptor.consumer.jdbc.TransactionalBiConsumer;
import io.micronaut.transaction.SynchronousTransactionManager;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.extern.slf4j.Slf4j;
import org.postgresql.PGConnection;

import javax.sql.DataSource;
import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.util.Collection;
import java.util.Objects;
import java.util.function.Function;

@Slf4j
public class PostgresCopyBatchConsumer<T> extends TransactionalBatchConsumer<T> {

    private static final int DEFAULT_MAX_BATCH_SIZE = 512;
    private static final int DEFAULT_ELEMENT_SIZE_IN_BYTES = 1 << 8;

    @Nonnull
    private final String query;

    @Nonnull
    private final Function<T, String> encoder;

    public PostgresCopyBatchConsumer(@Nonnull SynchronousTransactionManager<Connection> transaction, @Nonnull DataSource datasource, @Nonnull String query, @Nonnull Function<T, String> encoder, @Nullable TransactionalBiConsumer<T> txStarted, @Nullable TransactionalBiConsumer<T> txFinished) {
        this(transaction, datasource, query, encoder, txStarted, txFinished, DEFAULT_MAX_BATCH_SIZE);
    }

    public PostgresCopyBatchConsumer(@Nonnull SynchronousTransactionManager<Connection> transaction, @Nonnull DataSource datasource, @Nonnull String query, @Nonnull Function<T, String> encoder, @Nullable TransactionalBiConsumer<T> txStarted, @Nullable TransactionalBiConsumer<T> txFinished, @Nullable Integer limit) {
        super(transaction, datasource, txStarted, txFinished, limit);
        this.query = Objects.requireNonNull(query, "query");
        this.encoder = Objects.requireNonNull(encoder, "encoder");
    }

    @Override
    public void consume(@Nonnull Connection connection, @Nonnull Collection<T> elements) throws Exception {
        final var pg = connection.unwrap(PGConnection.class);
        try (final var stream = stream(encode(elements))) {
            pg.getCopyAPI().copyIn(query, stream);
        }
    }

    @Nonnull
    private byte[] encode(@Nonnull Collection<T> elements) {
        final var builder = new StringBuilder(DEFAULT_ELEMENT_SIZE_IN_BYTES * elements.size());
        for (final var element : elements) {
            builder.append(encoder.apply(element));
        }
        return builder.toString().getBytes(StandardCharsets.UTF_8);
    }

    @Nonnull
    private InputStream stream(@Nonnull byte[] data) {
        return new BufferedInputStream(new ByteArrayInputStream(data));
    }

    @Override
    public boolean isRetryable(@Nonnull Throwable cause) {
        //TODO: determine which exceptions are retryable
        return true;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this).add("query", query).add("limit", limit).toString();
    }
}
