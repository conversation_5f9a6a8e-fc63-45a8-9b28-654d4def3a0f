package global.symbio.billing.core.pipeline.spi.disruptor.consumer.jdbc;

import global.symbio.billing.core.pipeline.spi.disruptor.event.batch.BatchConsumer;
import io.micronaut.transaction.SynchronousTransactionManager;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.extern.slf4j.Slf4j;

import javax.sql.DataSource;
import java.sql.Connection;
import java.util.Collection;
import java.util.Objects;

@Slf4j
public abstract class TransactionalBatchConsumer<T> implements BatchConsumer<T> {

    @Nonnull
    protected final SynchronousTransactionManager<Connection> transaction;

    @Nonnull
    protected final DataSource datasource;

    @Nullable
    protected final TransactionalBiConsumer<T> txStarted, txFinished;

    @Nullable
    protected final Integer limit;

    public TransactionalBatchConsumer(@Nonnull SynchronousTransactionManager<Connection> transaction, @Nonnull DataSource datasource, @Nullable TransactionalBiConsumer<T> txStarted, @Nullable TransactionalBiConsumer<T> txFinished, @Nullable Integer limit) {
        this.transaction = Objects.requireNonNull(transaction, "transaction");
        this.datasource = Objects.requireNonNull(datasource, "datasource");
        this.txStarted = txStarted;
        this.txFinished = txFinished;
        this.limit = limit;
    }

    public abstract void consume(@Nonnull Connection connection, @Nonnull Collection<T> elements) throws Exception;

    @Override
    public final void consume(@Nonnull Collection<T> elements) {
        transaction.executeWrite(tx -> {
            try (final var connection = datasource.getConnection()) {
                invoke(txStarted, connection, elements);
                consume(connection, elements);
                invoke(txFinished, connection, elements);
            }
            return null;
        });
    }

    @Override
    public boolean isConsumable(@Nonnull Collection<T> elements) {
        return limit != null && limit <= elements.size();
    }

    private void invoke(@Nullable TransactionalBiConsumer<T> hook, @Nonnull Connection connection, @Nonnull Collection<T> elements) throws Exception {
        if (hook != null) {
            hook.accept(connection, elements);
        }
    }
}
