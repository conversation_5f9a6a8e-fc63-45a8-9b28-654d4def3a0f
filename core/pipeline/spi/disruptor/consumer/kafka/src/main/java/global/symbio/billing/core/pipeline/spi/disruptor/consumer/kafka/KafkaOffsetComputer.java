package global.symbio.billing.core.pipeline.spi.disruptor.consumer.kafka;

import com.google.common.base.MoreObjects;
import jakarta.annotation.Nonnull;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.OffsetAndMetadata;
import org.apache.kafka.common.TopicPartition;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

public record KafkaOffsetComputer<T, K, V>(@Nonnull Consumer<K, V> consumer, @Nonnull Function<T, ConsumerRecord<K, V>> record) {

    public KafkaOffsetComputer {
        Objects.requireNonNull(consumer, "consumer"); //TODO: is it necessary to couple this with a consumer..?
        Objects.requireNonNull(record, "record");
    }

    @Nonnull
    public Map<TopicPartition, OffsetAndMetadata> compute(@Nonnull Collection<T> elements) {
        final var records = elements.stream().map(record).toList();
        final var offsets = new HashMap<TopicPartition, Long>();

        for (final var record : records) {
            final var key = new TopicPartition(record.topic(), record.partition());
            final var offset = record.offset();
            offsets.merge(key, offset, Math::max);
        }

        return offsets.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, offset -> new OffsetAndMetadata(offset.getValue() + 1)));
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this).add("consumer", consumer.getClass().getSimpleName()).toString();
    }
}
