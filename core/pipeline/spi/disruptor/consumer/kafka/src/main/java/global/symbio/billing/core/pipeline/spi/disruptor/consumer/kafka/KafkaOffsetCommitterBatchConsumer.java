package global.symbio.billing.core.pipeline.spi.disruptor.consumer.kafka;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.pipeline.spi.disruptor.event.batch.BatchConsumer;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.OffsetCommitCallback;

import java.util.Collection;
import java.util.Objects;

@Slf4j
public class KafkaOffsetCommitterBatchConsumer<T, K, V> implements BatchConsumer<T> {

    private static final OffsetCommitCallback CALLBACK = new LoggingAsyncOffsetCommitCallback();
    private static final boolean SYNCHRONOUS_COMMIT = false;

    @Nonnull
    private final KafkaOffsetComputer<T, K, V> computer;

    private final boolean synchronous;

    @Nullable
    private final Integer limit;

    public KafkaOffsetCommitterBatchConsumer(@Nonnull KafkaOffsetComputer<T, K, V> computer, boolean synchronous, @Nullable Integer limit) {
        this.computer = Objects.requireNonNull(computer, "computer");
        this.synchronous = synchronous;
        this.limit = limit;
    }

    public KafkaOffsetCommitterBatchConsumer(@Nonnull KafkaOffsetComputer<T, K, V> computer) {
        this(computer, SYNCHRONOUS_COMMIT);
    }

    public KafkaOffsetCommitterBatchConsumer(@Nonnull KafkaOffsetComputer<T, K, V> computer, boolean synchronous) {
        this(computer, synchronous, null);
    }

    @Override
    public void consume(@Nonnull Collection<T> elements) {
        commit(elements);
    }

    public void commit(@Nonnull Collection<T> elements) {
        final var offsets = computer.compute(elements);
        final var consumer = computer.consumer();
        log.info("Committing offsets - {}.", offsets.size());
        log.trace("Committing offsets - {}.", offsets);
        synchronized (consumer) {
            if (synchronous) {
                consumer.commitSync(offsets);
            } else {
                consumer.commitAsync(offsets, CALLBACK);
            }
        }
    }

    @Override
    public boolean isConsumable(@Nonnull Collection<T> elements) {
        return limit != null && limit <= elements.size();
    }

    @Override
    public boolean isRetryable(@Nonnull Throwable cause) {
        //TODO: determine which exceptions are retryable
        return true;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this).add("synchronous", synchronous).add("limit", limit).toString();
    }
}
