package global.symbio.billing.core.pipeline.spi.disruptor.consumer.kafka;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.i18n.localisation.LocationAware;
import global.symbio.billing.core.i18n.provider.LocalisedServiceProvider;
import global.symbio.billing.core.pipeline.spi.disruptor.event.batch.BatchConsumer;
import io.micronaut.core.type.Argument;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.OffsetCommitCallback;

import java.util.Collection;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Deprecated(since = "PRGBIL-167: Global kafka is implemented")
//TODO: PRGBIL-167 remove when global kafka is implemented
public class LocationAwareKafkaOffsetCommitterBatchConsumer<T, K, V> implements BatchConsumer<T> {

    private static final OffsetCommitCallback CALLBACK = new LoggingAsyncOffsetCommitCallback();
    private static final boolean SYNCHRONOUS_COMMIT = false;

    @Nonnull
    private final LocalisedServiceProvider provider;

    @Nonnull
    private final Function<T, LocationAware> location;

    @Nonnull
    private final Argument<?>[] arguments;

    private final boolean synchronous;

    @Nullable
    private final Integer limit;

    public LocationAwareKafkaOffsetCommitterBatchConsumer(
        @Nonnull LocalisedServiceProvider provider,
        @Nonnull Function<T, LocationAware> location,
        boolean synchronous,
        @Nullable Integer limit,
        @Nonnull Argument<?>... arguments
    ) {
        this.provider = Objects.requireNonNull(provider, "provider");
        this.location = Objects.requireNonNull(location, "location");
        this.synchronous = synchronous;
        this.limit = limit;
        this.arguments = Objects.requireNonNull(arguments, "arguments");
    }

    public LocationAwareKafkaOffsetCommitterBatchConsumer(
        @Nonnull LocalisedServiceProvider provider,
        @Nonnull Function<T, LocationAware> location,
        @Nonnull Argument<?>... arguments
    ) {
        this(provider, location, SYNCHRONOUS_COMMIT, arguments);
    }

    public LocationAwareKafkaOffsetCommitterBatchConsumer(
        @Nonnull LocalisedServiceProvider provider,
        @Nonnull Function<T, LocationAware> location,
        boolean synchronous,
        @Nonnull Argument<?>... arguments
    ) {
        this(provider, location, synchronous, null, arguments);
    }

    @Override
    public void consume(@Nonnull Collection<T> elements) {
        commit(elements);
    }

    public void commit(@Nonnull Collection<T> elements) {
        final var locations = elements.stream().collect(Collectors.groupingBy(location));
        for (final var entry : locations.entrySet()) {
            final var location = entry.getKey();
            final var records = entry.getValue();
            @SuppressWarnings("unchecked")
            final KafkaOffsetComputer<T, K, V> computer = provider.lookup(KafkaOffsetComputer.class, location, arguments);
            final var offsets = computer.compute(records);
            final var consumer = computer.consumer();
            log.info("Committing offsets - {} - {}.", location, offsets.size());
            log.trace("Committing offsets - {} - {}.", location, offsets);
            synchronized (consumer) {
                if (synchronous) {
                    consumer.commitSync(offsets);
                } else {
                    consumer.commitAsync(offsets, CALLBACK);
                }
            }
        }
    }

    @Override
    public boolean isConsumable(@Nonnull Collection<T> elements) {
        return limit != null && limit <= elements.size();
    }

    @Override
    public boolean isRetryable(@Nonnull Throwable cause) {
        //TODO: determine which exceptions are retryable
        return true;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this).add("provider", provider).add("synchronous", synchronous).add("limit", limit).toString();
    }
}
