package global.symbio.billing.core.pipeline.spi.disruptor.consumer.kafka;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.pipeline.spi.disruptor.event.batch.BatchConsumer;
import io.micronaut.core.util.functional.ThrowingFunction;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerGroupMetadata;
import org.apache.kafka.clients.producer.Producer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.apache.kafka.common.KafkaException;
import org.apache.kafka.common.errors.AuthorizationException;
import org.apache.kafka.common.errors.OutOfOrderSequenceException;
import org.apache.kafka.common.errors.ProducerFencedException;
import org.apache.kafka.common.header.Header;

import java.util.Collection;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.function.Predicate;

@Slf4j
public class KafkaEntityEmitterBatchConsumer<T, K, V> implements BatchConsumer<T> {

    private static final boolean AWAIT_BY_DEFAULT = true;

    @Nonnull
    private final Producer<K, V> producer;

    @Nullable
    private final KafkaOffsetComputer<T, K, V> computer;

    @Nonnull
    private final ThrowingFunction<T, String, Exception> topic;

    @Nonnull
    private final ThrowingFunction<T, Integer, Exception> partition;

    @Nonnull
    private final ThrowingFunction<T, K, Exception> key;

    @Nonnull
    private final ThrowingFunction<T, V, Exception> value;

    @Nonnull
    private final ThrowingFunction<T, Iterable<Header>, Exception> headers;

    @Nonnull
    private final Predicate<T> filter;

    private final boolean transactional, await;

    @Nullable
    private final Integer limit;

    public KafkaEntityEmitterBatchConsumer(@Nonnull Producer<K, V> producer, @Nullable KafkaOffsetComputer<T, K, V> computer, @Nonnull ThrowingFunction<T, String, Exception> topic, @Nonnull ThrowingFunction<T, Integer, Exception> partition, @Nonnull ThrowingFunction<T, K, Exception> key, @Nonnull ThrowingFunction<T, V, Exception> value, @Nonnull ThrowingFunction<T, Iterable<Header>, Exception> headers, boolean transactional, @Nullable Integer limit) {
        this(producer, computer, topic, partition, key, value, headers, (v) -> true, transactional, AWAIT_BY_DEFAULT, limit);
    }

    public KafkaEntityEmitterBatchConsumer(@Nonnull Producer<K, V> producer, @Nullable KafkaOffsetComputer<T, K, V> computer, @Nonnull ThrowingFunction<T, String, Exception> topic, @Nonnull ThrowingFunction<T, Integer, Exception> partition, @Nonnull ThrowingFunction<T, K, Exception> key, @Nonnull ThrowingFunction<T, V, Exception> value, @Nonnull ThrowingFunction<T, Iterable<Header>, Exception> headers, @Nonnull Predicate<T> filter, boolean transactional, @Nullable Integer limit) {
        this(producer, computer, topic, partition, key, value, headers, filter, transactional, AWAIT_BY_DEFAULT, limit);
    }

    public KafkaEntityEmitterBatchConsumer(@Nonnull Producer<K, V> producer, @Nullable KafkaOffsetComputer<T, K, V> computer, @Nonnull ThrowingFunction<T, String, Exception> topic, @Nonnull ThrowingFunction<T, Integer, Exception> partition, @Nonnull ThrowingFunction<T, K, Exception> key, @Nonnull ThrowingFunction<T, V, Exception> value, @Nonnull ThrowingFunction<T, Iterable<Header>, Exception> headers, @Nonnull Predicate<T> filter, boolean transactional, boolean await, @Nullable Integer limit) {
        this.producer = Objects.requireNonNull(producer, "producer");
        this.computer = computer;
        this.topic = Objects.requireNonNull(topic, "topic");
        this.partition = Objects.requireNonNull(partition, "partition");
        this.key = Objects.requireNonNull(key, "key");
        this.value = Objects.requireNonNull(value, "value");
        this.headers = Objects.requireNonNull(headers, "headers");
        this.filter = Objects.requireNonNull(filter, "filter");
        this.transactional = transactional;
        this.await = await;
        this.limit = limit;

        if (transactional) {
            producer.initTransactions(); //TODO: wrong place to do it - similar issue to PipelineEventSink
        }
    }

    @Override
    public void consume(@Nonnull Collection<T> elements) throws Exception {
        try {
            begin();
            final var records = elements.stream().filter(filter).map(this::convert).toList();
            if (elements.size() != records.size()) {
                log.warn("Omitted events from the topic - Total: {}, Sent: {}", elements.size(), records.size());
            }
            if (await) {
                // await the completion of all records being sent to kafka (success or failure)
                final var tasks = records.stream().map(this::emit).toArray(CompletableFuture[]::new);
                final var awaiter = CompletableFuture.allOf(tasks);
                awaiter.get();
            } else {
                // fire-and-forget
                records.forEach(producer::send);
            }
            commit(elements);
        } catch (KafkaException cause) {
            abort(cause);
            throw cause;
        }
    }

    @Nonnull
    private CompletableFuture<RecordMetadata> emit(@Nonnull ProducerRecord<K, V> record) {
        // Create unstarted/uninitialised completable future.
        final var handle = new CompletableFuture<RecordMetadata>();
        // Send producer record with callback that "completes" the above future (successfully or exceptionally)
        // at some later point in time.
        producer.send(record, (metadata, exception) -> {
            if (exception != null) {
                handle.completeExceptionally(exception);
            } else {
                handle.complete(metadata);
            }
        });
        // Return the handle to the completable future immediately.
        return handle;
    }

    private void begin() {
        if (!transactional) {
            return;
        }
        producer.beginTransaction();
    }

    private void commit(@Nonnull Collection<T> elements) {
        if (!transactional) {
            return;
        }
        attachOffsetsToTransaction(elements);
        producer.commitTransaction();
    }

    private void abort(KafkaException cause) {
        if (!transactional || !isRetryable(cause)) {
            return;
        }
        producer.abortTransaction();
    }

    private void attachOffsetsToTransaction(@Nonnull Collection<T> elements) {
        if (computer == null) {
            return;
        }
        final var offsets = computer.compute(elements);
        final var consumer = computer.consumer();
        final ConsumerGroupMetadata metadata;
        synchronized (consumer) {
            metadata = consumer.groupMetadata();
        }
        producer.sendOffsetsToTransaction(offsets, metadata);
    }

    @Nonnull
    private ProducerRecord<K, V> convert(@Nonnull T element) {
        try {
            final var topic = this.topic.apply(element);
            final var partition = this.partition.apply(element);
            final var key = this.key.apply(element);
            final var value = this.value.apply(element);
            final var headers = this.headers.apply(element);
            return new ProducerRecord<>(topic, partition, key, value, headers);
        } catch (Throwable cause) {
            log.error("Unable to create producer record from event: {}", element, cause);
            throw new RuntimeException(cause);
        }
    }

    @Override
    public boolean isConsumable(@Nonnull Collection<T> elements) {
        return limit != null && limit <= elements.size();
    }

    @Override
    public boolean isRetryable(@Nonnull Throwable cause) {
        if (cause instanceof ProducerFencedException || cause instanceof OutOfOrderSequenceException || cause instanceof AuthorizationException) {
            // These exceptions can't be recovered from
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this).add("producer", producer.getClass().getSimpleName()).add("topic", topic).add("transactional", transactional).add("limit", limit).toString();
    }
}
