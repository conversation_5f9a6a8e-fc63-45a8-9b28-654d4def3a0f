package global.symbio.billing.core.pipeline.spi.disruptor.consumer.kafka;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.OffsetAndMetadata;
import org.apache.kafka.clients.consumer.OffsetCommitCallback;
import org.apache.kafka.common.TopicPartition;

import java.util.Map;

@Slf4j
public record LoggingAsyncOffsetCommitCallback() implements OffsetCommitCallback {

    @Override
    public void onComplete(Map<TopicPartition, OffsetAndMetadata> offsets, Exception exception) {
        if (exception == null) {
            log.info("Offsets committed - {}.", offsets.size());
            log.trace("Offsets committed - {}.", offsets);
        } else {
            log.warn("Offsets uncommitted - {}.", offsets, exception);
        }
    }
}
