package global.symbio.billing.core.pipeline.spi.disruptor.consumer.metrics;

import io.micrometer.core.instrument.MeterRegistry;
import jakarta.annotation.Nonnull;

import java.util.Collection;
import java.util.Objects;
import java.util.function.BiConsumer;

public class ItemwiseMetricsAwareBatchConsumer<T> extends MetricsAwareBatchConsumer<T> {

    @Nonnull
    private final BiConsumer<MeterRegistry, T> action;

    public ItemwiseMetricsAwareBatchConsumer(@Nonnull MeterRegistry metrics, @Nonnull BiConsumer<MeterRegistry, T> action) {
        super(metrics);
        this.action = Objects.requireNonNull(action, "action");
    }

    @Override
    public void consume(@Nonnull Collection<T> elements) {
        for (final var element : elements) {
            action.accept(metrics, element);
        }
    }
}
