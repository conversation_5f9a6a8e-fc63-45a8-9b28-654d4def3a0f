package global.symbio.billing.core.pipeline.spi.disruptor.consumer.metrics;

import io.micrometer.core.instrument.MeterRegistry;
import jakarta.annotation.Nonnull;

import java.util.Collection;
import java.util.Objects;
import java.util.function.BiConsumer;

public class GroupwiseMetricsAwareBatchConsumer<T> extends MetricsAwareBatchConsumer<T> {

    @Nonnull
    private final BiConsumer<MeterRegistry, Collection<T>> action;

    public GroupwiseMetricsAwareBatchConsumer(@Nonnull MeterRegistry metrics, @Nonnull BiConsumer<MeterRegistry, Collection<T>> action) {
        super(metrics);
        this.action = Objects.requireNonNull(action, "action");
    }

    @Override
    public void consume(@Nonnull Collection<T> elements) {
        action.accept(metrics, elements);
    }
}
