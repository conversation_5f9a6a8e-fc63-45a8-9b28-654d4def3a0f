package global.symbio.billing.core.pipeline.spi.disruptor.consumer.metrics;

import global.symbio.billing.core.pipeline.spi.disruptor.event.batch.BatchConsumer;
import io.micrometer.core.instrument.MeterRegistry;
import jakarta.annotation.Nonnull;

import java.util.Objects;

public abstract class MetricsAwareBatchConsumer<T> implements BatchConsumer<T> {

    @Nonnull
    protected final MeterRegistry metrics;

    public MetricsAwareBatchConsumer(@Nonnull MeterRegistry metrics) {
        this.metrics = Objects.requireNonNull(metrics, "metrics");
    }
}
