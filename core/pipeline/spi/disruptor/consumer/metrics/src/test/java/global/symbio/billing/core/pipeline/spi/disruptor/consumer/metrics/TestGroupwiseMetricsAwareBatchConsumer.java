package global.symbio.billing.core.pipeline.spi.disruptor.consumer.metrics;

import io.micrometer.core.instrument.MeterRegistry;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collection;
import java.util.function.BiConsumer;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class TestGroupwiseMetricsAwareBatchConsumer {

    @Mock
    private MeterRegistry metrics;

    @Mock
    private BiConsumer<MeterRegistry, Collection<Integer>> action;

    private MetricsAwareBatchConsumer<Integer> consumer;

    @BeforeEach
    public void setup() {
        consumer = new GroupwiseMetricsAwareBatchConsumer<>(metrics, action);
    }

    @Test
    @DisplayName("GroupwiseMetricsAwareBatchConsumer::new rejects null constructor arguments")
    public void groupwise_metrics_aware_batch_consumer_null_constructor() {
        assertThrows(NullPointerException.class, () -> new GroupwiseMetricsAwareBatchConsumer<>(null, action));
        assertThrows(NullPointerException.class, () -> new GroupwiseMetricsAwareBatchConsumer<>(metrics, null));
    }

    @Test
    @DisplayName("GroupwiseMetricsAwareBatchConsumer is subclass of MetricsAwareBatchConsumer")
    public void groupwise_metrics_aware_batch_consumer_is_subclass_of_metrics_aware_batch_consumer() {
        assertTrue(MetricsAwareBatchConsumer.class.isAssignableFrom(GroupwiseMetricsAwareBatchConsumer.class));
    }

    @ParameterizedTest
    @ValueSource(ints = {1, 3, 5})
    @DisplayName("GroupwiseMetricsAwareBatchConsumer::consume invokes action on batch of items")
    public void groupwise_metrics_aware_batch_consumer_invokes_action_on_batch_of_items(final int n) {
        final var elements = Stream.generate(() -> n).limit(n).toList();
        assertDoesNotThrow(() -> consumer.consume(elements));
        verify(action, times(1)).accept(eq(metrics), eq(elements));
    }
}