package global.symbio.billing.core.pipeline.spi.disruptor.event.batch;

import jakarta.annotation.Nonnull;

import java.util.Collection;

@FunctionalInterface
public interface BatchConsumer<T> {

    void consume(@Nonnull Collection<T> elements) throws Exception;

    default boolean isConsumable(@Nonnull Collection<T> elements) {
        return false;
    }

    default boolean isRetryable(@Nonnull Throwable cause) {
        return false;
    }

    default boolean isInterested(@Nonnull T element) {
        return true;
    }
}
