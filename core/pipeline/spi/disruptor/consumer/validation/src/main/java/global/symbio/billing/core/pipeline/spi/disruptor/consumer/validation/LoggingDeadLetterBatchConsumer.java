package global.symbio.billing.core.pipeline.spi.disruptor.consumer.validation;

import global.symbio.billing.core.pipeline.spi.disruptor.event.batch.BatchConsumer;
import global.symbio.billing.core.validation.Validatable;
import jakarta.annotation.Nonnull;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.CloseableThreadContext;

import java.util.Collection;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

@Slf4j
public record LoggingDeadLetterBatchConsumer<T>() implements BatchConsumer<Validatable<T>> {

    private static final LoggingDeadLetterBatchConsumer<?> INSTANCE = new LoggingDeadLetterBatchConsumer<>();

    @Override
    public void consume(@Nonnull Collection<Validatable<T>> elements) {
        if (log.isTraceEnabled()) {
            for (final var element : elements) {
                log.trace("Dead letter - {}. Cause - {}.", element.element(), element.cause().getMessage());
            }
        } else if (log.isInfoEnabled()) {
            log.info("Dead letters - {}.", elements.size());
        }

        final var key = UUID.randomUUID().toString();
        final var count = elements.size();
        final var mdc = Map.of("dlqId", key, "dlqCount", Objects.toString(count));
        try (final var context = CloseableThreadContext.putAll(mdc)) {
            log.info("Dead letter metric - id: {}, count: {}", key, count);
        }

    }

    @SuppressWarnings("unchecked")
    public static <T> LoggingDeadLetterBatchConsumer<T> getInstance() {
        return (LoggingDeadLetterBatchConsumer<T>) INSTANCE;
    }
}
