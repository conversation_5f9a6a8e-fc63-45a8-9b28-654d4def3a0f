package global.symbio.billing.core.pipeline.spi.disruptor.consumer.validation;

import global.symbio.billing.core.pipeline.api.sink.EventSink;
import global.symbio.billing.core.pipeline.spi.disruptor.event.batch.BatchConsumer;
import global.symbio.billing.core.validation.Validatable;
import jakarta.annotation.Nonnull;
import lombok.extern.slf4j.Slf4j;

import java.util.Collection;
import java.util.Objects;
import java.util.function.Predicate;

@Slf4j
public record TerminalValidatableBatchConsumer<T>(@Nonnull EventSink<Validatable<T>> accepted, @Nonnull EventSink<Validatable<T>> rejected) implements BatchConsumer<Validatable<T>> {

    public TerminalValidatableBatchConsumer {
        Objects.requireNonNull(accepted, "accepted");
        Objects.requireNonNull(rejected, "rejected");
    }

    @Override
    public void consume(@Nonnull Collection<Validatable<T>> elements) {
        handle(elements, Validatable::valid, accepted);
        handle(elements, Validatable::invalid, rejected);
    }

    private void handle(@Nonnull Collection<Validatable<T>> elements, @Nonnull Predicate<Validatable<T>> condition, @Nonnull EventSink<Validatable<T>> sink) {
        try {
            transfer(elements, condition, sink);
        } catch (Throwable cause) {
            log.error("Exception transferring elements to {}.", sink, cause);
        }
    }

    private void transfer(@Nonnull Collection<Validatable<T>> elements, @Nonnull Predicate<Validatable<T>> condition, @Nonnull EventSink<Validatable<T>> sink) throws Exception {
        final var events = elements.stream().filter(condition);
        sink.consume(events);
    }
}
