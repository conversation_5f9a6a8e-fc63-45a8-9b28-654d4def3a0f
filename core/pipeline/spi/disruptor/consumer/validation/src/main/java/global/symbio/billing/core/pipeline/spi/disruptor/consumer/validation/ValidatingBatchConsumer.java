package global.symbio.billing.core.pipeline.spi.disruptor.consumer.validation;

import global.symbio.billing.core.pipeline.spi.disruptor.event.batch.BatchConsumer;
import global.symbio.billing.core.validation.Validatable;
import global.symbio.billing.core.validation.Validator;
import jakarta.annotation.Nonnull;
import lombok.extern.slf4j.Slf4j;

import java.util.Collection;
import java.util.Objects;
import java.util.stream.Stream;

@Slf4j
public record ValidatingBatchConsumer<T>(boolean parallel, @Nonnull Validator<T> validator) implements BatchConsumer<Validatable<T>> {

    public ValidatingBatchConsumer {
        Objects.requireNonNull(validator, "validator");
    }

    public ValidatingBatchConsumer(@Nonnull Validator<T> validator) {
        this(validator.isShareable(), validator);
    }

    @Override
    public void consume(@Nonnull Collection<Validatable<T>> elements) {
        validate(elements);
    }

    private void validate(@Nonnull Collection<Validatable<T>> elements) {
        stream(elements).filter(Validatable::valid).forEach(validator::validate);
    }

    @Nonnull
    private Stream<Validatable<T>> stream(@Nonnull Collection<Validatable<T>> elements) {
        return parallel ? elements.parallelStream() : elements.stream();
    }
}