package global.symbio.billing.core.pipeline.spi.disruptor.consumer.validation;

import global.symbio.billing.core.annotation.Stateless;
import global.symbio.billing.core.exception.sdk.BillingIgnoredInputException;
import global.symbio.billing.core.kafka.api.EnrichedKafkaTransactionEvent;
import global.symbio.billing.core.micronaut.environment.CountryPlatformValidationConfiguration;
import global.symbio.billing.core.micronaut.environment.EnvironmentConfiguration;
import global.symbio.billing.core.validation.Validatable;
import global.symbio.billing.core.validation.Validator;
import jakarta.annotation.Nonnull;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

import static global.symbio.billing.core.util.transaction.TransactionDataExtractor.getPlatform;


@Slf4j
@Stateless
public record TransactionCountryValidator(
    @Nonnull EnvironmentConfiguration environment,
    @Nonnull CountryPlatformValidationConfiguration countryPlatformValidationConfiguration
) implements Validator<EnrichedKafkaTransactionEvent> {

    private static final BillingIgnoredInputException DISABLED_COUNTRY_OR_PLATFORM_EXCEPTION = new BillingIgnoredInputException("country is disabled or country-platform combination is disabled");

    public TransactionCountryValidator {
        Objects.requireNonNull(environment, "environment");
        Objects.requireNonNull(countryPlatformValidationConfiguration, "countryPlatformValidationConfig");
    }

    @Override
    public void validate(@Nonnull Validatable<EnrichedKafkaTransactionEvent> component) {
        try {
            final var record = component.element().record();
            final var transaction = record.value().getTransaction();
            final var country = transaction.getCountry();
            final var platform = getPlatform(component.element(), transaction.getCategory());
            validateCountryPlatform(country, platform);
            log.trace("Component validated - {}.", component.element());
        } catch (Throwable cause) {
            final var ignored = cause instanceof BillingIgnoredInputException;
            component.invalidate(cause, ignored);
            if (ignored) {
                log.trace("Component invalidated - {}.", component.element(), component.cause());
            } else {
                log.warn("Component invalidated - {}.", component.element(), component.cause());
            }
        }
    }

    /**
     * Validates that the provided country and platform are enabled.
     *
     * @param country  the country code to validate; must not be null
     * @param platform the platform to validate; must not be null
     * @throws BillingIgnoredInputException if either the country is disabled or the platform is not enabled for that country
     */
    private void validateCountryPlatform(@Nonnull String country, @Nonnull String platform) {
        Objects.requireNonNull(country, "Country must not be null");
        Objects.requireNonNull(platform, "Platform must not be null");
        if (!environment.inCountry(country) || !countryPlatformValidationConfiguration.isEnabled(country, platform)) {
            throw DISABLED_COUNTRY_OR_PLATFORM_EXCEPTION;
        }
    }
}