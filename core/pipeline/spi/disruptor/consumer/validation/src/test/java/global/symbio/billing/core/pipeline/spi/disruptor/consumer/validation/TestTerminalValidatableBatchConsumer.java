package global.symbio.billing.core.pipeline.spi.disruptor.consumer.validation;

import global.symbio.billing.core.pipeline.api.sink.EventSink;
import global.symbio.billing.core.validation.Validatable;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.stream.Stream;

import static java.util.Collections.emptyList;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class TestTerminalValidatableBatchConsumer {

    @Mock
    private EventSink<Validatable<Integer>> accepted;

    @Mock
    private EventSink<Validatable<Integer>> rejected;

    @Captor
    private ArgumentCaptor<Stream<Validatable<Integer>>> captor;

    private TerminalValidatableBatchConsumer<Integer> consumer;

    @BeforeEach
    public void setup() {
        consumer = new TerminalValidatableBatchConsumer<>(accepted, rejected);
    }

    @Test
    @DisplayName("TerminalValidatableBatchConsumer#consume given a valid Validatable accepts the element")
    public void valid_validatable_consumed_by_accepted_consumer() {
        final var validatable = Validatable.create(42);
        assertTrue(validatable.valid());

        assertDoesNotThrow(() -> consumer.consume(List.of(validatable)));

        assertDoesNotThrow(() -> verify(accepted, times(1)).consume(captor.capture()));
        assertEquals(List.of(validatable), captor.getValue().toList());

        assertDoesNotThrow(() -> verify(rejected, times(1)).consume(captor.capture()));
        assertEquals(emptyList(), captor.getValue().toList());
    }

    @Test
    @DisplayName("TerminalValidatableBatchConsumer#consume given an invalid Validatable rejects the element")
    public void invalid_validatable_consumed_by_rejected_consumer() {
        final var validatable = Validatable.create(42);
        validatable.invalidate();
        assertTrue(validatable.invalid());

        assertDoesNotThrow(() -> consumer.consume(List.of(validatable)));

        assertDoesNotThrow(() -> verify(accepted, times(1)).consume(captor.capture()));
        assertEquals(emptyList(), captor.getValue().toList());

        assertDoesNotThrow(() -> verify(rejected, times(1)).consume(captor.capture()));
        assertEquals(List.of(validatable), captor.getValue().toList());
    }
}
