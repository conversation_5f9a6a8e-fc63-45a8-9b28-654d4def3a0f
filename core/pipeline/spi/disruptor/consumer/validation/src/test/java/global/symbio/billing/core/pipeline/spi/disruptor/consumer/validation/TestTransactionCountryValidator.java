package global.symbio.billing.core.pipeline.spi.disruptor.consumer.validation;

import global.symbio.billing.core.exception.sdk.BillingIgnoredInputException;
import global.symbio.billing.core.i18n.ISO3166CountryCodes;
import global.symbio.billing.core.kafka.api.EnrichedKafkaTransactionEvent;
import global.symbio.billing.core.kafka.model.EnrichedKafkaTransaction;
import global.symbio.billing.core.kafka.model.KafkaTransaction;
import global.symbio.billing.core.micronaut.environment.CountryPlatformValidationConfiguration;
import global.symbio.billing.core.micronaut.environment.EnvironmentConfiguration;
import global.symbio.billing.core.util.transaction.TransactionDataExtractor;
import global.symbio.billing.core.validation.Validatable;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.UUID;
import java.util.stream.Stream;

import static global.symbio.billing.core.util.constants.CategoryConstants.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TestTransactionCountryValidator {

    @Mock
    private EnvironmentConfiguration environmentConfiguration;

    @Mock
    private CountryPlatformValidationConfiguration countryPlatformValidationConfiguration;

    @Mock
    private Validatable<EnrichedKafkaTransactionEvent> validatable;

    @Mock
    private EnrichedKafkaTransactionEvent enrichedEvent;

    @Mock
    private ConsumerRecord<UUID, EnrichedKafkaTransaction> consumerRecord;

    @Mock
    private EnrichedKafkaTransaction transaction;

    @Mock
    private KafkaTransaction kafkaTransaction;

    private TransactionCountryValidator validator;

    @BeforeEach
    void setUp() {
        validator = new TransactionCountryValidator(environmentConfiguration, countryPlatformValidationConfiguration);
        when(validatable.element()).thenReturn(enrichedEvent);
        when(enrichedEvent.record()).thenReturn(consumerRecord);
        when(consumerRecord.value()).thenReturn(transaction);
        when(transaction.getTransaction()).thenReturn(kafkaTransaction);
    }

    @ParameterizedTest
    @MethodSource("countryPlatformCombinations")
    @DisplayName("TransactionCountryValidator#validate given country-platform combinations validates appropriately")
    void country_platform_combination_validates_correctly(String country, String platform, Integer categoryId, boolean passed) {
        try (MockedStatic<TransactionDataExtractor> extractorMock = mockStatic(TransactionDataExtractor.class)) {
            // Given
            when(kafkaTransaction.getCountry()).thenReturn(country);
            when(kafkaTransaction.getCategory()).thenReturn(categoryId);
            extractorMock.when(() -> TransactionDataExtractor.getPlatform(enrichedEvent, categoryId)).thenReturn(platform);
            when(environmentConfiguration.inCountry(any())).thenReturn(false);
            lenient().when(environmentConfiguration.inCountry(ISO3166CountryCodes.AUSTRALIA)).thenReturn(true);
            lenient().when(environmentConfiguration.inCountry(ISO3166CountryCodes.NEW_ZEALAND)).thenReturn(true);
            lenient().when(environmentConfiguration.inCountry(ISO3166CountryCodes.SINGAPORE)).thenReturn(true);
            lenient().when(environmentConfiguration.inCountry(ISO3166CountryCodes.MALAYSIA)).thenReturn(true);
            when(countryPlatformValidationConfiguration.isEnabled(any(), any())).thenReturn(false);
            lenient().when(countryPlatformValidationConfiguration.isEnabled(eq(ISO3166CountryCodes.MALAYSIA), any())).thenReturn(true);
            lenient().when(countryPlatformValidationConfiguration.isEnabled(eq(ISO3166CountryCodes.SINGAPORE), any())).thenReturn(true);
            lenient().when(countryPlatformValidationConfiguration.isEnabled(eq(ISO3166CountryCodes.AUSTRALIA), eq("MVPCI"))).thenReturn(true);
            lenient().when(countryPlatformValidationConfiguration.isEnabled(eq(ISO3166CountryCodes.AUSTRALIA), eq("Billing"))).thenReturn(true);
            lenient().when(countryPlatformValidationConfiguration.isEnabled(eq(ISO3166CountryCodes.NEW_ZEALAND), eq("MVPCI"))).thenReturn(true);
            lenient().when(countryPlatformValidationConfiguration.isEnabled(eq(ISO3166CountryCodes.NEW_ZEALAND), eq("Billing"))).thenReturn(true);

            // When
            validator.validate(validatable);

            // Then
            if (passed) {
                verify(validatable, never()).invalidate(any(Throwable.class), anyBoolean());
            } else {
                verify(validatable).invalidate(any(BillingIgnoredInputException.class), eq(true));
            }
        }
    }

    static Stream<Arguments> countryPlatformCombinations() {
        return Stream.of(
            // Invalid country cases
            Arguments.of(ISO3166CountryCodes.TAIWAN, "MVPCI" , CATEGORY_CALL_CHARGE,  false),
            Arguments.of("UK", "MVPCI", CATEGORY_CALL_CHARGE, false),
            // Valid country with valid platform
            Arguments.of(ISO3166CountryCodes.AUSTRALIA, "MVPCI", CATEGORY_CALL_CHARGE, true),
            Arguments.of(ISO3166CountryCodes.AUSTRALIA, "Billing", CATEGORY_PAYMENT, true),
            Arguments.of(ISO3166CountryCodes.NEW_ZEALAND, "MVPCI", CATEGORY_CALL_CHARGE, true),
            Arguments.of(ISO3166CountryCodes.NEW_ZEALAND, "Billing", CATEGORY_PAYMENT_TRANSFER, true),
            // Valid country with invalid platform
            Arguments.of(ISO3166CountryCodes.AUSTRALIA, "MVP21", CATEGORY_CALL_CHARGE, false),
            Arguments.of(ISO3166CountryCodes.NEW_ZEALAND, "MVP04", CATEGORY_CALL_CHARGE, false),
            // Countries with all platforms enabled
            Arguments.of(ISO3166CountryCodes.SINGAPORE, "MVP21", CATEGORY_CALL_CHARGE, true),
            Arguments.of(ISO3166CountryCodes.MALAYSIA, "MVP04", CATEGORY_CALL_CHARGE, true),
            Arguments.of(ISO3166CountryCodes.MALAYSIA, "Billing", CATEGORY_PAYMENT, true),
            Arguments.of(ISO3166CountryCodes.MALAYSIA, "MVP02", CATEGORY_CALL_CHARGE, true),
            Arguments.of(ISO3166CountryCodes.MALAYSIA, "OCTANE", CATEGORY_CALL_CHARGE, true),
            Arguments.of(ISO3166CountryCodes.SINGAPORE, "MVP04", CATEGORY_CALL_CHARGE,true),
            Arguments.of(ISO3166CountryCodes.SINGAPORE, "Billing", CATEGORY_PAYMENT,true),
            Arguments.of(ISO3166CountryCodes.SINGAPORE, "Any platform", CATEGORY_CALL_CHARGE,true),
            Arguments.of(ISO3166CountryCodes.SINGAPORE, "ENT", CATEGORY_CALL_CHARGE,true),
            Arguments.of(ISO3166CountryCodes.SINGAPORE, "MVPAAA", CATEGORY_CALL_CHARGE, true)
        );
    }

    @Test
    @DisplayName("TransactionCountryValidator#validate given RuntimeException is thrown during validation invalidates component")
    void exception_thrown_during_validation_invalidates_component() {
        // Given
        when(kafkaTransaction.getCountry()).thenThrow(new RuntimeException("Unexpected error"));

        // When
        validator.validate(validatable);

        // Then
        verify(validatable).invalidate(any(RuntimeException.class), eq(false));
    }
}