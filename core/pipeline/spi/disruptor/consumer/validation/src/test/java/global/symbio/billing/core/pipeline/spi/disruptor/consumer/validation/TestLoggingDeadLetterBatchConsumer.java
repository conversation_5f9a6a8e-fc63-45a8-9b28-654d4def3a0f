package global.symbio.billing.core.pipeline.spi.disruptor.consumer.validation;

import global.symbio.billing.core.validation.Validatable;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertTrue;

@ExtendWith(MockitoExtension.class)
public class TestLoggingDeadLetterBatchConsumer {

    private LoggingDeadLetterBatchConsumer<Integer> consumer;

    @BeforeEach
    public void setup() {
        consumer = new LoggingDeadLetterBatchConsumer<>();
    }

    @Test
    @DisplayName("LoggingDeadLetterBatchConsumer#consume given a valid Validatable accepts the element")
    public void valid_validatable_consumed_by_accepted_consumer() {
        final var validatable = Validatable.create(42);
        assertTrue(validatable.valid());
        assertDoesNotThrow(() -> consumer.consume(List.of(validatable)));
    }

    @Test
    @DisplayName("LoggingDeadLetterBatchConsumer#consume given an invalid Validatable rejects the element")
    public void invalid_validatable_consumed_by_rejected_consumer() {
        final var validatable = Validatable.create(42);
        validatable.invalidate();
        assertTrue(validatable.invalid());
        assertDoesNotThrow(() -> consumer.consume(List.of(validatable)));
    }
}
