package global.symbio.billing.core.pipeline.api;

import jakarta.annotation.Nonnull;

import java.util.List;

//TODO: documentation
public interface PipelineEngine<T> {

    void receive(@Nonnull T element);

    void receive(@Nonnull List<T> elements);

    default void start() {}

    default void stop() {}

    //TODO: positive, non-zero value indicates that a size is present
    default int capacity() {
        return Integer.MIN_VALUE;
    }
}
