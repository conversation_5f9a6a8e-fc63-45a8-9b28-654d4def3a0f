package global.symbio.billing.core.pipeline.api.request.batch;

import global.symbio.billing.core.pipeline.api.PipelineEngine;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestDirectEngineRequestBatchingStrategy {

    @Mock
    private PipelineEngine<Integer> engine;

    @Captor
    private ArgumentCaptor<List<Integer>> captor;

    private final EngineRequestBatchingStrategy<Integer> strategy = EngineRequestBatchingStrategy.direct();

    @BeforeEach
    public void check() {
        assertInstanceOf(DirectEngineRequestBatchingStrategy.class, strategy);
    }

    @ParameterizedTest(name = "{index} DirectEngineRequestBatchingStrategy invokes PipelineEngine.receive(List<T>) once for a list of size {0}")
    @ValueSource(ints = {0, 1, 2, 3, 5, 10, 100, 1000})
    @DisplayName("DirectEngineRequestBatchingStrategy invokes PipelineEngine.receive(List<T>) once for a list of any size")
    public void direct_engine_request_batching_contract(int size) {
        final var list = IntStream.range(0, size).boxed().toList();
        strategy.transfer(list, engine);
        verify(engine, never()).receive(anyInt());
        verify(engine, times(1)).receive(captor.capture());
        assertEquals(list, captor.getValue());
    }
}
