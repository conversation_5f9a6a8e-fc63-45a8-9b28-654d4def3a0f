package global.symbio.billing.core.pipeline.api.request.handler;

import global.symbio.billing.core.pipeline.api.PipelineEngine;
import global.symbio.billing.core.pipeline.api.request.Request;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestMultipleItemRequestHandler {

    @Mock
    private PipelineEngine<Integer> engine;

    @Captor
    private ArgumentCaptor<List<Integer>> captor;
    private final Request<Integer> single = Request.of(42);
    private final Request<Integer> multiple = Request.of(List.of(42, 69, 420, 1337));
    private final RequestHandler<Integer> handler = MultipleItemRequestHandler.create();

    @BeforeEach
    public void check() {
        assertInstanceOf(MultipleItemRequestHandler.class, handler);
    }

    @Test
    @DisplayName("MultipleItemRequestHandler receiving a SingleItemRequest throws an exception and does not invoke any PipelineEngine methods")
    public void multiple_item_request_handler_single_item_request_throws_exception() {
        assertThrows(UnhandledRequestException.class, () -> handler.handle(engine, single));
        verify(engine, never()).receive(anyInt());
        verify(engine, never()).receive(anyList());
    }

    @Test
    @DisplayName("MultipleItemRequestHandler receiving a MultipleItemRequest invokes PipelineEngine.receive(List<T>) once")
    public void multiple_item_request_handler_multiple_item_request() {
        assertDoesNotThrow(() -> handler.handle(engine, multiple));
        verify(engine, never()).receive(anyInt());
        verify(engine, times(1)).receive(captor.capture());
        assertEquals(List.of(42, 69, 420, 1337), captor.getValue());
    }
}
