package global.symbio.billing.core.pipeline.api.request.handler;

import global.symbio.billing.core.pipeline.api.PipelineEngine;
import global.symbio.billing.core.pipeline.api.request.Request;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestUnhandledRequestHandler {

    @Mock
    private PipelineEngine<Integer> engine;
    private final Request<Integer> single = Request.of(42);
    private final Request<Integer> multiple = Request.of(List.of(42, 69, 420, 1337));
    private final RequestHandler<Integer> handler = UnhandledRequestHandler.getInstance();

    @BeforeEach
    public void check() {
        assertInstanceOf(UnhandledRequestHandler.class, handler);
    }

    @Test
    @DisplayName("UnhandledRequestHandler receiving any request throws an UnhandledRequestException and does not invoke any pipeline methods")
    public void unhandled_request_handler_any_request_throws_exception() {
        final var requests = List.of(single, multiple);
        for (final var request : requests) {
            final var exception = assertThrows(UnhandledRequestException.class, () -> handler.handle(engine, request));
            verify(engine, never()).receive(anyInt());
            verify(engine, never()).receive(anyList());
            assertEquals(request, exception.getRequest());
        }
    }
}
