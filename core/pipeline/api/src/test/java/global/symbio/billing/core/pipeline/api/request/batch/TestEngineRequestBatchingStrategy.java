package global.symbio.billing.core.pipeline.api.request.batch;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;

public class TestEngineRequestBatchingStrategy {

    @Test
    @DisplayName("EngineRequestBatchingStrategy.direct returns DirectEngineRequestBatchingStrategy singleton")
    public void direct_engine_request_batching_strategy() {
        final var strategy = EngineRequestBatchingStrategy.direct();
        assertInstanceOf(DirectEngineRequestBatchingStrategy.class, strategy);
        assertEquals(DirectEngineRequestBatchingStrategy.getInstance(), strategy);
    }

    @Test
    @DisplayName("EngineRequestBatchingStrategy.partitioned(25) returns PartitionedEngineRequestBatchingStrategy instance with size = 25")
    public void partitioned_engine_request_batching_strategy() {
        final var strategy = EngineRequestBatchingStrategy.partitioned(25);
        assertInstanceOf(PartitionedEngineRequestBatchingStrategy.class, strategy);
        assertEquals(25, ((PartitionedEngineRequestBatchingStrategy<?>) strategy).size());
    }

    @Test
    @DisplayName("EngineRequestBatchingStrategy.sequential returns SequentialEngineRequestBatchingStrategy singleton")
    public void sequential_engine_request_batching_strategy() {
        final var strategy = EngineRequestBatchingStrategy.sequential();
        assertInstanceOf(SequentialEngineRequestBatchingStrategy.class, strategy);
        assertEquals(SequentialEngineRequestBatchingStrategy.getInstance(), strategy);
    }
}
