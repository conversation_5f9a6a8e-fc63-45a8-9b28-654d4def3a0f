package global.symbio.billing.core.pipeline.api;

import global.symbio.billing.core.pipeline.api.request.Request;
import global.symbio.billing.core.pipeline.api.request.router.RequestRouter;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@SuppressWarnings("unchecked")
@ExtendWith(MockitoExtension.class)
public class TestPipeline {

    @Captor
    private ArgumentCaptor<Integer> ints;

    @Captor
    private ArgumentCaptor<List<Integer>> lists;

    @ParameterizedTest(name = "{index} Pipeline.start() invokes PipelineEngine.start() once for pipeline {0}")
    @MethodSource("pipelines")
    @DisplayName("Invoking Pipeline#start invokes PipelineEngine#start once")
    public void pipeline_start_invokes_on_engine(Pipeline<Integer, PipelineEngine<Integer>, RequestRouter<Integer>> pipeline) {
        assertDoesNotThrow(pipeline::start);
        verify(pipeline.engine(), times(1)).start();
    }

    @ParameterizedTest(name = "{index} Pipeline.stop() invokes PipelineEngine.stop() once for pipeline {0}")
    @MethodSource("pipelines")
    @DisplayName("Invoking Pipeline#stop invokes PipelineEngine#stop once")
    public void pipeline_stop_invokes_on_engine(Pipeline<Integer, PipelineEngine<Integer>, RequestRouter<Integer>> pipeline) {
        assertDoesNotThrow(pipeline::stop);
        verify(pipeline.engine(), times(1)).stop();
    }

    @ParameterizedTest(name = "{index} Pipeline.consume() cannot be invoked with null for pipeline {0}")
    @MethodSource("pipelines")
    @DisplayName("Pipeline#consume cannot be invoked with null")
    public void pipeline_consume_null_request(Pipeline<Integer, PipelineEngine<Integer>, RequestRouter<Integer>> pipeline) {
        assertThrows(NullPointerException.class, () -> pipeline.consume(null));
    }

    @ParameterizedTest(name = "{index} Pipeline.consume(SingleItemRequest<T>) invokes PipelineEngine.receive(T) for pipeline {0}")
    @MethodSource("pipelines")
    @DisplayName("Pipeline#consume(SingleItemRequest<T>) invokes PipelineEngine#receive(T) once")
    public void pipeline_consume_e2e_single_item_request(Pipeline<Integer, PipelineEngine<Integer>, RequestRouter<Integer>> pipeline) {
        final var values = List.of(42, 69, 420, 1337);
        var invocations = 0;
        for (final var value : values) {
            final var request = Request.of(value);
            assertDoesNotThrow(() -> pipeline.consume(request));
            verify(pipeline.engine(), times(++invocations)).receive(ints.capture());
            verify(pipeline.engine(), never()).receive(anyList());
            assertEquals(value, ints.getValue());
        }
    }

    @ParameterizedTest(name = "{index} Pipeline.consume(MultipleItemRequest<T>) invokes PipelineEngine.receive(List<T>) for pipeline {0}")
    @MethodSource("pipelines")
    @DisplayName("Pipeline#consume(MultipleItemRequest<T>) invokes PipelineEngine#receive(List<T>) once")
    public void pipeline_consume_e2e_multiple_item_request(Pipeline<Integer, PipelineEngine<Integer>, RequestRouter<Integer>> pipeline) {
        final var request = Request.of(List.of(42, 69, 420, 1337));
        assertDoesNotThrow(() -> pipeline.consume(request));
        verify(pipeline.engine(), never()).receive(anyInt());
        verify(pipeline.engine(), times(1)).receive(lists.capture());
        assertEquals(List.of(42, 69, 420, 1337), lists.getValue());
    }

    private static Stream<Arguments> pipelines() {
        return Stream.of(
                Arguments.of(Pipeline.mono("TestMonoPipeline", mock(PipelineEngine.class), RequestRouter.create())),
                Arguments.of(Pipeline.duo("TestDuoPipeline", mock(PipelineEngine.class), RequestRouter.create())),
                Arguments.of(Pipeline.trio("TestTrioPipeline", mock(PipelineEngine.class), RequestRouter.create()))
        );
    }
}
