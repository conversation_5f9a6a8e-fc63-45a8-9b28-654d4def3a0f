package global.symbio.billing.core.pipeline.api.request.handler;

import global.symbio.billing.core.pipeline.api.PipelineEngine;
import global.symbio.billing.core.pipeline.api.request.Request;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestSingleItemRequestHandler {

    @Mock
    private PipelineEngine<Integer> engine;

    @Captor
    private ArgumentCaptor<Integer> captor;
    private final Request<Integer> single = Request.of(42);
    private final Request<Integer> multiple = Request.of(List.of(42, 69, 420, 1337));
    private final RequestHandler<Integer> handler = SingleItemRequestHandler.create();

    @BeforeEach
    public void check() {
        assertInstanceOf(SingleItemRequestHandler.class, handler);
    }

    @Test
    @DisplayName("SingleItemRequestHandler receiving a SingleItemRequest invokes PipelineEngine.receive(T) once")
    public void single_item_request_handler_single_item_request() {
        assertDoesNotThrow(() -> handler.handle(engine, single));
        verify(engine, times(1)).receive(captor.capture());
        verify(engine, never()).receive(anyList());
        assertEquals(42, captor.getValue());
    }

    @Test
    @DisplayName("SingleItemRequestHandler receiving a MultipleItemRequest throws an exception and does not invoke any PipelineEngine methods")
    public void single_item_request_handler_multiple_item_request_throws_exception() {
        assertThrows(UnhandledRequestException.class, () -> handler.handle(engine, multiple));
        verify(engine, never()).receive(anyInt());
        verify(engine, never()).receive(anyList());
    }
}
