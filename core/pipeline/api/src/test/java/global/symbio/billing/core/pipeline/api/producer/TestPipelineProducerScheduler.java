package global.symbio.billing.core.pipeline.api.producer;

import global.symbio.billing.core.pipeline.api.producer.PipelineProducerScheduler.DelegatedPipelineProducerRunner;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Duration;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestPipelineProducerScheduler {

    @Mock
    private ScheduledExecutorService executor;

    @Mock
    private PipelineProducer<?> producer;

    private PipelineProducerScheduler scheduler;

    @BeforeEach
    public void setup() {
        scheduler = spy(new PipelineProducerScheduler(executor, producer));
    }

    @Test
    @DisplayName("PipelineProducerScheduler::new rejects null constructor arguments")
    public void pipeline_producer_scheduler_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new PipelineProducerScheduler(null, producer));
        assertThrows(NullPointerException.class, () -> new PipelineProducerScheduler(executor, null));
    }

    @Test
    @DisplayName("PipelineProducerScheduler::new rejects null constructor arguments")
    public void delegated_pipeline_producer_runner_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new DelegatedPipelineProducerRunner(null));
    }

    @Test
    @DisplayName("PipelineProducerScheduler::start starts and scheduler pipeline producer")
    public void pipeline_producer_scheduler_start_starts_and_schedules_producer() {
        assertDoesNotThrow(() -> scheduler.start());
        verify(producer, times(1)).start();
        verify(scheduler, times(1)).schedule(eq(producer));
    }

    @Test
    @DisplayName("PipelineProducerScheduler::stop stops pipeline producer")
    public void pipeline_producer_scheduler_stop_stops_producer() {
        assertDoesNotThrow(() -> scheduler.stop());
        verify(producer, times(1)).stop();
    }

    @ParameterizedTest
    @ValueSource(ints = {-1, 0, 1})
    @DisplayName("PipelineProducerScheduler::schedule schedules and executes pipeline producer")
    public void pipeline_producer_scheduler_schedule_schedules_and_executes_producer(final int duration) {
        final var period = duration < 0 ? null : Duration.ofSeconds(duration);
        when(producer.getPeriod()).thenReturn(period);
        assertDoesNotThrow(() -> scheduler.schedule(producer));
        if (period == null || !period.isPositive()) {
            final var runner = new DelegatedPipelineProducerRunner(producer);
            verify(executor, times(1)).submit(eq(runner));
        } else {
            verify(executor, times(1)).scheduleWithFixedDelay(eq(producer), eq(0L), eq(period.toMillis()), eq(TimeUnit.MILLISECONDS));
        }
    }

    @Test
    @DisplayName("DelegatedPipelineProducerRunner::run executes pipeline producer while it is running")
    public void delegated_pipeline_producer_runner_run_executes_pipeline_producer_while_it_is_running() {
        when(producer.running()).thenReturn(true, true, false);
        final var runner = new DelegatedPipelineProducerRunner(producer);
        assertDoesNotThrow(runner::run);
        verify(producer, times(3)).running();
        verify(producer, times(2)).run();
    }
}
