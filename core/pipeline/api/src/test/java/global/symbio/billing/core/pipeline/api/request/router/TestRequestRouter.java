package global.symbio.billing.core.pipeline.api.request.router;

import global.symbio.billing.core.pipeline.api.PipelineEngine;
import global.symbio.billing.core.pipeline.api.request.Request;
import global.symbio.billing.core.pipeline.api.request.handler.MultipleItemRequestHandler;
import global.symbio.billing.core.pipeline.api.request.handler.SingleItemRequestHandler;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestRequestRouter {

    @Mock
    private PipelineEngine<Integer> engine;

    @Captor
    private ArgumentCaptor<Integer> ints;

    @Captor
    private ArgumentCaptor<List<Integer>> lists;

    private final RequestRouter<Integer> router = RequestRouter.create();
    private final Request<Integer> single = Request.of(42);
    private final Request<Integer> multiple = Request.of(List.of(42, 69, 420, 1337));

    @Test
    @DisplayName("RequestRouter.create returns instance of DefaultRequestRouter")
    public void request_router_create_is_default_router() {
        assertInstanceOf(DefaultRequestRouter.class, router);
    }

    @Test
    @DisplayName("RequestRouter.create returns a router that can route SingleItemRequests")
    public void request_router_create_can_route_single_item_requests() {
        final var request = single;
        final var handler = router.route(request);
        assertInstanceOf(SingleItemRequestHandler.class, handler);
        assertDoesNotThrow(() -> handler.handle(engine, request));
        verify(engine, times(1)).receive(ints.capture());
        assertEquals(42, ints.getValue());
        verify(engine, never()).receive(anyList());
    }

    @Test
    @DisplayName("RequestRouter.create returns a router that can route MultipleItemRequests")
    public void request_router_create_can_route_multiple_item_requests() {
        final var request = multiple;
        final var handler = router.route(request);
        assertInstanceOf(MultipleItemRequestHandler.class, handler);
        assertDoesNotThrow(() -> handler.handle(engine, request));
        verify(engine, never()).receive(anyInt());
        verify(engine, times(1)).receive(lists.capture());
        assertEquals(List.of(42, 69, 420, 1337), lists.getValue());
    }
}
