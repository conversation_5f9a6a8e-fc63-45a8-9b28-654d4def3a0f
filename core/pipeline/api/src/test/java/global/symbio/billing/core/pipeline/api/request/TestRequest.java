package global.symbio.billing.core.pipeline.api.request;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

public class TestRequest {

    @Test
    @DisplayName("Request.of(T) invoked on a single item should produce an instance of SingleItemRequest")
    public void request_factory_single_item() {
        final var value = 42;
        final var request = assertInstanceOf(SingleItemRequest.class, Request.of(value));
        final var expected = new SingleItemRequest<>(value);
        assertEquals(expected, request);
        assertEquals(1L, request.size());
    }

    @Test
    @DisplayName("SingleItemRequest disallows null values")
    public void request_factory_single_null_item() {
        final Integer item = null;
        assertThrows(NullPointerException.class, () -> Request.of(item));
    }

    @Test
    @DisplayName("Request.of(List<T>) invoked on a single items should produce an instance of SingleItemRequest")
    public void request_factory_list_with_single_item() {
        final var value = List.of(42);
        final var request = assertInstanceOf(SingleItemRequest.class, Request.of(value));
        final var expected = new SingleItemRequest<>(42);
        assertEquals(expected, request);
        assertEquals(1L, request.size());
    }

    @Test
    @DisplayName("Request.of(List<T>) invoked on multiple items should produce an instance of MultipleItemRequest")
    public void request_factory_list_with_multiple_items() {
        final var value = List.of(42, 69, 420, 1337);
        final var request = assertInstanceOf(MultipleItemRequest.class, Request.of(value));
        final var expected = new MultipleItemRequest<>(value);
        assertEquals(expected, request);
        assertEquals(4L, request.size());
    }

    @Test
    @DisplayName("MultipleItemRequest disallows null values")
    public void request_factory_multiple_null_items() {
        final List<Integer> item = null;
        assertThrows(NullPointerException.class, () -> Request.of(item));
    }
}
