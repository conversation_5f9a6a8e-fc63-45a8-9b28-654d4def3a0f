package global.symbio.billing.core.pipeline.api.producer;

import global.symbio.billing.core.pipeline.api.MonoPipeline;
import global.symbio.billing.core.pipeline.api.request.Request;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.stream.Stream;

import static global.symbio.billing.core.pipeline.api.producer.PipelineProducer.ProducerState.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestPipelineProducer {
    
    @Mock
    private MonoPipeline<String> pipeline;

    private PipelineProducer<String> producer;

    @BeforeEach
    public void setup() {
        producer = Mockito.spy(new PipelineProducer<>(pipeline) {
            @Nonnull
            @Override
            protected Stream<Request<String>> poll() {
                // Override as needed for testing
                return Stream.of(Request.of("abc"));
            }
        });
    }

    @Test
    @DisplayName("PipelineProducer::run invokes PipelineProducer::poll and delegates requests to the pipeline")
    public void pipeline_producer_run_polls_and_processes_pipeline_requests() throws Exception {
        assertDoesNotThrow(() -> producer.run());
        verify(producer, times(1)).poll();
        verify(pipeline, times(1)).consume(eq(Request.of("abc")));
    }

    @Test
    @DisplayName("PipelineProducer::running when state is anything but STOPPED")
    public void pipeline_producer_running_is_true_when_state_is_anything_but_stopped() {
        // created
        assertEquals(CREATED, producer.getState());
        assertTrue(producer.running());
        // started
        assertDoesNotThrow(() -> producer.start());
        assertEquals(STARTED, producer.getState());
        assertTrue(producer.running());
        // stopped
        assertDoesNotThrow(() -> producer.stop());
        assertEquals(STOPPED, producer.getState());
        assertFalse(producer.running());
    }

    @Test
    @DisplayName("PipelineProducer::start and PipelineProducer::stop manage state transitions and pipeline lifecycle")
    public void pipeline_producer_start_stop_state_transitions_and_pipeline_lifecycle() {
        // created
        verifyNoInteractions(producer);
        assertEquals(CREATED, producer.getState());
        assertDoesNotThrow(() -> producer.start());
        verify(producer, times(1)).start();
        verify(producer, times(1)).onStart();
        verify(pipeline, times(1)).start();
        assertEquals(STARTED, producer.getState());
        verifyNoMoreInteractions(pipeline);
        // started
        assertDoesNotThrow(() -> producer.start());
        verify(producer, times(2)).start();
        verify(producer, times(1)).onStart();
        verify(pipeline, times(1)).start();
        assertEquals(STARTED, producer.getState());
        verifyNoMoreInteractions(pipeline);
        // stopped
        assertDoesNotThrow(() -> producer.stop());
        verify(producer, times(1)).stop();
        verify(producer, times(1)).onStop();
        verify(pipeline, times(1)).stop();
        assertEquals(STOPPED, producer.getState());
        verifyNoMoreInteractions(pipeline);
        // stopped
        assertDoesNotThrow(() -> producer.stop());
        verify(producer, times(2)).stop();
        verify(producer, times(1)).onStop();
        verify(pipeline, times(1)).stop();
        assertEquals(STOPPED, producer.getState());
        verifyNoMoreInteractions(pipeline);
    }

    @ParameterizedTest
    @EnumSource(value = PipelineProducer.ProducerState.class)
    @DisplayName("PipelineProducer::transition to same state throws IllegalStateException")
    public void pipeline_producer_transition_to_same_state_throws_illegal_state_exception(final PipelineProducer.ProducerState state) {
        final var cause = assertThrows(IllegalStateException.class, () -> producer.transition(state, state));
        assertEquals("Cannot transition to the same state: " + state + " - " + state + ".", cause.getMessage());
    }
}