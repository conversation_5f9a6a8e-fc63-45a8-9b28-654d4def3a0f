package global.symbio.billing.core.pipeline.api;

import global.symbio.billing.core.pipeline.api.request.MultipleItemRequest;
import global.symbio.billing.core.pipeline.api.request.Request;
import global.symbio.billing.core.pipeline.api.request.SingleItemRequest;
import global.symbio.billing.core.pipeline.api.request.router.RequestRouter;
import jakarta.annotation.Nonnull;
import lombok.Getter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SuppressWarnings("unchecked")
@ExtendWith(MockitoExtension.class)
public class TestPipelineE2E {

    private DuoPipeline<Integer, LifeEngine> pipeline;

    @BeforeEach
    public void setup() {
        pipeline = Pipeline.duo("LifePipeline", new LifeEngine(), RequestRouter.create());
    }

    @Test
    @DisplayName("Invoking Pipeline.start() invokes LifeEngine.start()")
    public void pipeline_start_invokes_on_engine() {
        assertFalse(pipeline.engine().isStarted());
        assertDoesNotThrow(pipeline::start);
        assertTrue(pipeline.engine().isStarted());
    }

    @Test
    @DisplayName("Invoking Pipeline.stop() invokes LifeEngine.stop()")
    public void pipeline_stop_invokes_on_engine() {
        assertFalse(pipeline.engine().isStopped());
        assertDoesNotThrow(pipeline::stop);
        assertTrue(pipeline.engine().isStopped());
    }

    @ParameterizedTest(name = "{index} Pipeline.consume(SingleItemRequest({0})) invokes LifeEngine.receive({0}) and LifeEngine.getLife() == {0}")
    @ValueSource(ints = {42, 69, 420, 1337})
    @DisplayName("Pipeline.consume(SingleItemRequest<Integer>) invokes LifeEngine.receive(Integer)")
    public void life_pipeline_consume_e2e_single_item_request(int value) {
        final var request = assertInstanceOf(SingleItemRequest.class, Request.of(value));
        assertDoesNotThrow(() -> pipeline.consume(request));
        assertEquals(value, pipeline.engine().getLife());
    }

    @Test
    @DisplayName("Pipeline.consume(MultipleItemRequest<Integer>) invokes LifeEngine.receive(List<Integer>)")
    public void life_pipeline_consume_e2e_multiple_item_request() {
        final var request = assertInstanceOf(MultipleItemRequest.class, Request.of(List.of(42, 69, 420, 1337)));
        assertDoesNotThrow(() -> pipeline.consume(request));
        assertEquals(1_868, pipeline.engine().getLife());
    }

    @Getter
    private static class LifeEngine implements PipelineEngine<Integer> {

        private boolean started, stopped;
        private int life;

        @Override
        public void receive(@Nonnull Integer element) {
            life += element;
        }

        @Override
        public void receive(@Nonnull List<Integer> elements) {
            for (final var element : elements) {
                life += element;
            }
        }

        @Override
        public void start() {
            started = true;
        }

        @Override
        public void stop() {
            stopped = true;
        }
    }

}
