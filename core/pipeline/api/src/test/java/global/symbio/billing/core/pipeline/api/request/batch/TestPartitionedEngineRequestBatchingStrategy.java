package global.symbio.billing.core.pipeline.api.request.batch;

import com.google.common.collect.Lists;
import global.symbio.billing.core.pipeline.api.PipelineEngine;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestPartitionedEngineRequestBatchingStrategy {

    @Mock
    private PipelineEngine<Integer> engine;

    @Captor
    private ArgumentCaptor<List<Integer>> captor;

    @ParameterizedTest(name = "{index} PartitionedEngineRequestBatchingStrategy invokes PipelineEngine.receive(List<T>) {2} times for a list of {0} elements partitioned in chunks of {1}")
    @CsvSource({"10,2,5", "10,5,2", "10,3,4", "10,1,10", "100,1,100", "100,2,50", "100,3,34", "100,4,25", "100,10,10", "100,25,4", "1,5,1", "1,10,1", "1,100,1"})
    @DisplayName("PartitionedEngineRequestBatchingStrategy invokes PipelineEngine.receive(List<T>) X times for a list of N elements partitioned in chunks of P")
    public void partitioned_engine_request_batching_contract(int size, int partitions, int invocations) {
        final var strategy = EngineRequestBatchingStrategy.<Integer>partitioned(partitions);
        assertInstanceOf(PartitionedEngineRequestBatchingStrategy.class, strategy);
        assertEquals(partitions, ((PartitionedEngineRequestBatchingStrategy<?>) strategy).size());
        final var list = IntStream.range(0, size).boxed().toList();
        strategy.transfer(list, engine);
        verify(engine, never()).receive(anyInt());
        verify(engine, times(invocations)).receive(captor.capture());
        assertEquals(Lists.partition(list, partitions), captor.getAllValues());
    }
}
