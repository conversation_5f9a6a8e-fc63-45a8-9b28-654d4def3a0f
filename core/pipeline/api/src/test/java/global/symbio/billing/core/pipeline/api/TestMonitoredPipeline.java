package global.symbio.billing.core.pipeline.api;

import global.symbio.billing.core.pipeline.api.request.Request;
import global.symbio.billing.core.pipeline.api.request.router.RequestRouter;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Answers;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestMonitoredPipeline {

    private static final String PIPELINE_NAME = "test-monitored-pipeline";

    @Mock(answer = Answers.RETURNS_DEEP_STUBS)
    private MonoPipeline<Object> pipeline;

    @Mock(answer = Answers.RETURNS_DEEP_STUBS)
    private MeterRegistry registry;

    private Pipeline<Object, PipelineEngine<Object>, RequestRouter<Object>> monitored;

    @BeforeEach
    public void setup() {
        monitored = Pipeline.monitor(PIPELINE_NAME, registry, pipeline);
    }

    @Test
    @DisplayName("MonitoredPipeline::new rejects null parameters")
    public void monitored_pipeline_rejects_null_constructor_parameters() {
        assertThrows(NullPointerException.class, () -> new MonitoredPipeline<>(null, registry, pipeline));
        assertThrows(NullPointerException.class, () -> new MonitoredPipeline<>(PIPELINE_NAME, null, pipeline));
        assertThrows(NullPointerException.class, () -> new MonitoredPipeline<>(PIPELINE_NAME, registry, null));
    }

    @Test
    @DisplayName("Pipeline::monitor returns instance of MonitoredPipeline")
    public void monitored_pipeline_is_instance_of_monitored_pipeline() {
        assertInstanceOf(MonitoredPipeline.class, monitored);
    }

    @Test
    @DisplayName("Pipeline::monitor on instance of MonitoredPipeline throws IllegalStateException")
    public void pipeline_monitor_invoked_on_monitored_pipeline_raises_illegal_state_exception() {
        assertThrows(IllegalStateException.class, () -> Pipeline.monitor(PIPELINE_NAME, registry, monitored));
    }

    @Test
    @DisplayName("MonitoredPipeline::engine invokes Pipeline::engine on the delegated pipeline")
    public void monitored_pipeline_directs_invocation_of_engine_to_delegate_pipeline() {
        final var engine = monitored.engine();
        verify(pipeline, times(1)).engine();
        assertSame(pipeline.engine(), engine);
    }

    @Test
    @DisplayName("MonitoredPipeline::router invokes Pipeline::router on the delegated pipeline")
    public void monitored_pipeline_directs_invocation_of_router_to_delegate_pipeline() {
        final var router = monitored.router();
        verify(pipeline, times(1)).router();
        assertSame(pipeline.router(), router);
    }

    @Test
    @DisplayName("MonitoredPipeline::start invokes Pipeline::start on the delegated pipeline")
    public void monitored_pipeline_directs_invocation_of_start_to_delegate_pipeline() {
        monitored.start();
        verify(pipeline, times(1)).start();
    }

    @Test
    @DisplayName("MonitoredPipeline::stop invokes Pipeline::stop on the delegated pipeline")
    public void monitored_pipeline_directs_invocation_of_stop_to_delegate_pipeline() {
        monitored.stop();
        verify(pipeline, times(1)).stop();
    }

    @Test
    @DisplayName("MonitoredPipeline::consume invokes Pipeline::consume on the delegated pipeline and captures metrics about request/item totals")
    public void monitored_pipeline_directs_invocation_of_consume_to_delegate_pipeline_and_captures_metrics() throws Exception {
        final var request = spy(Request.of(1337));
        final var count = mock(Counter.class, RETURNS_DEEP_STUBS);
        final var items = mock(Counter.class, RETURNS_DEEP_STUBS);
//        final var latency = mock(LongTaskTimer.class, RETURNS_DEEP_STUBS);

        final var type = request.getClass().getSimpleName();

        when(registry.counter("pipeline.invocations", "pipeline.name", PIPELINE_NAME, "pipeline.request.type", type)).thenReturn(count);
        when(registry.counter("pipeline.events", "pipeline.name", PIPELINE_NAME, "pipeline.request.type", type)).thenReturn(items);
//        when(registry.more().longTaskTimer("pipeline.latency", "pipeline.name", PIPELINE_NAME, "pipeline.request.type", type)).thenReturn(latency);

        assertDoesNotThrow(() -> monitored.consume(request));

        verify(request, times(1)).size();
        verify(registry, times(1)).counter("pipeline.invocations", "pipeline.name", PIPELINE_NAME, "pipeline.request.type", type);
        verify(registry, times(1)).counter("pipeline.events", "pipeline.name", PIPELINE_NAME, "pipeline.request.type", type);
//        verify(registry, times(1)).more().longTaskTimer("pipeline.latency", "pipeline.name", PIPELINE_NAME, "pipeline.request.type", type);

        verify(count, times(1)).increment();
        verify(items, times(1)).increment(request.size());
//        verify(latency, times(1)).recordCallable(any(Callable.class));
    }
}
