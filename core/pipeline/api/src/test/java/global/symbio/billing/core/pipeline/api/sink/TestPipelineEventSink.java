package global.symbio.billing.core.pipeline.api.sink;

import global.symbio.billing.core.pipeline.api.Pipeline;
import global.symbio.billing.core.pipeline.api.PipelineEngine;
import global.symbio.billing.core.pipeline.api.request.router.RequestRouter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestPipelineEventSink {

    @Mock
    private PipelineEngine<Integer> engine;

    @Captor
    private ArgumentCaptor<List<Integer>> captor;

    private Pipeline<Integer, PipelineEngine<Integer>, RequestRouter<Integer>> pipeline;
    private EventSink<Integer> sink;

    @BeforeEach
    public void setup() {
        pipeline = Pipeline.mono("test-event-sink", engine, RequestRouter.create());
        sink = new PipelineEventSink<>(pipeline, Function.identity());
    }

    @Test
    @DisplayName("PipelineEventSink::new rejects null constructor arguments")
    public void pipeline_event_sink_rejects_null_pipeline() {
        assertThrows(NullPointerException.class, () -> new PipelineEventSink<>(null, Function.identity()));
        assertThrows(NullPointerException.class, () -> new PipelineEventSink<>(pipeline, null));
    }

    @Test
    @DisplayName("PipelineEventSink does not propagate empty event streams to the underlying pipeline.")
    public void pipeline_event_sink_does_not_propagate_empty_stream() {
        final var events = Stream.<Integer>empty();
        assertDoesNotThrow(() -> sink.consume(events));
        verify(pipeline.engine(), never()).receive(anyInt());
        verify(pipeline.engine(), never()).receive(anyList());
    }

    @Test
    @DisplayName("PipelineEventSink propagates non-empty event streams to the underlying pipeline.")
    public void pipeline_event_sink_propagates_stream_with_values_to_pipeline() {
        final var events = Stream.of(42, 69, 420, 1337);
        assertDoesNotThrow(() -> sink.consume(events));
        verify(pipeline.engine(), never()).receive(anyInt());
        verify(pipeline.engine(), times(1)).receive(captor.capture());
        assertEquals(List.of(42, 69, 420, 1337), captor.getValue());
    }
}
