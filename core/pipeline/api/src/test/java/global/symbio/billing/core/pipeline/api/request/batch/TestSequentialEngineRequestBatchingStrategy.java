package global.symbio.billing.core.pipeline.api.request.batch;

import global.symbio.billing.core.pipeline.api.PipelineEngine;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestSequentialEngineRequestBatchingStrategy {

    @Mock
    private PipelineEngine<Integer> engine;

    @Captor
    private ArgumentCaptor<Integer> captor;

    private final EngineRequestBatchingStrategy<Integer> strategy = EngineRequestBatchingStrategy.sequential();

    @BeforeEach
    public void check() {
        assertInstanceOf(SequentialEngineRequestBatchingStrategy.class, strategy);
    }

    @ParameterizedTest(name = "{index} SequentialEngineRequestBatchingStrategy invokes PipelineEngine.receive(T) {0} times for a list of {0} elements")
    @ValueSource(ints = {0, 1, 2, 3, 5, 10, 100, 1000})
    @DisplayName("SequentialEngineRequestBatchingStrategy invokes PipelineEngine.receive(T) N times for a list of N elements")
    public void sequential_engine_request_batching_contract(int size) {
        final var list = IntStream.range(0, size).boxed().toList();
        strategy.transfer(list, engine);
        verify(engine, times(size)).receive(captor.capture());
        verify(engine, never()).receive(anyList());
        assertEquals(list, captor.getAllValues());
    }
}
