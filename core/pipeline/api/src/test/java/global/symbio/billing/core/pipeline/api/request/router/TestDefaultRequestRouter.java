package global.symbio.billing.core.pipeline.api.request.router;

import global.symbio.billing.core.pipeline.api.PipelineEngine;
import global.symbio.billing.core.pipeline.api.request.Request;
import global.symbio.billing.core.pipeline.api.request.handler.*;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
public class TestDefaultRequestRouter {

    @Mock
    private PipelineEngine<Object> engine;
    private final Request<?> single = Request.of(42);
    private final Request<?> multiple = Request.of(List.of(42, 69, 420, 1337));

    @Test
    @DisplayName("DefaultRouter routes requests to UnhandledRequestHandler when not configured")
    public void default_router_unconfigured_throws_exception() {
        final var router = router(null, null, null);
        final var requests = List.of(single, multiple);
        for (final var request : requests) {
            final var handler = router.route(request);
            assertInstanceOf(UnhandledRequestHandler.class, handler);
            final var exception = assertThrows(UnhandledRequestException.class, () -> handler.handle(engine, request));
            assertEquals(request, exception.getRequest());
        }
    }

    @Test
    @DisplayName("DefaultRouter routes configured requests to expected RequestHandlers when configured")
    public void default_router_configured_okay() {
        final var router = router(SingleItemRequestHandler.create(), MultipleItemRequestHandler.create(), null);
        final var types = List.of(SingleItemRequestHandler.class, MultipleItemRequestHandler.class);
        final var requests = List.of(single, multiple);
        for (int i = 0; i < requests.size(); i++) {
            final var request = requests.get(i);
            final var type = types.get(i);
            final var handler = router.route(request);
            assertInstanceOf(type, handler);
            assertDoesNotThrow(() -> handler.handle(engine, request));
        }
    }

    @Test
    @DisplayName("DefaultRouter routes SingleItemRequest to SingleItemRequestHandler when configured")
    public void default_router_single_single_okay() {
        final var router = router(SingleItemRequestHandler.create(), null, null);
        final var request = single;
        final var handler = router.route(request);
        assertInstanceOf(SingleItemRequestHandler.class, handler);
        assertDoesNotThrow(() -> handler.handle(engine, request));
    }

    @Test
    @DisplayName("DefaultRouter routes SingleItemRequest to UnhandledRequestHandler when not configured")
    public void default_router_single_multiple_throws_exception() {
        final var router = router(SingleItemRequestHandler.create(), null, null);
        final var request = multiple;
        final var handler = router.route(request);
        assertInstanceOf(UnhandledRequestHandler.class, handler);
        final var exception = assertThrows(UnhandledRequestException.class, () -> handler.handle(engine, request));
        assertEquals(request, exception.getRequest());
    }

    @Test
    @DisplayName("DefaultRouter routes MultipleItemRequest to MultipleItemRequestHandler when configured")
    public void default_router_multiple_multiple_okay() {
        final var router = router(null, MultipleItemRequestHandler.create(), null);
        final var request = multiple;
        final var handler = router.route(request);
        assertInstanceOf(MultipleItemRequestHandler.class, handler);
        assertDoesNotThrow(() -> handler.handle(engine, request));
    }

    @Test
    @DisplayName("DefaultRouter routes MultipleItemRequest to UnhandledRequestHandler when not configured")
    public void default_router_multiple_single_throws_exception() {
        final var router = router(null, MultipleItemRequestHandler.create(), null);
        final var request = single;
        final var handler = router.route(request);
        assertInstanceOf(UnhandledRequestHandler.class, handler);
        final var exception = assertThrows(UnhandledRequestException.class, () -> handler.handle(engine, request));
        assertEquals(request, exception.getRequest());
    }

    private <T> RequestRouter<T> router(SingleItemRequestHandler<T> single, MultipleItemRequestHandler<T> multiple, RequestHandler<T> fallback) {
        final var builder = DefaultRequestRouter.<T>builder();
        if (single != null) {
            builder.withSingle(single);
        }
        if (multiple != null) {
            builder.withMultiple(multiple);
        }
        if (fallback != null) {
            builder.withFallback(fallback);
        }
        return builder.build();
    }
}
