package global.symbio.billing.core.pipeline.api.request.router;

import global.symbio.billing.core.pipeline.api.request.Request;
import global.symbio.billing.core.pipeline.api.request.handler.MultipleItemRequestHandler;
import global.symbio.billing.core.pipeline.api.request.handler.RequestHandler;
import global.symbio.billing.core.pipeline.api.request.handler.SingleItemRequestHandler;
import jakarta.annotation.Nonnull;

@FunctionalInterface
public interface RequestRouter<T> {

    @Nonnull
    RequestHandler<T> route(@Nonnull Request<? extends T> request);

    @Nonnull
    static <T> RequestRouter<T> create() {
        return DefaultRequestRouter.<T>builder()
                .withSingle(SingleItemRequestHandler.create())
                .withMultiple(MultipleItemRequestHandler.create())
                .build();
    }
}