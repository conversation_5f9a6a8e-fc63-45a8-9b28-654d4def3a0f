package global.symbio.billing.core.pipeline.api.sink;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.pipeline.api.Pipeline;
import global.symbio.billing.core.pipeline.api.PipelineEngine;
import global.symbio.billing.core.pipeline.api.request.Request;
import global.symbio.billing.core.pipeline.api.request.router.RequestRouter;
import jakarta.annotation.Nonnull;

import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Stream;

public record PipelineEventSink<T, E>(@Nonnull Pipeline<E, PipelineEngine<E>, RequestRouter<E>> pipeline, @Nonnull Function<T, E> encoder) implements EventSink<T> {

    public PipelineEventSink {
        Objects.requireNonNull(pipeline, "pipeline");
        Objects.requireNonNull(encoder, "encoder");
        pipeline.start(); //TODO: starting the pipeline - handle this elsewhere, as this component shouldn't be responsible for the life-cycle of the pipeline
    }

    @Override
    public void consume(@Nonnull Stream<T> events) throws Exception {
        final var elements = events.map(encoder).toList();
        if (!elements.isEmpty()) {
            pipeline.consume(Request.of(elements));
        }
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this).add("pipeline", pipeline).toString();
    }
}
