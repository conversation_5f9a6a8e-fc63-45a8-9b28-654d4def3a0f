package global.symbio.billing.core.pipeline.api;

import global.symbio.billing.core.pipeline.api.request.Request;
import global.symbio.billing.core.pipeline.api.request.router.RequestRouter;
import io.micrometer.core.instrument.MeterRegistry;
import jakarta.annotation.Nonnull;

public sealed interface Pipeline<T, E extends PipelineEngine<T>, R extends RequestRouter<T>> permits MonoPipeline, DuoPipeline, TrioPipeline, MonitoredPipeline {

    @Nonnull
    E engine();

    @Nonnull
    R router();

    default void consume(@Nonnull Request<? extends T> request) throws Exception {
        final var event = router().route(request);
        event.handle(engine(), request);
    }

    default void start() {
        engine().start();
    }

    default void stop() {
        engine().stop();
    }

    @Nonnull
    static <T> MonoPipeline<T> mono(@Nonnull String name, @Nonnull PipelineEngine<T> engine, @Nonnull RequestRouter<T> router) {
        return new MonoPipeline<>(name, engine, router);
    }

    @Nonnull
    static <T, E extends PipelineEngine<T>> DuoPipeline<T, E> duo(@Nonnull String name, @Nonnull E engine, @Nonnull RequestRouter<T> router) {
        return new DuoPipeline<>(name, engine, router);
    }

    @Nonnull
    static <T, E extends PipelineEngine<T>, R extends RequestRouter<T>> TrioPipeline<T, E, R> trio(@Nonnull String name, @Nonnull E engine, @Nonnull R router) {
        return new TrioPipeline<>(name, engine, router);
    }

    @Nonnull
    static <T, E extends PipelineEngine<T>, R extends RequestRouter<T>, P extends Pipeline<T, E, R>> Pipeline<T, E, R> monitor(@Nonnull String name, @Nonnull MeterRegistry registry, @Nonnull P pipeline) {
        if (pipeline instanceof MonitoredPipeline<?,?,?,?> monitored) {
            throw new IllegalStateException("Pipeline " + monitored.getName() + " is already being monitored.");
        }
        return new MonitoredPipeline<>(name, registry, pipeline);
    }
}
