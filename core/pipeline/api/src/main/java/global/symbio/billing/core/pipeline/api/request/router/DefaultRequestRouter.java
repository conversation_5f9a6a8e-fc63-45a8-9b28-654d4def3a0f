package global.symbio.billing.core.pipeline.api.request.router;

import global.symbio.billing.core.pipeline.api.request.MultipleItemRequest;
import global.symbio.billing.core.pipeline.api.request.Request;
import global.symbio.billing.core.pipeline.api.request.SingleItemRequest;
import global.symbio.billing.core.pipeline.api.request.handler.MultipleItemRequestHandler;
import global.symbio.billing.core.pipeline.api.request.handler.RequestHandler;
import global.symbio.billing.core.pipeline.api.request.handler.SingleItemRequestHandler;
import global.symbio.billing.core.pipeline.api.request.handler.UnhandledRequestHandler;
import jakarta.annotation.Nonnull;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public record DefaultRequestRouter<T>(Map<Class<?>, RequestHandler<T>> handlers, RequestHandler<T> fallback) implements RequestRouter<T> {

    //TODO: probably more efficient to just declare 3 variables and use if/switch statements than it is to use a map.
    public DefaultRequestRouter {
        Objects.requireNonNull(handlers, "handlers");
        Objects.requireNonNull(fallback, "fallback");
    }

    @Override @Nonnull
    public RequestHandler<T> route(@Nonnull Request<? extends T> request) {
        return handlers.getOrDefault(request.getClass(), fallback);
    }

    public static <T> Builder<T> builder() {
        return new Builder<>();
    }

    public static class Builder<T> {

        private final Map<Class<?>, RequestHandler<T>> handlers = new HashMap<>();
        private RequestHandler<T> fallback = UnhandledRequestHandler.getInstance();

        public Builder<T> withHandler(@Nonnull Class<?> type, @Nonnull RequestHandler<T> handler) {
            Objects.requireNonNull(type, "type");
            Objects.requireNonNull(handler, "handler");
            handlers.put(type, handler);
            return this;
        }

        public Builder<T> withSingle(@Nonnull SingleItemRequestHandler<T> handler) {
            return withHandler(SingleItemRequest.class, handler);
        }

        public Builder<T> withMultiple(@Nonnull MultipleItemRequestHandler<T> handler) {
            return withHandler(MultipleItemRequest.class, handler);
        }

        public Builder<T> withFallback(@Nonnull RequestHandler<T> fallback) {
            this.fallback = Objects.requireNonNull(fallback, "fallback");
            return this;
        }

        public RequestRouter<T> build() {
            return new DefaultRequestRouter<>(Map.copyOf(handlers), fallback);
        }
    }
}
