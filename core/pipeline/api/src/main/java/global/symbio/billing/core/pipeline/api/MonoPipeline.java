package global.symbio.billing.core.pipeline.api;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.pipeline.api.request.router.RequestRouter;
import io.micronaut.core.naming.Named;
import jakarta.annotation.Nonnull;

import java.util.Objects;

public record MonoPipeline<T>(@Nonnull String name, @Nonnull PipelineEngine<T> engine, @Nonnull RequestRouter<T> router) implements Pipeline<T, PipelineEngine<T>, RequestRouter<T>>, Named {

    public MonoPipeline {
        Objects.requireNonNull(name, "name");
        Objects.requireNonNull(engine, "engine");
        Objects.requireNonNull(router, "router");
    }

    @Override @Nonnull
    public String getName() {
        return name();
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this).add("name", name).add("engine", engine).add("router", router.getClass().getSimpleName()).toString();
    }
}
