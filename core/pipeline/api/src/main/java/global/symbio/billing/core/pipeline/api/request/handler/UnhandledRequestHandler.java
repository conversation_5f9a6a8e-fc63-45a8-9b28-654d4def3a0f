package global.symbio.billing.core.pipeline.api.request.handler;

import global.symbio.billing.core.pipeline.api.PipelineEngine;
import global.symbio.billing.core.pipeline.api.request.Request;
import jakarta.annotation.Nonnull;

public record UnhandledRequestHandler<T>() implements RequestHandler<T> {

    private static final RequestHandler<?> INSTANCE = new UnhandledRequestHandler<>();

    @Override
    public void handle(@Nonnull PipelineEngine<T> engine, @Nonnull Request<? extends T> request) throws UnhandledRequestException {
        unhandled(engine, request);
    }

    @SuppressWarnings("unchecked")
    public static <T> RequestHandler<T> getInstance() {
        return (RequestHandler<T>) INSTANCE;
    }
}
