package global.symbio.billing.core.pipeline.api.request.handler;

import global.symbio.billing.core.pipeline.api.PipelineEngine;
import global.symbio.billing.core.pipeline.api.request.Request;
import jakarta.annotation.Nonnull;

@FunctionalInterface
public interface RequestHandler<T> {

    void handle(@Nonnull PipelineEngine<T> engine, @Nonnull Request<? extends T> request) throws UnhandledRequestException;

    default void unhandled(@Nonnull PipelineEngine<T> engine, @Nonnull Request<? extends T> request) throws UnhandledRequestException {
        throw new UnhandledRequestException(request);
    }
}