package global.symbio.billing.core.pipeline.api.request.batch;

import com.google.common.collect.Lists;
import global.symbio.billing.core.pipeline.api.PipelineEngine;
import jakarta.annotation.Nonnull;

import java.util.List;

public record PartitionedEngineRequestBatchingStrategy<T>(int size) implements EngineRequestBatchingStrategy<T> {

    @Override
    public void transfer(@Nonnull List<T> elements, @Nonnull PipelineEngine<T> engine) {
        final var partitions = Lists.partition(elements, size);
        for (final var partition : partitions) {
            engine.receive(partition);
        }
    }

    @Override
    public String toString() {
        return "PartitionedEngineRequestBatchingStrategy{M:" + size + "}";
    }
}