package global.symbio.billing.core.pipeline.api.request;

import jakarta.annotation.Nonnull;

import java.util.List;

public sealed interface Request<T>
    permits SingleItemRequest, MultipleItemRequest {

    long size();

    @Nonnull
    static <T> Request<T> of(@Nonnull T element) {
        return new SingleItemRequest<>(element);
    }

    @Nonnull
    static <T> Request<T> of(@Nonnull List<T> elements) {
        if (elements.size() == 1) {
            return new SingleItemRequest<>(elements.getFirst());
        }
        return new MultipleItemRequest<>(elements);
    }
}
