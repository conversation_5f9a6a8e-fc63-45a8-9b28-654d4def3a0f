package global.symbio.billing.core.pipeline.api;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.pipeline.api.request.router.RequestRouter;
import io.micronaut.core.naming.Named;
import jakarta.annotation.Nonnull;

import java.util.Objects;

public record TrioPipeline<T, E extends PipelineEngine<T>, R extends RequestRouter<T>>(@Nonnull String name, @Nonnull E engine, @Nonnull R router) implements Pipeline<T, E, R>, Named {

    public TrioPipeline {
        Objects.requireNonNull(name, "name");
        Objects.requireNonNull(engine, "engine");
        Objects.requireNonNull(router, "router");
    }

    @Override @Nonnull
    public String getName() {
        return name();
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this).add("name", name).add("engine", engine).add("router", router.getClass().getSimpleName()).toString();
    }
}
