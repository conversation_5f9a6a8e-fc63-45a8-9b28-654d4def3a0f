package global.symbio.billing.core.pipeline.api.request.handler;

import global.symbio.billing.core.pipeline.api.PipelineEngine;
import global.symbio.billing.core.pipeline.api.request.MultipleItemRequest;
import global.symbio.billing.core.pipeline.api.request.Request;
import global.symbio.billing.core.pipeline.api.request.batch.EngineRequestBatchingStrategy;
import jakarta.annotation.Nonnull;

import java.util.Objects;

@FunctionalInterface
public interface MultipleItemRequestHandler<T> extends RequestHandler<T> {

    void handle(@Nonnull PipelineEngine<T> engine, @Nonnull MultipleItemRequest<T> request);

    @Override @SuppressWarnings({"rawtypes", "unchecked"})
    default void handle(@Nonnull PipelineEngine<T> engine, @Nonnull Request<? extends T> request) throws UnhandledRequestException {
        if (request instanceof MultipleItemRequest req) {
            handle(engine, req);
        } else {
            unhandled(engine, request);
        }
    }

    @Nonnull
    static <T> MultipleItemRequestHandler<T> create() {
        return DefaultMultipleItemRequestHandler.getInstance();
    }

    @Nonnull
    static <T> MultipleItemRequestHandler<T> create(@Nonnull EngineRequestBatchingStrategy<T> batch) {
        return new BatchingMultipleItemRequestHandler<>(batch);
    }

    record DefaultMultipleItemRequestHandler<T>() implements MultipleItemRequestHandler<T> {

        private static final MultipleItemRequestHandler<?> INSTANCE = new DefaultMultipleItemRequestHandler<>();

        @Override
        public void handle(@Nonnull PipelineEngine<T> engine, @Nonnull MultipleItemRequest<T> request) {
            engine.receive(request.elements());
        }

        @SuppressWarnings("unchecked")
        public static <T> MultipleItemRequestHandler<T> getInstance() {
            return (MultipleItemRequestHandler<T>) INSTANCE;
        }
    }

    record BatchingMultipleItemRequestHandler<T>(@Nonnull EngineRequestBatchingStrategy<T> batch) implements MultipleItemRequestHandler<T> {

        public BatchingMultipleItemRequestHandler {
            Objects.requireNonNull(batch, "batch");
        }

        @Override
        public void handle(@Nonnull PipelineEngine<T> engine, @Nonnull MultipleItemRequest<T> request) {
            batch.transfer(request.elements(), engine);
        }
    }
}

