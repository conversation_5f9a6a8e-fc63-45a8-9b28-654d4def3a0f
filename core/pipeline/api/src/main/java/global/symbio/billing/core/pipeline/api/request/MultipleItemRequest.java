package global.symbio.billing.core.pipeline.api.request;

import jakarta.annotation.Nonnull;

import java.util.List;
import java.util.Objects;

public record MultipleItemRequest<T>(
    @Nonnull List<T> elements
) implements Request<T> {

    public MultipleItemRequest {
        Objects.requireNonNull(elements, "elements");
    }

    @Override
    public long size() {
        return elements.size();
    }
}