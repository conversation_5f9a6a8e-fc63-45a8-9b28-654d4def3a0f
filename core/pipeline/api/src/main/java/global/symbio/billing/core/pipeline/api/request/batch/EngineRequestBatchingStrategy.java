package global.symbio.billing.core.pipeline.api.request.batch;

import global.symbio.billing.core.pipeline.api.PipelineEngine;
import jakarta.annotation.Nonnull;

import java.util.List;

@FunctionalInterface
public interface EngineRequestBatchingStrategy<T> {

    void transfer(@Nonnull List<T> elements, @Nonnull PipelineEngine<T> engine);

    static <T> EngineRequestBatchingStrategy<T> direct() {
        return DirectEngineRequestBatchingStrategy.getInstance();
    }

    static <T> EngineRequestBatchingStrategy<T> partitioned(int size) {
        return new PartitionedEngineRequestBatchingStrategy<>(size);
    }

    static <T> EngineRequestBatchingStrategy<T> sequential() {
        return SequentialEngineRequestBatchingStrategy.getInstance();
    }
}
