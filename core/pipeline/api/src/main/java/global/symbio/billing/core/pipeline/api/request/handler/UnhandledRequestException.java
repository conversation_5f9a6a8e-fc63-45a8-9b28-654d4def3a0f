package global.symbio.billing.core.pipeline.api.request.handler;

import global.symbio.billing.core.pipeline.api.request.Request;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.Getter;
import lombok.ToString;

import java.util.Objects;

@Getter @ToString
public class UnhandledRequestException extends Exception {

    private final Request<?> request;

    public UnhandledRequestException(@Nonnull Request<?> request) {
        this(null, request);
    }

    public UnhandledRequestException(@Nullable Throwable cause, @Nonnull Request<?> request) {
        super(cause);
        this.request = Objects.requireNonNull(request, "request");
    }
}
