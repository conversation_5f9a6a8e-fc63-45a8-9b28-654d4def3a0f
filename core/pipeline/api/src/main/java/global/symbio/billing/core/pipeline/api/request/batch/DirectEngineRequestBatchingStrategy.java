package global.symbio.billing.core.pipeline.api.request.batch;

import global.symbio.billing.core.pipeline.api.PipelineEngine;
import jakarta.annotation.Nonnull;

import java.util.List;

public record DirectEngineRequestBatchingStrategy<T>() implements EngineRequestBatchingStrategy<T> {

    private static final EngineRequestBatchingStrategy<?> INSTANCE = new DirectEngineRequestBatchingStrategy<>();

    @Override
    public void transfer(@Nonnull List<T> elements, @Nonnull PipelineEngine<T> engine) {
        engine.receive(elements);
    }

    @Override
    public String toString() {
        return "DirectEngineRequestBatchingStrategy{M:M}";
    }

    @SuppressWarnings("unchecked")
    public static <T> EngineRequestBatchingStrategy<T> getInstance() {
        return (EngineRequestBatchingStrategy<T>) INSTANCE;
    }
}
