package global.symbio.billing.core.pipeline.api.request;

import jakarta.annotation.Nonnull;

import java.util.Objects;

public record SingleItemRequest<T>(
    @Nonnull T element
) implements Request<T> {

    private static final long REQUEST_SIZE = 1L;

    public SingleItemRequest {
        Objects.requireNonNull(element, "element");
    }

    @Override
    public long size() {
        return REQUEST_SIZE;
    }
}