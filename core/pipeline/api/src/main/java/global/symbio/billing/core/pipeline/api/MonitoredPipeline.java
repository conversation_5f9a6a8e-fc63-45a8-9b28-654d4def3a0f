package global.symbio.billing.core.pipeline.api;

import global.symbio.billing.core.pipeline.api.request.Request;
import global.symbio.billing.core.pipeline.api.request.router.RequestRouter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micronaut.core.naming.Named;
import jakarta.annotation.Nonnull;

import java.util.Objects;

public record MonitoredPipeline<T, E extends PipelineEngine<T>, R extends RequestRouter<T>, P extends Pipeline<T, E, R>>(
    @Nonnull String name,
    @Nonnull MeterRegistry registry,
    @Nonnull P pipeline
) implements Pipeline<T, E, R>, Named {

    private static final String METRIC_GROUP_PREFIX = "pipeline.";
    private static final String METRIC_LATENCY = METRIC_GROUP_PREFIX + "latency";
    private static final String METRIC_EVENTS = METRIC_GROUP_PREFIX + "events";
    private static final String METRIC_INVOCATIONS = METRIC_GROUP_PREFIX + "invocations";
    private static final String TAG_PIPELINE_NAME = METRIC_GROUP_PREFIX + "name";
    private static final String TAG_PIPELINE_REQUEST_TYPE = METRIC_GROUP_PREFIX + "request.type";

    public MonitoredPipeline {
        Objects.requireNonNull(registry, "registry");
        Objects.requireNonNull(pipeline, "pipeline");
        Objects.requireNonNull(name, "name");
    }

    @Nonnull
    @Override
    public E engine() {
        return pipeline.engine();
    }

    @Nonnull
    @Override
    public R router() {
        return pipeline.router();
    }

    @Override
    public void consume(@Nonnull final Request<? extends T> request) throws Exception {
        final var type = request.getClass().getSimpleName();
        registry.more().longTaskTimer(METRIC_LATENCY, TAG_PIPELINE_NAME, name, TAG_PIPELINE_REQUEST_TYPE, type).recordCallable(() -> {
            pipeline.consume(request);
            return null;
        });
        registry.counter(METRIC_EVENTS, TAG_PIPELINE_NAME, name, TAG_PIPELINE_REQUEST_TYPE, type).increment(request.size());
        registry.counter(METRIC_INVOCATIONS, TAG_PIPELINE_NAME, name, TAG_PIPELINE_REQUEST_TYPE, type).increment();
    }

    @Override
    public void start() {
        pipeline.start();
    }

    @Override
    public void stop() {
        pipeline.stop();
    }

    @Override
    @Nonnull
    public String getName() {
        return name;
    }
}
