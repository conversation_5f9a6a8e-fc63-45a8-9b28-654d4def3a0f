package global.symbio.billing.core.pipeline.api;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.pipeline.api.request.router.RequestRouter;
import io.micronaut.core.naming.Named;
import jakarta.annotation.Nonnull;

import java.util.Objects;

public record DuoPipeline<T, E extends PipelineEngine<T>>(@Nonnull String name, @Nonnull E engine, @Nonnull RequestRouter<T> router) implements Pipeline<T, E, RequestRouter<T>>, Named {

    public DuoPipeline {
        Objects.requireNonNull(name, "name");
        Objects.requireNonNull(engine, "engine");
        Objects.requireNonNull(router, "router");
    }

    @Override @Nonnull
    public String getName() {
        return name();
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this).add("name", name).add("engine", engine).add("router", router.getClass().getSimpleName()).toString();
    }
}
