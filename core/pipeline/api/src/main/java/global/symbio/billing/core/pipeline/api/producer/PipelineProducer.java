package global.symbio.billing.core.pipeline.api.producer;

import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.pipeline.api.Pipeline;
import global.symbio.billing.core.pipeline.api.PipelineEngine;
import global.symbio.billing.core.pipeline.api.request.Request;
import global.symbio.billing.core.pipeline.api.request.router.RequestRouter;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Stream;

import static global.symbio.billing.core.pipeline.api.producer.PipelineProducer.ProducerState.*;

/**
 * {@link PipelineProducer} provides a mechanism for consuming events from some source
 * and processing them within a {@link Pipeline}.
 *
 * @param <T>
 *     The type of event consumed by the pipeline.
 */
@Slf4j
public abstract class PipelineProducer<T> implements Runnable {

    @Nonnull
    protected final Pipeline<T, PipelineEngine<T>, RequestRouter<T>> pipeline;

    /**
     * The current {@link ProducerState} of this {@link PipelineProducer}.
     */
    @Nonnull
    private final AtomicReference<ProducerState> state = new AtomicReference<>(CREATED);

    /**
     * The periodicity of this {@link PipelineProducer}.
     * If non-null, this {@link PipelineProducer} will run in intervals of the specified {@link Duration}.
     * If null, this {@link PipelineProducer} will run continuously.
     */
    @Getter
    @Nullable
    private final Duration period;

    public PipelineProducer(@Nonnull Pipeline<T, PipelineEngine<T>, RequestRouter<T>> pipeline) {
        this(pipeline, null);
    }

    public PipelineProducer(@Nonnull Pipeline<T, PipelineEngine<T>, RequestRouter<T>> pipeline, @Nullable Duration period) {
        this.pipeline = Objects.requireNonNull(pipeline, "pipeline");
        this.period = period;
    }

    @Override
    public void run() {
        try {
            poll().forEach(request -> {
                try {
                    pipeline.consume(request);
                } catch (Throwable cause) {
                    log.warn("Exception submitting events to pipeline: {}", this, cause);
                }
            });
        } catch (Throwable cause) {
            log.warn("Exception polling events to submit to pipeline: {}", this, cause);
        }
    }

    @Nonnull
    protected abstract Stream<Request<T>> poll();

    /**
     * Life-cycle event invoked after the {@link Pipeline} has been started.
     */
    protected void onStart() {}

    /**
     * Life-cycle event invoked before the {@link Pipeline} is stopped.
     */
    protected void onStop() {}

    public boolean running() {
        return getState() != STOPPED;
    }

    /**
     * Life-cycle method invoked by Micronaut after this bean is instantiated - allocates any necessary resources.
     */
    @PostConstruct
    public void start() {
        if (transition(CREATED, STARTED)) {
            log.info("Starting - {}.", this);
            pipeline.start();
            onStart();
            log.info("Started - {}.", this);
        }
    }

    /**
     * Life-cycle method invoked by Micronaut prior to this bean being destroyed - performs any necessary cleanup of resources.
     */
    @PreDestroy
    public void stop() {
        if (transition(STARTED, STOPPED)) {
            log.info("Stopping - {}.", this);
            try {
                onStop();
            } finally {
                pipeline.stop();
            }
            log.info("Stopped - {}.", this);
        }
    }

    /**
     * Atomically guards life-cycle state changes and transitions.
     *
     * @param from The expected state of this {@link PipelineProducer}.
     * @param to   The state this {@link PipelineProducer} is transitioning to.
     * @return Whether the {@link PipelineProducer} transitioned to the desired state.
     */
    @VisibleForTesting
    boolean transition(ProducerState from, ProducerState to) {
        if (from == to) {
            throw new IllegalStateException("Cannot transition to the same state: " + from + " - " + to + ".");
        }
        return from == state.compareAndExchange(from, to);
    }

    public ProducerState getState() {
        return state.get();
    }

    public enum ProducerState {
        /**
         * The state of a {@link PipelineProducer} that has been instantiated and is ready to consume messages from Kafka.
         */
        CREATED,
        /**
         * The state of a {@link PipelineProducer} that has been started and is consuming messages from Kafka.
         */
        STARTED,
        /**
         * The state of a {@link PipelineProducer} that has been stopped and is no longer consuming messages from Kafka.
         */
        STOPPED
    }
}
