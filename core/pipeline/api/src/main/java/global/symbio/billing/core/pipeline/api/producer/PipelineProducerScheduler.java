package global.symbio.billing.core.pipeline.api.producer;

import com.google.common.annotations.VisibleForTesting;
import io.micronaut.context.annotation.Context;
import io.micronaut.context.annotation.EachBean;
import io.micronaut.context.event.ApplicationEventListener;
import io.micronaut.runtime.event.ApplicationShutdownEvent;
import io.micronaut.scheduling.TaskExecutors;
import jakarta.annotation.Nonnull;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * This Java Bean automatically manages the life-cycle of {@link PipelineProducer} instances within Micronauts
 * dependency injection framework.
 */
@Slf4j
@Context
@EachBean(value = PipelineProducer.class)
public class PipelineProducerScheduler implements ApplicationEventListener<ApplicationShutdownEvent> {

    /**
     * The {@link ExecutorService} for scheduling message consumer workloads on.
     */
    private final ScheduledExecutorService executor;

    /**
     * The {@link PipelineProducer} to whose life-cycle is being managed.
     */
    private final PipelineProducer<?> producer;

    @Inject
    public PipelineProducerScheduler(
        @Named(TaskExecutors.MESSAGE_CONSUMER) ExecutorService executor,
        PipelineProducer<?> producer
    ) {
        this.executor = Objects.requireNonNull((ScheduledExecutorService) executor, "executor");
        this.producer = Objects.requireNonNull(producer, "producer");
    }

    /**
     * Life-cycle method invoked after this bean is instantiated - starts the {@link PipelineProducer} and submits it to the {@link ExecutorService}.
     */
    @PostConstruct
    public void start() {
        log.info("Starting - {}.", producer);
        producer.start();
        schedule(producer);
        log.info("Started - {}.", producer);
    }

    /**
     * Life-cycle method invoked prior to this bean being destroyed - stops the {@link PipelineProducer}.
     */
    @PreDestroy
    public void stop() {
        log.info("Stopping - {}.", producer);
        producer.stop();
        log.info("Stopped - {}.", producer);
    }

    @Override
    public void onApplicationEvent(@Nonnull ApplicationShutdownEvent event) {
        log.info("Application shut down event received: {}", event);
        stop();
        log.info("Application shut down event processed: {}", event);
    }

    @Nonnull
    public Future<?> schedule(@Nonnull PipelineProducer<?> producer) {
        final var period = producer.getPeriod();
        if (period == null || !period.isPositive()) {
            final var runner = new DelegatedPipelineProducerRunner(producer);
            return executor.submit(runner);
        } else {
            return executor.scheduleWithFixedDelay(producer, 0L, period.toMillis(), TimeUnit.MILLISECONDS);
        }
    }

    @VisibleForTesting
    record DelegatedPipelineProducerRunner(
        @Nonnull PipelineProducer<?> producer
    ) implements Runnable {

        /*package*/ DelegatedPipelineProducerRunner {
            Objects.requireNonNull(producer, "producer");
        }

        @Override
        public void run() {
            while (producer.running()) {
                producer.run();
            }
        }
    }
}
