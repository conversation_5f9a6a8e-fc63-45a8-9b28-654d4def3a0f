package global.symbio.billing.core.pipeline.api.request.batch;

import global.symbio.billing.core.pipeline.api.PipelineEngine;
import jakarta.annotation.Nonnull;

import java.util.List;

public record SequentialEngineRequestBatchingStrategy<T>() implements EngineRequestBatchingStrategy<T> {

    private static final EngineRequestBatchingStrategy<?> INSTANCE = new SequentialEngineRequestBatchingStrategy<>();

    @Override
    public void transfer(@Nonnull List<T> elements, @Nonnull PipelineEngine<T> engine) {
        for (final var element : elements) {
            engine.receive(element);
        }
    }

    @Override
    public String toString() {
        return "SequentialEngineRequestBatchingStrategy{M:S}";
    }

    @SuppressWarnings("unchecked")
    public static <T> EngineRequestBatchingStrategy<T> getInstance() {
        return (EngineRequestBatchingStrategy<T>) INSTANCE;
    }
}
