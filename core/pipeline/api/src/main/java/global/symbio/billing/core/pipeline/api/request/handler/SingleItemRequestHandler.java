package global.symbio.billing.core.pipeline.api.request.handler;

import global.symbio.billing.core.pipeline.api.PipelineEngine;
import global.symbio.billing.core.pipeline.api.request.Request;
import global.symbio.billing.core.pipeline.api.request.SingleItemRequest;
import jakarta.annotation.Nonnull;

@FunctionalInterface
public interface SingleItemRequestHandler<T> extends RequestHandler<T> {

    void handle(@Nonnull PipelineEngine<T> engine, @Nonnull SingleItemRequest<T> request);

    @Override @SuppressWarnings({"rawtypes", "unchecked"})
    default void handle(@Nonnull PipelineEngine<T> engine, @Nonnull Request<? extends T> request) throws UnhandledRequestException {
        if (request instanceof SingleItemRequest req) {
            handle(engine, req);
        } else {
            unhandled(engine, request);
        }
    }

    @Nonnull
    static <T> SingleItemRequestHandler<T> create() {
        return DefaultSingleItemRequestHandler.getInstance();
    }

    record DefaultSingleItemRequestHandler<T>() implements SingleItemRequestHandler<T> {

        private static final SingleItemRequestHandler<?> INSTANCE = new DefaultSingleItemRequestHandler<>();

        @Override
        public void handle(@Nonnull PipelineEngine<T> engine, @Nonnull SingleItemRequest<T> request) {
            engine.receive(request.element());
        }

        @SuppressWarnings("unchecked")
        public static <T> SingleItemRequestHandler<T> getInstance() {
            return (SingleItemRequestHandler<T>) INSTANCE;
        }
    }
}
