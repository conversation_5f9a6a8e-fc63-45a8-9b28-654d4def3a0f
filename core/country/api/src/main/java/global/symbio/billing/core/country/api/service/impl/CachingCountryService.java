package global.symbio.billing.core.country.api.service.impl;

import com.github.benmanes.caffeine.cache.AsyncLoadingCache;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.country.api.service.CountryService;
import global.symbio.billing.core.country.persistence.api.Country;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.util.Objects;

@Singleton
@VisibleForTesting
record CachingCountryService(
    @Inject @Nonnull LoadingCache<String, Country> countries
) implements CountryService {

    public CachingCountryService {
        Objects.requireNonNull(countries, "countries");
    }

    @Inject
    public CachingCountryService(@Nonnull AsyncLoadingCache<String, Country> countries) {
        this(countries.synchronous());
    }

    @Nullable
    @Override
    public Country lookup(@Nonnull String code) {
        return countries.get(code);
    }
}
