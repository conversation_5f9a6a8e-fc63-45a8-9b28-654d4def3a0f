package global.symbio.billing.core.country.persistence.api.repository;

import global.symbio.billing.core.country.persistence.api.CountryDataAccessObject;
import io.micronaut.context.annotation.Executable;
import io.micronaut.data.repository.CrudRepository;
import jakarta.annotation.Nonnull;

import java.util.Optional;

public interface CountryRepository<T extends CountryDataAccessObject> extends CrudRepository<T, Integer> {

    @Nonnull
    @Executable
    Optional<T> findByCode(@Nonnull String country);
}