package global.symbio.billing.core.country.persistence.api;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.persistence.api.DataAccessObject;
import jakarta.annotation.Nonnull;

import java.util.Objects;

public abstract class CountryDataAccessObject implements DataAccessObject<Integer, CountryDataAccessObject> {

    @Nonnull
    public abstract String getName();

    @Nonnull
    public abstract CountryDataAccessObject name(@Nonnull String name);

    @Nonnull
    public abstract String getCode();

    @Nonnull
    public abstract CountryDataAccessObject code(@Nonnull String code);

    @Override @Nonnull
    public Country entity() {
        return new Country(this);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof CountryDataAccessObject country)) return false;
        return Objects.equals(getIdentifier(), country.getIdentifier()) && Objects.equals(getName(), country.getName()) && Objects.equals(getCode(), country.getCode());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getIdentifier(), getName(), getCode());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("identifier", getIdentifier())
            .add("name", getName())
            .add("code", getCode())
            .toString();
    }
}
