package global.symbio.billing.core.country.persistence.api;

import global.symbio.billing.core.persistence.api.Entity;
import jakarta.annotation.Nonnull;

public class Country extends Entity<Integer, CountryDataAccessObject> {

    public Country(@Nonnull CountryDataAccessObject data) {
        super(data);
    }

    @Nonnull
    public String getName() {
        return data().getName();
    }

    @Nonnull
    public String getCode() {
        return data().getCode();
    }
}