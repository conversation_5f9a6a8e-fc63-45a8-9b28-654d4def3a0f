package global.symbio.billing.core.country.persistence.spi.jdbc.jpa;

import global.symbio.billing.core.country.persistence.api.repository.CountryRepository;
import global.symbio.billing.core.persistence.api.Repositories;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

public class TestCountryRepository {

    @Test
    @DisplayName("ReadOnlyCountryRepository is connected to read-only datasource")
    public void test_read_only_repository() {
        Assertions.assertTrue(CountryRepository.class.isAssignableFrom(ReadOnlyJPACountryRepository.class));
        Assertions.assertTrue(Repositories.isReadOnlyRepository(ReadOnlyJPACountryRepository.class));
    }

    @Test
    @DisplayName("ReadWriteCountryRepository is connected to read-write datasource")
    public void test_read_write_repository() {
        Assertions.assertTrue(CountryRepository.class.isAssignableFrom(ReadWriteJPACountryRepository.class));
        Assertions.assertTrue(Repositories.isReadWriteRepository(ReadWriteJPACountryRepository.class));
    }
}
