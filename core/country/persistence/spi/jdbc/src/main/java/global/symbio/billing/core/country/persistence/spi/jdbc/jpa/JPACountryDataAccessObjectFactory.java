package global.symbio.billing.core.country.persistence.spi.jdbc.jpa;

import global.symbio.billing.core.country.persistence.api.CountryDataAccessObject;
import global.symbio.billing.core.country.persistence.api.CountryDataAccessObjectFactory;
import io.micronaut.context.annotation.Primary;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

@Primary
@Singleton
public class JPACountryDataAccessObjectFactory implements CountryDataAccessObjectFactory {

    @Nonnull
    @Override
    public Class<? extends CountryDataAccessObject> type() {
        return JPACountryDataAccessObject.class;
    }

    @Nonnull
    @Override
    public CountryDataAccessObject create() {
        return new JPACountryDataAccessObject();
    }
}
