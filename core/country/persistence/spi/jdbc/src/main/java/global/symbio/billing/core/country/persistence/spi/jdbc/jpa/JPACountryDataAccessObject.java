package global.symbio.billing.core.country.persistence.spi.jdbc.jpa;

import global.symbio.billing.core.country.persistence.api.CountryDataAccessObject;
import io.micronaut.core.annotation.Introspected;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Immutable;

@Immutable @Entity
@Table(name = "country")
@Getter @Setter
@Introspected(includedAnnotations = Entity.class)
public class JPACountryDataAccessObject extends CountryDataAccessObject {

    @Id @Nonnull
    @Column(name = "id", unique = true, nullable = false, updatable = false)
    private Integer identifier;

    @Nonnull
    @Column(name = "name", unique = true, nullable = false, length = 128)
    private String name;

    @Nonnull
    @Column(name = "code", unique = true, nullable = false, length = 2)
    private String code;

    @Nonnull
    @Override
    public CountryDataAccessObject identifier(@Nonnull Integer identifier) {
        setIdentifier(identifier);
        return this;
    }

    @Nonnull
    @Override
    public CountryDataAccessObject name(@Nonnull String name) {
        setName(name);
        return this;
    }

    @Nonnull
    @Override
    public CountryDataAccessObject code(@Nonnull String code) {
        setCode(code);
        return this;
    }
}
