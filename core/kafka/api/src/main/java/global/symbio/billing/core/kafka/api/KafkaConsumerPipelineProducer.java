package global.symbio.billing.core.kafka.api;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.pipeline.api.Pipeline;
import global.symbio.billing.core.pipeline.api.PipelineEngine;
import global.symbio.billing.core.pipeline.api.producer.PipelineProducer;
import global.symbio.billing.core.pipeline.api.request.Request;
import global.symbio.billing.core.pipeline.api.request.router.RequestRouter;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.ConsumerRebalanceListener;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.TopicPartition;

import java.time.Duration;
import java.util.Collection;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Stream;

/**
 * {@link KafkaConsumerPipelineProducer} provides a mechanism for uniformly consuming events from a collection of
 * Kafka topics and processing them within a {@link Pipeline}.
 *
 * @param <K> The partition identifier of an event within our Kafka topics.
 * @param <V> The payload of an event within our Kafka topics.
 * @param <E> The transformed/marshalled type of event consumed by the pipeline.
 */
@Slf4j
@Getter(AccessLevel.PACKAGE)
public class KafkaConsumerPipelineProducer<K, V, E> extends PipelineProducer<E> implements ConsumerRebalanceListener {

    /**
     * The shared Kafka {@link Consumer} instance for interacting with our Kafka broker.
     * All access to this variable must be mutually exclusive by means of explicit synchronisation.
     */
    @Nonnull
    private final Consumer<K, V> consumer;

    /**
     * The topics to {@link Consumer#subscribe subscribe} to and {@link Consumer#poll poll} from.
     */
    @Nonnull
    private final Collection<String> topics;

    /**
     * The maximum interval between polling cycles.
     */
    @Nonnull
    private final Duration timeout;

    /**
     * The {@link Function} that transforms a {@link ConsumerRecord} into an object of type {@link E}.
     */
    @Nonnull
    private final Function<ConsumerRecord<K, V>, E> encoder;

    public KafkaConsumerPipelineProducer(@Nonnull Pipeline<E, PipelineEngine<E>, RequestRouter<E>> pipeline, @Nonnull Consumer<K, V> consumer, @Nonnull Collection<String> topics, @Nonnull Duration timeout, @Nonnull Function<ConsumerRecord<K, V>, E> encoder) {
        this(pipeline, consumer, topics, timeout, encoder, null);
    }

    public KafkaConsumerPipelineProducer(@Nonnull Pipeline<E, PipelineEngine<E>, RequestRouter<E>> pipeline, @Nonnull Consumer<K, V> consumer, @Nonnull Collection<String> topics, @Nonnull Duration timeout, @Nonnull Function<ConsumerRecord<K, V>, E> encoder, @Nullable Duration period) {
        super(pipeline, period);
        this.consumer = Objects.requireNonNull(consumer, "consumer");
        this.topics = Objects.requireNonNull(topics, "topics");
        this.timeout = Objects.requireNonNull(timeout, "timeout");
        this.encoder = Objects.requireNonNull(encoder, "encoder");
    }

    /**
     * Subscribes the Kafka {@link Consumer} to the specified topics.
     *
     * @see PipelineProducer#onStart
     */
    @Override
    protected void onStart() {
        run(kafka -> kafka.subscribe(topics, this));
    }

    /**
     * Polls the subscribed topics for events and maps each batch of records to a {@link Request}.
     *
     * @return A {@link Stream} of {@link Request} encoded events.
     * @see PipelineProducer#poll
     */
    @Nonnull
    @Override
    protected Stream<Request<E>> poll() {
        final var batch = call(kafka -> kafka.poll(timeout));
        return batch.partitions().stream().map(records -> Request.of(batch.records(records).stream().map(encoder).toList()));
    }

    /**
     * Closes the Kafka {@link Consumer}.
     *
     * @see PipelineProducer#onStop
     */
    @Override
    protected void onStop() {
        log.info("Exiting the Kafka Consumer poll loop");
        //TODO: a shared, concurrent map/view of offsets..?
        //TODO: commit sync then close..?
        run(Consumer::close);
    }

    //TODO: re-balancing logic
    @Override
    public void onPartitionsRevoked(Collection<TopicPartition> partitions) {
        log.debug("onPartitionsRevoked invoked: {}", partitions);
    }

    @Override
    public void onPartitionsAssigned(Collection<TopicPartition> partitions) {
        log.debug("onPartitionsAssigned invoked: {}", partitions);
    }

    /**
     * Invokes the parameterised {@link java.util.function.Consumer} upon the {@link Consumer} within a {@code synchronized} block
     * ensuring mutual exclusion.
     *
     * @param task The action to invoke on the Kafka {@link Consumer}.
     */
    private void run(java.util.function.Consumer<Consumer<K, V>> task) {
        synchronized (consumer) {
            task.accept(consumer);
        }
    }

    /**
     * Invokes the parameterised {@link Function} upon the {@link Consumer} within a {@code synchronized} block
     * ensuring mutual exclusion.
     *
     * @param task The {@link Function} to invoke.
     * @param <T>  The inferred type parameter of the result of the task.
     * @return The result of the task.
     */
    private <T> T call(java.util.function.Function<Consumer<K, V>, T> task) {
        synchronized (consumer) {
            return task.apply(consumer);
        }
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this).add("state", getState()).add("topics", getTopics()).add("timeout", getTimeout()).add("period", getPeriod()).add("pipeline", pipeline).toString();
    }
}
