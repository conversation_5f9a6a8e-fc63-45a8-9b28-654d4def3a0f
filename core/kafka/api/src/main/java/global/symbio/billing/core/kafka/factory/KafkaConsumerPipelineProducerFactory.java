package global.symbio.billing.core.kafka.factory;

import global.symbio.billing.core.kafka.api.DelegatingSafeDeserializer;
import global.symbio.billing.core.kafka.model.EnrichedKafkaTransaction;
import io.micronaut.configuration.kafka.KafkaConsumerFactory;
import io.micronaut.configuration.kafka.config.AbstractKafkaConsumerConfiguration;
import io.micronaut.configuration.kafka.serde.SerdeRegistry;
import io.micronaut.context.annotation.Factory;
import io.micronaut.context.annotation.Requires;
import io.micronaut.core.type.Argument;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.inject.Singleton;
import org.apache.kafka.clients.consumer.Consumer;

import java.util.UUID;

import static global.symbio.billing.core.kafka.api.CommonKafkaConfigurationGroups.CONFIGURATION_GROUP_BILLING_TRANSACTIONS;

/**
 * This factory class exposes constants for use in other components of the application and implements logic for automatically
 * configuring (autowiring) Java Beans within Micronauts dependency injection framework.
 */
@Factory
public class KafkaConsumerPipelineProducerFactory {

    public static final String POLLING_TIMEOUT_DURATION = "500ms";

    /**
     * Automatically configures a {@link Consumer} within Micronauts dependency injection framework that is not managed
     * by Micronauts Kafka implementation, as their current implementation prohibits multi-threading capabilities <a href="https://github.com/micronaut-projects/micronaut-kafka/issues/613">currently</a>.
     *
     * @param registry      The registry of type {@link org.apache.kafka.common.serialization.Serializer}s and {@link org.apache.kafka.common.serialization.Deserializer}s.
     * @param factory       The factory object for instantiating {@link Consumer}s.
     * @param configuration The Kafka configuration of the {@link Consumer}.
     * @return The configured {@link Consumer}.
     */
    @Inject
    @Singleton
    @Named(CONFIGURATION_GROUP_BILLING_TRANSACTIONS)
    @Requires(beans = {SerdeRegistry.class, KafkaConsumerFactory.class, AbstractKafkaConsumerConfiguration.class})
    public Consumer<UUID, EnrichedKafkaTransaction> autowire_kafka_consumer(
        @Nonnull SerdeRegistry registry,
        @Nonnull KafkaConsumerFactory factory,
        @Nonnull @Named(CONFIGURATION_GROUP_BILLING_TRANSACTIONS) AbstractKafkaConsumerConfiguration<UUID, EnrichedKafkaTransaction> configuration) {
        if (configuration.getKeyDeserializer().isEmpty()) {
            final var type = Argument.of(UUID.class);
            configuration.setKeyDeserializer(DelegatingSafeDeserializer.wrap(registry.pickDeserializer(type)));
        }
        if (configuration.getValueDeserializer().isEmpty()) {
            final var type = Argument.of(EnrichedKafkaTransaction.class);
            configuration.setValueDeserializer(DelegatingSafeDeserializer.wrap(registry.pickDeserializer(type)));
        }
        return factory.createConsumer(configuration);
    }
}