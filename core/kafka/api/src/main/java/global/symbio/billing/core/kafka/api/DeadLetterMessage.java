package global.symbio.billing.core.kafka.api;

import jakarta.annotation.Nonnull;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.header.Header;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

public record DeadLetterMessage(
    @Nonnull ErrorDetails error,
    @Nonnull KafkaMessage<?, ?> message
) {

    public record ErrorDetails(
        @Nonnull String service,
        @Nonnull String details
    ) {}

    public record KafkaMessage<K, V>(
        @Nonnull String topic,
        @Nonnull K key,
        @Nonnull V body,
        @Nonnull Integer partition,
        @Nonnull Long timestamp,
        @Nonnull Map<String, String> headers
    ) {}

    @Nonnull
    public static <K, V> DeadLetterMessage create(@Nonnull ConsumerRecord<K, V> record, @Nonnull Throwable cause, @Nonnull String service) {
        return create(record, cause.getMessage(), service);
    }

    @Nonnull
    public static <K, V> DeadLetterMessage create(@Nonnull ConsumerRecord<K, V> record, @Nonnull String cause, @Nonnull String service) {
        final var headers = Arrays.stream(record.headers().toArray()).collect(Collectors.toMap(Header::key, DeadLetterMessage::decode));
        final var message = new KafkaMessage<K, V>(record.topic(), record.key(), record.value(), record.partition(), record.timestamp(), headers);
        final var details = new ErrorDetails(service, cause);
        return new DeadLetterMessage(details, message);
    }

    @Nonnull
    private static String decode(@Nonnull Header header) {
        try {
            return KafkaUtil.convertHeaderToString(header);
        } catch (Exception cause) {
            return "Unable to decode header: " + header.key() + " - " + cause.getMessage();
        }
    }
}
