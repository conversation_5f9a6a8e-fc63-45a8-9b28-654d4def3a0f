package global.symbio.billing.core.kafka.api;

import com.google.common.base.MoreObjects;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.common.header.Headers;
import org.apache.kafka.common.serialization.Deserializer;

import java.util.Map;
import java.util.Objects;

@Slf4j
public record DelegatingSafeDeserializer<T>(
    @Nonnull Deserializer<T> delegate
) implements Deserializer<T> {

    private static final String LOG_MESSAGE = "Exception deserializing data from topic: {}";

    public DelegatingSafeDeserializer {
        Objects.requireNonNull(delegate, "delegate");
    }

    @Override
    @Nullable
    public T deserialize(String topic, byte[] data) {
        try {
            return delegate.deserialize(topic, data);
        } catch (Throwable cause) {
            log.warn(LOG_MESSAGE, topic, cause);
        }
        return null;
    }

    @Override
    @Nullable
    public T deserialize(String topic, Headers headers, byte[] data) {
        try {
            return delegate.deserialize(topic, headers, data);
        } catch (Throwable cause) {
            log.warn(LOG_MESSAGE, topic, cause);
        }
        return null;
    }

    @Override
    public void configure(Map<String, ?> configs, boolean isKey) {
        delegate.configure(configs, isKey);
    }

    @Override
    public void close() {
        delegate.close();
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this).add("delegate", delegate).toString();
    }

    @Nonnull
    public static <T> DelegatingSafeDeserializer<T> wrap(@Nonnull Deserializer<T> delegate) {
        if (delegate instanceof DelegatingSafeDeserializer<T> wrapped) {
            return wrapped;
        }
        return new DelegatingSafeDeserializer<>(delegate);
    }
}
