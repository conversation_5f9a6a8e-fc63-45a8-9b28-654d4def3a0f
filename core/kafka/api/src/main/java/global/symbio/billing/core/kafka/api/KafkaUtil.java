package global.symbio.billing.core.kafka.api;

import jakarta.annotation.Nonnull;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.kafka.common.header.Header;

import java.nio.charset.StandardCharsets;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class KafkaUtil {

    @Nonnull
    public static String convertHeaderToString(@Nonnull Header header) {
        return new String(header.value(), StandardCharsets.UTF_8);
    }
}