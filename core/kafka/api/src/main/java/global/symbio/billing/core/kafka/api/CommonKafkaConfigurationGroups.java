package global.symbio.billing.core.kafka.api;

import global.symbio.billing.core.i18n.ISO3166CountryCodes;
import global.symbio.billing.core.i18n.localisation.LocationAware;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class CommonKafkaConfigurationGroups {

    public static final String CONFIGURATION_GROUP_BILLING_TRANSACTIONS = "billing-transactions";
    public static final String CONFIGURATION_GROUP_FINANCE_REPORTING = "finance-reporting";
    public static final String CONFIGURATION_GROUP_FINANCE_REPORTING_SG = "finance-reporting-sg";
    public static final String CONFIGURATION_GROUP_FINANCE_REPORTING_AU = "finance-reporting-au";
    public static final String CONFIGURATION_GROUP_PLEXUS_ACCOUNT_SG = "plexus-account-sg";
    public static final String CONFIGURATION_GROUP_PLEXUS_ACCOUNT_AU = "plexus-account-au";
    public static final String CONFIGURATION_GROUP_DEAD_LETTER_QUEUE = "deadletter";
    public static final String CONFIGURATION_GROUP_DATA_WAREHOUSE_REPORTING = "dwh-reporting";
    public static final String CONFIGURATION_GROUP_AUDIT = "audit";

    @Deprecated(since = "Once Sonar consolidates Finance reporting into global kafka")
    public static final LocationAware AUSTRALIAN_KAFKA_CLUSTER = LocationAware.of(ISO3166CountryCodes.SINGAPORE);

    @Deprecated(since = "Once Sonar consolidates Finance reporting into global kafka")
    public static final LocationAware SINGAPOREAN_KAFKA_CLUSTER = LocationAware.of(ISO3166CountryCodes.MALAYSIA);
}