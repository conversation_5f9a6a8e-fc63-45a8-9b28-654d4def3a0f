package global.symbio.billing.core.kafka.api;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.kafka.model.plexus.PlexusAccount;
import jakarta.annotation.Nonnull;
import org.apache.kafka.clients.consumer.ConsumerRecord;

import java.util.Objects;
import java.util.UUID;

/**
 * {@link PlexusAccountEvent} correlates an {@link PlexusAccount} record and {@link UUID} through the {@link ConsumerRecord} returned from Kafka.
 * The {@link ConsumerRecord} is exposed as an attribute of an {@link PlexusAccountEvent} to allow for easy
 * modification of this data-structure as requirements change with fewer downstream consequences.
 *
 * @param record
 * The {@link ConsumerRecord} containing the {@link UUID} of the event from our Kafka topic, and the {@link PlexusAccount} payload.
 */
public record PlexusAccountEvent(@Nonnull ConsumerRecord<UUID, PlexusAccount> record) {

    public PlexusAccountEvent {
        Objects.requireNonNull(record, "record");
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this).add("topic", record.topic()).add("offset", record.offset()).add("partition", record.partition()).toString();
    }
}