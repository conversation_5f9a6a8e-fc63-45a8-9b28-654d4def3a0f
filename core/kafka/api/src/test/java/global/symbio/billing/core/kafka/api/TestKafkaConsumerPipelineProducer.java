package global.symbio.billing.core.kafka.api;

import global.symbio.billing.core.pipeline.api.Pipeline;
import global.symbio.billing.core.pipeline.api.PipelineEngine;
import global.symbio.billing.core.pipeline.api.request.Request;
import global.symbio.billing.core.pipeline.api.request.router.RequestRouter;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.MockConsumer;
import org.apache.kafka.clients.consumer.OffsetResetStrategy;
import org.apache.kafka.common.TopicPartition;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Duration;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;

import static global.symbio.billing.core.pipeline.api.producer.PipelineProducer.ProducerState.*;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestKafkaConsumerPipelineProducer {

    private static final String TOPIC_NAME = "topic-name";
    private static final TopicPartition INTERESTS = new TopicPartition(TOPIC_NAME, 0);

    private MockConsumer<String, String> consumer;
    private Collection<String> topics;
    private Duration timeout;
    private Function<ConsumerRecord<String, String>, String> encoder;
    private Pipeline<String, PipelineEngine<String>, RequestRouter<String>> pipeline;
    private KafkaConsumerPipelineProducer<String, String, String> producer;

    @Captor
    private ArgumentCaptor<String> captor;

    @Mock
    private PipelineEngine<String> engine;

    @BeforeEach
    public void setup() {
        consumer = spy(new MockConsumer<>(OffsetResetStrategy.EARLIEST));
        consumer.schedulePollTask(() -> {
            consumer.unsubscribe(); // unsubscribe as the default behaviour when running is to subscribe
            consumer.assign(List.of(INTERESTS)); // assign a list of topics and partitions
            consumer.updateBeginningOffsets(Map.of(INTERESTS, 0L)); // reset the beginning offsets
        });
        topics = Set.of(TOPIC_NAME);
        timeout = Duration.ofMillis(1L);
        encoder = (record) -> record.topic() + ":" + record.key() + ":" + record.value();
        pipeline = Pipeline.mono("TestKafkaConsumerPipelineProducer", engine, RequestRouter.create());
        producer = new KafkaConsumerPipelineProducer<>(pipeline, consumer, topics, timeout, encoder);
    }

    @AfterEach
    public void teardown() {
        consumer.close();
        assertDoesNotThrow(producer::stop); // start and stop operations are idempotent
    }

    @Test
    @DisplayName("KafkaConsumerPipelineProducer state transitions manage pipeline life-cycle")
    public void kafka_consumer_pipeline_producer_state_transitions_and_pipeline_lifecycle() {
        assertEquals(CREATED, producer.getState());
        verify(engine, never()).start();
        verify(engine, never()).stop();
        assertDoesNotThrow(producer::start);
        assertEquals(STARTED, producer.getState());
        verify(engine, times(1)).start();
        verify(engine, never()).stop();
        assertDoesNotThrow(producer::stop);
        assertEquals(STOPPED, producer.getState());
        verify(engine, times(1)).start();
        verify(engine, times(1)).start();
    }

    @Test
    @DisplayName("KafkaConsumerPipelineProducer state transitions are idempotent")
    public void kafka_consumer_pipeline_producer_state_transitions_idempotency() {
        assertEquals(CREATED, producer.getState());
        verify(engine, never()).start();
        verify(engine, never()).stop();

        for (int i = 0; i < 5; i++) {
            assertDoesNotThrow(producer::start);
            assertEquals(STARTED, producer.getState());
        }

        verify(engine, times(1)).start();
        verify(engine, never()).stop();

        for (int i = 0; i < 5; i++) {
            assertDoesNotThrow(producer::stop);
            assertEquals(STOPPED, producer.getState());
        }

        verify(engine, times(1)).start();
        verify(engine, times(1)).start();
    }

    @Test
    @DisplayName("KafkaConsumerPipelineProducer state transitions are correctly guarded")
    public void kafka_consumer_pipeline_producer_state_transition_guards() {
        assertEquals(CREATED, producer.getState());
        verify(engine, never()).start();
        verify(engine, never()).stop();

        assertDoesNotThrow(producer::stop);
        assertEquals(CREATED, producer.getState());
        verify(engine, never()).start();
        verify(engine, never()).stop();

        assertDoesNotThrow(producer::start);
        assertEquals(STARTED, producer.getState());
        verify(engine, times(1)).start();
        verify(engine, never()).stop();

        assertDoesNotThrow(producer::start);
        assertEquals(STARTED, producer.getState());
        verify(engine, times(1)).start();
        verify(engine, never()).stop();

        assertDoesNotThrow(producer::stop);
        assertEquals(STOPPED, producer.getState());
        verify(engine, times(1)).start();
        verify(engine, times(1)).start();
    }

    @Test
    @Timeout(value = 5, threadMode = Timeout.ThreadMode.SEPARATE_THREAD)
    @DisplayName("KafkaConsumerPipelineProducer receives messages from consumer and passes them to pipeline")
    public void kafka_consumer_pipeline_producer_message_passing() {
        final var record = new ConsumerRecord<>(INTERESTS.topic(), INTERESTS.partition(), 0L, "the-key", "the-value");

        consumer.schedulePollTask(() -> consumer.addRecord(record));
        consumer.schedulePollTask(() -> producer.stop());

        assertDoesNotThrow(producer::start);

        for (int i = 0; i < 3; i++) {
            assertDoesNotThrow(producer::run);
        }

        assertEquals(STOPPED, producer.getState(), "Producer did not enter " + STOPPED + " state within expected timeframe");
        verify(engine, times(1)).receive(captor.capture());
        assertEquals(encoder.apply(record), captor.getValue());
    }

    @Test
    @DisplayName("KafkaConsumerPipelineProducer::poll polls kafka consumer and returns results")
    public void kafka_consumer_pipeline_producer_poll_invokes_consumer_poll() {
        final var record = new ConsumerRecord<>(INTERESTS.topic(), INTERESTS.partition(), 0L, "the-key", "the-value");
        consumer.schedulePollTask(() -> consumer.addRecord(record));

        final var warmup = assertDoesNotThrow(() -> producer.poll());
        verify(consumer, times(1)).poll(eq(timeout));
        assertEquals(List.of(), warmup.toList());

        final var requests = assertDoesNotThrow(() -> producer.poll());
        verify(consumer, times(2)).poll(eq(timeout));
        final var expected = Request.of(List.of(encoder.apply(record)));
        assertEquals(List.of(expected), requests.toList());
    }
}
