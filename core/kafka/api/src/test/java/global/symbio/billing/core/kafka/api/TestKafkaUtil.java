package global.symbio.billing.core.kafka.api;

import org.apache.kafka.common.header.internals.RecordHeader;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.nio.charset.StandardCharsets;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class TestKafkaUtil {

    @Test
    @DisplayName("KafkaUtil::convertHeaderToString decodes header value as UTF-8 String")
    public void kafka_util_convert_header_to_string_decodes_header_value_as_utf8_string() {
        final var expected = "value".getBytes(StandardCharsets.UTF_8);
        final var header = new RecordHeader("key", expected);
        final var result = assertDoesNotThrow(() -> KafkaUtil.convertHeaderToString(header));
        assertEquals(new String(expected, StandardCharsets.UTF_8), result);
    }
}
