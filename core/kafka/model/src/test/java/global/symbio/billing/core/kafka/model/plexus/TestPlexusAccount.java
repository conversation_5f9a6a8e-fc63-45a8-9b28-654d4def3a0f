package global.symbio.billing.core.kafka.model.plexus;

import io.micronaut.test.extensions.junit5.annotation.MicronautTest;
import io.micronaut.validation.validator.Validator;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.ZoneOffset;
import java.time.ZonedDateTime;

import static org.junit.jupiter.api.Assertions.assertEquals;

@MicronautTest(startApplication = false)
public class TestPlexusAccount {

    @Inject
    private Validator validator;

    private PlexusAccount valid;

    @BeforeEach
    public void setup() {
        valid = getValidPlexusAccount();
    }

    @Test
    @DisplayName("Default PlexusAccount bean does not pass validation")
    public void default_plexus_account_bean_does_not_pass_validation() {
        validate(new PlexusAccount(), 5);
    }

    @Test
    @DisplayName("Valid PlexusAccount bean passes validation")
    public void valid_plexus_account_bean_passes_validation() {
        validate(valid, 0);
    }

    @Test
    @DisplayName("Validate PlexusAccount::locale constraints")
    public void validate_plexus_account_locale_constraints() {
        validate(valid, 0);
        valid.setLocale("");
        validate(valid, 2);
        valid.setLocale("AU");
        validate(valid, 0);
        valid.setLocale("MY");
        validate(valid, 0);
        valid.setLocale(null);
        validate(valid, 1);
    }

    @Test
    @DisplayName("Validate PlexusAccount::isTestAccount constraints")
    public void validate_plexus_account_is_test_account_constraints() {
        validate(valid, 0);
        valid.setTestAccount(true);
        validate(valid, 0);
        valid.setTestAccount(false);
        validate(valid, 0);
    }

    @Test
    @DisplayName("Validate PlexusAccount::status constraints")
    public void validate_plexus_account_status_constraints() {
        validate(valid, 0);
        valid.setStatus("");
        validate(valid, 0);
        valid.setStatus("open");
        validate(valid, 0);
        valid.setStatus("closed");
        validate(valid, 0);
        valid.setStatus("suspended");
        validate(valid, 0);
        valid.setStatus(null);
        validate(valid, 1);
    }

    @Test
    @DisplayName("Validate PlexusAccount::billingPeriod constraints")
    public void validate_plexus_account_billing_period_constraints() {
        validate(valid, 0);
        valid.setBillingPeriod("");
        validate(valid, 0);
        valid.setBillingPeriod("monthly");
        validate(valid, 0);
        valid.setBillingPeriod("daily");
        validate(valid, 0);
        valid.setBillingPeriod("weekly");
        validate(valid, 0);
        valid.setBillingPeriod("yearly");
        validate(valid, 0);
        valid.setBillingPeriod(null);
        validate(valid, 1);
    }

    @Test
    @DisplayName("Validate PlexusAccount::billingDate constraints")
    public void validate_plexus_account_billing_date_constraints() {
        validate(valid, 0);
        valid.setBillingDate(ZonedDateTime.now());
        validate(valid, 0);
        valid.setBillingDate(ZonedDateTime.now().plusDays(30));
        validate(valid, 0);
        valid.setBillingDate(ZonedDateTime.now().minusDays(30));
        validate(valid, 0);
        valid.setBillingDate(ZonedDateTime.now().plusYears(1));
        validate(valid, 0);
        valid.setBillingDate(null);
        validate(valid, 1);
    }

    @Test
    @DisplayName("Validate PlexusAccount::setOwner constraints")
    public void validate_plexus_account_owner_constraints() {
        validate(valid, 0);
        valid.setOwner(new PlexusReference("new-ref"));
        validate(valid, 0);
        valid.setOwner(null);
        validate(valid, 1);
    }

    private void validate(@Nonnull PlexusAccount bean, int expected) {
        final var violations = validator.validate(bean);
        assertEquals(expected, violations.size());
    }

    @Nonnull
    public static PlexusAccount getValidPlexusAccount() {
        final var bean = new PlexusAccount();
        bean.setLocale("AU");
        bean.setTestAccount(false);
        bean.setStatus("open");
        bean.setBillingPeriod("monthly");
        bean.setBillingDate(ZonedDateTime.now().plusDays(30).withZoneSameInstant(ZoneOffset.UTC));
        bean.setOwner(new PlexusReference("test-ref"));
        return bean;
    }
}