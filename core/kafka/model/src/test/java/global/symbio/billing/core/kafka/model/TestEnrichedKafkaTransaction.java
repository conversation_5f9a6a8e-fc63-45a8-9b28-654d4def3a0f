package global.symbio.billing.core.kafka.model;

import io.micronaut.test.extensions.junit5.annotation.MicronautTest;
import io.micronaut.validation.validator.Validator;
import jakarta.inject.Inject;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;

@MicronautTest(startApplication = false)
public class TestEnrichedKafkaTransaction {

    @Inject
    private Validator validator;

    @Test
    @DisplayName("Default EnrichedKafkaTransaction bean does not pass validation")
    public void default_enriched_kafka_transaction_bean_does_not_pass_validation() {
        final var bean = new EnrichedKafkaTransaction();
        final var violations = validator.validate(bean);
        assertEquals(2, violations.size(),
            "Failed validation because 2 fields marked @NotNull are null: transaction and enrichment");
    }

    @Test
    @DisplayName("Valid KafkaTransaction bean passes validation")
    public void valid_enriched_kafka_transaction_bean_passes_validation() {
        final var bean = new EnrichedKafkaTransaction();
        Map<String, Object> enrichment = Map.of("life", 42, "fun", "69");
        bean.setTransaction(TestKafkaTransaction.getValidKafkaTransaction());
        bean.setEnrichment(enrichment);
        assertEquals(0, validator.validate(bean).size(), "Bean should pass validation");

        enrichment = new HashMap<>();
        enrichment.put("non-null-key", null);
        bean.setEnrichment(enrichment);
        assertEquals(0, validator.validate(bean).size(), "Bean should pass validation");

        enrichment = new HashMap<>();
        enrichment.put(null, "non-null-value");
        bean.setEnrichment(enrichment);
        assertEquals(0, validator.validate(bean).size(), "Bean should pass validation");
    }
}
