package global.symbio.billing.core.kafka.model;

import io.micronaut.test.extensions.junit5.annotation.MicronautTest;
import io.micronaut.validation.validator.Validator;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;

@MicronautTest(startApplication = false)
public class TestKafkaTransaction {

    @Inject
    private Validator validator;

    private KafkaTransaction valid;

    @BeforeEach
    public void setup() {
        valid = getValidKafkaTransaction();
    }

    @Test
    @DisplayName("Default KafkaTransaction bean does not pass validation")
    public void default_kafka_transaction_bean_does_not_pass_validation() {
        validate(new KafkaTransaction(), 10);
    }

    @Test
    @DisplayName("Valid KafkaTransaction bean passes validation")
    public void valid_kafka_transaction_bean_passes_validation() {
        validate(valid, 0);
    }

    @Test
    @DisplayName("Validate KafkaTransaction::currency constraints")
    public void validate_kafka_transaction_currency_constraints() {
        validate(valid, 0);
        valid.setCurrency(0);
        validate(valid, 0);
        valid.setCurrency(Integer.MAX_VALUE);
        validate(valid, 0);
        valid.setCurrency(Integer.MIN_VALUE);
        validate(valid, 0);
        valid.setCurrency(null);
        validate(valid, 1);
    }

    @Test
    @DisplayName("Validate KafkaTransaction::category constraints")
    public void validate_kafka_transaction_category_constraints() {
        validate(valid, 0);
        valid.setCategory(0);
        validate(valid, 0);
        valid.setCategory(Integer.MAX_VALUE);
        validate(valid, 0);
        valid.setCategory(Integer.MIN_VALUE);
        validate(valid, 0);
        valid.setCategory(null);
        validate(valid, 1);
    }

    @Test
    @DisplayName("Validate KafkaTransaction::amount constraints")
    public void validate_kafka_transaction_amount_constraints() {
        validate(valid, 0);
        valid.setAmount(null);
        validate(valid, 1);
        valid.setAmount(BigDecimal.ONE.negate());
        validate(valid, 1);
    }

    @Test
    @DisplayName("Validate KafkaTransaction::taxation constraints")
    public void validate_kafka_transaction_taxation_constraints() {
        validate(valid, 0);
        valid.setTaxation(null);
        validate(valid, 1);
        valid.setTaxation(BigDecimal.ONE.negate());
        validate(valid, 1);
    }

    @Test
    @DisplayName("Validate KafkaTransaction::debit constraints")
    public void validate_kafka_transaction_debit_constraints() {
        validate(valid, 0);
        valid.setDebit(UUID.nameUUIDFromBytes(new byte[16]));
        validate(valid, 0);
        valid.setDebit(UUID.randomUUID());
        validate(valid, 0);
        valid.setDebit(null);
        validate(valid, 1);
    }

    @Test
    @DisplayName("Validate KafkaTransaction::credit constraints")
    public void validate_kafka_transaction_credit_constraints() {
        validate(valid, 0);
        valid.setCredit(UUID.nameUUIDFromBytes(new byte[16]));
        validate(valid, 0);
        valid.setCredit(UUID.randomUUID());
        validate(valid, 0);
        valid.setCredit(null);
        validate(valid, 1);
    }

    @Test
    @DisplayName("Validate KafkaTransaction::isDebitingPartyDifferentToCreditingParty")
    public void validate_kafka_transaction_is_debiting_party_different_to_crediting_party() {
        validate(valid, 0);
        final var account = UUID.nameUUIDFromBytes(new byte[16]);
        valid.setCredit(account);
        valid.setDebit(account);
        validate(valid, 1);
    }

    @Test
    @DisplayName("Validate KafkaTransaction::description constraints")
    public void validate_kafka_transaction_description_constraints() {
        validate(valid, 0);
        valid.setDescription(null);
        validate(valid, 0);
        valid.setDescription("A");
        validate(valid, 0);
        valid.setDescription("A".repeat(128));
        validate(valid, 0);
        valid.setDescription("A".repeat(129));
        validate(valid, 1);
    }

    @Test
    @DisplayName("Validate KafkaTransaction::ref constraints")
    public void validate_kafka_transaction_ref_constraints() {
        validate(valid, 0);
        valid.setRef(null);
        validate(valid, 1);
        valid.setRef("");
        validate(valid, 1);
        valid.setRef("A");
        validate(valid, 0);
        valid.setRef("A".repeat(512));
        validate(valid, 0);
        valid.setRef("A".repeat(513));
        validate(valid, 1);
    }

    @Test
    @DisplayName("Validate KafkaTransaction::country constraints")
    public void validate_kafka_transaction_country_constraints() {
        validate(valid, 0);
        valid.setCountry(null);
        validate(valid, 1);
        valid.setCountry("");
        validate(valid, 2);
        valid.setCountry("A");
        validate(valid, 1);
        valid.setCountry("AA");
        validate(valid, 0);
        valid.setCountry("AAA");
        validate(valid, 1);
    }

    @Test
    @DisplayName("Validate KafkaTransaction::timestamp constraints")
    public void validate_kafka_transaction_timestamp_constraints() {
        validate(valid, 0);
        valid.setTimestamp(null);
        validate(valid, 1);
        valid.setTimestamp(ZonedDateTime.now());
        validate(valid, 0);
        valid.setTimestamp(ZonedDateTime.now().plusYears(10));
        validate(valid, 0);
        valid.setTimestamp(ZonedDateTime.now().minusYears(10));
        validate(valid, 0);
    }

    private void validate(@Nonnull KafkaTransaction bean, int expected) {
        final var violations = validator.validate(bean);
        assertEquals(expected, violations.size());
    }

    @Nonnull
    public static KafkaTransaction getValidKafkaTransaction() {
        final var bean = new KafkaTransaction();
        bean.setCurrency(1);
        bean.setCategory(1);
        bean.setAmount(BigDecimal.ZERO);
        bean.setTaxation(BigDecimal.ZERO);
        bean.setDebit(UUID.randomUUID());
        bean.setCredit(UUID.randomUUID());
        bean.setRef("plexid:tx");
        bean.setCountry("XD");
        bean.setTimestamp(Instant.EPOCH.atZone(ZoneOffset.UTC));
        return bean;
    }
}
