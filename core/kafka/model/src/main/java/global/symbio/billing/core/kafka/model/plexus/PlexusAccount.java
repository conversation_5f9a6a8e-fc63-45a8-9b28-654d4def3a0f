package global.symbio.billing.core.kafka.model.plexus;

import io.micronaut.core.annotation.Introspected;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

/**
 * The POJO/Java Bean representation of a plexus account update within our Kafka topic.
 * {@link Introspected} to integrate with Micronauts dependency injection and bean validation frameworks.
 * {@link Data}/{@link NoArgsConstructor} to adhere to the definition of a Java Bean.
 */
@Introspected
@Data @NoArgsConstructor @AllArgsConstructor
public class PlexusAccount {
    /**
     * The locale associated with the account, determines which plexus account service the account sits in.
     */
    @NotBlank
    @Size(min = 2, max = 2)
    private String locale;

    /**
     * Indicates if the account is a test account.
     */
    @NotNull
    private boolean isTestAccount;

    /**
     * The current status of the account (e.g., "open", "closed").
     */
    @NotNull
    private String status;

    /**
     * The billing frequency for the account (e.g., "monthly", "daily").
     */
    @NotNull
    private String billingPeriod;

    /**
     * The date when the next billing is scheduled to occur.
     */
    @NotNull
    private ZonedDateTime billingDate;

    /**
     * The plexus identifier of the account owner, the business in v2 plexus Customer service definition
     */
    @NotNull
    private PlexusReference owner;
}
