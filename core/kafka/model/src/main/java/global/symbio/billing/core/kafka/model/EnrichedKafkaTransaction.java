package global.symbio.billing.core.kafka.model;

import io.micronaut.core.annotation.Introspected;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * The POJO/Java Bean representation of a transaction with enrichment information within our Kafka topic.
 * {@link Introspected} to integrate with Micronauts dependency injection and bean validation frameworks.
 * {@link Data}/{@link NoArgsConstructor} to adhere to the definition of a Java Bean.
 */
@Introspected
@Data @NoArgsConstructor @AllArgsConstructor
public class EnrichedKafkaTransaction {

    @NotNull @Valid
    private KafkaTransaction transaction;

    @NotNull
    private Map<String, ?> enrichment;
}
