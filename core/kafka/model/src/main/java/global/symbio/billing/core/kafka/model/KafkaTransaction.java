package global.symbio.billing.core.kafka.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.micronaut.core.annotation.Introspected;
import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Objects;
import java.util.UUID;

/**
 * The POJO/Java Bean representation of a transaction within our Kafka topic.
 * {@link Introspected} to integrate with Micronauts dependency injection and bean validation frameworks.
 * {@link Data}/{@link NoArgsConstructor} to adhere to the definition of a Java Bean.
 */
@Introspected @DynamoDbBean
@Data @NoArgsConstructor @AllArgsConstructor
public class KafkaTransaction {

    @NotNull
    private Integer currency;

    @NotNull
    private Integer category;

    @NotNull
    @PositiveOrZero @Digits(integer = 30, fraction = 12)
    private BigDecimal amount;

    @NotNull
    @PositiveOrZero @Digits(integer = 30, fraction = 12)
    private BigDecimal taxation;

    @NotNull
    private UUID debit;

    @NotNull
    private UUID credit;

    @Size(max = 128)
    private String description;

    @NotBlank @Size(max = 512)
    private String ref; //TODO: validate plexid..?

    @NotBlank @Size(min = 2, max = 2)
    private String country;

    @NotNull
    private ZonedDateTime timestamp; // This is the source system timestamp

    /**
     * This method validates whether the debiting and crediting parties are different.
     * Must be public and start with 'is' in order to be included in bean validation.
     * Not intended as part of the public API of this data class.
     */
    @JsonIgnore
    @AssertFalse(message = "Accounts being debited and credited can't be the same")
    public final boolean isDebitingPartyEqualToCreditingParty() {
        return Objects.equals(debit, credit);
    }
}
