package global.symbio.billing.core.payment.persistence.api.allocation.state;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.annotation.Nonnull;
import lombok.Getter;

import java.math.BigDecimal;

@Getter
public final class Allocated extends AllocationState {

    public static final String TYPE = "ALLOCATED";

    @JsonCreator(mode = JsonCreator.Mode.PROPERTIES)
    public Allocated(
        @Nonnull @JsonProperty("total") BigDecimal total
    ) {
        super(TYPE, total);
    }

    @Nonnull
    @Override
    public BigDecimal getAllocated() {
        return total;
    }

    @Nonnull
    @Override
    public BigDecimal getUnallocated() {
        return BigDecimal.ZERO;
    }

    @Nonnull
    @Override
    public AllocationState allocate(@Nonnull BigDecimal amount) {
        ensurePositive(amount);
        throw new IllegalStateException("Unable to allocate any more funds.");
    }

    @Nonnull
    @Override
    public AllocationState deallocate(@Nonnull BigDecimal amount) {
        ensurePositive(amount);
        final var deallocation = amount.compareTo(getAllocated());
        if (deallocation > 0) {
            throw new IllegalStateException("Unable to deallocate more than what has been allocated. Allocated: " + getAllocated() + ", Requested: " + amount + ".");
        } else if (deallocation == 0) {
            return new Unallocated(getAllocated());
        }
        return new PartiallyAllocated(total, getAllocated().subtract(amount), amount);
    }
}