package global.symbio.billing.core.payment.persistence.api.allocation.account;

import global.symbio.billing.core.country.persistence.api.CountryDataAccessObject;
import global.symbio.billing.core.currency.persistence.api.CurrencyDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.allocation.state.AllocationState;
import global.symbio.billing.core.persistence.api.DataAccessObject;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.UUID;

public interface AccountAllocationDataAccessObject<T extends AccountAllocationDataAccessObject<T>> extends DataAccessObject<UUID, T> {

    @Nonnull
    UUID getAccount();

    @Nonnull
    T account(@Nonnull UUID account);

    @Nonnull
    AllocationType getType();

    @Nonnull
    T type(@Nonnull AllocationType type);

    @Nonnull
    CountryDataAccessObject getCountry();

    @Nonnull
    T country(@Nonnull CountryDataAccessObject country);

    @Nonnull
    CurrencyDataAccessObject getCurrency();

    @Nonnull
    T currency(@Nonnull CurrencyDataAccessObject currency);

    @Nonnull
    BigDecimal getAmount();

    @Nonnull
    T amount(@Nonnull BigDecimal amount);

    @Nonnull
    AllocationState getAllocationState(); //TODO: rename to `getState`

    @Nonnull
    T allocationState(@Nonnull AllocationState state); //TODO: rename to `state`

    @Nonnull
    AllocationDirection getDirection();

    @Nonnull
    T direction(@Nonnull AllocationDirection direction);

    @Nullable
    ZonedDateTime getSentTimestamp();

    @Nonnull
    T sentTimestamp(@Nullable ZonedDateTime timestamp);

    @Nullable
    ZonedDateTime getSettledTimestamp();

    @Nonnull
    T settledTimestamp(@Nullable ZonedDateTime timestamp);

    @Nonnull
    ZonedDateTime getTimestamp();

    @Nonnull
    T timestamp(@Nonnull ZonedDateTime timestamp);
}