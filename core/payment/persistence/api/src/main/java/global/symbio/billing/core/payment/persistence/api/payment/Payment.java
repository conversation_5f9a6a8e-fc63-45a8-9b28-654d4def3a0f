package global.symbio.billing.core.payment.persistence.api.payment;

import global.symbio.billing.core.country.persistence.api.Country;
import global.symbio.billing.core.currency.persistence.api.Currency;
import global.symbio.billing.core.payment.persistence.api.payment.method.PaymentMethod;
import global.symbio.billing.core.persistence.api.Entity;
import global.symbio.billing.core.reference.persistence.api.reference.Reference;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.UUID;

public class Payment extends Entity<UUID, PaymentDataAccessObject> {

    public Payment(@Nonnull PaymentDataAccessObject data) {
        super(data);
    }

    @Nonnull
    public UUID getBusiness() {
        return data().getBusiness();
    }

    @Nonnull
    public PaymentMethod getMethod() {
        return unwrap(data().getMethod());
    }

    @Nonnull
    public Currency getCurrency() {
        return unwrap(data().getCurrency());
    }

    @Nonnull
    public BigDecimal getAmount() {
        return data().getAmount();
    }

    @Nonnull
    public ZonedDateTime getReceiptDate() {
        return data().getReceiptDate();
    }

    @Nonnull
    public PaymentStatus getStatus() {
        return data().getStatus();
    }

    @Nullable
    public String getDescription() {
        return data().getDescription();
    }

    @Nonnull
    public Country getCountry() {
        return unwrap(data().getCountry());
    }

    @Nullable
    public Reference getReference() {
        return unwrap(data().getReference());
    }

    @Nonnull
    public ZonedDateTime getTimestamp() {
        return data().getTimestamp();
    }
}
