package global.symbio.billing.core.payment.persistence.api.repository;

import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.transfer.TransferOutAccountAllocationDataAccessObject;
import io.micronaut.context.annotation.Executable;
import io.micronaut.data.repository.CrudRepository;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.math.BigDecimal;
import java.util.UUID;

public interface TransferOutAccountAllocationRepository<T extends TransferOutAccountAllocationDataAccessObject<T>> extends CrudRepository<T, UUID> {

    @Nullable
    @Executable
    BigDecimal getSumOfTransferOutAccountAllocationsByPaymentAccountAllocationAndAccount(
        @Nonnull UUID paymentAccountAllocation,
        @Nonnull UUID account
    );
}