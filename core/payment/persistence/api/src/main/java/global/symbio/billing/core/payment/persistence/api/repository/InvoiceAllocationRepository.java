package global.symbio.billing.core.payment.persistence.api.repository;

import global.symbio.billing.core.payment.persistence.api.allocation.invoice.InvoiceAllocationDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.allocation.invoice.InvoiceAllocationIdentifier;
import io.micronaut.context.annotation.Executable;
import io.micronaut.data.repository.CrudRepository;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;
import java.util.UUID;

public interface InvoiceAllocationRepository<I extends InvoiceAllocationIdentifier, T extends InvoiceAllocationDataAccessObject> extends CrudRepository<T, I> {

    @Nonnull
    List<T> findAllByIdentifierAllocationIdentifier(@Nonnull UUID allocation);

    @Nonnull
    List<T> findAllByIdentifierAllocationIdentifierIn(@Nonnull Set<UUID> allocations);

    @Nullable
    @Executable
    BigDecimal getSumOfInvoiceAllocationsByAllocation(@Nonnull UUID allocation);

    boolean existsByIdentifierAllocationIdentifier(@Nonnull UUID allocation);

    @Nonnull
    @Executable
    List<T> findAllByIdentifierInvoice(@Nonnull UUID invoice);
}
