package global.symbio.billing.core.payment.persistence.api.payment.method;

import global.symbio.billing.core.persistence.api.Entity;
import jakarta.annotation.Nonnull;

public class PaymentMethod extends Entity<Integer, PaymentMethodDataAccessObject> {

    public PaymentMethod(@Nonnull PaymentMethodDataAccessObject data) {
        super(data);
    }

    @Nonnull
    public String getName() {
        return data().getName();
    }

    @Nonnull
    public String getDisplayName() {
        return data().getDisplayName();
    }
}
