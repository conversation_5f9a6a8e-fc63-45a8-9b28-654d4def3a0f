package global.symbio.billing.core.payment.persistence.api.payment;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.country.persistence.api.Country;
import global.symbio.billing.core.country.persistence.api.CountryDataAccessObject;
import global.symbio.billing.core.currency.persistence.api.Currency;
import global.symbio.billing.core.currency.persistence.api.CurrencyDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.payment.method.PaymentMethod;
import global.symbio.billing.core.payment.persistence.api.payment.method.PaymentMethodDataAccessObject;
import global.symbio.billing.core.persistence.api.DataAccessObject;
import global.symbio.billing.core.reference.persistence.api.reference.Reference;
import global.symbio.billing.core.reference.persistence.api.reference.ReferenceDataAccessObject;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Objects;
import java.util.UUID;

public abstract class PaymentDataAccessObject implements DataAccessObject<UUID, PaymentDataAccessObject> {

    @Nonnull
    public abstract UUID getBusiness();

    @Nonnull
    public abstract PaymentDataAccessObject business(@Nonnull UUID business);

    @Nonnull
    public abstract PaymentMethodDataAccessObject getMethod();

    @Nonnull
    public abstract PaymentDataAccessObject method(@Nonnull PaymentMethodDataAccessObject method);

    @Nonnull
    public PaymentDataAccessObject method(@Nonnull PaymentMethod method) {
        return method(method.data());
    }

    @Nonnull
    public abstract CurrencyDataAccessObject getCurrency();

    @Nonnull
    public abstract PaymentDataAccessObject currency(@Nonnull CurrencyDataAccessObject currency);

    @Nonnull
    public PaymentDataAccessObject currency(@Nonnull Currency currency) {
        return currency(currency.data());
    }

    @Nonnull
    public abstract BigDecimal getAmount();

    @Nonnull
    public abstract PaymentDataAccessObject amount(@Nonnull BigDecimal amount);

    @Nonnull
    public abstract ZonedDateTime getReceiptDate();

    @Nonnull
    public abstract PaymentDataAccessObject receiptDate(@Nonnull ZonedDateTime receiptDate);

    @Nonnull
    public abstract PaymentStatus getStatus();

    @Nonnull
    public abstract PaymentDataAccessObject status(@Nonnull PaymentStatus status);

    @Nullable
    public abstract String getDescription();

    @Nonnull
    public abstract PaymentDataAccessObject description(@Nullable String description);

    @Nonnull
    public abstract CountryDataAccessObject getCountry();

    @Nonnull
    public abstract PaymentDataAccessObject country(@Nonnull CountryDataAccessObject country);

    @Nonnull
    public PaymentDataAccessObject country(@Nonnull Country country) {
        return country(country.data());
    }

    @Nullable
    public abstract ReferenceDataAccessObject getReference();

    @Nonnull
    public abstract PaymentDataAccessObject reference(@Nullable ReferenceDataAccessObject reference);

    @Nonnull
    public PaymentDataAccessObject reference(@Nonnull Reference entity) {
        return reference(entity.data());
    }

    @Nonnull
    public abstract ZonedDateTime getTimestamp();

    @Nonnull
    public abstract PaymentDataAccessObject timestamp(@Nonnull ZonedDateTime timestamp);

    @Nonnull
    @Override
    public Payment entity() {
        return new Payment(this);
    }

    @Override
    public boolean equals(Object other) {
        if (this == other) return true;
        if (!(other instanceof PaymentDataAccessObject payment)) return false;
        return Objects.equals(getIdentifier(), payment.getIdentifier())
            && Objects.equals(getBusiness(), payment.getBusiness())
            && Objects.equals(getMethod(), payment.getMethod())
            && Objects.equals(getCurrency(), payment.getCurrency())
            && Objects.equals(getAmount(), payment.getAmount())
            && Objects.equals(getReceiptDate(), payment.getReceiptDate())
            && Objects.equals(getStatus(), payment.getStatus())
            && Objects.equals(getDescription(), payment.getDescription())
            && Objects.equals(getCountry(), payment.getCountry())
            && Objects.equals(getReference(), payment.getReference())
            && Objects.equals(getTimestamp(), payment.getTimestamp());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getIdentifier(), getBusiness(), getMethod(), getCurrency(), getAmount(), getReceiptDate(), getStatus(), getDescription(), getCountry(), getReference(), getTimestamp());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("identifier", getIdentifier())
            .add("business", getBusiness())
            .add("method", getMethod())
            .add("currency", getCurrency())
            .add("amount", getAmount())
            .add("receiptDate", getReceiptDate())
            .add("status", getStatus())
            .add("description", getDescription())
            .add("country", getCountry())
            .add("reference", getReference())
            .add("timestamp", getTimestamp())
            .toString();
    }
}