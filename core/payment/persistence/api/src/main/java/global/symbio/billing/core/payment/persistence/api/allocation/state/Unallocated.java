package global.symbio.billing.core.payment.persistence.api.allocation.state;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.annotation.Nonnull;
import lombok.Getter;

import java.math.BigDecimal;

@Getter
public final class Unallocated extends AllocationState {

    public static final String TYPE = "UNALLOCATED";

    @JsonCreator(mode = JsonCreator.Mode.PROPERTIES)
    public Unallocated(
        @Nonnull @JsonProperty("total") BigDecimal total
    ) {
        super(TYPE, total);
    }

    @Nonnull
    @Override
    public BigDecimal getAllocated() {
        return BigDecimal.ZERO;
    }

    @Nonnull
    @Override
    public BigDecimal getUnallocated() {
        return total;
    }

    @Nonnull
    @Override
    public AllocationState allocate(@Nonnull BigDecimal amount) {
        ensurePositive(amount);
        final var payment = amount.compareTo(getUnallocated());
        if (payment > 0) {
            throw new IllegalStateException("Unable to create an allocation greater than the unallocated amount. Unallocated: " + getUnallocated() + ", Requested: " + amount + ".");
        } else if (payment == 0) {
            return new Allocated(getUnallocated());
        }
        return new PartiallyAllocated(total, amount, getUnallocated().subtract(amount));
    }

    @Nonnull
    @Override
    public AllocationState deallocate(@Nonnull BigDecimal amount) {
        ensurePositive(amount);
        throw new IllegalStateException("Unable to deallocate an unallocated account allocation.");
    }
}