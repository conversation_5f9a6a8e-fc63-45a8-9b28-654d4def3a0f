package global.symbio.billing.core.payment.persistence.api.cancellation;

import global.symbio.billing.core.payment.persistence.api.Recordable;
import global.symbio.billing.core.payment.persistence.api.payment.Payment;
import global.symbio.billing.core.persistence.api.Entity;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;
import java.util.UUID;

public class PaymentCancellation extends Entity<UUID, PaymentCancellationDataAccessObject> implements Recordable {

    public PaymentCancellation(@Nonnull PaymentCancellationDataAccessObject data) {
        super(data);
    }

    @Nonnull
    public Payment getPayment() {
        return unwrap(data().getPayment());
    }

    @Nonnull
    public String getReason() {
        return data().getReason();
    }

    @Nonnull
    public String getReference() {
        return data().getReference();
    }

    @Nullable
    public ZonedDateTime getSentTimestamp() {
        return data().getSentTimestamp();
    }

    @Nullable
    public ZonedDateTime getSettledTimestamp() {
        return data().getSettledTimestamp();
    }

    @Nonnull
    public ZonedDateTime getTimestamp() {
        return data().getTimestamp();
    }
}
