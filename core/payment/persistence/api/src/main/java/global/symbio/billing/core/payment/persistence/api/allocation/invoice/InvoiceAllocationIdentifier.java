package global.symbio.billing.core.payment.persistence.api.allocation.invoice;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AccountAllocationDataAccessObject;
import jakarta.annotation.Nonnull;

import java.util.Comparator;
import java.util.Objects;
import java.util.UUID;

public abstract class InvoiceAllocationIdentifier implements Comparable<InvoiceAllocationIdentifier> {

    @Nonnull
    public abstract UUID getInvoice();

    @Nonnull
    public abstract AccountAllocationDataAccessObject getAllocation();

    @Override
    public boolean equals(Object other) {
        if (this == other) return true;
        if (!(other instanceof InvoiceAllocationIdentifier identifier)) return false;
        return Objects.equals(getInvoice(), identifier.getInvoice()) && Objects.equals(getAllocation(), identifier.getAllocation());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getInvoice(), getAllocation());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this).add("invoice", getInvoice()).add("allocation", getAllocation()).toString();
    }

    @Override
    public int compareTo(@Nonnull InvoiceAllocationIdentifier other) {
        return COMPARATOR.compare(this, other);
    }

    private static final Comparator<InvoiceAllocationIdentifier> COMPARATOR = Comparator
        .comparing(InvoiceAllocationIdentifier::getInvoice)
        .thenComparing(identifier -> identifier.getAllocation().getIdentifier());
}
