package global.symbio.billing.core.payment.persistence.api.repository;

import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.transfer.TransferInAccountAllocationDataAccessObject;
import io.micronaut.context.annotation.Executable;
import io.micronaut.data.repository.CrudRepository;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.math.BigDecimal;
import java.util.UUID;

public interface TransferInAccountAllocationRepository<T extends TransferInAccountAllocationDataAccessObject<T>> extends CrudRepository<T, UUID> {

    @Nullable
    @Executable
    BigDecimal getSumOfTransferInAccountAllocationsByPaymentAccountAllocationAndAccount(
        @Nonnull UUID paymentAccountAllocation,
        @Nonnull UUID account
    );

    @Executable
    boolean existsTransferInAllocationsWithInvoiceAllocationsByPaymentAccountAllocation(
        @Nonnull UUID paymentAccountAllocation
    );
}