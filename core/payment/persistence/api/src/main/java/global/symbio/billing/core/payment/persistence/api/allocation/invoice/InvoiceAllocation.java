package global.symbio.billing.core.payment.persistence.api.allocation.invoice;

import com.fasterxml.jackson.annotation.JsonIgnore;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AccountAllocation;
import global.symbio.billing.core.persistence.api.Entity;
import jakarta.annotation.Nonnull;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.UUID;

public class InvoiceAllocation extends Entity<InvoiceAllocationIdentifier, InvoiceAllocationDataAccessObject> {

    public InvoiceAllocation(@Nonnull InvoiceAllocationDataAccessObject data) {
        super(data);
    }

    @Nonnull
    public UUID getInvoice() {
        return getIdentifier().getInvoice();
    }

    @Nonnull
    @JsonIgnore
    public AccountAllocation<?> getAllocation() {
        return (AccountAllocation) unwrap(getIdentifier().getAllocation());
    }

    @Nonnull
    public BigDecimal getAmount() {
        return data().getAmount();
    }

    @Nonnull
    public ZonedDateTime getTimestamp() {
        return data().getTimestamp();
    }
}
