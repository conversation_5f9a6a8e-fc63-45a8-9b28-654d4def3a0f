package global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.transfer;

import global.symbio.billing.core.payment.persistence.api.allocation.account.AccountAllocationDataAccessObject;
import jakarta.annotation.Nonnull;

import java.util.UUID;

public interface TransferOutAccountAllocationDataAccessObject<T extends TransferOutAccountAllocationDataAccessObject<T>> extends AccountAllocationDataAccessObject<T> {

    @Nonnull
    UUID getParent();

    @Nonnull
    TransferOutAccountAllocationDataAccessObject<T> parent(@Nonnull UUID parent);

    @Nonnull
    @Override
    default TransferOutAccountAllocation<T> entity() {
        return new TransferOutAccountAllocation<>((T) this);
    }
}