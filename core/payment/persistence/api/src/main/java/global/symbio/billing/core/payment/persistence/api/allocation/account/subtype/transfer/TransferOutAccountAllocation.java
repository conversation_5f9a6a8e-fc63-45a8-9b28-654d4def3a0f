package global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.transfer;

import global.symbio.billing.core.payment.persistence.api.allocation.account.AccountAllocation;
import jakarta.annotation.Nonnull;

import java.util.UUID;

public class TransferOutAccountAllocation<T extends TransferOutAccountAllocationDataAccessObject<T>> extends AccountAllocation<T> {

    public TransferOutAccountAllocation(@Nonnull T data) {
        super(data);
    }

    @Nonnull
    public UUID getParent() {
        return data().getParent();
    }
}