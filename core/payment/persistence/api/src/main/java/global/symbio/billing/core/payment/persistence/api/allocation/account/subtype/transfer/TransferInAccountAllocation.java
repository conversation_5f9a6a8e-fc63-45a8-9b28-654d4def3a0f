package global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.transfer;

import global.symbio.billing.core.payment.persistence.api.Recordable;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AccountAllocation;
import jakarta.annotation.Nonnull;

import java.util.UUID;

public class TransferInAccountAllocation<T extends TransferInAccountAllocationDataAccessObject<T>> extends AccountAllocation<T> implements Recordable {

    public TransferInAccountAllocation(@Nonnull T data) {
        super(data);
    }

    @Nonnull
    public UUID getParent() {
        return data().getParent();
    }
}