package global.symbio.billing.core.payment.persistence.api.repository;

import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.payment.PaymentAccountAllocationDataAccessObject;
import io.micronaut.context.annotation.Executable;
import io.micronaut.data.repository.CrudRepository;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.Set;
import java.util.UUID;

public interface PaymentAccountAllocationRepository<T extends PaymentAccountAllocationDataAccessObject<T>> extends CrudRepository<T, UUID> {

    @Nonnull
    @Executable
    Collection<T> getPaymentAccountAllocations(
        @Nullable UUID account,
        @Nullable ZonedDateTime paymentDateStart,
        @Nullable ZonedDateTime paymentDateEnd,
        @Nullable ZonedDateTime receiptDateStart,
        @Nullable ZonedDateTime receiptDateEnd,
        @Nullable Set<String> allocation,
        @Nullable Set<Integer> methods,
        @Nullable Set<String> status
    );

    @Nonnull
    @Executable
    Collection<T> getPaymentAccountAllocationsByTimestampBetweenAndCountryEquals(
        @Nonnull ZonedDateTime start,
        @Nonnull ZonedDateTime end,
        @Nonnull Integer country);
}
