package global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.payment;

import global.symbio.billing.core.payment.persistence.api.Recordable;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AccountAllocation;
import global.symbio.billing.core.payment.persistence.api.payment.Payment;
import jakarta.annotation.Nonnull;

public class PaymentAccountAllocation<T extends PaymentAccountAllocationDataAccessObject<T>> extends AccountAllocation<T> implements Recordable {

    public PaymentAccountAllocation(@Nonnull T data) {
        super(data);
    }

    @Nonnull
    public Payment getPayment() {
        return unwrap(data().getPayment());
    }
}