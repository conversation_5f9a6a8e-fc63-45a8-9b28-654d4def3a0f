package global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.transfer;

import global.symbio.billing.core.payment.persistence.api.allocation.account.AccountAllocationDataAccessObject;
import jakarta.annotation.Nonnull;

import java.util.UUID;

public interface TransferInAccountAllocationDataAccessObject<T extends TransferInAccountAllocationDataAccessObject<T>> extends AccountAllocationDataAccessObject<T> {

    @Nonnull
    UUID getParent();

    @Nonnull
    TransferInAccountAllocationDataAccessObject<T> parent(@Nonnull UUID parent);

    @Nonnull
    @Override
    default TransferInAccountAllocation<T> entity() {
        return new TransferInAccountAllocation<>((T) this);
    }

}