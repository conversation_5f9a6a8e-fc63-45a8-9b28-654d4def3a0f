package global.symbio.billing.core.payment.persistence.api.allocation.invoice;

import global.symbio.billing.core.payment.persistence.api.allocation.account.AccountAllocationDataAccessObject;
import global.symbio.billing.core.persistence.api.DataAccessObjectFactory;
import jakarta.annotation.Nonnull;

import java.util.UUID;

public interface InvoiceAllocationDataAccessObjectFactory extends DataAccessObjectFactory<InvoiceAllocationIdentifier, InvoiceAllocationDataAccessObject> {

    @Nonnull
    InvoiceAllocationDataAccessObject create(@Nonnull UUID invoice, @Nonnull AccountAllocationDataAccessObject<?> allocation);
}