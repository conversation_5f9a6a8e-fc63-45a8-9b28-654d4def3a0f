package global.symbio.billing.core.payment.persistence.api.allocation.state;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.google.common.base.MoreObjects;
import jakarta.annotation.Nonnull;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.Objects;

@Getter
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "type")
@JsonSubTypes(
    value = {
        @JsonSubTypes.Type(value = Unallocated.class, name = Unallocated.TYPE),
        @JsonSubTypes.Type(value = PartiallyAllocated.class, name = PartiallyAllocated.TYPE),
        @JsonSubTypes.Type(value = Allocated.class, name = Allocated.TYPE),
    }
)
public sealed abstract class AllocationState
    permits Unallocated, PartiallyAllocated, Allocated {

    @Nonnull
    protected final String type;

    @Nonnull
    protected final BigDecimal total;

    public AllocationState(@Nonnull String type, @Nonnull BigDecimal total) {
        this.type = Objects.requireNonNull(type, "type");
        this.total = Objects.requireNonNull(total, "total");
    }

    @Nonnull
    public abstract BigDecimal getAllocated();

    @Nonnull
    public abstract BigDecimal getUnallocated();

    @Nonnull
    public abstract AllocationState allocate(@Nonnull BigDecimal amount);

    @Nonnull
    public abstract AllocationState deallocate(@Nonnull BigDecimal amount);

    @Nonnull
    public static AllocationState create(@Nonnull BigDecimal amount) {
        ensurePositiveOrZero(amount);
        return new Unallocated(amount);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof AllocationState allocation)) return false;
        return Objects.equals(getType(), allocation.getType())
            && Objects.equals(getTotal(), allocation.getTotal())
            && Objects.equals(getAllocated(), allocation.getAllocated())
            && Objects.equals(getUnallocated(), allocation.getUnallocated());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getType(), getTotal(), getAllocated(), getUnallocated());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("type", getType())
            .add("total", getTotal())
            .add("allocated", getAllocated())
            .add("unallocated", getUnallocated())
            .toString();
    }

    protected static void ensurePositiveOrZero(@Nonnull BigDecimal amount) {
        final var comparison = amount.compareTo(BigDecimal.ZERO);
        if (comparison < 0) {
            throw new IllegalStateException("Amount is required to be positive or 0.");
        }
    }

    protected static void ensurePositive(@Nonnull BigDecimal amount) {
        final var comparison = amount.compareTo(BigDecimal.ZERO);
        if (comparison <= 0) {
            throw new IllegalStateException("Amount is required to be positive and greater than 0.");
        }
    }
}