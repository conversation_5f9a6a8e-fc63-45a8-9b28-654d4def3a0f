package global.symbio.billing.core.payment.persistence.api.transfer;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.country.persistence.api.Country;
import global.symbio.billing.core.country.persistence.api.CountryDataAccessObject;
import global.symbio.billing.core.currency.persistence.api.Currency;
import global.symbio.billing.core.currency.persistence.api.CurrencyDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AccountAllocationDataAccessObject;
import global.symbio.billing.core.persistence.api.DataAccessObject;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Objects;
import java.util.UUID;

public abstract class TransferDataAccessObject implements DataAccessObject<UUID, TransferDataAccessObject> {

    @Nonnull
    public abstract AccountAllocationDataAccessObject getTransferIn();

    @Nonnull
    public abstract TransferDataAccessObject transferIn(@Nonnull AccountAllocationDataAccessObject<?> transferIn);

    @Nonnull
    public abstract AccountAllocationDataAccessObject getTransferOut();

    @Nonnull
    public abstract TransferDataAccessObject transferOut(@Nonnull AccountAllocationDataAccessObject<?> transferOut);

    @Nonnull
    public abstract CurrencyDataAccessObject getCurrency();

    @Nonnull
    public abstract TransferDataAccessObject currency(@Nonnull CurrencyDataAccessObject currency);

    @Nonnull
    public TransferDataAccessObject currency(@Nonnull Currency currency) {
        return currency(currency.data());
    }

    @Nonnull
    public abstract BigDecimal getAmount();

    @Nonnull
    public abstract TransferDataAccessObject amount(@Nonnull BigDecimal amount);

    @Nullable
    public abstract String getDescription();

    @Nonnull
    public abstract TransferDataAccessObject description(@Nullable String description);

    @Nonnull
    public abstract CountryDataAccessObject getCountry();

    @Nonnull
    public abstract TransferDataAccessObject country(@Nonnull CountryDataAccessObject country);

    @Nonnull
    public TransferDataAccessObject country(@Nonnull Country country) {
        return country(country.data());
    }

    @Nonnull
    public abstract AccountAllocationDataAccessObject getOrigin();

    @Nonnull
    public abstract TransferDataAccessObject origin(@Nonnull AccountAllocationDataAccessObject<?> origin);

    @Nonnull
    public abstract ZonedDateTime getTimestamp();

    @Nonnull
    public abstract TransferDataAccessObject timestamp(@Nonnull ZonedDateTime timestamp);

    @Nonnull
    @Override
    public Transfer entity() {
        return new Transfer(this);
    }

    @Override
    public boolean equals(Object other) {
        if (this == other) return true;
        if (!(other instanceof TransferDataAccessObject transfer)) return false;
        return Objects.equals(getIdentifier(), transfer.getIdentifier())
            && Objects.equals(getTransferOut(), transfer.getTransferOut())
            && Objects.equals(getTransferIn(), transfer.getTransferIn())
            && Objects.equals(getCurrency(), transfer.getCurrency())
            && Objects.equals(getAmount(), transfer.getAmount())
            && Objects.equals(getDescription(), transfer.getDescription())
            && Objects.equals(getCountry(), transfer.getCountry())
            && Objects.equals(getOrigin(), transfer.getOrigin())
            && Objects.equals(getTimestamp(), transfer.getTimestamp());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getIdentifier(), getTransferOut(), getTransferIn(), getCurrency(), getAmount(), getDescription(), getCountry(), getOrigin(), getTimestamp());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("identifier", getIdentifier())
            .add("transferOut", getTransferOut())
            .add("transferIn", getTransferIn())
            .add("currency", getCurrency())
            .add("amount", getAmount())
            .add("description", getDescription())
            .add("country", getCountry())
            .add("origin", getOrigin())
            .add("timestamp", getTimestamp())
            .toString();
    }
}