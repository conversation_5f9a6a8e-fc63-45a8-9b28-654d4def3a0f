package global.symbio.billing.core.payment.persistence.api.payment.method;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.persistence.api.DataAccessObject;
import jakarta.annotation.Nonnull;

import java.util.Objects;

public abstract class PaymentMethodDataAccessObject implements DataAccessObject<Integer, PaymentMethodDataAccessObject> {

    @Nonnull
    public abstract String getName();

    @Nonnull
    public abstract PaymentMethodDataAccessObject name(@Nonnull String name);

    @Nonnull
    public abstract String getDisplayName();

    @Nonnull
    public abstract PaymentMethodDataAccessObject displayName(@Nonnull String name);

    @Nonnull
    @Override
    public PaymentMethod entity() {
        return new PaymentMethod(this);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof PaymentMethodDataAccessObject method)) return false;
        return Objects.equals(getIdentifier(), method.getIdentifier()) && Objects.equals(getName(), method.getName()) && Objects.equals(getDisplayName(), method.getDisplayName());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getIdentifier(), getName(), getDisplayName());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this).add("identifier", getIdentifier()).add("name", getName()).add("displayName", getDisplayName()).toString();
    }
}
