package global.symbio.billing.core.payment.persistence.api.allocation.invoice;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.persistence.api.DataAccessObject;
import jakarta.annotation.Nonnull;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Objects;

public abstract class InvoiceAllocationDataAccessObject implements DataAccessObject<InvoiceAllocationIdentifier, InvoiceAllocationDataAccessObject> {

    @Nonnull
    public abstract BigDecimal getAmount();

    @Nonnull
    public abstract InvoiceAllocationDataAccessObject amount(@Nonnull BigDecimal amount);

    @Nonnull
    public abstract ZonedDateTime getLastUpdated();

    @Nonnull
    public abstract InvoiceAllocationDataAccessObject lastUpdated(@Nonnull ZonedDateTime lastUpdated);

    @Nonnull
    public abstract ZonedDateTime getTimestamp();

    @Nonnull
    public abstract InvoiceAllocationDataAccessObject timestamp(@Nonnull ZonedDateTime timestamp);

    @Nonnull
    @Override
    public InvoiceAllocation entity() {
        return new InvoiceAllocation(this);
    }

    @Override
    public boolean equals(Object other) {
        if (this == other) return true;
        if (!(other instanceof InvoiceAllocationDataAccessObject invoiceAllocation)) return false;
        return Objects.equals(getIdentifier(), invoiceAllocation.getIdentifier())
            && Objects.equals(getAmount(), invoiceAllocation.getAmount())
            && Objects.equals(getLastUpdated(), invoiceAllocation.getLastUpdated())
            && Objects.equals(getTimestamp(), invoiceAllocation.getTimestamp());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getIdentifier(), getAmount(), getLastUpdated(), getTimestamp());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("identifier", getIdentifier())
            .add("amount", getAmount())
            .add("lastUpdated", getLastUpdated())
            .add("timestamp", getTimestamp())
            .toString();
    }
}
