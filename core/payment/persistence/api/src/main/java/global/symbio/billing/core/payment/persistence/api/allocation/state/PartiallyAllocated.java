package global.symbio.billing.core.payment.persistence.api.allocation.state;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.annotation.Nonnull;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.Objects;

@Getter
public final class PartiallyAllocated extends AllocationState {

    public static final String TYPE = "PARTIALLY_ALLOCATED";

    @Nonnull
    private final BigDecimal allocated;

    @Nonnull
    private final BigDecimal unallocated;

    @JsonCreator(mode = JsonCreator.Mode.PROPERTIES)
    public PartiallyAllocated(
        @Nonnull @JsonProperty("total") BigDecimal total,
        @Nonnull @JsonProperty("allocated") BigDecimal allocated,
        @Nonnull @JsonProperty("unallocated") BigDecimal unallocated
    ) {
        super(TYPE, total);
        this.allocated = Objects.requireNonNull(allocated, "allocated");
        this.unallocated = Objects.requireNonNull(unallocated, "unallocated");
    }

    @Nonnull
    @Override
    public AllocationState allocate(@Nonnull BigDecimal amount) {
        ensurePositive(amount);
        final var allocation = amount.compareTo(getUnallocated());
        if (allocation > 0) {
            throw new IllegalStateException("Unable to create an allocation greater than the unallocated amount. Unallocated: " + getUnallocated() + ", Requested: " + amount + ".");
        } else if (allocation == 0) {
            return new Allocated(total);
        }
        return new PartiallyAllocated(total, getAllocated().add(amount), getUnallocated().subtract(amount));
    }

    @Nonnull
    @Override
    public AllocationState deallocate(@Nonnull BigDecimal amount) {
        ensurePositive(amount);
        final var deallocation = amount.compareTo(getAllocated());
        if (deallocation > 0) {
            throw new IllegalStateException("Unable to deallocate more than what has been allocated. Allocated: " + getAllocated() + ", Requested: " + amount + ".");
        } else if (deallocation == 0) {
            return new Unallocated(total);
        }
        return new PartiallyAllocated(total, getAllocated().subtract(amount), getUnallocated().add(amount));
    }
}