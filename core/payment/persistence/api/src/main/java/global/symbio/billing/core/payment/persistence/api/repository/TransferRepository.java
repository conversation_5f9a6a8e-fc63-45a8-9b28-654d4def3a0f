package global.symbio.billing.core.payment.persistence.api.repository;

import global.symbio.billing.core.payment.persistence.api.transfer.TransferDataAccessObject;
import io.micronaut.context.annotation.Executable;
import io.micronaut.data.repository.CrudRepository;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.Optional;
import java.util.UUID;

public interface TransferRepository<T extends TransferDataAccessObject> extends CrudRepository<T, UUID> {

    @Nonnull
    @Executable
    Optional<T> findByTransferInIdentifier(@Nonnull UUID identifier);

    @Nonnull
    @Executable
    Collection<T> getTransfers(
        @Nonnull UUID account,
        @Nullable ZonedDateTime transferDateStart,
        @Nullable ZonedDateTime transferDateEnd
    );
}