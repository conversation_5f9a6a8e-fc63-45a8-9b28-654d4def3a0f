package global.symbio.billing.core.payment.persistence.api.repository;

import global.symbio.billing.core.payment.persistence.api.cancellation.PaymentCancellationDataAccessObject;
import io.micronaut.context.annotation.Executable;
import io.micronaut.data.repository.CrudRepository;
import jakarta.annotation.Nonnull;

import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.Optional;
import java.util.UUID;

public interface PaymentCancellationRepository<T extends PaymentCancellationDataAccessObject> extends CrudRepository<T, UUID> {

    @Nonnull
    @Executable
    Optional<T> findByPaymentIdentifier(@Nonnull UUID identifier);

    @Nonnull
    @Executable
    Collection<T> getPaymentCancellationsByTimestampBetweenAndCountryEquals(
        @Nonnull ZonedDateTime start,
        @Nonnull ZonedDateTime end,
        @Nonnull Integer country);
}