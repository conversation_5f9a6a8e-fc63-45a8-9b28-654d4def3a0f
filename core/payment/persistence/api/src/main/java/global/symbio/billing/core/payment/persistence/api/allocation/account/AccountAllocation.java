package global.symbio.billing.core.payment.persistence.api.allocation.account;

import global.symbio.billing.core.country.persistence.api.Country;
import global.symbio.billing.core.currency.persistence.api.Currency;
import global.symbio.billing.core.payment.persistence.api.allocation.state.AllocationState;
import global.symbio.billing.core.persistence.api.Entity;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.UUID;

public abstract class AccountAllocation<T extends AccountAllocationDataAccessObject<T>> extends Entity<UUID, T> {

    protected AccountAllocation(@Nonnull T data) {
        super(data);
    }

    @Nonnull
    public UUID getAccount() {
        return data().getAccount();
    }

    @Nonnull
    public AllocationType getType() {
        return data().getType();
    }

    @Nonnull
    public Country getCountry() {
        return unwrap(data().getCountry());
    }

    @Nonnull
    public Currency getCurrency() {
        return unwrap(data().getCurrency());
    }

    @Nonnull
    public BigDecimal getAmount() {
        return data().getAmount();
    }

    @Nonnull
    public AllocationState getAllocationState() {
        //TODO: rename to `getState`
        return data().getAllocationState();
    }

    @Nullable
    public ZonedDateTime getSentTimestamp() {
        return data().getSentTimestamp();
    }

    @Nullable
    public ZonedDateTime getSettledTimestamp() {
        return data().getSettledTimestamp();
    }

    @Nonnull
    public AllocationDirection getDirection() {
        return data().getDirection();
    }

    @Nonnull
    public ZonedDateTime getTimestamp() {
        return data().getTimestamp();
    }
}