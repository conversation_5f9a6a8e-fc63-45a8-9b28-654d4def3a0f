package global.symbio.billing.core.payment.persistence.api.cancellation;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.payment.persistence.api.payment.PaymentDataAccessObject;
import global.symbio.billing.core.persistence.api.DataAccessObject;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;
import java.util.Objects;
import java.util.UUID;

public abstract class PaymentCancellationDataAccessObject implements DataAccessObject<UUID, PaymentCancellationDataAccessObject> {

    @Nonnull
    public abstract PaymentDataAccessObject getPayment();

    @Nonnull
    public abstract PaymentCancellationDataAccessObject payment(@Nonnull PaymentDataAccessObject payment);

    @Nonnull
    public abstract String getReason();

    @Nonnull
    public abstract PaymentCancellationDataAccessObject reason(@Nonnull String reason);

    @Nonnull
    public abstract String getReference();

    @Nonnull
    public abstract PaymentCancellationDataAccessObject reference(@Nonnull String reference);

    @Nullable
    public abstract ZonedDateTime getSentTimestamp();

    @Nonnull
    public abstract PaymentCancellationDataAccessObject sentTimestamp(@Nullable ZonedDateTime sentTimestamp);

    @Nullable
    public abstract ZonedDateTime getSettledTimestamp();

    @Nonnull
    public abstract PaymentCancellationDataAccessObject settledTimestamp(@Nullable ZonedDateTime settledTimestamp);

    @Nonnull
    public abstract ZonedDateTime getTimestamp();

    @Nonnull
    public abstract PaymentCancellationDataAccessObject timestamp(@Nonnull ZonedDateTime timestamp);

    @Nonnull
    @Override
    public PaymentCancellation entity() {
        return new PaymentCancellation(this);
    }

    @Override
    public boolean equals(Object other) {
        if (this == other) return true;
        if (!(other instanceof PaymentCancellationDataAccessObject paymentCancellation)) return false;
        return Objects.equals(getIdentifier(), paymentCancellation.getIdentifier())
            && Objects.equals(getPayment(), paymentCancellation.getPayment())
            && Objects.equals(getReason(), paymentCancellation.getReason())
            && Objects.equals(getReference(), paymentCancellation.getReference())
            && Objects.equals(getSentTimestamp(), paymentCancellation.getSentTimestamp())
            && Objects.equals(getSettledTimestamp(), paymentCancellation.getSettledTimestamp())
            && Objects.equals(getTimestamp(), paymentCancellation.getTimestamp());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getIdentifier(), getPayment(), getReason(), getReference(), getSentTimestamp(), getSettledTimestamp(), getTimestamp());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("identifier", getIdentifier())
            .add("payment", getPayment())
            .add("reason", getReason())
            .add("reference", getReference())
            .add("sentTimestamp", getSentTimestamp())
            .add("settledTimestamp", getSettledTimestamp())
            .add("timestamp", getTimestamp())
            .toString();
    }
}