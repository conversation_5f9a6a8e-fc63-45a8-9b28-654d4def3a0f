package global.symbio.billing.core.payment.persistence.api.transfer;

import global.symbio.billing.core.country.persistence.api.Country;
import global.symbio.billing.core.currency.persistence.api.Currency;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AccountAllocation;
import global.symbio.billing.core.persistence.api.Entity;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.UUID;

public class Transfer extends Entity<UUID, TransferDataAccessObject> {

    public Transfer(@Nonnull TransferDataAccessObject data) {
        super(data);
    }

    @Nonnull
    public AccountAllocation<?> getTransferIn() {
        return (AccountAllocation<?>) unwrap(data().getTransferIn());
    }

    @Nonnull
    public AccountAllocation<?> getTransferOut() {
        return (AccountAllocation<?>) unwrap(data().getTransferOut());
    }

    @Nonnull
    public Currency getCurrency() {
        return unwrap(data().getCurrency());
    }

    @Nonnull
    public BigDecimal getAmount() {
        return data().getAmount();
    }

    @Nullable
    public String getDescription() {
        return data().getDescription();
    }

    @Nonnull
    public Country getCountry() {
        return unwrap(data().getCountry());
    }

    @Nonnull
    public AccountAllocation<?> getOrigin() {
        return (AccountAllocation<?>) unwrap(data().getOrigin());
    }

    @Nonnull
    public ZonedDateTime getTimestamp() {
        return data().getTimestamp();
    }
}
