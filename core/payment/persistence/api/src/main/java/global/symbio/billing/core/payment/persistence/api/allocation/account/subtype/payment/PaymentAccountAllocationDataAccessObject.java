package global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.payment;

import global.symbio.billing.core.payment.persistence.api.allocation.account.AccountAllocationDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.payment.Payment;
import global.symbio.billing.core.payment.persistence.api.payment.PaymentDataAccessObject;
import jakarta.annotation.Nonnull;

public interface PaymentAccountAllocationDataAccessObject<T extends PaymentAccountAllocationDataAccessObject<T>> extends AccountAllocationDataAccessObject<T> {

    @Nonnull
    PaymentDataAccessObject getPayment();

    @Nonnull
    PaymentAccountAllocationDataAccessObject<T> payment(@Nonnull PaymentDataAccessObject payment);

    @Nonnull
    default PaymentAccountAllocationDataAccessObject<T> payment(@Nonnull Payment payment) {
        return payment(payment.data());
    }

    @Nonnull
    @Override
    default PaymentAccountAllocation<T> entity() {
        return new PaymentAccountAllocation<>((T) this);
    }
}