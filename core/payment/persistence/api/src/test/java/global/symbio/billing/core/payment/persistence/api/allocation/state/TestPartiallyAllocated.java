package global.symbio.billing.core.payment.persistence.api.allocation.state;

import com.fasterxml.jackson.core.JsonProcessingException;
import global.symbio.billing.core.persistence.api.DeterministicObjectMapper;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;

public class TestPartiallyAllocated {

    private PartiallyAllocated state;

    @BeforeEach
    public void setup() {
        state = new PartiallyAllocated(BigDecimal.TEN, BigDecimal.TEN.subtract(BigDecimal.ONE), BigDecimal.ONE);
    }

    @Test
    @DisplayName("PartiallyAllocated is subclass of AllocationState")
    public void partially_allocated_is_subtype_of_allocation_state() {
        assertInstanceOf(PartiallyAllocated.class, state);
        assertInstanceOf(AllocationState.class, state);
    }

    @Test
    @DisplayName("PartiallyAllocated#TYPE equals PARTIALLY_ALLOCATED and PartiallyAllocated#type equals PartiallyAllocated#TYPE")
    public void partially_allocated_type_equals_partially_allocated() {
        assertSame("PARTIALLY_ALLOCATED", PartiallyAllocated.TYPE);
        assertEquals(PartiallyAllocated.TYPE, state.getType());
        assertSame(PartiallyAllocated.TYPE, state.getType());
    }

    @Test
    @DisplayName("PartiallyAllocated::allocate greater than unallocated throws IllegalStateException")
    public void partially_allocated_allocate_greater_than_unallocated_throws_illegal_state_exception() {
        final var amount = BigDecimal.TEN.multiply(BigDecimal.TEN);
        assertEquals(BigDecimal.ONE, state.getUnallocated());
        final var cause = assertThrows(IllegalStateException.class, () -> state.allocate(amount));
        final var error = "Unable to create an allocation greater than the unallocated amount. Unallocated: " + state.getUnallocated() + ", Requested: " + amount + ".";
        assertEquals(error, cause.getMessage());
    }

    @Test
    @DisplayName("PartiallyAllocated::allocate full allocation transitions to Allocated state")
    public void partially_allocated_allocate_full_allocation_transitions_to_allocated_state() {
        assertEquals(BigDecimal.ONE, state.getUnallocated());
        final var allocated = assertDoesNotThrow(() -> state.allocate(state.getUnallocated()));
        assertInstanceOf(Allocated.class, allocated);
        assertEquals(allocated.getUnallocated(), BigDecimal.ZERO);
        assertEquals(allocated.getAllocated(), state.getTotal());
    }

    @Test
    @DisplayName("PartiallyAllocated::allocate less than unallocated transitions to PartiallyAllocated state")
    public void partially_allocated_allocate_less_than_unallocated_transitions_to_partially_allocated_state() {
        final var amount = new BigDecimal("0.5");
        assertEquals(BigDecimal.ONE, state.getUnallocated());
        final var partial = assertDoesNotThrow(() -> state.allocate(amount));
        assertInstanceOf(PartiallyAllocated.class, partial);
        assertEquals(partial.getUnallocated(), state.getUnallocated().subtract(amount));
        assertEquals(partial.getAllocated(), state.getAllocated().add(amount));
    }

    @ParameterizedTest
    @CsvSource(value = { "-10", "-1", "0" })
    @DisplayName("PartiallyAllocated::allocate negative or zero amount throws IllegalStateException")
    public void partially_allocated_allocate_negative_or_zero_amount_throws_illegal_state_exception(@Nonnull BigDecimal amount) {
        final var cause = assertThrows(IllegalStateException.class, () -> state.allocate(amount));
        assertEquals("Amount is required to be positive and greater than 0.", cause.getMessage());
    }

    @Test
    @DisplayName("PartiallyAllocated::deallocate greater than allocated throws IllegalStateException")
    public void partially_allocated_deallocate_greater_than_allocated_throws_illegal_state_exception() {
        final var amount = BigDecimal.TEN.multiply(BigDecimal.TEN);
        assertEquals(BigDecimal.TEN.subtract(BigDecimal.ONE), state.getAllocated());
        final var cause = assertThrows(IllegalStateException.class, () -> state.deallocate(amount));
        final var error = "Unable to deallocate more than what has been allocated. Allocated: " + state.getAllocated() + ", Requested: " + amount + ".";
        assertEquals(error, cause.getMessage());
    }

    @Test
    @DisplayName("PartiallyAllocated::deallocate allocated transitions to Unallocated state")
    public void partially_allocated_deallocate_allocated_transitions_to_unallocated_state() {
        assertEquals(BigDecimal.TEN.subtract(BigDecimal.ONE), state.getAllocated());
        final var unallocated = assertDoesNotThrow(() -> state.deallocate(state.getAllocated()));
        assertInstanceOf(Unallocated.class, unallocated);
        assertEquals(unallocated.getUnallocated(), state.getTotal());
        assertEquals(unallocated.getAllocated(), BigDecimal.ZERO);
    }

    @Test
    @DisplayName("PartiallyAllocated::deallocate less than allocated transitions to PartiallyAllocated state")
    public void partially_allocated_deallocate_less_than_allocated_transitions_to_partially_allocated_state() {
        final var amount = new BigDecimal("0.5");
        assertEquals(BigDecimal.ONE, state.getUnallocated());
        final var partial = assertDoesNotThrow(() -> state.deallocate(amount));
        assertInstanceOf(PartiallyAllocated.class, partial);
        assertEquals(partial.getUnallocated(), state.getUnallocated().add(amount));
        assertEquals(partial.getAllocated(), state.getAllocated().subtract(amount));
    }

    @ParameterizedTest
    @CsvSource(value = { "-10", "-1", "0" })
    @DisplayName("PartiallyAllocated::deallocate negative or zero amount throws IllegalStateException")
    public void partially_allocated_deallocate_negative_or_zero_amount_throws_illegal_state_exception(@Nonnull BigDecimal amount) {
        final var cause = assertThrows(IllegalStateException.class, () -> state.deallocate(amount));
        assertEquals("Amount is required to be positive and greater than 0.", cause.getMessage());
    }

    @Test
    @DisplayName("PartiallyAllocated state JSON serialisation and deserialisation functions correctly")
    public void partially_allocated_json_serialisation_and_deserialisation() throws JsonProcessingException {
        final var mapper = DeterministicObjectMapper.get();
        final var input = "{\"allocated\":0,\"total\":0,\"type\":\"" + PartiallyAllocated.TYPE + "\",\"unallocated\":0}";

        final var base = assertInstanceOf(PartiallyAllocated.class, mapper.readValue(input, AllocationState.class));
        final var impl = assertInstanceOf(PartiallyAllocated.class, mapper.readValue(input, PartiallyAllocated.class));

        assertEquals(base, impl);

        final var output = mapper.writeValueAsString(impl);
        assertEquals(input, output);
    }
}
