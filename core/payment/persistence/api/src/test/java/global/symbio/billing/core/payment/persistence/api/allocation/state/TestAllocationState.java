package global.symbio.billing.core.payment.persistence.api.allocation.state;

import com.fasterxml.jackson.core.JsonProcessingException;
import global.symbio.billing.core.persistence.api.DeterministicObjectMapper;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import java.lang.reflect.Modifier;
import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.junit.jupiter.api.Assertions.assertInstanceOf;

public class TestAllocationState {

    @Test
    @DisplayName("AllocationState is abstract class.")
    public void allocation_state_is_abstract_class() {
        assertTrue(Modifier.isAbstract(AllocationState.class.getModifiers()));
    }

    @Test
    @DisplayName("AllocationState is sealed class.")
    public void allocation_state_is_sealed_class() {
        assertTrue(AllocationState.class.isSealed());
    }

    @Test
    @DisplayName("AllocationState only permits Unallocated, Allocated and PartiallyAllocated implementations.")
    public void allocation_state_only_permits_unallocated_allocated_partially_allocated_subtypes() {
        final var permitted = AllocationState.class.getPermittedSubclasses();
        final var expected = new Class[]{ Unallocated.class, PartiallyAllocated.class, Allocated.class  };
        assertArrayEquals(expected, permitted);
    }

    @ParameterizedTest
    @ValueSource(strings = {"10", "0"})
    @DisplayName("AllocationState::create creates an `Unallocated` AllocationState for the given amount")
    public void allocation_state_create_instantiates_unallocated_allocation_state_for_given_amount(@Nonnull String input) {
        final var amount = new BigDecimal(input);
        final var state = assertInstanceOf(Unallocated.class, assertDoesNotThrow(() -> AllocationState.create(amount)));
        assertEquals(amount, state.getUnallocated());
    }

    @Test
    @DisplayName("AllocationState::create throws IllegalStateException given negative amount")
    public void allocation_state_create_throws_illegal_state_exception_given_negative_amount() {
        final var amount = BigDecimal.TEN.negate();
        final var cause = assertThrows(IllegalStateException.class, () -> AllocationState.create(amount));
        assertEquals(cause.getMessage(), "Amount is required to be positive or 0.");
    }

    @ParameterizedTest
    @ValueSource(strings = {"10", "50"})
    @DisplayName("AllocationState:: transitions from unallocated to allocated and back")
    public void allocation_state_transitions_from_unallocated_to_allocated(@Nonnull String input) {
        final var amount = new BigDecimal(input);
        AllocationState state = assertInstanceOf(Unallocated.class, assertDoesNotThrow(() -> AllocationState.create(amount)));
        state = state.allocate(amount);

        assertInstanceOf(Allocated.class, state);
        assertEquals(amount, state.getAllocated());

        state = state.deallocate(amount);
        assertInstanceOf(Unallocated.class, state);
        assertEquals(amount, state.getUnallocated());
    }

    @ParameterizedTest
    @ValueSource(strings = {"3", "5"})
    @DisplayName("AllocationState:: transitions from unallocated to partially allocated and back")
    public void allocation_state_transitions_from_unallocated_to_partially_allocated(@Nonnull String input) {
        final var amount = BigDecimal.TEN;
        final var partialAllocationAmount = new BigDecimal(input);
        AllocationState state = assertInstanceOf(Unallocated.class, assertDoesNotThrow(() -> AllocationState.create(amount)));
        state = state.allocate(partialAllocationAmount);

        assertInstanceOf(PartiallyAllocated.class, state);
        assertEquals(amount.subtract(partialAllocationAmount), state.getUnallocated());
        assertEquals(partialAllocationAmount, state.getAllocated());

        state = state.deallocate(partialAllocationAmount);
        assertInstanceOf(Unallocated.class, state);
        assertEquals(amount, state.getUnallocated());
    }
}
