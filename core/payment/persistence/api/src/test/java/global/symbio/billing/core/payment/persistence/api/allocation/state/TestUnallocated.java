package global.symbio.billing.core.payment.persistence.api.allocation.state;

import com.fasterxml.jackson.core.JsonProcessingException;
import global.symbio.billing.core.persistence.api.DeterministicObjectMapper;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;

public class TestUnallocated {

    private Unallocated state;

    @BeforeEach
    public void setup() {
        state = new Unallocated(BigDecimal.ONE);
    }

    @Test
    @DisplayName("Unallocated is subclass of AllocationState")
    public void unallocated_is_subtype_of_allocation_state() {
        assertInstanceOf(Unallocated.class, state);
        assertInstanceOf(AllocationState.class, state);
    }

    @Test
    @DisplayName("Unallocated#TYPE equals UNALLOCATED and Unallocated#type equals Unallocated#TYPE")
    public void unallocated_type_equals_unallocated() {
        assertSame("UNALLOCATED", Unallocated.TYPE);
        assertEquals(Unallocated.TYPE, state.getType());
        assertSame(Unallocated.TYPE, state.getType());
    }

    @Test
    @DisplayName("Unallocated::allocate with allocation greater than unallocated throws IllegalStateException")
    public void unallocated_allocate_with_allocation_greater_than_allocated_throws_illegal_state_exception() {
        final var cause = assertThrows(IllegalStateException.class, () -> state.allocate(BigDecimal.TEN));
        final var error = "Unable to create an allocation greater than the unallocated amount. Unallocated: " + state.getUnallocated() + ", Requested: " + BigDecimal.TEN + ".";
        assertEquals(error, cause.getMessage());
    }

    @Test
    @DisplayName("Unallocated::deallocate throws IllegalStateException")
    public void unallocated_deallocate_throws_illegal_state_exception() {
        final var cause = assertThrows(IllegalStateException.class, () -> state.deallocate(BigDecimal.TEN));
        assertEquals("Unable to deallocate an unallocated account allocation.", cause.getMessage());
    }

    @Test
    @DisplayName("Unallocated::allocate full allocation transitions to Allocated state")
    public void unallocated_allocate_full_allocation_transitions_to_allocated_state() {
        assertEquals(BigDecimal.ONE, state.getUnallocated());
        final var allocated = assertDoesNotThrow(() -> state.allocate(state.getUnallocated()));
        assertInstanceOf(Allocated.class, allocated);
        assertEquals(allocated.getUnallocated(), BigDecimal.ZERO);
        assertEquals(allocated.getAllocated(), state.getTotal());
    }

    @Test
    @DisplayName("Unallocated::allocate partial allocation transitions to PartiallyAllocated state")
    public void unallocated_allocate_partial_allocation_transitions_to_partially_allocated_state() {
        final var amount = new BigDecimal("0.5");
        assertEquals(BigDecimal.ONE, state.getUnallocated());
        final var partial = assertDoesNotThrow(() -> state.allocate(amount));
        assertInstanceOf(PartiallyAllocated.class, partial);
        assertEquals(partial.getUnallocated(), state.getUnallocated().subtract(amount));
        assertEquals(partial.getAllocated(), state.getAllocated().add(amount));
    }

    @ParameterizedTest
    @CsvSource(value = { "-10", "-1", "0" })
    @DisplayName("Unallocated::allocate negative or zero amount throws IllegalStateException")
    public void uallocated_allocate_negative_or_zero_amount_throws_illegal_state_exception(@Nonnull BigDecimal amount) {
        final var cause = assertThrows(IllegalStateException.class, () -> state.allocate(amount));
        assertEquals("Amount is required to be positive and greater than 0.", cause.getMessage());
    }

    @Test
    @DisplayName("Unallocated state JSON serialisation and deserialisation functions correctly")
    public void unallocated_json_serialisation_and_deserialisation() throws JsonProcessingException {
        final var mapper = DeterministicObjectMapper.get();
        final var input = "{\"allocated\":0,\"total\":0,\"type\":\"" + Unallocated.TYPE + "\",\"unallocated\":0}";

        final var base = assertInstanceOf(Unallocated.class, mapper.readValue(input, AllocationState.class));
        final var impl = assertInstanceOf(Unallocated.class, mapper.readValue(input, Unallocated.class));

        assertEquals(base, impl);

        final var output = mapper.writeValueAsString(impl);
        assertEquals(input, output);
    }
}

