package global.symbio.billing.core.payment.persistence.api.allocation.state;

import com.fasterxml.jackson.core.JsonProcessingException;
import global.symbio.billing.core.persistence.api.DeterministicObjectMapper;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;

public class TestAllocated {

    private Allocated state;

    @BeforeEach
    public void setUp() {
        state = new Allocated(BigDecimal.TEN);
    }

    @Test
    @DisplayName("Allocated is subclass of AllocationState")
    public void allocated_is_subtype_of_allocation_state() {
        assertInstanceOf(Allocated.class, state);
        assertInstanceOf(AllocationState.class, state);
    }

    @Test
    @DisplayName("Allocated#TYPE equals ALLOCATED and Allocated#type equals Allocated#TYPE")
    public void allocated_type_equals_allocated() {
        assertSame("ALLOCATED", Allocated.TYPE);
        assertEquals(Allocated.TYPE, state.getType());
        assertSame(Allocated.TYPE, state.getType());
    }

    @Test
    @DisplayName("Allocated::allocate throws IllegalStateException")
    public void allocated_allocate_throws_illegal_state_exception() {
        final var cause = assertThrows(IllegalStateException.class, () -> state.allocate(BigDecimal.TEN));
        assertEquals("Unable to allocate any more funds.", cause.getMessage());
    }

    @ParameterizedTest
    @CsvSource(value = { "-10", "-1", "0" })
    @DisplayName("Allocated::allocate negative or zero amount throws IllegalStateException")
    public void allocate_allocate_negative_or_zero_amount_throws_illegal_state_exception(@Nonnull BigDecimal amount) {
        final var cause = assertThrows(IllegalStateException.class, () -> state.allocate(amount));
        assertEquals("Amount is required to be positive and greater than 0.", cause.getMessage());
    }

    @Test
    @DisplayName("Allocated::deallocate greater than allocated throws IllegalStateException")
    public void allocated_deallocate_greater_than_allocated_throws_illegal_state_exception() {
        final var amount = BigDecimal.TEN.multiply(BigDecimal.TEN);
        assertEquals(state.getAllocated(), BigDecimal.TEN);
        final var cause = assertThrows(IllegalStateException.class, () -> state.deallocate(amount));
        final var error = "Unable to deallocate more than what has been allocated. Allocated: " + BigDecimal.TEN + ", Requested: " + amount + ".";
        assertEquals(error, cause.getMessage());
    }

    @Test
    @DisplayName("Allocated::deallocate full allocation transitions to Unallocated state")
    public void allocated_deallocate_full_allocation_transitions_to_unallocated_state() {
        assertEquals(state.getAllocated(), BigDecimal.TEN);
        final var unallocated = assertDoesNotThrow(() -> state.deallocate(state.getAllocated()));
        assertInstanceOf(Unallocated.class, unallocated);
        assertEquals(unallocated.getUnallocated(), state.getAllocated());
        assertEquals(unallocated.getAllocated(), BigDecimal.ZERO);
    }

    @Test
    @DisplayName("Allocated::deallocate less than allocated transitions to PartiallyAllocated state")
    public void allocated_deallocate_less_than_allocated_throws_transitions_to_partially_allocated_state() {
        final var amount = BigDecimal.ONE;
        assertEquals(state.getAllocated(), BigDecimal.TEN);
        final var partial = assertDoesNotThrow(() -> state.deallocate(amount));
        assertInstanceOf(PartiallyAllocated.class, partial);
        assertEquals(partial.getUnallocated(), amount);
        assertEquals(partial.getAllocated(), state.getAllocated().subtract(amount));
    }

    @ParameterizedTest
    @CsvSource(value = { "-10", "-1", "0" })
    @DisplayName("Allocated::deallocate negative or zero amount throws IllegalStateException")
    public void allocate_deallocate_negative_or_zero_amount_throws_illegal_state_exception(@Nonnull BigDecimal amount) {
        final var cause = assertThrows(IllegalStateException.class, () -> state.deallocate(amount));
        assertEquals("Amount is required to be positive and greater than 0.", cause.getMessage());
    }

    @Test
    @DisplayName("Allocated state JSON serialisation and deserialisation functions correctly")
    public void allocated_json_serialisation_and_deserialisation() throws JsonProcessingException {
        final var mapper = DeterministicObjectMapper.get();
        final var input = "{\"allocated\":0,\"total\":0,\"type\":\"" + Allocated.TYPE + "\",\"unallocated\":0}";

        final var base = assertInstanceOf(Allocated.class, mapper.readValue(input, AllocationState.class));
        final var impl = assertInstanceOf(Allocated.class, mapper.readValue(input, Allocated.class));

        assertEquals(base, impl);

        final var output = mapper.writeValueAsString(impl);
        assertEquals(input, output);
    }
}
