package global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.repository;

import global.symbio.billing.core.payment.persistence.api.repository.PaymentRepository;
import global.symbio.billing.core.persistence.api.Repositories;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

public class TestPaymentRepository {

    @Test
    @DisplayName("ReadOnlyPaymentRepository is connected to read-only datasource")
    public void test_read_only_repository() {
        Assertions.assertTrue(PaymentRepository.class.isAssignableFrom(ReadOnlyJPAPaymentRepository.class));
        Assertions.assertTrue(Repositories.isReadOnlyRepository(ReadOnlyJPAPaymentRepository.class));
    }

    @Test
    @DisplayName("ReadWritePaymentRepository is connected to read-write datasource")
    public void test_read_write_repository() {
        Assertions.assertTrue(PaymentRepository.class.isAssignableFrom(ReadWriteJPAPaymentRepository.class));
        Assertions.assertTrue(Repositories.isReadWriteRepository(ReadWriteJPAPaymentRepository.class));
    }
}