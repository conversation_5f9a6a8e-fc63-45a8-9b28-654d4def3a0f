package global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.repository;

import global.symbio.billing.core.payment.persistence.api.repository.PaymentCancellationRepository;
import global.symbio.billing.core.persistence.api.Repositories;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

public class TestPaymentCancellationRepository {

    @Test
    @DisplayName("ReadOnlyPaymentCancellationRepository is connected to read-only datasource")
    public void test_read_only_repository() {
        Assertions.assertTrue(PaymentCancellationRepository.class.isAssignableFrom(ReadOnlyJPAPaymentCancellationRepository.class));
        Assertions.assertTrue(Repositories.isReadOnlyRepository(ReadOnlyJPAPaymentCancellationRepository.class));
    }

    @Test
    @DisplayName("ReadWritePaymentCancellationRepository is connected to read-write datasource")
    public void test_read_write_repository() {
        Assertions.assertTrue(PaymentCancellationRepository.class.isAssignableFrom(ReadWriteJPAPaymentCancellationRepository.class));
        Assertions.assertTrue(Repositories.isReadWriteRepository(ReadWriteJPAPaymentCancellationRepository.class));
    }
}