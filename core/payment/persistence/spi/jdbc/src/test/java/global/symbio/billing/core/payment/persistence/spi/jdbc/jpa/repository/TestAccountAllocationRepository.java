package global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.repository;

import global.symbio.billing.core.payment.persistence.api.repository.AccountAllocationRepository;
import global.symbio.billing.core.persistence.api.Repositories;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

public class TestAccountAllocationRepository {

    @Test
    @DisplayName("ReadOnlyAccountAllocationRepository is connected to read-only datasource")
    public void test_read_only_repository() {
        Assertions.assertTrue(AccountAllocationRepository.class.isAssignableFrom(ReadOnlyJPAAccountAllocationRepository.class));
        Assertions.assertTrue(Repositories.isReadOnlyRepository(ReadOnlyJPAAccountAllocationRepository.class));
    }

    @Test
    @DisplayName("ReadWriteAccountAllocationRepository is connected to read-write datasource")
    public void test_read_write_repository() {
        Assertions.assertTrue(AccountAllocationRepository.class.isAssignableFrom(ReadWriteJPAAccountAllocationRepository.class));
        Assertions.assertTrue(Repositories.isReadWriteRepository(ReadWriteJPAAccountAllocationRepository.class));
    }
}