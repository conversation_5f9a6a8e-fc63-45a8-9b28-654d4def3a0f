package global.symbio.billing.core.payment.peresistence.spi.jdbc.jpa.payment.method;

import global.symbio.billing.core.payment.persistence.api.repository.PaymentMethodRepository;
import global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.repository.ReadOnlyJPAPaymentMethodRepository;
import global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.repository.ReadWriteJPAPaymentMethodRepository;
import global.symbio.billing.core.persistence.api.Repositories;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

public class TestPaymentMethodRepository {

    @Test
    @DisplayName("ReadOnlyPaymentMethodRepository is connected to read-only datasource")
    public void test_read_only_repository() {
        Assertions.assertTrue(PaymentMethodRepository.class.isAssignableFrom(ReadOnlyJPAPaymentMethodRepository.class));
        Assertions.assertTrue(Repositories.isReadOnlyRepository(ReadOnlyJPAPaymentMethodRepository.class));
    }

    @Test
    @DisplayName("ReadWritePaymentMethodRepository is connected to read-write datasource")
    public void test_read_write_repository() {
        Assertions.assertTrue(PaymentMethodRepository.class.isAssignableFrom(ReadWriteJPAPaymentMethodRepository.class));
        Assertions.assertTrue(Repositories.isReadWriteRepository(ReadWriteJPAPaymentMethodRepository.class));
    }
}
