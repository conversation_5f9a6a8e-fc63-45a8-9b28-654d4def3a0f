package global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.transfer;

import global.symbio.billing.core.payment.persistence.api.transfer.TransferDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.transfer.TransferDataAccessObjectFactory;
import io.micronaut.context.annotation.Primary;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

@Primary
@Singleton
public class JPATransferDataAccessObjectFactory implements TransferDataAccessObjectFactory {

    @Nonnull
    @Override
    public Class<? extends TransferDataAccessObject> type() {
        return JPATransferDataAccessObject.class;
    }

    @Nonnull
    @Override
    public TransferDataAccessObject create() {
        return new JPATransferDataAccessObject();
    }
}