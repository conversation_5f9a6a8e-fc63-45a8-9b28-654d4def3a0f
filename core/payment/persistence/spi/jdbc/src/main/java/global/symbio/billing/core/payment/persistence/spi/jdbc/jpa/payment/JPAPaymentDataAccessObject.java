package global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.payment;

import global.symbio.billing.core.country.persistence.api.CountryDataAccessObject;
import global.symbio.billing.core.country.persistence.spi.jdbc.jpa.JPACountryDataAccessObject;
import global.symbio.billing.core.currency.persistence.api.CurrencyDataAccessObject;
import global.symbio.billing.core.currency.persistence.spi.jdbc.jpa.JPACurrencyDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.payment.PaymentDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.payment.PaymentStatus;
import global.symbio.billing.core.payment.persistence.api.payment.method.PaymentMethodDataAccessObject;
import global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.payment.method.JPAPaymentMethodDataAccessObject;
import global.symbio.billing.core.persistence.api.Types;
import global.symbio.billing.core.reference.persistence.api.reference.ReferenceDataAccessObject;
import global.symbio.billing.core.reference.persistence.spi.jdbc.jpa.reference.JPAReferenceDataAccessObject;
import io.micronaut.core.annotation.Introspected;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.UUID;

@Entity
@Table(name = "payment", indexes = {@Index(columnList = "customer"), @Index(columnList = "method"), @Index(columnList = "currency"), @Index(columnList = "country"), @Index(columnList = "timestamp")})
@Getter @Setter
@Introspected(includedAnnotations = Entity.class)
public class JPAPaymentDataAccessObject extends PaymentDataAccessObject {

    @Id
    @Nonnull
    @Column(name = "id", unique = true, nullable = false, updatable = false)
    private UUID identifier;

    @Nonnull
    @Column(name = "business", nullable = false, updatable = false)
    private UUID business;

    @Nullable
    @ManyToOne
    @JoinColumn(name = "method")
    private JPAPaymentMethodDataAccessObject method;

    @Nonnull
    @ManyToOne
    @JoinColumn(name = "currency")
    private JPACurrencyDataAccessObject currency;

    @Nonnull
    @Column(name = "amount", nullable = false, updatable = false, scale = 32, precision = 12)
    private BigDecimal amount;

    @Nonnull
    @Column(name = "receipt_date", nullable = false, updatable = false)
    private ZonedDateTime receiptDate;

    @Nonnull
    @Column(name = "status", columnDefinition = "payment_status", nullable = false)
    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    private PaymentStatus status;

    @Nullable
    @Column(name = "description")
    private String description;

    @Nonnull
    @ManyToOne
    @JoinColumn(name = "country")
    private JPACountryDataAccessObject country;

    @PrimaryKeyJoinColumn
    @OneToOne(cascade = CascadeType.ALL)
    private JPAReferenceDataAccessObject reference;

    @Nonnull
    @Column(name = "timestamp", nullable = false, updatable = false, insertable = false)
    private ZonedDateTime timestamp;

    @Nonnull
    @Override
    public PaymentDataAccessObject identifier(@Nonnull UUID identifier) {
        setIdentifier(identifier);
        return this;
    }

    @Nonnull
    @Override
    public PaymentDataAccessObject business(@Nonnull UUID business) {
        setBusiness(business);
        return this;
    }

    @Nonnull
    @Override
    public PaymentDataAccessObject method(@Nonnull PaymentMethodDataAccessObject method) {
        final var data = Types.require(method, "method", JPAPaymentMethodDataAccessObject.class);
        setMethod(data);
        return this;
    }

    @Nonnull
    @Override
    public PaymentDataAccessObject currency(@Nonnull CurrencyDataAccessObject currency) {
        final var data = Types.require(currency, "currency", JPACurrencyDataAccessObject.class);
        setCurrency(data);
        return this;
    }

    @Nonnull
    @Override
    public PaymentDataAccessObject amount(@Nonnull BigDecimal amount) {
        setAmount(amount);
        return this;
    }

    @Nonnull
    @Override
    public PaymentDataAccessObject receiptDate(@Nonnull ZonedDateTime receiptDate) {
        setReceiptDate(receiptDate);
        return this;
    }

    @Nonnull
    @Override
    public PaymentDataAccessObject status(@Nonnull PaymentStatus status) {
        setStatus(status);
        return this;
    }

    @Nonnull
    @Override
    public PaymentDataAccessObject description(@Nullable String description) {
        setDescription(description);
        return this;
    }

    @Nonnull
    @Override
    public PaymentDataAccessObject country(@Nonnull CountryDataAccessObject country) {
        final var data = Types.require(country, "country", JPACountryDataAccessObject.class);
        setCountry(data);
        return this;
    }

    @Nonnull
    @Override
    public PaymentDataAccessObject reference(@Nullable ReferenceDataAccessObject reference) {
        final var data = Types.require(reference, "reference", JPAReferenceDataAccessObject.class);
        setReference(data);
        return this;
    }

    @Nonnull
    @Override
    public PaymentDataAccessObject timestamp(@Nonnull ZonedDateTime timestamp) {
        setTimestamp(timestamp);
        return this;
    }
}