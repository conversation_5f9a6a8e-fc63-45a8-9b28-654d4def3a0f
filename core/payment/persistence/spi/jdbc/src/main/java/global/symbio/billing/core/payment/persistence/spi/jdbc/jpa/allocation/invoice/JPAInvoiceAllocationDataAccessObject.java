package global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.allocation.invoice;

import global.symbio.billing.core.payment.persistence.api.allocation.invoice.InvoiceAllocationDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.allocation.invoice.InvoiceAllocationIdentifier;
import global.symbio.billing.core.persistence.api.Types;
import io.micronaut.core.annotation.Introspected;
import jakarta.annotation.Nonnull;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.ZonedDateTime;

@Entity
@Table(name = "invoice_allocation", indexes = {@Index(columnList = "invoice"), @Index(columnList = "allocation"), @Index(columnList = "timestamp")})
@Getter
@Setter
@Introspected(includedAnnotations = Entity.class)
public class JPAInvoiceAllocationDataAccessObject extends InvoiceAllocationDataAccessObject {

    @Nonnull
    @EmbeddedId
    private JPAInvoiceAllocationIdentifier identifier;

    @Nonnull
    @Column(name = "amount", nullable = false, scale = 32, precision = 12)
    protected BigDecimal amount;

    @Nonnull
    @Column(name = "last_updated", nullable = false, insertable = false)
    protected ZonedDateTime lastUpdated;

    @Nonnull
    @Column(name = "timestamp", nullable = false, updatable = false, insertable = false)
    protected ZonedDateTime timestamp;

    @Nonnull
    @Override
    public InvoiceAllocationDataAccessObject identifier(@Nonnull InvoiceAllocationIdentifier identifier) {
        final var data = Types.require(identifier, "identifier", JPAInvoiceAllocationIdentifier.class);
        setIdentifier(data);
        return this;
    }

    @Nonnull
    @Override
    public InvoiceAllocationDataAccessObject amount(@Nonnull BigDecimal amount) {
        setAmount(amount);
        return this;
    }

    @Nonnull
    @Override
    public InvoiceAllocationDataAccessObject lastUpdated(@Nonnull ZonedDateTime timestamp) {
        setLastUpdated(timestamp);
        return this;
    }

    @Nonnull
    @Override
    public InvoiceAllocationDataAccessObject timestamp(@Nonnull ZonedDateTime timestamp) {
        setTimestamp(timestamp);
        return this;
    }
}
