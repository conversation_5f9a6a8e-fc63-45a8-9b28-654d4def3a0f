package global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.transfer;

import global.symbio.billing.core.country.persistence.api.CountryDataAccessObject;
import global.symbio.billing.core.country.persistence.spi.jdbc.jpa.JPACountryDataAccessObject;
import global.symbio.billing.core.currency.persistence.api.CurrencyDataAccessObject;
import global.symbio.billing.core.currency.persistence.spi.jdbc.jpa.JPACurrencyDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AccountAllocationDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.payment.PaymentDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.transfer.TransferDataAccessObject;
import global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.allocation.JPAAccountAllocationDataAccessObject;
import global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.payment.JPAPaymentDataAccessObject;
import global.symbio.billing.core.persistence.api.Types;
import io.micronaut.core.annotation.Introspected;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.UUID;

@Entity
@Table(name = "transfer", indexes = {@Index(columnList = "debit"), @Index(columnList = "credit"), @Index(columnList = "timestamp")})
@Getter @Setter
@Introspected(includedAnnotations = Entity.class)
public class JPATransferDataAccessObject extends TransferDataAccessObject {

    @Id
    @Nonnull
    @Column(name = "id", unique = true, nullable = false, updatable = false)
    private UUID identifier;

    @Nonnull
    @ManyToOne
    @JoinColumn(name = "transfer_out")
    private JPAAccountAllocationDataAccessObject<?> transferOut;

    @Nonnull
    @ManyToOne
    @JoinColumn(name = "transfer_in")
    private JPAAccountAllocationDataAccessObject<?> transferIn;

    @Nonnull
    @ManyToOne
    @JoinColumn(name = "currency")
    private JPACurrencyDataAccessObject currency;

    @Nonnull
    @Column(name = "amount", nullable = false, updatable = false, scale = 32, precision = 12)
    private BigDecimal amount;

    @Nullable
    @Column(name = "description")
    private String description;

    @Nonnull
    @ManyToOne
    @JoinColumn(name = "country")
    private JPACountryDataAccessObject country;

    @Nonnull
    @ManyToOne
    @JoinColumn(name = "origin")
    private JPAAccountAllocationDataAccessObject<?> origin;

    @Nonnull
    @Column(name = "timestamp", nullable = false, updatable = false, insertable = false)
    private ZonedDateTime timestamp;

    @Nonnull
    @Override
    public TransferDataAccessObject identifier(@Nonnull UUID identifier) {
        setIdentifier(identifier);
        return this;
    }

    @Nonnull
    @Override
    public TransferDataAccessObject transferOut(@Nonnull AccountAllocationDataAccessObject<?> transferOut) {
        final var data = Types.require(transferOut, "transferOut", JPAAccountAllocationDataAccessObject.class);
        setTransferOut(data);
        return this;
    }

    @Nonnull
    @Override
    public TransferDataAccessObject transferIn(@Nonnull AccountAllocationDataAccessObject<?> transferIn) {
        final var data = Types.require(transferIn, "transferIn", JPAAccountAllocationDataAccessObject.class);
        setTransferIn(data);
        return this;
    }

    @Nonnull
    @Override
    public TransferDataAccessObject currency(@Nonnull CurrencyDataAccessObject currency) {
        final var data = Types.require(currency, "currency", JPACurrencyDataAccessObject.class);
        setCurrency(data);
        return this;
    }

    @Nonnull
    @Override
    public TransferDataAccessObject amount(@Nonnull BigDecimal amount) {
        setAmount(amount);
        return this;
    }

    @Nonnull
    @Override
    public TransferDataAccessObject description(@Nullable String description) {
        setDescription(description);
        return this;
    }

    @Nonnull
    @Override
    public TransferDataAccessObject country(@Nonnull CountryDataAccessObject country) {
        final var data = Types.require(country, "country", JPACountryDataAccessObject.class);
        setCountry(data);
        return this;
    }

    @Nonnull
    @Override
    public TransferDataAccessObject origin(@Nonnull AccountAllocationDataAccessObject<?> origin) {
        final var data = Types.require(origin, "origin", JPAAccountAllocationDataAccessObject.class);
        setOrigin(data);
        return this;
    }

    @Nonnull
    @Override
    public TransferDataAccessObject timestamp(@Nonnull ZonedDateTime timestamp) {
        setTimestamp(timestamp);
        return this;
    }
}