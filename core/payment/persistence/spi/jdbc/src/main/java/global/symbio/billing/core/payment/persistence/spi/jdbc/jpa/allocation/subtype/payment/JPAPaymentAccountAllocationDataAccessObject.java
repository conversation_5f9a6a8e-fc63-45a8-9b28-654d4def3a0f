package global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.allocation.subtype.payment;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AllocationDirection;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AllocationType;
import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.payment.PaymentAccountAllocationDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.payment.PaymentDataAccessObject;
import global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.allocation.JPAAccountAllocationDataAccessObject;
import global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.payment.JPAPaymentDataAccessObject;
import global.symbio.billing.core.persistence.api.Types;
import io.micronaut.core.annotation.Introspected;
import jakarta.annotation.Nonnull;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Objects;

@Entity
@Table(name = "account_allocation_subtype_payment")
@Getter @Setter
@DiscriminatorValue("PAYMENT") //AllocationType::PAYMENT
@PrimaryKeyJoinColumn(name = "allocation", referencedColumnName = "id")
@Introspected(includedAnnotations = Entity.class)
public class JPAPaymentAccountAllocationDataAccessObject
    extends JPAAccountAllocationDataAccessObject<JPAPaymentAccountAllocationDataAccessObject>
    implements PaymentAccountAllocationDataAccessObject<JPAPaymentAccountAllocationDataAccessObject> {

    @Nonnull
    @ManyToOne
    @JoinColumn(name = "payment")
    private JPAPaymentDataAccessObject payment;

    @Nonnull
    @Override
    public JPAPaymentAccountAllocationDataAccessObject payment(@Nonnull PaymentDataAccessObject payment) {
        final var data = Types.require(payment, "payment", JPAPaymentDataAccessObject.class);
        setPayment(data);
        return self();
    }

    @Nonnull
    @Override
    public AllocationType getType() {
        return AllocationType.PAYMENT;
    }

    @Nonnull
    @Override
    public AllocationDirection getDirection() {
        return AllocationDirection.IN;
    }

    @Nonnull
    @Override
    protected JPAPaymentAccountAllocationDataAccessObject self() {
        return this;
    }

    @Override
    public boolean equals(Object other) {
        if (this == other) return true;
        if (!(other instanceof JPAPaymentAccountAllocationDataAccessObject allocation)) return false;
        return Objects.equals(getIdentifier(), allocation.getIdentifier()) &&
            Objects.equals(getAccount(), allocation.getAccount()) &&
            getType() == allocation.getType() &&
            Objects.equals(getAmount(), allocation.getAmount()) &&
            Objects.equals(getAllocationState(), allocation.getAllocationState()) &&
            Objects.equals(getDirection(), allocation.getDirection()) &&
            Objects.equals(getSentTimestamp(), allocation.getSentTimestamp()) &&
            Objects.equals(getSettledTimestamp(), allocation.getSettledTimestamp()) &&
            Objects.equals(getTimestamp(), allocation.getTimestamp()) &&
            Objects.equals(getPayment(), allocation.getPayment());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getIdentifier(), getAccount(), getType(), getAmount(), getAllocationState(), getDirection(), getSentTimestamp(), getSettledTimestamp(), getTimestamp(), getPayment());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("identifier", getIdentifier())
            .add("account", getAccount())
            .add("type", getType())
            .add("amount", getAmount())
            .add("allocationState", getAllocationState())
            .add("allocationDirection", getDirection())
            .add("sentTimestamp", getSentTimestamp())
            .add("settledTimestamp", getSettledTimestamp())
            .add("timestamp", getTimestamp())
            .add("payment", getPayment())
            .toString();
    }
}
