package global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.repository;

import global.symbio.billing.core.payment.persistence.api.repository.AccountAllocationRepository;
import global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.allocation.JPAAccountAllocationDataAccessObject;

public interface JPAAccountAllocationRepository<T extends JPAAccountAllocationDataAccessObject<T>> extends AccountAllocationRepository<T> {
}