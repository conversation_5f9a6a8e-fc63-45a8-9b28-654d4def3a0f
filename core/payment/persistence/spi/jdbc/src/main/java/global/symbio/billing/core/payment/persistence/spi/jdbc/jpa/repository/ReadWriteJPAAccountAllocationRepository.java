package global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.repository;

import global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.allocation.JPAAccountAllocationDataAccessObject;
import global.symbio.billing.core.persistence.api.PersistenceStore;
import io.micronaut.context.annotation.Secondary;
import io.micronaut.data.annotation.Repository;
import jakarta.inject.Named;
import jakarta.inject.Singleton;

import static global.symbio.billing.core.persistence.api.StandardDataSourceNames.READ_WRITE;

@Secondary
@Singleton
@Repository(READ_WRITE) @Named(READ_WRITE)
@PersistenceStore(PersistenceStore.Capabilities.READ_WRITE)
public interface ReadWriteJPAAccountAllocationRepository<T extends JPAAccountAllocationDataAccessObject<T>> extends JPAAccountAllocationRepository<T> {}