package global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.payment.method;

import global.symbio.billing.core.payment.persistence.api.payment.method.PaymentMethodDataAccessObject;
import io.micronaut.core.annotation.Introspected;
import jakarta.annotation.Nonnull;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Immutable;

@Immutable @Entity
@Table(name = "payment_method", uniqueConstraints = @UniqueConstraint(columnNames = "name"))
@Getter @Setter
@Introspected(includedAnnotations = Entity.class)
public class JPAPaymentMethodDataAccessObject extends PaymentMethodDataAccessObject {

    @Id @Nonnull
    @Column(name = "id", unique = true, nullable = false, updatable = false)
    private Integer identifier;

    @Nonnull
    @Column(name = "name", length = 128, unique = true, nullable = false, updatable = false)
    private String name;

    @Nonnull
    @Column(name = "display_name", length = 128, nullable = false)
    private String displayName;

    @Nonnull
    @Override
    public PaymentMethodDataAccessObject identifier(@Nonnull Integer identifier) {
        setIdentifier(identifier);
        return this;
    }

    @Nonnull
    @Override
    public PaymentMethodDataAccessObject name(@Nonnull String name) {
        setName(name);
        return this;
    }

    @Nonnull
    @Override
    public PaymentMethodDataAccessObject displayName(@Nonnull String name) {
        setDisplayName(name);
        return this;
    }
}
