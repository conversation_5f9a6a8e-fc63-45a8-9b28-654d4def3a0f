package global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.allocation.subtype.transfer;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AllocationDirection;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AllocationType;
import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.transfer.TransferInAccountAllocationDataAccessObject;
import global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.allocation.JPAAccountAllocationDataAccessObject;
import io.micronaut.core.annotation.Introspected;
import jakarta.annotation.Nonnull;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.util.Objects;
import java.util.UUID;

@Entity
@Table(name = "account_allocation_subtype_transfer_in")
@Getter @Setter
@DiscriminatorValue("TRANSFER_IN")
@PrimaryKeyJoinColumn(name = "allocation", referencedColumnName = "id")
@Introspected(includedAnnotations = Entity.class)
public class JPATransferInAccountAllocationDataAccessObject
    extends JPAAccountAllocationDataAccessObject<JPATransferInAccountAllocationDataAccessObject>
    implements TransferInAccountAllocationDataAccessObject<JPATransferInAccountAllocationDataAccessObject> {

    @Nonnull
    @Column(name = "parent")
    private UUID parent;

    @Nonnull
    @Override
    public JPATransferInAccountAllocationDataAccessObject parent(@Nonnull UUID parent) {
        setParent(parent);
        return self();
    }

    @Nonnull
    @Override
    public AllocationType getType() {
        return AllocationType.TRANSFER_IN;
    }

    @Nonnull
    @Override
    public AllocationDirection getDirection() {
        return AllocationDirection.IN;
    }

    @Nonnull
    @Override
    protected JPATransferInAccountAllocationDataAccessObject self() {
        return this;
    }

    @Override
    public boolean equals(Object other) {
        if (this == other) return true;
        if (!(other instanceof JPATransferOutAccountAllocationDataAccessObject allocation)) return false;
        return Objects.equals(getIdentifier(), allocation.getIdentifier()) &&
            Objects.equals(getAccount(), allocation.getAccount()) &&
            getType() == allocation.getType() &&
            Objects.equals(getAmount(), allocation.getAmount()) &&
            Objects.equals(getAllocationState(), allocation.getAllocationState()) &&
            Objects.equals(getDirection(), allocation.getDirection()) &&
            Objects.equals(getSentTimestamp(), allocation.getSentTimestamp()) &&
            Objects.equals(getSettledTimestamp(), allocation.getSettledTimestamp()) &&
            Objects.equals(getTimestamp(), allocation.getTimestamp()) &&
            Objects.equals(getParent(), allocation.getParent());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getIdentifier(), getAccount(), getType(), getAmount(), getAllocationState(), getDirection(), getSentTimestamp(), getSettledTimestamp(), getTimestamp(), getParent());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("identifier", getIdentifier())
            .add("account", getAccount())
            .add("type", getType())
            .add("amount", getAmount())
            .add("allocationState", getAllocationState())
            .add("allocationDirection", getDirection())
            .add("sentTimestamp", getSentTimestamp())
            .add("settledTimestamp", getSettledTimestamp())
            .add("timestamp", getTimestamp())
            .add("parent", getParent())
            .toString();
    }
}
