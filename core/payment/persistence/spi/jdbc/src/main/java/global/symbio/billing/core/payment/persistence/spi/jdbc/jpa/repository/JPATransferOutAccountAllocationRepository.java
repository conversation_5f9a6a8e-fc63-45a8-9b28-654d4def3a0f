package global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.repository;

import global.symbio.billing.core.payment.persistence.api.repository.TransferOutAccountAllocationRepository;
import global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.allocation.subtype.transfer.JPATransferOutAccountAllocationDataAccessObject;
import io.micronaut.context.annotation.Executable;
import io.micronaut.data.annotation.Query;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.math.BigDecimal;
import java.util.UUID;

public interface JPATransferOutAccountAllocationRepository extends TransferOutAccountAllocationRepository<JPATransferOutAccountAllocationDataAccessObject> {

    @Nullable
    @Override
    @Executable
    @Query(
        value = """
            SELECT SUM(aa.amount) FROM account_allocation as aa
                INNER JOIN transfer as t on aa.id = t.transfer_out
            WHERE
                aa.type = 'TRANSFER_OUT'
                AND t.origin = (:paymentAccountAllocation)
                AND aa.account = (:account)
        """,
        nativeQuery = true
    )
    BigDecimal getSumOfTransferOutAccountAllocationsByPaymentAccountAllocationAndAccount(
        @Nonnull UUID paymentAccountAllocation,
        @Nonnull UUID account
    );
}