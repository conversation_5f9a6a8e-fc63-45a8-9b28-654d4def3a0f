package global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.allocation;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.country.persistence.api.CountryDataAccessObject;
import global.symbio.billing.core.country.persistence.spi.jdbc.jpa.JPACountryDataAccessObject;
import global.symbio.billing.core.currency.persistence.api.CurrencyDataAccessObject;
import global.symbio.billing.core.currency.persistence.spi.jdbc.jpa.JPACurrencyDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AccountAllocationDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AllocationDirection;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AllocationType;
import global.symbio.billing.core.payment.persistence.api.allocation.state.AllocationState;
import global.symbio.billing.core.persistence.api.Types;
import io.hypersistence.utils.hibernate.type.json.JsonBinaryType;
import io.micronaut.core.annotation.Introspected;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.DiscriminatorOptions;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.Type;
import org.hibernate.type.SqlTypes;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Objects;
import java.util.UUID;

@Entity
@Table(name = "account_allocation")
@Getter
@Setter
@Inheritance(strategy = InheritanceType.JOINED)
@DiscriminatorColumn(name = "type", discriminatorType = DiscriminatorType.STRING, columnDefinition = "account_allocation_type")
@DiscriminatorOptions(force = true)
@Introspected(includedAnnotations = Entity.class)
public abstract class JPAAccountAllocationDataAccessObject<T extends JPAAccountAllocationDataAccessObject<T>> implements AccountAllocationDataAccessObject<T> {

    @Id
    @Nonnull
    @Column(name = "id", unique = true, nullable = false, updatable = false)
    protected UUID identifier;

    @Nonnull
    @Column(name = "account", nullable = false, updatable = false)
    protected UUID account;

    @Nonnull
    @ManyToOne
    @JoinColumn(name = "country")
    protected JPACountryDataAccessObject country;

    @Nonnull
    @ManyToOne
    @JoinColumn(name = "currency")
    protected JPACurrencyDataAccessObject currency;

    @Nonnull
    @Column(name = "amount", nullable = false, updatable = false, scale = 32, precision = 12)
    protected BigDecimal amount;

    @Nonnull
    @Type(JsonBinaryType.class)
    @Column(name = "state", nullable = false)
    protected AllocationState allocationState;

    @Nonnull
    @Column(name = "direction", nullable = false)
    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    protected AllocationDirection allocationDirection;

    @Nullable
    @Column(name = "sent_timestamp")
    protected ZonedDateTime sentTimestamp;

    @Nullable
    @Column(name = "settled_timestamp")
    protected ZonedDateTime settledTimestamp;

    @Nonnull
    @Column(name = "timestamp", nullable = false, updatable = false, insertable = false)
    protected ZonedDateTime timestamp;

    @Nonnull
    protected abstract T self();

    @Nonnull
    public abstract AllocationDirection getDirection();

    @Nonnull
    @Override
    public T identifier(@Nonnull UUID identifier) {
        setIdentifier(identifier);
        setAllocationDirection(getDirection());
        return self();
    }

    @Nonnull
    @Override
    public T account(@Nonnull UUID account) {
        setAccount(account);
        return self();
    }

    @Nonnull
    @Override
    public T type(@Nonnull AllocationType type) {
        //TODO: exception type
        throw new IllegalStateException("Unable to explictly set `type` in JDBC SPI layer.");
    }

    @Nonnull
    @Override
    public T country(@Nonnull CountryDataAccessObject country) {
        final var data = Types.require(country, "country", JPACountryDataAccessObject.class);
        setCountry(data);
        return self();
    }

    @Nonnull
    @Override
    public T currency(@Nonnull CurrencyDataAccessObject currency) {
        final var data = Types.require(currency, "currency", JPACurrencyDataAccessObject.class);
        setCurrency(data);
        return self();
    }

    @Nonnull
    @Override
    public T amount(@Nonnull BigDecimal amount) {
        setAmount(amount);
        return self();
    }

    @Nonnull
    @Override
    public T allocationState(@Nonnull AllocationState state) {
        setAllocationState(state);
        return self();
    }

    @Nonnull
    @Override
    public T direction(@Nonnull AllocationDirection direction) {
        throw new IllegalStateException("Unable to explictly set `type` in JDBC SPI layer.");
    }

    @Nonnull
    @Override
    public T sentTimestamp(@Nullable ZonedDateTime sentTimestamp) {
        setSentTimestamp(sentTimestamp);
        return self();
    }

    @Nonnull
    @Override
    public T settledTimestamp(@Nullable ZonedDateTime settledTimestamp) {
        setSettledTimestamp(settledTimestamp);
        return self();
    }

    @Nonnull
    @Override
    public T timestamp(@Nonnull ZonedDateTime timestamp) {
        setTimestamp(timestamp);
        return self();
    }

    @Override
    public boolean equals(Object other) {
        if (this == other) return true;
        if (!(other instanceof JPAAccountAllocationDataAccessObject<?> allocation)) return false;
        return Objects.equals(getIdentifier(), allocation.getIdentifier()) &&
            Objects.equals(getAccount(), allocation.getAccount()) &&
            getType() == allocation.getType() &&
            Objects.equals(getCountry(), allocation.getCountry()) &&
            Objects.equals(getCurrency(), allocation.getCurrency()) &&
            Objects.equals(getAmount(), allocation.getAmount()) &&
            Objects.equals(getAllocationState(), allocation.getAllocationState()) &&
            Objects.equals(getDirection(), allocation.getDirection()) &&
            Objects.equals(getSentTimestamp(), allocation.getSentTimestamp()) &&
            Objects.equals(getSettledTimestamp(), allocation.getSettledTimestamp()) &&
            Objects.equals(getTimestamp(), allocation.getTimestamp());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getIdentifier(), getAccount(), getType(), getCountry(), getCurrency(), getAmount(), getAllocationState(), getDirection(), getSentTimestamp(), getSettledTimestamp(), getTimestamp());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("identifier", getIdentifier())
            .add("account", getAccount())
            .add("type", getType())
            .add("country", getCountry())
            .add("currency", getCurrency())
            .add("amount", getAmount())
            .add("allocationState", getAllocationState())
            .add("allocationDirection", getDirection())
            .add("sentTimestamp", getSentTimestamp())
            .add("settledTimestamp", getSettledTimestamp())
            .add("timestamp", getTimestamp())
            .toString();
    }
}