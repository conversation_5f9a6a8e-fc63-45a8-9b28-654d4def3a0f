package global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.allocation.invoice;

import global.symbio.billing.core.payment.persistence.api.allocation.invoice.InvoiceAllocationIdentifier;
import global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.allocation.JPAAccountAllocationDataAccessObject;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Objects;
import java.util.UUID;

@Getter
@Setter
@Embeddable
public class JPAInvoiceAllocationIdentifier extends InvoiceAllocationIdentifier implements Serializable {

    @Nonnull
    @Column(name = "invoice", nullable = false, updatable = false)
    private UUID invoice;

    @Nonnull
    @ManyToOne
    @JoinColumn(name = "allocation")
    private JPAAccountAllocationDataAccessObject<?> allocation;

    public JPAInvoiceAllocationIdentifier() {}

    public JPAInvoiceAllocationIdentifier(@Nonnull UUID invoice, @Nonnull JPAAccountAllocationDataAccessObject<?> allocation) {
        this.invoice = Objects.requireNonNull(invoice, "invoice");
        this.allocation = Objects.requireNonNull(allocation, "allocation");
    }
}
