package global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.cancellation;

import global.symbio.billing.core.payment.persistence.api.cancellation.PaymentCancellationDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.payment.PaymentDataAccessObject;
import global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.payment.JPAPaymentDataAccessObject;
import global.symbio.billing.core.persistence.api.Types;
import io.micronaut.core.annotation.Introspected;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.time.ZonedDateTime;
import java.util.UUID;

@Entity
@Table(name = "payment_cancellation", indexes = {@Index(columnList = "reference"), @Index(columnList = "timestamp")})
@Getter
@Setter
@Introspected(includedAnnotations = Entity.class)
public class JPAPaymentCancellationDataAccessObject extends PaymentCancellationDataAccessObject {

    @Id
    @Nonnull
    @Column(name = "id", unique = true, nullable = false, updatable = false)
    private UUID identifier;

    @Nonnull
    @OneToOne
    @JoinColumn(name = "payment")
    private JPAPaymentDataAccessObject payment;

    @Nonnull
    @Column(name = "reason")
    private String reason;

    @Nonnull
    @Column(name = "reference")
    private String reference;

    @Nullable
    @Column(name = "sent_timestamp")
    protected ZonedDateTime sentTimestamp;

    @Nullable
    @Column(name = "settled_timestamp")
    protected ZonedDateTime settledTimestamp;

    @Nonnull
    @Column(name = "timestamp", nullable = false, updatable = false, insertable = false)
    private ZonedDateTime timestamp;

    @Nonnull
    @Override
    public PaymentCancellationDataAccessObject identifier(@Nonnull UUID identifier) {
        setIdentifier(identifier);
        return this;
    }

    @Nonnull
    @Override
    public PaymentCancellationDataAccessObject payment(@Nonnull PaymentDataAccessObject payment) {
        final var data = Types.require(payment, "payment", JPAPaymentDataAccessObject.class);
        setPayment(data);
        return this;
    }

    @Nonnull
    @Override
    public PaymentCancellationDataAccessObject reason(@Nonnull String reason) {
        setReason(reason);
        return this;
    }

    @Nonnull
    @Override
    public PaymentCancellationDataAccessObject reference(@Nonnull String reference) {
        setReference(reference);
        return this;
    }

    @Nonnull
    @Override
    public PaymentCancellationDataAccessObject sentTimestamp(@Nullable ZonedDateTime sentTimestamp) {
        setSentTimestamp(sentTimestamp);
        return this;
    }

    @Nonnull
    @Override
    public PaymentCancellationDataAccessObject settledTimestamp(@Nullable ZonedDateTime settledTimestamp) {
        setSettledTimestamp(settledTimestamp);
        return this;
    }

    @Nonnull
    @Override
    public PaymentCancellationDataAccessObject timestamp(@Nonnull ZonedDateTime timestamp) {
        setTimestamp(timestamp);
        return this;
    }
}
