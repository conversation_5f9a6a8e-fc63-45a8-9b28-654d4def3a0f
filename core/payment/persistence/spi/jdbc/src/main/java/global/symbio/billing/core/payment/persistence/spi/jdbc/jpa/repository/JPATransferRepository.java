package global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.repository;

import global.symbio.billing.core.payment.persistence.api.repository.TransferRepository;
import global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.transfer.JPATransferDataAccessObject;
import io.micronaut.context.annotation.Executable;
import io.micronaut.data.annotation.Query;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.UUID;

public interface JPATransferRepository extends TransferRepository<JPATransferDataAccessObject> {

    @Nonnull
    @Override
    @Executable
    @Query(
        value = """
            SELECT DISTINCT(t.*) FROM transfer AS t
                INNER JOIN account_allocation aa ON t.transfer_in = aa.id
                            OR t.transfer_out = aa.id
            WHERE
                aa.account = :account
                AND (CAST(:transferDateStart AS TIMESTAMPTZ) IS NULL OR t.timestamp >= CAST(:transferDateStart AS TIMESTAMPTZ))
                AND (CAST(:transferDateEnd AS TIMESTAMPTZ) IS NULL OR t.timestamp <= CAST(:transferDateEnd AS TIMESTAMPTZ))
        """,
        nativeQuery = true
    )
    Collection<JPATransferDataAccessObject> getTransfers(
        @Nonnull UUID account,
        @Nullable ZonedDateTime transferDateStart,
        @Nullable ZonedDateTime transferDateEnd
    );
}