package global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.allocation.subtype.transfer;

import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.transfer.TransferOutAccountAllocationDataAccessObjectFactory;
import io.micronaut.context.annotation.Primary;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

@Primary
@Singleton
public class JPATransferOutAccountAllocationDataAccessObjectFactory implements TransferOutAccountAllocationDataAccessObjectFactory<JPATransferOutAccountAllocationDataAccessObject> {

    @Nonnull
    @Override
    public Class<? extends JPATransferOutAccountAllocationDataAccessObject> type() {
        return JPATransferOutAccountAllocationDataAccessObject.class;
    }

    @Nonnull
    @Override
    public JPATransferOutAccountAllocationDataAccessObject create() {
        return new JPATransferOutAccountAllocationDataAccessObject();
    }
}