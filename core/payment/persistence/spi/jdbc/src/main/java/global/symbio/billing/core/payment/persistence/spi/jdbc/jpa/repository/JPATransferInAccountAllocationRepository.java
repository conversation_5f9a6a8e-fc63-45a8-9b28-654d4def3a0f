package global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.repository;

import global.symbio.billing.core.payment.persistence.api.repository.TransferInAccountAllocationRepository;
import global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.allocation.subtype.transfer.JPATransferInAccountAllocationDataAccessObject;
import io.micronaut.context.annotation.Executable;
import io.micronaut.data.annotation.Query;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.math.BigDecimal;
import java.util.UUID;

public interface JPATransferInAccountAllocationRepository extends TransferInAccountAllocationRepository<JPATransferInAccountAllocationDataAccessObject> {

    @Nullable
    @Override
    @Executable
    @Query(
        value = """
            SELECT SUM(aa.amount) FROM account_allocation as aa
                INNER JOIN transfer as t on aa.id = t.transfer_in
            WHERE
                aa.type = 'TRANSFER_IN'
                AND t.origin = (:paymentAccountAllocation)
                AND aa.account = (:account)
        """,
        nativeQuery = true
    )
    BigDecimal getSumOfTransferInAccountAllocationsByPaymentAccountAllocationAndAccount(
        @Nonnull UUID paymentAccountAllocation,
        @Nonnull UUID account
    );

    @Override
    @Executable
    @Query(
        value = """
            SELECT EXISTS (
                SELECT 1
                FROM account_allocation aa
                    INNER JOIN transfer t ON aa.id = t.transfer_in
                    INNER JOIN invoice_allocation ia ON ia.allocation = aa.id
                WHERE 
                    aa.type = 'TRANSFER_IN'
                    AND t.origin = (:paymentAccountAllocation)
            );
        """,
        nativeQuery = true
    )
    boolean existsTransferInAllocationsWithInvoiceAllocationsByPaymentAccountAllocation(
        @Nonnull UUID paymentAccountAllocation
    );
}