package global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.repository;

import global.symbio.billing.core.payment.persistence.api.repository.InvoiceAllocationRepository;
import global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.allocation.invoice.JPAInvoiceAllocationDataAccessObject;
import global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.allocation.invoice.JPAInvoiceAllocationIdentifier;
import io.micronaut.context.annotation.Executable;
import io.micronaut.data.annotation.Query;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.math.BigDecimal;
import java.util.UUID;

public interface JPAInvoiceAllocationRepository extends InvoiceAllocationRepository<JPAInvoiceAllocationIdentifier, JPAInvoiceAllocationDataAccessObject> {

    @Nullable
    @Override
    @Executable
    @Query(
        value = """
            SELECT SUM(amount) FROM invoice_allocation WHERE allocation = :allocation
        """,
        nativeQuery = true
    )
    BigDecimal getSumOfInvoiceAllocationsByAllocation(@Nonnull UUID allocation);
}
