package global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.allocation.invoice;

import global.symbio.billing.core.payment.persistence.api.allocation.account.AccountAllocationDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.allocation.invoice.InvoiceAllocationDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.allocation.invoice.InvoiceAllocationDataAccessObjectFactory;
import global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.allocation.JPAAccountAllocationDataAccessObject;
import io.micronaut.context.annotation.Primary;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

import java.util.UUID;

@Primary
@Singleton
public class JPAInvoiceAllocationDataAccessObjectFactory implements InvoiceAllocationDataAccessObjectFactory {

    @Nonnull
    @Override
    public Class<? extends InvoiceAllocationDataAccessObject> type() {
        return JPAInvoiceAllocationDataAccessObject.class;
    }

    @Nonnull
    @Override
    public InvoiceAllocationDataAccessObject create() {
        return new JPAInvoiceAllocationDataAccessObject();
    }

    @Nonnull
    @Override
    public InvoiceAllocationDataAccessObject create(@Nonnull UUID invoice, @Nonnull AccountAllocationDataAccessObject<?> allocation) {
        final var identifier = new JPAInvoiceAllocationIdentifier(invoice, (JPAAccountAllocationDataAccessObject<?>) allocation);
        return create(identifier);
    }
}