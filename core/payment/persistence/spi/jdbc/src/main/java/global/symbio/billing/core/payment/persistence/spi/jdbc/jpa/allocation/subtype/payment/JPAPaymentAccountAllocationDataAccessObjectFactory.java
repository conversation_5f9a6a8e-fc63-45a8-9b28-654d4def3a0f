package global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.allocation.subtype.payment;

import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.payment.PaymentAccountAllocationDataAccessObjectFactory;
import io.micronaut.context.annotation.Primary;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

@Primary
@Singleton
public class JPAPaymentAccountAllocationDataAccessObjectFactory implements PaymentAccountAllocationDataAccessObjectFactory<JPAPaymentAccountAllocationDataAccessObject> {

    @Nonnull
    @Override
    public Class<? extends JPAPaymentAccountAllocationDataAccessObject> type() {
        return JPAPaymentAccountAllocationDataAccessObject.class;
    }

    @Nonnull
    @Override
    public JPAPaymentAccountAllocationDataAccessObject create() {
        return new JPAPaymentAccountAllocationDataAccessObject();
    }
}