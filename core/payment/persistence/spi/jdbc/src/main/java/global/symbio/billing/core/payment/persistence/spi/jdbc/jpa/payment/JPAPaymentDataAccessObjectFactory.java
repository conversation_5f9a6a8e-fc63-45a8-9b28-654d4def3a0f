package global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.payment;

import global.symbio.billing.core.payment.persistence.api.payment.PaymentDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.payment.PaymentDataAccessObjectFactory;
import io.micronaut.context.annotation.Primary;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

@Primary
@Singleton
public class JPAPaymentDataAccessObjectFactory implements PaymentDataAccessObjectFactory {

    @Nonnull
    @Override
    public Class<? extends PaymentDataAccessObject> type() {
        return JPAPaymentDataAccessObject.class;
    }

    @Nonnull
    @Override
    public PaymentDataAccessObject create() {
        return new JPAPaymentDataAccessObject();
    }
}