package global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.allocation.subtype.transfer;

import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.transfer.TransferInAccountAllocationDataAccessObjectFactory;
import io.micronaut.context.annotation.Primary;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

@Primary
@Singleton
public class JPATransferInAccountAllocationDataAccessObjectFactory implements TransferInAccountAllocationDataAccessObjectFactory<JPATransferInAccountAllocationDataAccessObject> {

    @Nonnull
    @Override
    public Class<? extends JPATransferInAccountAllocationDataAccessObject> type() {
        return JPATransferInAccountAllocationDataAccessObject.class;
    }

    @Nonnull
    @Override
    public JPATransferInAccountAllocationDataAccessObject create() {
        return new JPATransferInAccountAllocationDataAccessObject();
    }
}