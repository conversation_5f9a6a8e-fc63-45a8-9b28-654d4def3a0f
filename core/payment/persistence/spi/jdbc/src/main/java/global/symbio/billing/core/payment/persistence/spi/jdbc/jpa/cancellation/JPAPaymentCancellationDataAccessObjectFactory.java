package global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.cancellation;

import global.symbio.billing.core.payment.persistence.api.cancellation.PaymentCancellationDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.cancellation.PaymentCancellationDataAccessObjectFactory;
import io.micronaut.context.annotation.Primary;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

@Primary
@Singleton
public class JPAPaymentCancellationDataAccessObjectFactory implements PaymentCancellationDataAccessObjectFactory {

    @Nonnull
    @Override
    public Class<? extends PaymentCancellationDataAccessObject> type() {
        return JPAPaymentCancellationDataAccessObject.class;
    }

    @Nonnull
    @Override
    public PaymentCancellationDataAccessObject create() {
        return new JPAPaymentCancellationDataAccessObject();
    }
}