package global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.payment.method;

import global.symbio.billing.core.payment.persistence.api.payment.method.PaymentMethodDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.payment.method.PaymentMethodDataAccessObjectFactory;
import io.micronaut.context.annotation.Primary;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

@Primary
@Singleton
public class JPAPaymentMethodDataAccessObjectFactory implements PaymentMethodDataAccessObjectFactory {

    @Nonnull
    @Override
    public Class<? extends PaymentMethodDataAccessObject> type() {
        return JPAPaymentMethodDataAccessObject.class;
    }

    @Nonnull
    @Override
    public PaymentMethodDataAccessObject create() {
        return new JPAPaymentMethodDataAccessObject();
    }
}
