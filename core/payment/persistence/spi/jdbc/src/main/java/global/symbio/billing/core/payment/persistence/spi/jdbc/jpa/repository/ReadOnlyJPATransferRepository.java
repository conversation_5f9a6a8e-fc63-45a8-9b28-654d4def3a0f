package global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.repository;

import global.symbio.billing.core.persistence.api.PersistenceStore;
import io.micronaut.context.annotation.Primary;
import io.micronaut.data.annotation.Repository;
import jakarta.inject.Named;
import jakarta.inject.Singleton;

import static global.symbio.billing.core.persistence.api.StandardDataSourceNames.READ_ONLY;

@Primary
@Singleton
@Repository(READ_ONLY) @Named(READ_ONLY)
@PersistenceStore(PersistenceStore.Capabilities.READ_ONLY)
public interface ReadOnlyJPATransferRepository extends JPATransferRepository {}