package global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.repository;

import global.symbio.billing.core.payment.persistence.api.repository.PaymentCancellationRepository;
import global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.cancellation.JPAPaymentCancellationDataAccessObject;
import io.micronaut.context.annotation.Executable;
import io.micronaut.data.annotation.Query;
import jakarta.annotation.Nonnull;

import java.time.ZonedDateTime;
import java.util.Collection;

public interface JPAPaymentCancellationRepository extends PaymentCancellationRepository<JPAPaymentCancellationDataAccessObject> {

    @Nonnull
    @Override
    @Query(
        value = """
            SELECT pc.* FROM payment_cancellation as pc
                INNER JOIN payment as p on pc.payment = p.id
            WHERE
                p.country = :country
                AND pc.timestamp BETWEEN CAST(:start AS TIMESTAMPTZ) AND CAST(:end AS TIMESTAMPTZ)
        """,
        nativeQuery = true
    )
    @Executable
    Collection<JPAPaymentCancellationDataAccessObject> getPaymentCancellationsByTimestampBetweenAndCountryEquals(@Nonnull ZonedDateTime start, @Nonnull ZonedDateTime end, @Nonnull Integer country);

}