package global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.repository;

import global.symbio.billing.core.payment.persistence.api.repository.PaymentAccountAllocationRepository;
import global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.allocation.subtype.payment.JPAPaymentAccountAllocationDataAccessObject;
import io.micronaut.context.annotation.Executable;
import io.micronaut.data.annotation.Query;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.Set;
import java.util.UUID;

public interface JPAPaymentAccountAllocationRepository extends PaymentAccountAllocationRepository<JPAPaymentAccountAllocationDataAccessObject> {

    @Nonnull
    @Override
    @Executable
    @Query(
        value = """
            SELECT aasp.*, aa.* FROM account_allocation_subtype_payment as aasp
                INNER JOIN account_allocation as aa on aa.id = aasp.allocation
                INNER JOIN payment as p on aasp.payment = p.id
            WHERE
                ((:account) IS NULL OR aa.account = (:account))
                AND ((:allocation) IS NULL OR aa.state->>'type' IN (:allocation))
                AND ((:methods) IS NULL OR p.method IN (:methods))
                AND ((:status) IS NULL OR CAST(p.status AS TEXT) IN (:status))
                AND (CAST(:paymentDateStart AS TIMESTAMPTZ) IS NULL OR p.timestamp >= CAST(:paymentDateStart AS TIMESTAMPTZ))
                AND (CAST(:paymentDateEnd AS TIMESTAMPTZ) IS NULL OR p.timestamp <= CAST(:paymentDateEnd AS TIMESTAMPTZ))
                AND (CAST(:receiptDateStart AS TIMESTAMPTZ) IS NULL OR p.receipt_date >= CAST(:receiptDateStart AS TIMESTAMPTZ))
                AND (CAST(:receiptDateEnd AS TIMESTAMPTZ) IS NULL OR p.receipt_date <= CAST(:receiptDateStart AS TIMESTAMPTZ))
        """,
        nativeQuery = true
    )
    Collection<JPAPaymentAccountAllocationDataAccessObject> getPaymentAccountAllocations(
        @Nullable UUID account,
        @Nullable ZonedDateTime paymentDateStart,
        @Nullable ZonedDateTime paymentDateEnd,
        @Nullable ZonedDateTime receiptDateStart,
        @Nullable ZonedDateTime receiptDateEnd,
        @Nullable Set<String> allocation,
        @Nullable Set<Integer> methods,
        @Nullable Set<String> status
    );

    @Nonnull
    @Override
    @Query(
        value = """
            SELECT aasp.*, aa.* FROM account_allocation_subtype_payment as aasp
                INNER JOIN account_allocation as aa on aa.id = aasp.allocation
                INNER JOIN payment as p on aasp.payment = p.id
            WHERE
                p.country = :country
                AND p.timestamp BETWEEN CAST(:start AS TIMESTAMPTZ) AND CAST(:end AS TIMESTAMPTZ)
        """,
        nativeQuery = true
    )
    @Executable
    Collection<JPAPaymentAccountAllocationDataAccessObject> getPaymentAccountAllocationsByTimestampBetweenAndCountryEquals(@Nonnull ZonedDateTime start, @Nonnull ZonedDateTime end, @Nonnull Integer country);
}