package global.symbio.billing.core.payment.handler;

import global.symbio.billing.core.payment.persistence.api.allocation.account.AccountAllocationDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AllocationType;
import global.symbio.billing.core.payment.persistence.api.repository.PaymentAccountAllocationRepository;
import global.symbio.billing.core.payment.persistence.api.repository.TransferInAccountAllocationRepository;
import global.symbio.billing.core.payment.persistence.api.repository.TransferOutAccountAllocationRepository;
import global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.allocation.subtype.payment.JPAPaymentAccountAllocationDataAccessObject;
import global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.allocation.subtype.transfer.JPATransferInAccountAllocationDataAccessObject;
import global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.allocation.subtype.transfer.JPATransferOutAccountAllocationDataAccessObject;
import global.symbio.billing.core.persistence.api.CompositeRepository;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestAccountAllocationRepositoryHandler {

    @Mock
    private CompositeRepository<PaymentAccountAllocationRepository<JPAPaymentAccountAllocationDataAccessObject>> paymentAllocations;

    @Mock
    private CompositeRepository<TransferInAccountAllocationRepository<JPATransferInAccountAllocationDataAccessObject>> transferInAllocations;

    @Mock
    private CompositeRepository<TransferOutAccountAllocationRepository<JPATransferOutAccountAllocationDataAccessObject>> transferOutAllocations;

    private static AllocationType UNSUPPORTED;

    private AccountAllocationRepositoryHandler handler;

    @BeforeAll
    public static void setUpBeforeClass() {
        UNSUPPORTED = mock(AllocationType.class);
        lenient().when(UNSUPPORTED.ordinal()).thenReturn(3);
        mockStatic(AllocationType.class);
        lenient().when(AllocationType.values()).thenReturn(new AllocationType[] {AllocationType.PAYMENT, AllocationType.TRANSFER_OUT, AllocationType.TRANSFER_IN, UNSUPPORTED});
    }

    @BeforeEach
    public void setup() {
        handler = new AccountAllocationRepositoryHandler(paymentAllocations, transferInAllocations, transferOutAllocations);
    }

    @Test
    @DisplayName("AccountAllocationRepositoryHandler::new rejects null constructor arguments")
    public void account_allocation_repository_handler_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new AccountAllocationRepositoryHandler(null, transferInAllocations, transferOutAllocations));
        assertThrows(NullPointerException.class, () -> new AccountAllocationRepositoryHandler(paymentAllocations, null, transferOutAllocations));
        assertThrows(NullPointerException.class, () -> new AccountAllocationRepositoryHandler(paymentAllocations, transferInAllocations, null));
    }

    @ParameterizedTest
    @EnumSource(value = AllocationType.class, names = {"PAYMENT" , "TRANSFER_IN", "TRANSFER_OUT"})
    @DisplayName("AccountAllocationRepositoryHandler::updateAllocation updates allocation")
    public void account_allocation_repository_handler_updates_allocation(final AllocationType type) {
        final var allocation = switch (type) {
            case PAYMENT -> mock(JPAPaymentAccountAllocationDataAccessObject.class);
            case TRANSFER_IN -> mock(JPATransferInAccountAllocationDataAccessObject.class);
            case TRANSFER_OUT -> mock(JPATransferOutAccountAllocationDataAccessObject.class);
        };

        when(allocation.getType()).thenReturn(type);

        final var paymentAllocRepo = mock(PaymentAccountAllocationRepository.class);
        final var transferInRepo = mock(TransferInAccountAllocationRepository.class);
        final var transferOutRepo = mock(TransferOutAccountAllocationRepository.class);

        switch (type) {
            case PAYMENT -> {
                when(paymentAllocRepo.update(any(AccountAllocationDataAccessObject.class))).thenReturn(allocation);
                when(paymentAllocations.write()).thenReturn(paymentAllocRepo);
            }
            case TRANSFER_IN -> {
                when(transferInRepo.update(any(AccountAllocationDataAccessObject.class))).thenReturn(allocation);
                when(transferInAllocations.write()).thenReturn(transferInRepo);
            }
            case TRANSFER_OUT -> {
                when(transferOutRepo.update(any(AccountAllocationDataAccessObject.class))).thenReturn(allocation);
                when(transferOutAllocations.write()).thenReturn(transferOutRepo);
            }
        }

        final var result = assertDoesNotThrow(() -> handler.updateAllocation(allocation));
        assertEquals(type, result.getType());

        switch (type) {
            case PAYMENT -> {
                verify(paymentAllocations, times(1)).write();
                verify(paymentAllocRepo, times(1)).update(any(AccountAllocationDataAccessObject.class));
            }
            case TRANSFER_IN -> {
                verify(transferInAllocations, times(1)).write();
                verify(transferInRepo, times(1)).update(any(AccountAllocationDataAccessObject.class));
            }
            case TRANSFER_OUT -> {
                verify(transferOutAllocations, times(1)).write();
                verify(transferOutRepo, times(1)).update(any(AccountAllocationDataAccessObject.class));
            }
        }
    }

    @Test
    @DisplayName("AccountAllocationRepositoryHandler::updateAllocation throws an exception if allocation type is unsupported")
    public void account_allocation_repository_handler_update_allocation_throws_exception() {
        final var allocation = mock(AccountAllocationDataAccessObject.class);
        when(allocation.getType()).thenReturn(UNSUPPORTED);
        assertThrows(IllegalStateException.class, () -> handler.updateAllocation(allocation));
    }

    @ParameterizedTest
    @EnumSource(value = AllocationType.class, names = {"PAYMENT" , "TRANSFER_IN", "TRANSFER_OUT"})
    @DisplayName("AccountAllocationRepositoryHandler::saveAllocation saves allocation")
    public void account_allocation_repository_handler_saves_allocation(final AllocationType type) {
        final var allocation = switch (type) {
            case PAYMENT -> mock(JPAPaymentAccountAllocationDataAccessObject.class);
            case TRANSFER_IN -> mock(JPATransferInAccountAllocationDataAccessObject.class);
            case TRANSFER_OUT -> mock(JPATransferOutAccountAllocationDataAccessObject.class);
        };

        when(allocation.getType()).thenReturn(type);

        final var paymentAllocRepo = mock(PaymentAccountAllocationRepository.class);
        final var transferInRepo = mock(TransferInAccountAllocationRepository.class);
        final var transferOutRepo = mock(TransferOutAccountAllocationRepository.class);

        switch (type) {
            case PAYMENT -> {
                when(paymentAllocRepo.save(any(AccountAllocationDataAccessObject.class))).thenReturn(allocation);
                when(paymentAllocations.write()).thenReturn(paymentAllocRepo);
            }
            case TRANSFER_IN -> {
                when(transferInRepo.save(any(AccountAllocationDataAccessObject.class))).thenReturn(allocation);
                when(transferInAllocations.write()).thenReturn(transferInRepo);
            }
            case TRANSFER_OUT -> {
                when(transferOutRepo.save(any(AccountAllocationDataAccessObject.class))).thenReturn(allocation);
                when(transferOutAllocations.write()).thenReturn(transferOutRepo);
            }
        }

        final var result = assertDoesNotThrow(() -> handler.saveAllocation(allocation));
        assertEquals(type, result.getType());

        switch (type) {
            case PAYMENT -> {
                verify(paymentAllocations, times(1)).write();
                verify(paymentAllocRepo, times(1)).save(any(AccountAllocationDataAccessObject.class));
            }
            case TRANSFER_IN -> {
                verify(transferInAllocations, times(1)).write();
                verify(transferInRepo, times(1)).save(any(AccountAllocationDataAccessObject.class));
            }
            case TRANSFER_OUT -> {
                verify(transferOutAllocations, times(1)).write();
                verify(transferOutRepo, times(1)).save(any(AccountAllocationDataAccessObject.class));
            }
        }
    }

    @Test
    @DisplayName("AccountAllocationRepositoryHandler::saveAllocation throws an exception if allocation type is unsupported")
    public void account_allocation_repository_handler_save_allocation_throws_exception() {
        final var allocation = mock(AccountAllocationDataAccessObject.class);
        when(allocation.getType()).thenReturn(UNSUPPORTED);
        assertThrows(IllegalStateException.class, () -> handler.saveAllocation(allocation));
    }
}
