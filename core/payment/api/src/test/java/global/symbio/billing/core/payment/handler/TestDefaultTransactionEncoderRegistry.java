package global.symbio.billing.core.payment.handler;

import global.symbio.billing.core.payment.persistence.api.Recordable;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AllocationType;
import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.payment.PaymentAccountAllocation;
import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.transfer.TransferInAccountAllocation;
import global.symbio.billing.core.payment.persistence.api.cancellation.PaymentCancellation;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class TestDefaultTransactionEncoderRegistry {

    @Mock
    private TransactionEncoder handler;
    private DefaultTransactionEncoderRegistry registry;

    @BeforeEach
    public void setup() {
        Mockito.<Class<? extends Recordable>>when(handler.type()).thenReturn(PaymentAccountAllocation.class);
        registry = new DefaultTransactionEncoderRegistry(Set.of(handler));
    }

    @Test
    @DisplayName("DefaultAccountAllocationTransactionEncoderRegistry::new rejects null constructor arguments")
    public void default_account_allocation_transaction_encoder_registry_registry_null_constructor() {
        assertThrows(NullPointerException.class, () -> new DefaultTransactionEncoderRegistry(null));
    }

    @Test
    @DisplayName("DefaultAccountAllocationTransactionEncoderRegistry::lookup retrieves correct handler given mapped category")
    public void default_account_allocation_transaction_encoder_registry_lookup_retrieves_correct_handler_given_mapped_category() {
        final var type = PaymentAccountAllocation.class;
        final var result = registry.lookup(type);
        assertNotNull(result);
        assertSame(handler, result);
    }

    @Test
    @DisplayName("DefaultAccountAllocationTransactionEncoderRegistry::lookup returns null when no handler is found for given category")
    public void default_account_allocation_transaction_encoder_registry_lookup_retrieves_fallback_handler_given_no_handler_found() {
        final var type = TransferInAccountAllocation.class;
        final var result = registry.lookup(type);
        assertNull(result);
    }
}
