package global.symbio.billing.core.payment.handler;

import global.symbio.billing.core.country.persistence.api.CountryDataAccessObject;
import global.symbio.billing.core.currency.persistence.api.CurrencyDataAccessObject;
import global.symbio.billing.core.i18n.LocalisationSettings;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AccountAllocation;
import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.payment.PaymentAccountAllocation;
import global.symbio.billing.core.payment.persistence.api.cancellation.PaymentCancellation;
import global.symbio.billing.core.payment.persistence.api.cancellation.PaymentCancellationDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.payment.Payment;
import global.symbio.billing.core.payment.persistence.api.payment.PaymentDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.repository.PaymentCancellationRepository;
import global.symbio.billing.core.persistence.api.CompositeRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestPaymentCancellationTransactionEncoder {

    private PaymentCancellationTransactionEncoder encoder;

    @Mock
    private PaymentAccountAllocation<?> paymentAccountAllocation;

    @Mock
    private Payment payment;

    @Mock
    private LocalisationSettings localisationSettings;

    @Mock
    private CompositeRepository<PaymentCancellationRepository<PaymentCancellationDataAccessObject>> cancellations;

    @BeforeEach
    public void setup() {
        encoder = new PaymentCancellationTransactionEncoder(cancellations);
    }

    @Test
    @DisplayName("PaymentCancellationTransactionEncoder::build maps payment cancellation to transaction correctly")
    public void payment_cancellation_transaction_encoder_maps_correctly() {
        final var paymentId = UUID.randomUUID();
        final var currencyCode = 458;
        final var countryCode = "MY";
        final var reason = "Test Reason";
        final var referenceId = "MYCOPYMX00000000353";
        final var symbio = UUID.randomUUID();
        final var amount = BigDecimal.valueOf(100);
        final var account = UUID.randomUUID();
        final var timestamp = ZonedDateTime.now();
        final var paymentCancellationCategory = 10;

        final var currency = mock(CurrencyDataAccessObject.class);
        when(currency.getIdentifier()).thenReturn(currencyCode);

        final var country = mock(CountryDataAccessObject.class);
        when(country.getCode()).thenReturn(countryCode);

        final var paymentDao = mock(PaymentDataAccessObject.class);
        when(paymentDao.getCurrency()).thenReturn(currency);
        when(paymentDao.getAmount()).thenReturn(amount);
        when(paymentDao.getCountry()).thenReturn(country);

        final var paymentCancellation = mock(PaymentCancellationDataAccessObject.class);
        when(paymentCancellation.getPayment()).thenReturn(paymentDao);
        when(paymentCancellation.getReason()).thenReturn(reason);
        when(paymentCancellation.getReference()).thenReturn(referenceId);
        when(paymentCancellation.getTimestamp()).thenReturn(timestamp);

        final var cancellationRepo = mock(PaymentCancellationRepository.class);
        when(cancellations.write()).thenReturn(cancellationRepo);
        when(cancellationRepo.findByPaymentIdentifier(any(UUID.class))).thenReturn(Optional.of(paymentCancellation));

        when(payment.getIdentifier()).thenReturn(paymentId);

        when(localisationSettings.getSymbioAccountIdentifier()).thenReturn(symbio);
        when(paymentAccountAllocation.getAccount()).thenReturn(account);
        when(paymentAccountAllocation.getPayment()).thenReturn(payment);

        final var transaction = encoder.build(paymentAccountAllocation, localisationSettings);

        assertEquals(currencyCode, transaction.getCurrency());
        assertEquals(paymentCancellationCategory, transaction.getCategory());
        assertEquals(amount, transaction.getAmount());
        assertEquals(BigDecimal.ZERO, transaction.getTaxation());
        assertEquals(symbio, transaction.getCredit());
        assertEquals(account, transaction.getDebit());
        assertEquals(reason, transaction.getDescription());
        assertEquals(referenceId, transaction.getRef());
        assertEquals(countryCode, transaction.getCountry());
        assertEquals(timestamp, transaction.getTimestamp());
    }

    @Test
    @DisplayName("PaymentCancellationTransactionEncoder::build throws given invalid account allocation")
    void payment_cancellation_transaction_encoder_invalid_allocation() {
        final var invalidAllocation = mock(AccountAllocation.class);

        final var exception = assertThrows(IllegalStateException.class, () ->
            encoder.build(invalidAllocation, localisationSettings)
        );

        assertEquals("Account Allocation is not a Payment Account Allocation", exception.getMessage());
    }


    @Test
    @DisplayName("PaymentCancellationTransactionEncoder::type is PaymentCancellation")
    public void payment_cancellation_transaction_encoder_returns_correct_type() {
        assertSame(PaymentCancellation.class, encoder.type());
    }

    @Test
    @DisplayName("PaymentCancellationTransactionEncoder::enrich returns a Map")
    public void payment_cancellation_transaction_encoder_enrich_returns_map() {
        final var identifier = UUID.randomUUID();
        when(paymentAccountAllocation.getIdentifier()).thenReturn(identifier);
        final var enrichment = encoder.enrich(paymentAccountAllocation, localisationSettings);
        assertEquals(Map.of("accountAllocationId", identifier), enrichment);
    }

    @Test
    @DisplayName("PaymentCancellationTransactionEncoder::onPublished updates timestamp")
    public void payment_cancellation_transaction_encoder_on_published_updates_timestamp() {
        final var paymentId = UUID.randomUUID();
        when(paymentAccountAllocation.getPayment()).thenReturn(payment);
        when(payment.getIdentifier()).thenReturn(paymentId);

        final var paymentCancellation = mock(PaymentCancellationDataAccessObject.class);
        when(paymentCancellation.sentTimestamp(any(ZonedDateTime.class))).thenReturn(paymentCancellation);

        final var cancellationRepo = mock(PaymentCancellationRepository.class);
        when(cancellations.write()).thenReturn(cancellationRepo);
        when(cancellationRepo.findByPaymentIdentifier(any(UUID.class))).thenReturn(Optional.of(paymentCancellation));
        when(cancellationRepo.update(any(PaymentCancellationDataAccessObject.class))).thenReturn(paymentCancellation);
        
        assertDoesNotThrow(() -> encoder.onPublished(paymentAccountAllocation));
        verify(cancellations, times(2)).write();
        verify(paymentCancellation, times(1)).sentTimestamp(any(ZonedDateTime.class));
    }

    @Test
    @DisplayName("PaymentCancellationTransactionEncoder::onSettled updates timestamp")
    public void payment_cancellation_transaction_encoder_on_settled_updates_timestamp() {
        final var paymentId = UUID.randomUUID();
        when(paymentAccountAllocation.getPayment()).thenReturn(payment);
        when(payment.getIdentifier()).thenReturn(paymentId);

        final var paymentCancellation = mock(PaymentCancellationDataAccessObject.class);
        when(paymentCancellation.settledTimestamp(any(ZonedDateTime.class))).thenReturn(paymentCancellation);

        final var cancellationRepo = mock(PaymentCancellationRepository.class);
        when(cancellations.write()).thenReturn(cancellationRepo);
        when(cancellationRepo.findByPaymentIdentifier(any(UUID.class))).thenReturn(Optional.of(paymentCancellation));
        when(cancellationRepo.update(any(PaymentCancellationDataAccessObject.class))).thenReturn(paymentCancellation);


        assertDoesNotThrow(() -> encoder.onSettled(paymentAccountAllocation));
        verify(cancellations, times(2)).write();
        verify(paymentCancellation, times(1)).settledTimestamp(any(ZonedDateTime.class));
    }
}
