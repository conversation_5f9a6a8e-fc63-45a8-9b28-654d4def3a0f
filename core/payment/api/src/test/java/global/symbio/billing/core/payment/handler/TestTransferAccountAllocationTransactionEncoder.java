package global.symbio.billing.core.payment.handler;

import global.symbio.billing.core.currency.persistence.api.Currency;
import global.symbio.billing.core.exception.sdk.BillingEntityNotFoundException;
import global.symbio.billing.core.i18n.LocalisationSettings;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AccountAllocation;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AccountAllocationDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.payment.PaymentAccountAllocation;
import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.payment.PaymentAccountAllocationDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.transfer.TransferInAccountAllocation;
import global.symbio.billing.core.payment.persistence.api.payment.Payment;
import global.symbio.billing.core.payment.persistence.api.repository.AccountAllocationRepository;
import global.symbio.billing.core.payment.persistence.api.repository.TransferRepository;
import global.symbio.billing.core.payment.persistence.api.transfer.Transfer;
import global.symbio.billing.core.payment.persistence.api.transfer.TransferDataAccessObject;
import global.symbio.billing.core.persistence.api.CompositeRepository;
import global.symbio.billing.core.reference.persistence.api.reference.Reference;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestTransferAccountAllocationTransactionEncoder {

    private TransferAccountAllocationTransactionEncoder encoder;

    @Mock
    private TransferInAccountAllocation<?> transferInAccountAllocation;

    @Mock
    private Payment payment;

    @Mock
    private Transfer transfer;

    @Mock
    private LocalisationSettings localisationSettings;

    @Mock
    private CompositeRepository<TransferRepository<TransferDataAccessObject>> transfers;

    @Mock
    private CompositeRepository<AccountAllocationRepository> allocations;

    @BeforeEach
    public void setup() {
        encoder = new TransferAccountAllocationTransactionEncoder(transfers);
    }

    @Test
    @DisplayName("TransferAccountAllocationTransactionEncoder::build maps transfer and payment allocation to transaction correctly")
    public void transfer_account_allocation_transaction_encoder_maps_correctly() {
        final var currency = mock(Currency.class);
        final var currencyCode = 458;
        when(currency.getIdentifier()).thenReturn(currencyCode);
        final var description = "Test Transfer";
        final var reference = mock(Reference.class);
        final var referenceId = UUID.randomUUID().toString();
        when(reference.getReference()).thenReturn(referenceId);
        final var countryCode = "MY";
        final var amount = BigDecimal.valueOf(100);
        final var timestamp = ZonedDateTime.now();
        final var transferCategory = 38;
        final var transferInUuid = UUID.randomUUID();
        final var transferOutUuid = UUID.randomUUID();

        final var paymentAccountAllocation = mock(PaymentAccountAllocation.class);
        final var paymentAccountAllocationDao = mock(PaymentAccountAllocationDataAccessObject.class);
        final var transferDao = mock(TransferDataAccessObject.class, RETURNS_DEEP_STUBS);
        when(transferDao.getOrigin()).thenReturn(paymentAccountAllocationDao);
        when(paymentAccountAllocationDao.entity()).thenReturn(paymentAccountAllocation);
        when(paymentAccountAllocation.getPayment()).thenReturn(payment);

        when(transferInAccountAllocation.getIdentifier()).thenReturn(transferInUuid);

        final var transferRepo = mock(TransferRepository.class);
        when(transfers.read()).thenReturn(transferRepo);
        when(transferRepo.findByTransferInIdentifier(eq(transferInUuid))).thenReturn(Optional.of(transferDao));

        when(payment.getCurrency()).thenReturn(currency);
        when(payment.getReference()).thenReturn(reference);
        when(transferDao.getDescription()).thenReturn(description);
        when(transferDao.getCountry().getCode()).thenReturn(countryCode);
        when(transferDao.getAmount()).thenReturn(BigDecimal.valueOf(100));

        final var transferIn = mock(AccountAllocationDataAccessObject.class);
        when(transferIn.getAccount()).thenReturn(transferInUuid);
        when(transferDao.getTransferIn()).thenReturn(transferIn);

        final var transferOut = mock(AccountAllocationDataAccessObject.class);
        when(transferOut.getAccount()).thenReturn(transferOutUuid);
        when(transferDao.getTransferOut()).thenReturn(transferOut);

        when(transferInAccountAllocation.getTimestamp()).thenReturn(timestamp);

        final var transaction = encoder.build(transferInAccountAllocation, localisationSettings);

        assertEquals(currencyCode, transaction.getCurrency());
        assertEquals(transferCategory, transaction.getCategory());
        assertEquals(amount, transaction.getAmount());
        assertEquals(BigDecimal.ZERO, transaction.getTaxation());
        assertEquals(transferOutUuid, transaction.getDebit());
        assertEquals(transferInUuid, transaction.getCredit());
        assertEquals(description, transaction.getDescription());
        assertEquals(referenceId, transaction.getRef());
        assertEquals(countryCode, transaction.getCountry());
        assertEquals(timestamp, transaction.getTimestamp());
    }

    @Test
    @DisplayName("TransferAccountAllocationTransactionEncoder::build throws exception given invalid account allocation")
    void transfer_account_allocation_transaction_encoder_invalid_allocation() {
        final var invalidAllocation = mock(AccountAllocation.class);

        final var exception = assertThrows(IllegalStateException.class, () ->
            encoder.build(invalidAllocation, localisationSettings)
        );
        assertEquals("Account Allocation is not a TransferInAccountAllocation", exception.getMessage());
    }

    @Test
    @DisplayName("TransferAccountAllocationTransactionEncoder::build throws exception when origin is not a PaymentAccountAllocation")
    void transfer_account_allocation_transaction_encoder_invalid_allocation_origin() {
        final var transferInUuid = UUID.randomUUID();
        final var accountAllocation = mock(AccountAllocation.class);
        final var accountAllocationDao = mock(AccountAllocationDataAccessObject.class);
        final var transferDao = mock(TransferDataAccessObject.class, RETURNS_DEEP_STUBS);
        when(transferDao.getOrigin()).thenReturn(accountAllocationDao);
        when(accountAllocationDao.entity()).thenReturn(accountAllocation);

        when(transferInAccountAllocation.getIdentifier()).thenReturn(transferInUuid);

        final var transferRepo = mock(TransferRepository.class);
        when(transfers.read()).thenReturn(transferRepo);
        when(transferRepo.findByTransferInIdentifier(eq(transferInUuid))).thenReturn(Optional.of(transferDao));
        final var exception = assertThrows(IllegalStateException.class, () ->
            encoder.build(transferInAccountAllocation, localisationSettings)
        );
        assertEquals("Origin Account Allocation is not a PaymentAccountAllocation", exception.getMessage());
    }

    @Test
    @DisplayName("TransferAccountAllocationTransactionEncoder::build throws exception when null transfer")
    void transfer_account_allocation_transaction_encoder_null_transfer() {
        final var transferInUuid = UUID.randomUUID();
        when(transferInAccountAllocation.getIdentifier()).thenReturn(transferInUuid);

        final var transferRepo = mock(TransferRepository.class);
        when(transfers.read()).thenReturn(transferRepo);
        when(transferRepo.findByTransferInIdentifier(eq(transferInUuid))).thenReturn(Optional.empty());

        assertThrows(BillingEntityNotFoundException.class, () -> encoder.build(transferInAccountAllocation, localisationSettings));
    }

    @Test
    @DisplayName("TransferAccountAllocationTransactionEncoder::build throws given null reference")
    void transfer_account_allocation_transaction_encoder_null_reference() {
        final var transferInUuid = UUID.randomUUID();
        when(transferInAccountAllocation.getIdentifier()).thenReturn(transferInUuid);

        final var paymentAccountAllocation = mock(PaymentAccountAllocation.class);
        final var paymentAccountAllocationDao = mock(PaymentAccountAllocationDataAccessObject.class);
        when(paymentAccountAllocationDao.entity()).thenReturn(paymentAccountAllocation);
        when(paymentAccountAllocation.getPayment()).thenReturn(payment);

        final var transferDao = mock(TransferDataAccessObject.class, RETURNS_DEEP_STUBS);
        when(transferDao.getOrigin()).thenReturn(paymentAccountAllocationDao);

        final var transferRepo = mock(TransferRepository.class);
        when(transfers.read()).thenReturn(transferRepo);
        when(transferRepo.findByTransferInIdentifier(eq(transferInUuid))).thenReturn(Optional.of(transferDao));
        when(payment.getReference()).thenReturn(null);

        final var exception = assertThrows(IllegalStateException.class, () ->
            encoder.build(transferInAccountAllocation, localisationSettings)
        );
        assertEquals("Payment is missing a reference", exception.getMessage());
    }

    @Test
    @DisplayName("TransferAccountAllocationTransactionEncoder::type is TransferInAccountAllocation")
    public void transfer_account_allocation_transaction_encoder_returns_correct_type() {
        assertSame(TransferInAccountAllocation.class, encoder.type());
    }

    @Test
    @DisplayName("TransferAccountAllocationTransactionEncoder::enrich returns a Map")
    public void transfer_account_allocation_transaction_encoder_enrich_returns_map() {
        final var enrichment = encoder.enrich(transferInAccountAllocation, localisationSettings);
        assertEquals(Map.of(), enrichment);
    }

    @Test
    @DisplayName("TransferAccountAllocationTransactionEncoder::onPublished updates timestamp")
    public void transfer_account_allocation_transaction_encoder_on_published_updates_timestamp() {
        final var transferInUuid = UUID.randomUUID();
        when(transferInAccountAllocation.getIdentifier()).thenReturn(transferInUuid);

        final var transferDao = mock(TransferDataAccessObject.class);
        final var transferRepo = mock(TransferRepository.class);
        when(transfers.write()).thenReturn(transferRepo);
        when(transferRepo.findByTransferInIdentifier(eq(transferInUuid))).thenReturn(Optional.of(transferDao));

        final var transferIn = mock(AccountAllocationDataAccessObject.class);
        when(transferDao.getTransferIn()).thenReturn(transferIn);

        final var transferOut = mock(AccountAllocationDataAccessObject.class);
        when(transferDao.getTransferOut()).thenReturn(transferOut);

        assertDoesNotThrow(() -> encoder.onPublished(transferInAccountAllocation));
        verify(transfers, times(2)).write();
        verify(transferRepo, times(1)).update(transferDao);
        verify(transferIn, times(1)).sentTimestamp(any(ZonedDateTime.class));
        verify(transferOut, times(1)).sentTimestamp(any(ZonedDateTime.class));
    }

    @Test
    @DisplayName("TransferAccountAllocationTransactionEncoder::onSettled updates timestamp")
    public void transfer_account_allocation_transaction_encoder_on_settled_updates_timestamp() {
        final var transferInUuid = UUID.randomUUID();
        when(transferInAccountAllocation.getIdentifier()).thenReturn(transferInUuid);

        final var transferDao = mock(TransferDataAccessObject.class);
        final var transferRepo = mock(TransferRepository.class);
        when(transfers.write()).thenReturn(transferRepo);
        when(transferRepo.findByTransferInIdentifier(eq(transferInUuid))).thenReturn(Optional.of(transferDao));

        final var transferIn = mock(AccountAllocationDataAccessObject.class);
        when(transferDao.getTransferIn()).thenReturn(transferIn);

        final var transferOut = mock(AccountAllocationDataAccessObject.class);
        when(transferDao.getTransferOut()).thenReturn(transferOut);

        assertDoesNotThrow(() -> encoder.onSettled(transferInAccountAllocation));
        verify(transfers, times(2)).write();
        verify(transferRepo, times(1)).update(transferDao);
        verify(transferIn, times(1)).settledTimestamp(any(ZonedDateTime.class));
        verify(transferOut, times(1)).settledTimestamp(any(ZonedDateTime.class));
    }
}
