package global.symbio.billing.core.payment.handler;

import global.symbio.billing.core.country.persistence.api.Country;
import global.symbio.billing.core.currency.persistence.api.Currency;
import global.symbio.billing.core.i18n.LocalisationSettings;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AccountAllocation;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AccountAllocationDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.payment.PaymentAccountAllocation;
import global.symbio.billing.core.payment.persistence.api.payment.Payment;
import global.symbio.billing.core.payment.persistence.api.repository.AccountAllocationRepository;
import global.symbio.billing.core.persistence.api.CompositeRepository;
import global.symbio.billing.core.reference.persistence.api.reference.Reference;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Map;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestPaymentAccountAllocationTransactionEncoder {

    private PaymentAccountAllocationTransactionEncoder encoder;

    @Mock
    private PaymentAccountAllocation<?> paymentAccountAllocation;

    @Mock
    private Payment payment;

    @Mock
    private LocalisationSettings localisationSettings;

    @Mock
    private CompositeRepository<AccountAllocationRepository> allocations;

    @BeforeEach
    public void setup() {
        encoder = new PaymentAccountAllocationTransactionEncoder(allocations);
    }

    @Test
    @DisplayName("PaymentAccountAllocationTransactionEncoder::build maps payment to transaction correctly")
    public void payment_account_allocation_transaction_encoder_maps_correctly() {
        final var currency = mock(Currency.class);
        final var currencyCode = 458;
        when(currency.getIdentifier()).thenReturn(currencyCode);
        final var description = "Test Payment";
        final var reference = mock(Reference.class);
        final var referenceId = UUID.randomUUID().toString();
        when(reference.getReference()).thenReturn(referenceId);
        final var country = mock(Country.class);
        final var countryCode = "MY";
        when(country.getCode()).thenReturn(countryCode);
        final var symbio = UUID.randomUUID();
        final var amount = BigDecimal.valueOf(100);
        final var account = UUID.randomUUID();
        final var timestamp = ZonedDateTime.now();
        final var paymentCategory = 3;

        when(localisationSettings.getSymbioAccountIdentifier()).thenReturn(symbio);
        when(paymentAccountAllocation.getPayment()).thenReturn(payment);
        when(payment.getCurrency()).thenReturn(currency);
        when(payment.getDescription()).thenReturn(description);
        when(payment.getReference()).thenReturn(reference);
        when(payment.getCountry()).thenReturn(country);
        when(paymentAccountAllocation.getAmount()).thenReturn(BigDecimal.valueOf(100));
        when(paymentAccountAllocation.getAccount()).thenReturn(account);
        when(paymentAccountAllocation.getTimestamp()).thenReturn(timestamp);

        final var transaction = encoder.build(paymentAccountAllocation, localisationSettings);

        assertEquals(currencyCode, transaction.getCurrency());
        assertEquals(paymentCategory, transaction.getCategory());
        assertEquals(amount, transaction.getAmount());
        assertEquals(BigDecimal.ZERO, transaction.getTaxation());
        assertEquals(symbio, transaction.getDebit());
        assertEquals(account, transaction.getCredit());
        assertEquals(description, transaction.getDescription());
        assertEquals(referenceId, transaction.getRef());
        assertEquals(countryCode, transaction.getCountry());
        assertEquals(timestamp, transaction.getTimestamp());
    }

    @Test
    @DisplayName("PaymentAccountAllocationTransactionEncoder::build throws given invalid account allocation")
    void payment_account_allocation_transaction_encoder_invalid_allocation() {
        final var invalidAllocation = mock(AccountAllocation.class);

        final var exception = assertThrows(IllegalStateException.class, () ->
            encoder.build(invalidAllocation, localisationSettings)
        );

        assertEquals("Account Allocation is not a Payment Account Allocation", exception.getMessage());
    }

    @Test
    @DisplayName("PaymentAccountAllocationTransactionEncoder::build throws given null reference")
    void payment_account_allocation_transaction_encoder_null_reference() {
        when(paymentAccountAllocation.getPayment()).thenReturn(payment);
        when(payment.getReference()).thenReturn(null);

        final var exception = assertThrows(IllegalStateException.class, () ->
            encoder.build(paymentAccountAllocation, localisationSettings)
        );

        assertEquals("Payment is missing a reference", exception.getMessage());
    }

    @Test
    @DisplayName("PaymentAccountAllocationTransactionEncoder::type is PaymentAccountAllocation")
    public void payment_account_allocation_transaction_encoder_returns_correct_type() {
        assertSame(PaymentAccountAllocation.class, encoder.type());
    }

    @Test
    @DisplayName("PaymentAccountAllocationTransactionEncoder::enrich returns a Map")
    public void payment_account_allocation_transaction_encoder_enrich_returns_map() {
        final var enrichment = encoder.enrich(paymentAccountAllocation, localisationSettings);
        assertEquals(Map.of(), enrichment);
    }

    @Test
    @DisplayName("PaymentAccountAllocationTransactionEncoder::onPublished updates timestamp")
    public void payment_account_allocation_transaction_encoder_on_published_updates_timestamp() {
        final var allocationRepo = mock(AccountAllocationRepository.class);
        final var allocationDao = mock(AccountAllocationDataAccessObject.class);
        doReturn(allocationDao).when(paymentAccountAllocation).data();
        when(allocations.write()).thenReturn(allocationRepo);

        assertDoesNotThrow(() -> encoder.onPublished(paymentAccountAllocation));
        verify(allocations, times(1)).write();
        verify(allocationDao, times(1)).sentTimestamp(any(ZonedDateTime.class));
    }

    @Test
    @DisplayName("PaymentAccountAllocationTransactionEncoder::onSettled updates timestamp")
    public void payment_account_allocation_transaction_encoder_on_settled_updates_timestamp() {
        final var allocationRepo = mock(AccountAllocationRepository.class);
        final var allocationDao = mock(AccountAllocationDataAccessObject.class);
        doReturn(allocationDao).when(paymentAccountAllocation).data();
        when(allocations.write()).thenReturn(allocationRepo);

        assertDoesNotThrow(() -> encoder.onSettled(paymentAccountAllocation));
        verify(allocations, times(1)).write();
        verify(allocationDao, times(1)).settledTimestamp(any(ZonedDateTime.class));
    }
}
