package global.symbio.billing.core.payment.handler;

import global.symbio.billing.core.i18n.LocalisationSettings;
import global.symbio.billing.core.kafka.model.KafkaTransaction;
import global.symbio.billing.core.payment.persistence.api.Recordable;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AccountAllocation;
import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.payment.PaymentAccountAllocation;
import global.symbio.billing.core.payment.persistence.api.repository.AccountAllocationRepository;
import global.symbio.billing.core.persistence.api.CompositeRepository;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.transaction.Transactional;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Objects;

import static global.symbio.billing.core.util.constants.CategoryConstants.CATEGORY_PAYMENT;

@Singleton
public class PaymentAccountAllocationTransactionEncoder implements TransactionEncoder {

    @Nonnull
    private final CompositeRepository<AccountAllocationRepository> allocations;

    @Inject
    public PaymentAccountAllocationTransactionEncoder(@Nonnull CompositeRepository<AccountAllocationRepository> allocations) {
        this.allocations = Objects.requireNonNull(allocations, "allocations");
    }

    @Nonnull
    @Override
    public KafkaTransaction build(@Nonnull AccountAllocation<?> allocation, @Nonnull LocalisationSettings localisation) {
        if (!(allocation instanceof PaymentAccountAllocation<?> type)) {
            throw new IllegalStateException("Account Allocation is not a Payment Account Allocation");
        }

        final var payment = type.getPayment();
        if (payment.getReference() == null) {
            throw new IllegalStateException("Payment is missing a reference");
        }

        final var transaction = new KafkaTransaction();
        transaction.setCurrency(payment.getCurrency().getIdentifier());
        transaction.setCategory(CATEGORY_PAYMENT);
        transaction.setAmount(type.getAmount());
        transaction.setTaxation(BigDecimal.ZERO);
        transaction.setDebit(localisation.getSymbioAccountIdentifier());
        transaction.setCredit(allocation.getAccount());
        transaction.setDescription(payment.getDescription());
        transaction.setRef(payment.getReference().getReference());
        transaction.setCountry(payment.getCountry().getCode());
        transaction.setTimestamp(allocation.getTimestamp());
        return transaction;
    }

    @Override
    @Transactional
    public void onPublished(@Nonnull AccountAllocation<?> allocation) {
        final var published = ZonedDateTime.now();
        allocations.write().update(allocation.data().sentTimestamp(published));
    }

    @Override
    @Transactional
    public void onSettled(@Nonnull AccountAllocation<?> allocation) {
        final var settled = ZonedDateTime.now();
        allocations.write().update(allocation.data().settledTimestamp(settled));
    }

    @Nonnull
    @Override
    public Class<? extends Recordable> type() {
        return PaymentAccountAllocation.class;
    }
}