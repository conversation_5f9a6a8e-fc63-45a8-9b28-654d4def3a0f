package global.symbio.billing.core.payment.handler;

import global.symbio.billing.core.payment.persistence.api.allocation.account.AccountAllocationDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.repository.PaymentAccountAllocationRepository;
import global.symbio.billing.core.payment.persistence.api.repository.TransferInAccountAllocationRepository;
import global.symbio.billing.core.payment.persistence.api.repository.TransferOutAccountAllocationRepository;
import global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.allocation.subtype.payment.JPAPaymentAccountAllocationDataAccessObject;
import global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.allocation.subtype.transfer.JPATransferInAccountAllocationDataAccessObject;
import global.symbio.billing.core.payment.persistence.spi.jdbc.jpa.allocation.subtype.transfer.JPATransferOutAccountAllocationDataAccessObject;
import global.symbio.billing.core.persistence.api.CompositeRepository;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.util.Objects;

@Singleton
public class AccountAllocationRepositoryHandler {

    @Nonnull
    private final CompositeRepository<PaymentAccountAllocationRepository<JPAPaymentAccountAllocationDataAccessObject>> paymentAllocations;

    @Nonnull
    private final CompositeRepository<TransferInAccountAllocationRepository<JPATransferInAccountAllocationDataAccessObject>> transferInAllocations;

    @Nonnull
    private final CompositeRepository<TransferOutAccountAllocationRepository<JPATransferOutAccountAllocationDataAccessObject>> transferOutAllocations;

    @Inject
    public AccountAllocationRepositoryHandler(
        @Nonnull CompositeRepository<PaymentAccountAllocationRepository<JPAPaymentAccountAllocationDataAccessObject>> paymentAllocations,
        @Nonnull CompositeRepository<TransferInAccountAllocationRepository<JPATransferInAccountAllocationDataAccessObject>> transferInAllocations,
        @Nonnull CompositeRepository<TransferOutAccountAllocationRepository<JPATransferOutAccountAllocationDataAccessObject>> transferOutAllocations
    ) {
        this.paymentAllocations = Objects.requireNonNull(paymentAllocations, "paymentAllocations");
        this.transferInAllocations = Objects.requireNonNull(transferInAllocations, "transferInAllocations");
        this.transferOutAllocations = Objects.requireNonNull(transferOutAllocations, "transferOutAllocations");
    }

    @Nonnull
    public AccountAllocationDataAccessObject<?> updateAllocation(@Nonnull AccountAllocationDataAccessObject<?> allocation) {
        return switch (allocation.getType()) {
            case PAYMENT -> paymentAllocations.write().update((JPAPaymentAccountAllocationDataAccessObject) allocation);
            case TRANSFER_IN ->
                transferInAllocations.write().update((JPATransferInAccountAllocationDataAccessObject) allocation);
            case TRANSFER_OUT ->
                transferOutAllocations.write().update((JPATransferOutAccountAllocationDataAccessObject) allocation);
            default -> throw new IllegalStateException("Unsupported type " + allocation.getType());
        };
    }

    @Nonnull
    public AccountAllocationDataAccessObject<?> saveAllocation(@Nonnull AccountAllocationDataAccessObject<?> allocation) {
        return switch (allocation.getType()) {
            case PAYMENT -> paymentAllocations.write().save((JPAPaymentAccountAllocationDataAccessObject) allocation);
            case TRANSFER_IN ->
                transferInAllocations.write().save((JPATransferInAccountAllocationDataAccessObject) allocation);
            case TRANSFER_OUT ->
                transferOutAllocations.write().save((JPATransferOutAccountAllocationDataAccessObject) allocation);
            default -> throw new IllegalStateException("Unsupported type " + allocation.getType());
        };
    }
}
