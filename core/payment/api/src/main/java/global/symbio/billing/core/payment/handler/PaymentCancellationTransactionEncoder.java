package global.symbio.billing.core.payment.handler;

import global.symbio.billing.core.exception.sdk.BillingEntityNotFoundException;
import global.symbio.billing.core.i18n.LocalisationSettings;
import global.symbio.billing.core.kafka.model.KafkaTransaction;
import global.symbio.billing.core.payment.persistence.api.Recordable;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AccountAllocation;
import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.payment.PaymentAccountAllocation;
import global.symbio.billing.core.payment.persistence.api.cancellation.PaymentCancellation;
import global.symbio.billing.core.payment.persistence.api.cancellation.PaymentCancellationDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.repository.PaymentCancellationRepository;
import global.symbio.billing.core.persistence.api.CompositeRepository;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.transaction.Transactional;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Map;
import java.util.Objects;

import static global.symbio.billing.core.util.constants.EnrichmentConstants.FIELD_ACCOUNT_ALLOCATION_ID;
import static global.symbio.billing.core.util.constants.CategoryConstants.CATEGORY_PAYMENT_CANCELLATION;

@Singleton
public class PaymentCancellationTransactionEncoder implements TransactionEncoder {

    @Nonnull
    private final CompositeRepository<PaymentCancellationRepository<PaymentCancellationDataAccessObject>> cancellations;

    @Inject
    public PaymentCancellationTransactionEncoder(
        @Nonnull CompositeRepository<PaymentCancellationRepository<PaymentCancellationDataAccessObject>> cancellations
    ) {
        this.cancellations = Objects.requireNonNull(cancellations, "cancellations");
    }

    @Nonnull
    @Override
    public KafkaTransaction build(@Nonnull AccountAllocation<?> allocation, @Nonnull LocalisationSettings localisation) {
        final var cancellation = getCancellation(allocation);
        final var payment = cancellation.getPayment();

        final var transaction = new KafkaTransaction();
        transaction.setCurrency(payment.getCurrency().getIdentifier());
        transaction.setCategory(CATEGORY_PAYMENT_CANCELLATION);
        transaction.setAmount(payment.getAmount());
        transaction.setTaxation(BigDecimal.ZERO);
        transaction.setDebit(allocation.getAccount());
        transaction.setCredit(localisation.getSymbioAccountIdentifier());
        transaction.setDescription(cancellation.getReason());
        transaction.setRef(cancellation.getReference());
        transaction.setCountry(payment.getCountry().getCode());
        transaction.setTimestamp(cancellation.getTimestamp());
        return transaction;
    }

    @Nonnull
    @Override
    public Map<String, ?> enrich(@Nonnull AccountAllocation<?> allocation, @Nonnull LocalisationSettings localisation) {
        return Map.of(FIELD_ACCOUNT_ALLOCATION_ID, allocation.getIdentifier());
    }

    @Override
    @Transactional
    public void onPublished(@Nonnull AccountAllocation<?> allocation) {
        final var cancellation = getCancellation(allocation);
        final var published = ZonedDateTime.now();
        cancellations.write().update(cancellation.sentTimestamp(published));
    }

    @Override
    @Transactional
    public void onSettled(@Nonnull AccountAllocation<?> allocation) {
        final var cancellation = getCancellation(allocation);
        final var settled = ZonedDateTime.now();
        cancellations.write().update(cancellation.settledTimestamp(settled));
    }

    @Nonnull
    @Override
    public Class<? extends Recordable> type() {
        return PaymentCancellation.class;
    }

    @Nonnull
    PaymentCancellationDataAccessObject getCancellation(@Nonnull AccountAllocation<?> allocation) {
        if (allocation instanceof PaymentAccountAllocation<?> paymentAccountAllocation) {
            final var payment = paymentAccountAllocation.getPayment();
            return cancellations.write().findByPaymentIdentifier(payment.getIdentifier()).orElseThrow(() -> new BillingEntityNotFoundException(PaymentCancellation.class, payment.getIdentifier()));
        } else {
            throw new IllegalStateException("Account Allocation is not a Payment Account Allocation");
        }
    }
}