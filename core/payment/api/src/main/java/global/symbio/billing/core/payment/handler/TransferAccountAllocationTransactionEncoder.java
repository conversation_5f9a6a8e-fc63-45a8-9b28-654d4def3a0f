package global.symbio.billing.core.payment.handler;

import global.symbio.billing.core.exception.sdk.BillingEntityNotFoundException;
import global.symbio.billing.core.i18n.LocalisationSettings;
import global.symbio.billing.core.kafka.model.KafkaTransaction;
import global.symbio.billing.core.payment.persistence.api.Recordable;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AccountAllocation;
import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.payment.PaymentAccountAllocation;
import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.transfer.TransferInAccountAllocation;
import global.symbio.billing.core.payment.persistence.api.repository.TransferRepository;
import global.symbio.billing.core.payment.persistence.api.transfer.TransferDataAccessObject;
import global.symbio.billing.core.persistence.api.CompositeRepository;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.transaction.Transactional;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Objects;

import static global.symbio.billing.core.util.constants.CategoryConstants.CATEGORY_PAYMENT_TRANSFER;

@Singleton
public class TransferAccountAllocationTransactionEncoder implements TransactionEncoder {

    @Nonnull
    private final CompositeRepository<TransferRepository<TransferDataAccessObject>> transfers;

    @Inject
    public TransferAccountAllocationTransactionEncoder(@Nonnull CompositeRepository<TransferRepository<TransferDataAccessObject>> transfers) {
        this.transfers = Objects.requireNonNull(transfers, "transfers");
    }

    @Nonnull
    @Override
    public KafkaTransaction build(@Nonnull AccountAllocation<?> allocation, @Nonnull LocalisationSettings localisation) {
        if (!(allocation instanceof TransferInAccountAllocation<?> inbound)) {
            throw new IllegalStateException("Account Allocation is not a TransferInAccountAllocation");
        }

        final var transfer = transfers.read().findByTransferInIdentifier(inbound.getIdentifier()).orElseThrow(() -> new BillingEntityNotFoundException(AccountAllocation.class, allocation.getIdentifier()));
        if (!(transfer.getOrigin().entity() instanceof PaymentAccountAllocation<?> origin)) {
            throw new IllegalStateException("Origin Account Allocation is not a PaymentAccountAllocation");
        }

        final var payment = origin.getPayment();
        if (payment.getReference() == null) {
            throw new IllegalStateException("Payment is missing a reference");
        }

        final var transaction = new KafkaTransaction();
        transaction.setCurrency(payment.getCurrency().getIdentifier());
        transaction.setCategory(CATEGORY_PAYMENT_TRANSFER);
        transaction.setAmount(transfer.getAmount());
        transaction.setTaxation(BigDecimal.ZERO);
        transaction.setDebit(transfer.getTransferOut().getAccount());
        transaction.setCredit(transfer.getTransferIn().getAccount());
        transaction.setDescription(transfer.getDescription());
        transaction.setRef(payment.getReference().getReference());
        transaction.setCountry(transfer.getCountry().getCode());
        transaction.setTimestamp(inbound.getTimestamp());
        return transaction;
    }

    @Override
    @Transactional
    public void onPublished(@Nonnull AccountAllocation<?> allocation) {
        final var published = ZonedDateTime.now();
        final var transfer = transfers.write().findByTransferInIdentifier(allocation.getIdentifier()).orElseThrow(() -> new BillingEntityNotFoundException(AccountAllocation.class, allocation.getIdentifier()));
        transfer.getTransferIn().sentTimestamp(published);
        transfer.getTransferOut().sentTimestamp(published);
        transfers.write().update(transfer);
    }

    @Override
    @Transactional
    public void onSettled(@Nonnull AccountAllocation<?> allocation) {
        final var settled = ZonedDateTime.now();
        final var transfer = transfers.write().findByTransferInIdentifier(allocation.getIdentifier()).orElseThrow(() -> new BillingEntityNotFoundException(AccountAllocation.class, allocation.getIdentifier()));
        transfer.getTransferIn().settledTimestamp(settled);
        transfer.getTransferOut().settledTimestamp(settled);
        transfers.write().update(transfer);
    }

    @Nonnull
    @Override
    public Class<? extends Recordable> type() {
        // we only need an encoder for TRANSFER_IN/inbound leg of a transfer
        // a transaction covers both debiting and crediting parties
        return TransferInAccountAllocation.class;
    }
}