package global.symbio.billing.core.payment.handler;

import global.symbio.billing.core.payment.persistence.api.Recordable;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Singleton
public class DefaultTransactionEncoderRegistry implements TransactionEncoderRegistry {

    @Nonnull
    private final Map<Class<? extends Recordable>, TransactionEncoder> handlers;

    @Inject
    public DefaultTransactionEncoderRegistry(
        @Nonnull Set<TransactionEncoder> handlers
    ) {
        this.handlers = Objects.requireNonNull(handlers, "handlers").stream().collect(Collectors.toUnmodifiableMap(TransactionEncoder::type, Function.identity()));
    }

    @Nullable
    @Override
    public TransactionEncoder lookup(@Nonnull Class<? extends Recordable> type) {
        return handlers.get(type);
    }
}
