package global.symbio.billing.core.payment.handler;

import global.symbio.billing.core.i18n.LocalisationSettings;
import global.symbio.billing.core.kafka.model.EnrichedKafkaTransaction;
import global.symbio.billing.core.kafka.model.KafkaTransaction;
import global.symbio.billing.core.payment.persistence.api.Recordable;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AccountAllocation;
import jakarta.annotation.Nonnull;

import java.util.Map;

public interface TransactionEncoder {

    @Nonnull
    Class<? extends Recordable> type();

    @Nonnull
    default EnrichedKafkaTransaction encode(@Nonnull AccountAllocation<?> allocation, @Nonnull LocalisationSettings localisation) {
        final var transaction = build(allocation, localisation);
        final var enrichment = enrich(allocation, localisation);
        return new EnrichedKafkaTransaction(transaction, enrichment);
    }

    @Nonnull
    KafkaTransaction build(@Nonnull AccountAllocation<?> allocation, @Nonnull LocalisationSettings localisation);

    @Nonnull
    default Map<String, ?> enrich(@Nonnull AccountAllocation<?> allocation, @Nonnull LocalisationSettings localisation) {
        return Map.of();
    }

    void onPublished(@Nonnull AccountAllocation<?> allocation);

    void onSettled(@Nonnull AccountAllocation<?> allocation);
}
