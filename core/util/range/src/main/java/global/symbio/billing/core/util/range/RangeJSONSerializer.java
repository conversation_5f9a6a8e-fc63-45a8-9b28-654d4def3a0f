package global.symbio.billing.core.util.range;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.google.common.collect.Range;
import io.hypersistence.utils.hibernate.type.range.guava.PostgreSQLGuavaRangeType;
import io.micronaut.context.annotation.Context;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.Objects;

@Slf4j
@Context
@Getter(AccessLevel.PACKAGE)
public class RangeJSONSerializer extends JsonSerializer<Range<?>> {

    @Nonnull
    private final PostgreSQLGuavaRangeType encoder;

    @Inject
    public RangeJSONSerializer() {
        this(PostgreSQLGuavaRangeType.INSTANCE);
    }

    @Inject
    public RangeJSONSerializer(@Nonnull PostgreSQLGuavaRangeType encoder) {
        this.encoder = Objects.requireNonNull(encoder, "encoder");
        log.info("Created {}", RangeJSONSerializer.class.getSimpleName());
    }

    @Override
    public void serialize(Range<?> value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        gen.writeString(encoder.asString(value));
    }
}
