package global.symbio.billing.core.util.range;

import com.google.common.collect.BoundType;
import com.google.common.collect.DiscreteDomain;
import com.google.common.collect.Range;
import jakarta.annotation.Nonnull;

/**
 * Utility class for working with Guava {@link Range} objects in a consistent, inclusive way.
 */
public final class Ranges {

    /**
     * Returns the inclusive lower bound of the given {@link Range}, adjusting for bound type.
     * <p>
     * If the lower bound is {@code CLOSED}, the lower endpoint is returned as-is.
     * If the lower bound is {@code OPEN}, the next discrete value is returned using the provided {@link DiscreteDomain}.
     *
     * @param range  The range to extract the lower bound from.
     * @param domain The discrete domain to use for adjusting open bounds.
     * @param <T>    The type of the range values.
     * @return The inclusive lowerbound value.
     */
    public static <T extends Comparable<? super T>> T lowerbound(@Nonnull final Range<T> range, @Nonnull final DiscreteDomain<T> domain) {
        return range.lowerBoundType() == BoundType.CLOSED ? range.lowerEndpoint() : domain.next(range.lowerEndpoint());
    }

    /**
     * Returns the inclusive lower bound of a {@link Range<Long>}, adjusting for bound type.
     *
     * @param range The range to extract the lower bound from.
     * @return The inclusive lowerbound value.
     */
    public static long lowerbound(@Nonnull final Range<Long> range) {
        return lowerbound(range, DiscreteDomain.longs());
    }

    /**
     * Returns the inclusive upper bound of the given {@link Range}, adjusting for bound type.
     * <p>
     * If the upper bound is {@code CLOSED}, the upper endpoint is returned as-is.
     * If the upper bound is {@code OPEN}, the previous discrete value is returned using the provided {@link DiscreteDomain}.
     *
     * @param range  The range to extract the upper bound from.
     * @param domain The discrete domain to use for adjusting open bounds.
     * @param <T>    The type of the range values.
     * @return The inclusive upperbound value.
     */
    public static <T extends Comparable<? super T>> T upperbound(@Nonnull final Range<T> range, @Nonnull final DiscreteDomain<T> domain) {
        return range.upperBoundType() == BoundType.CLOSED ? range.upperEndpoint() : domain.previous(range.upperEndpoint());
    }

    /**
     * Returns the inclusive upper bound of a {@link Range<Long>}, adjusting for bound type.
     *
     * @param range The range to extract the upper bound from.
     * @return The inclusive upperbound value.
     */
    public static long upperbound(@Nonnull final Range<Long> range) {
        return upperbound(range, DiscreteDomain.longs());
    }

    /**
     * Checks whether the given {@link Range} has both lower and upper bounds.
     *
     * @param range The range to check.
     * @param <T>   The type of the range values.
     * @return {@code true} if the range has both a lower and upper bound, {@code false} otherwise.
     */
    public static <T extends Comparable<? super T>> boolean isBounded(@Nonnull final Range<T> range) {
        return range.hasLowerBound() && range.hasUpperBound();
    }

    /**
     * Returns {@code true} if the range represents an empty set.
     * For example, {@code [5, 5)} is empty.
     *
     * @param range The range to evaluate.
     * @param <T>   The type of the range values.
     * @return {@code true} if the range is empty; {@code false} otherwise.
     */
    public static <T extends Comparable<? super T>> boolean isEmpty(@Nonnull final Range<T> range) {
        return range.isEmpty();
    }
}