package global.symbio.billing.core.util.range.discrete;

import com.google.common.collect.DiscreteDomain;
import jakarta.annotation.Nonnull;

import java.time.temporal.Temporal;
import java.time.temporal.TemporalAmount;
import java.util.Objects;

public class TemporalDiscreteDomain<T extends Temporal & Comparable<?>> extends DiscreteDomain<T> {

    @Nonnull
    private final TemporalAmount step;

    public TemporalDiscreteDomain(@Nonnull TemporalAmount step) {
        this.step = Objects.requireNonNull(step, "step");
    }

    @Nonnull
    @Override
    public T next(@Nonnull T value) {
        return (T) value.plus(step);
    }

    @Nonnull
    @Override
    public T previous(@Nonnull T value) {
        return (T) value.minus(step);
    }

    @Override
    public long distance(@Nonnull T start, @Nonnull T end) {
        var distance = 0L;
        for (final var unit : step.getUnits()) {
            distance += unit.between(start, end);
        }
        return distance;
    }
}
