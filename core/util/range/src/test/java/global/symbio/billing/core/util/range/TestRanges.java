package global.symbio.billing.core.util.range;

import com.google.common.collect.DiscreteDomain;
import com.google.common.collect.Range;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import static org.junit.jupiter.api.Assertions.*;

public class TestRanges {

    private static final long DEFAULT_LOWER_BOUND = 0L;
    private static final long DEFAULT_UPPER_BOUND = 1_000_000L;

    @Test
    @DisplayName("Closed range is bounded")
    public void closed_range_is_bounded() {
        final var range = Range.closed(DEFAULT_LOWER_BOUND, DEFAULT_UPPER_BOUND);
        assertTrue(Ranges.isBounded(range));
    }

    @ParameterizedTest
    @ValueSource(longs = {42, 69, 420, 1337})
    @DisplayName("Lower bound of closed range is inclusive")
    public void lower_bound_of_closed_range_is_equal_to_lowerbound(long bound) {
        final var range = Range.closed(bound, DEFAULT_UPPER_BOUND);
        final var lower = Ranges.lowerbound(range);
        assertEquals(bound, lower);
    }

    @ParameterizedTest
    @ValueSource(longs = {42, 69, 420, 1337})
    @DisplayName("Upper bound of closed range is inclusive")
    public void upper_bound_of_closed_range_is_equal_to_upperbound(long bound) {
        final var range = Range.closed(DEFAULT_LOWER_BOUND, bound);
        final var upper = Ranges.upperbound(range);
        assertEquals(bound, upper);
    }

    @Test
    @DisplayName("Open range is bounded")
    public void open_range_is_bounded() {
        final var range = Range.open(DEFAULT_LOWER_BOUND, DEFAULT_UPPER_BOUND);
        assertTrue(Ranges.isBounded(range));
    }

    @ParameterizedTest
    @ValueSource(longs = {42, 69, 420, 1337})
    @DisplayName("Lower bound of open range is exclusive")
    public void lower_bound_of_open_range_is_equal_to_lower_bound_plus_one(long bound) {
        final var range = Range.open(bound, DEFAULT_UPPER_BOUND);
        final var lower = Ranges.lowerbound(range);
        assertEquals(bound + 1L, lower);
    }

    @ParameterizedTest
    @ValueSource(longs = {42, 69, 420, 1337})
    @DisplayName("Upper bound of open range is exclusive")
    public void upper_bound_of_open_range_is_equal_to_upper_bound_minus_one(long bound) {
        final var range = Range.open(DEFAULT_LOWER_BOUND, bound);
        final var upper = Ranges.upperbound(range);
        assertEquals(bound - 1L, upper);
    }

    @Test
    @DisplayName("Infinite range is not empty")
    public void infinite_range_is_not_empty() {
        final var range = Range.<Long>all();
        assertFalse(Ranges.isEmpty(range));
    }

    @Test
    @DisplayName("Infinite range is unbounded")
    public void infinite_range_is_unbounded() {
        final var range = Range.<Long>all();
        assertFalse(Ranges.isBounded(range));
    }

    @Test
    @DisplayName("Infinite range has no lower bound and throws IllegalStateException")
    public void lower_bound_of_infinite_range_does_not_exist() {
        final var range = Range.<Long>all();
        assertThrows(IllegalStateException.class, () -> Ranges.lowerbound(range));
    }
    
    @Test
    @DisplayName("Infinite range has no upper bound and throws IllegalStateException")
    public void upper_bound_of_infinite_range_does_not_exist() {
        final var range = Range.<Long>all();
        assertThrows(IllegalStateException.class, () -> Ranges.upperbound(range));
    }

    @ParameterizedTest
    @ValueSource(longs = {42, 69, 420, 1337})
    @DisplayName("Empty range is empty")
    public void empty_range_is_empty(long bound) {
        final var range = Range.closedOpen(bound, bound);
        assertTrue(Ranges.isEmpty(range));
    }

    @Test
    @DisplayName("Canonical forms of open and closed ranges are equal")
    public void canonical_forms_of_open_and_closed_ranges_are_equal() {
        final var open = Range.open(DEFAULT_LOWER_BOUND - 1, DEFAULT_UPPER_BOUND + 1);
        final var closed = Range.closed(DEFAULT_LOWER_BOUND, DEFAULT_UPPER_BOUND);
        assertNotEquals(open, closed);

        final var openCanonicalForm = open.canonical(DiscreteDomain.longs());
        final var closedCanonicalForm = closed.canonical(DiscreteDomain.longs());
        assertEquals(openCanonicalForm, closedCanonicalForm);
    }
}
