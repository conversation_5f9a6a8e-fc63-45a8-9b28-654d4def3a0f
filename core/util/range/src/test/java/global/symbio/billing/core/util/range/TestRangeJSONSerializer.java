package global.symbio.billing.core.util.range;

import com.fasterxml.jackson.core.JsonGenerator;
import com.google.common.collect.Range;
import io.hypersistence.utils.hibernate.type.range.guava.PostgreSQLGuavaRangeType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestRangeJSONSerializer {

    private RangeJSONSerializer serializer;

    @Mock
    private JsonGenerator generator;

    @Mock
    private PostgreSQLGuavaRangeType encoder;

    @BeforeEach
    public void setup() {
        serializer = new RangeJSONSerializer(encoder);
    }

    @Test
    @DisplayName("RangeJSONSerializer::new requires non-null constructor arguments")
    public void range_json_serializer_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new RangeJSONSerializer(null));
    }

    @Test
    @DisplayName("RangeJSONSerializer::new with no-arg constructor initialises encoder to PostgreSQLGuavaRangeType::INSTANCE")
    public void range_json_serializer_no_arg_constructor_uses_shared_singleton() {
        final var serializer = new RangeJSONSerializer();
        assertSame(PostgreSQLGuavaRangeType.INSTANCE, serializer.getEncoder());
    }

    @Test
    @DisplayName("RangeJSONSerializer::serialize encodes range as JSON string")
    public void range_json_serializer_serialize_encodes_range() throws IOException {
        when(encoder.asString(any(Range.class))).thenCallRealMethod();
        final var range = Range.closed(0, 10);
        assertDoesNotThrow(() -> serializer.serialize(range, generator, null));
        verify(encoder, times(1)).asString(any(Range.class));
        verify(generator, times(1)).writeString(anyString());
    }
}