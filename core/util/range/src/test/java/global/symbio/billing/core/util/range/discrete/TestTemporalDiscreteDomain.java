package global.symbio.billing.core.util.range.discrete;

import com.google.common.collect.DiscreteDomain;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.Period;
import java.time.ZonedDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class TestTemporalDiscreteDomain {

    private DiscreteDomain<ZonedDateTime> domain;

    @Test
    @DisplayName("Next value is current date plus the temporal amount")
    public void next_is_current_date_plus_temporal_amount() {
        for (var step = 0; step < 12; step++) {
            final var periods = List.of(Period.ofDays(step), Period.ofMonths(step));
            for (final var period : periods) {
                domain = new TemporalDiscreteDomain<>(period);
                final var now = ZonedDateTime.now();
                final var next = domain.next(now);
                assertEquals(now.plus(period), next);
            }
        }
    }

    @Test
    @DisplayName("Previous value is current date minus the temporal amount")
    public void previous_is_current_date_minus_temporal_amount() {
        for (var step = 0; step < 12; step++) {
            final var periods = List.of(Period.ofDays(step), Period.ofMonths(step));
            for (final var period : periods) {
                domain = new TemporalDiscreteDomain<>(period);
                final var now = ZonedDateTime.now();
                final var previous = domain.previous(now);
                assertEquals(now.minus(period), previous);
            }
        }
    }

    @Test
    @DisplayName("Distance value is the duration between start and end values")
    public void distance_is_duration_between_start_and_end() {
        domain = new TemporalDiscreteDomain<>(Period.ofDays(1));
        final var now = ZonedDateTime.now();
        final var next = now.plusDays(1L);

        final var distance = domain.distance(now, next);
        assertEquals(1L, distance);
    }
}
