package global.symbio.billing.core.util.mapper;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;

public class TestMapperUtil {

    @Test
    @DisplayName("MapperUtil::fromJson returns new Object given Json payload")
    void mapper_util_from_json_returns_new_object_given_json_payload() {
        final var json = """
            {
                "field1": "field1 value",
                "field2": "field2 value",
                "field3": "field3 value"
            }
            """;
        final var result = MapperUtil.fromJson(json, Map.class);
        assertEquals("field1 value", result.get("field1"));
        assertEquals("field2 value", result.get("field2"));
        assertEquals("field3 value", result.get("field3"));
    }

    @Test
    @DisplayName("MapperUtil::fromJson throws RuntimeException given invalid Json payload")
    void mapper_util_from_json_throws_runtime_exception_given_invalid_json_payload() {
        final var json = """
            {
                "field1": "field1 value",
                "field2": ,
                "field3": "field3 value"
            }
            """;
        assertThrows(RuntimeException.class, () -> MapperUtil.fromJson(json, Map.class));
    }

    @ParameterizedTest
    @MethodSource("data")
    @DisplayName("MapperUtil::toJson returns new Object given Json payload")
    void mapper_util_to_json_returns_new_object_given_json_payload(final Map map) {
        final var result = MapperUtil.toJson(map);
        final var field1 = map.get("field1");
        final var field2 = map.get("field2");
        final var field3 = map.get("field3");

        assertTrue(result.contains(field1.toString()));
        assertTrue(result.contains(field2.toString()));
        assertTrue(result.contains(field3.toString()));
    }

    @ParameterizedTest
    @MethodSource("data")
    @DisplayName("MapperUtil::convertValue returns new Object given payload")
    void mapper_util_convert_value_returns_new_object_given_payload(final Map map) {
        final var result = MapperUtil.convertValue(map, LinkedHashMap.class);
        assertInstanceOf(LinkedHashMap.class, result);
        assertEquals(map.get("field1"), result.get("field1"));
        assertEquals(map.get("field2"), result.get("field2"));
        assertEquals(map.get("field3"), result.get("field3"));
    }

    @Test
    @DisplayName("MapperUtil::hasFieldWithValue correctly matches string values")
    void has_field_with_value_correctly_matches_string_values() {
        final var json = "{\"name\":\"John\", \"age\":30}";

        assertTrue(MapperUtil.hasFieldWithValue(json, "name", "John"));
        assertFalse(MapperUtil.hasFieldWithValue(json, "name", "Jane"));
    }

    @Test
    @DisplayName("MapperUtil::hasFieldWithValue correctly matches numeric values")
    void has_field_with_value_correctly_matches_numeric_values() {
        final var json = "{\"id\":100, \"amount\":99.99}";

        assertTrue(MapperUtil.hasFieldWithValue(json, "id", 100));
        assertFalse(MapperUtil.hasFieldWithValue(json, "id", 101));
        assertTrue(MapperUtil.hasFieldWithValue(json, "amount", 99.99));
    }

    @Test
    @DisplayName("MapperUtil::hasFieldWithValue correctly matches boolean values")
    void has_field_with_value_correctly_matches_boolean_values() {
        final var json = "{\"active\":true, \"verified\":false}";

        assertTrue(MapperUtil.hasFieldWithValue(json, "active", true));
        assertTrue(MapperUtil.hasFieldWithValue(json, "verified", false));
        assertFalse(MapperUtil.hasFieldWithValue(json, "active", false));
    }

    @Test
    @DisplayName("MapperUtil::hasFieldWithValue returns false for missing fields")
    void has_field_with_value_returns_false_for_missing_fields() {
        final var json = "{\"name\":\"John\", \"age\":30}";

        assertFalse(MapperUtil.hasFieldWithValue(json, "address", "New York"));
    }

    @Test
    @DisplayName("MapperUtil::hasFieldWithValue handles invalid JSON gracefully")
    void has_field_with_value_handles_invalid_json_gracefully() {
        String invalidJson = "{name:John, age:30}"; // Missing quotes

        assertFalse(MapperUtil.hasFieldWithValue(invalidJson, "name", "John"));
    }

    @Test
    @DisplayName("MapperUtil::hasFieldWithValue handles edge cases")
    void has_field_with_value_handles_edge_cases() {
        assertFalse(MapperUtil.hasFieldWithValue("{}", "name", "John"));
        assertFalse(MapperUtil.hasFieldWithValue("", "name", "John"));
        assertFalse(MapperUtil.hasFieldWithValue(null, "name", "John"));
        assertFalse(MapperUtil.hasFieldWithValue("{\"name\":\"John\"}", null, "John"));
    }

    @Test
    @DisplayName("MapperUtil::hasFieldWithValue matches accountingEntryCategoryId correctly")
    void has_field_with_value_matches_accounting_entry_category_id_correctly() {
        final var json = """
        {
          "amountWithoutTax": 0.0000,
          "accountId": 17,
          "accountingEntryId": ***********,
          "chargedDuration": 0,
          "description": "Call To VHA141520415933097",
          "invoiceId": 6432,
          "tax": 0,
          "amountWithTax": 0.0000,
          "subscriptionId": 19,
          "accountingEntryCategoryId": 1,
          "timestamp": *************
        }
        """;
        assertTrue(MapperUtil.hasFieldWithValue(json, "accountingEntryCategoryId", 1));
        assertFalse(MapperUtil.hasFieldWithValue(json, "accountingEntryCategoryId", 2));
    }

    private static Stream<Arguments> data() {
        return Stream.of(
            Arguments.of(Map.of("field1", "value1", "field2", 50, "field3", "")),
            Arguments.of(Map.of("field1", "hello", "field2", "world", "field3", "test")),
            Arguments.of(Map.of("field1", "2025/06/07", "field2", "value2", "field3", new BigDecimal("10.75")))
        );
    }

}
