package global.symbio.billing.core.util.mapper;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import jakarta.annotation.Nonnull;

public class MapperUtil {

    private static final ObjectMapper MAPPER = createMapper();

    public static ObjectMapper createMapper() {
        return new ObjectMapper().enable(DeserializationFeature.USE_BIG_DECIMAL_FOR_FLOATS).enable(JsonGenerator.Feature.WRITE_BIGDECIMAL_AS_PLAIN)
            .registerModule(new JavaTimeModule()).disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
            .disable(DeserializationFeature.ADJUST_DATES_TO_CONTEXT_TIME_ZONE)
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public static <T> T fromJson(@Nonnull String content, @Nonnull Class<T> type) {
        try {
            return MAPPER.readValue(content, type);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public static String toJson(@Nonnull Object object) {
        try {
            return MAPPER.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> T convertValue(@Nonnull Object object, @Nonnull Class<T> type) {
        return MAPPER.convertValue(object, type);
    }

    /**
     * Checks if a JSON string contains a specific field with a matching value using
     *
     * @param json The JSON string to check
     * @param fieldName The name of the field to look for
     * @param expectedValue The expected value of the field
     * @return true if the field exists with the expected value, false otherwise
     */
    public static boolean hasFieldWithValue(@Nonnull String json, @Nonnull String fieldName, @Nonnull Object expectedValue) {
        if (json == null || json.isEmpty() || fieldName == null || fieldName.isEmpty()) {
            return false;
        }

        try {
            JsonParser parser = MAPPER.getFactory().createParser(json);
            while (!parser.isClosed()) {
                if (parser.nextToken() == JsonToken.FIELD_NAME &&
                    fieldName.equals(parser.currentName())) {
                    JsonToken valueToken = parser.nextToken();
                    // Handle different value types
                    if (expectedValue instanceof String && valueToken == JsonToken.VALUE_STRING) {
                        return expectedValue.equals(parser.getValueAsString());
                    } else if (expectedValue instanceof Number && valueToken.isNumeric()) {
                        return expectedValue.toString().equals(parser.getValueAsString());
                    } else if (expectedValue instanceof Boolean &&
                        (valueToken == JsonToken.VALUE_TRUE || valueToken == JsonToken.VALUE_FALSE)) {
                        return expectedValue.equals(parser.getBooleanValue());
                    }
                    // Field found but value doesn't match type or value
                    return false;
                }
            }
        } catch (Exception e) {
            return false;
        }
        return false;
    }

}
