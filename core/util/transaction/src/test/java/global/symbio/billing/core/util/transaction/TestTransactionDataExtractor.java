package global.symbio.billing.core.util.transaction;

import global.symbio.billing.core.kafka.api.EnrichedKafkaTransactionEvent;
import global.symbio.billing.core.kafka.model.EnrichedKafkaTransaction;
import global.symbio.billing.core.util.constants.CategoryConstants;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Map;

import static global.symbio.billing.core.util.constants.EnrichmentConstants.FIELD_PLATFORM;
import static global.symbio.billing.core.util.constants.EnrichmentConstants.FIELD_PROVIDER;
import static global.symbio.billing.core.util.constants.MetricsConstants.PLATFORM_BILLING;
import static global.symbio.billing.core.util.transaction.TransactionDataExtractor.getAuditGroup;
import static global.symbio.billing.core.util.transaction.TransactionDataExtractor.getPlatform;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.when;

public class TestTransactionDataExtractor {
    @Test
    @DisplayName("TransactionDataExtractor::getPlatform returns PLATFORM_BILLING for CATEGORY_PAYMENT")
    void transactionDataExtractor_get_platform_returns_billing_platform_for_category_payment() {
        final var transactionEvent = mock(EnrichedKafkaTransactionEvent.class, RETURNS_DEEP_STUBS);
        final var categoryId = CategoryConstants.CATEGORY_PAYMENT;

        var result = getPlatform(transactionEvent, categoryId);

        assertEquals(PLATFORM_BILLING, result);
    }

    @Test
    @DisplayName("TransactionDataExtractor::getPlatform returns provider platform for CATEGORY_CALL_CHARGE")
    void transactionDataExtractor_get_platform_returns_provider_platform_for_category_call_charge() {
        int categoryId = CategoryConstants.CATEGORY_CALL_CHARGE;
        final var expected = "Sonar MVP02 MY";
        final var enrichedKafkaTransactionEvent = mock(EnrichedKafkaTransactionEvent.class);
        final var record = mock(ConsumerRecord.class, RETURNS_DEEP_STUBS);
        final var enrichedKafka = mock(EnrichedKafkaTransaction.class);
        final var enrichment = mock(Map.class);

        when(enrichment.get(FIELD_PROVIDER)).thenReturn(Map.of(FIELD_PLATFORM, expected));
        when(enrichedKafka.getEnrichment()).thenReturn(enrichment);
        when(record.value()).thenReturn(enrichedKafka);
        when(enrichedKafkaTransactionEvent.record()).thenReturn(record);

        final var result = getPlatform(enrichedKafkaTransactionEvent, categoryId);

        assertEquals(expected, result);
    }

    @Test
    @DisplayName("TransactionDataExtractor::getPlatform returns correct platform for other categories")
    void transactionDataExtractor_get_platform_calls_plexIDConverter_for_other_categories() {
        final var transactionEvent = mock(EnrichedKafkaTransactionEvent.class, RETURNS_DEEP_STUBS);
        final var categoryId = 999;
        final var ref = "product-bridge:SG MVP02 SONAR:63:19:573cf732-c94b-4710-b5f2-34a787c318c5";

        when(transactionEvent.record().value().getTransaction().getRef()).thenReturn(ref);

        final var result = getPlatform(transactionEvent, categoryId);
        assertEquals("MVP02", result);
    }

    @Test
    @DisplayName("TransactionDataExtractor::getAuditGroup returns transaction reference")
    void transactionDataExtractor_get_auditGroup_returns_transaction_reference() {
        final var transactionEvent = mock(EnrichedKafkaTransactionEvent.class, RETURNS_DEEP_STUBS);

        when(transactionEvent.record().value().getTransaction().getRef()).thenReturn("plexid");

        final var result = getAuditGroup(transactionEvent);

        assertEquals("plexid", result);
    }

    @Test
    @DisplayName("TransactionDataExtractor::extractPlatformFromEnrichment returns correct platform from enrichment")
    void transactionDataExtractor_extractPlatformFromEnrichment_returns_correct_platform() {
        final var expectedPlatform = "Sonar MVP02 MY";
        final var enrichedKafkaTransaction = mock(EnrichedKafkaTransaction.class);
        final var enrichment = mock(Map.class);

        when(enrichment.get(FIELD_PROVIDER)).thenReturn(Map.of(FIELD_PLATFORM, expectedPlatform));
        when(enrichedKafkaTransaction.getEnrichment()).thenReturn(enrichment);

        final var result = TransactionDataExtractor.extractPlatformFromEnrichment(enrichedKafkaTransaction);

        assertEquals(expectedPlatform, result);
    }

}
