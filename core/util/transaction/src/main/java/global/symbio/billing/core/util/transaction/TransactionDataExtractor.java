package global.symbio.billing.core.util.transaction;

import global.symbio.billing.core.kafka.api.EnrichedKafkaTransactionEvent;
import global.symbio.billing.core.kafka.model.EnrichedKafkaTransaction;
import global.symbio.billing.core.util.constants.CategoryConstants;
import global.symbio.billing.core.util.plexid.PlexIDConverter;
import jakarta.annotation.Nonnull;

import java.util.Map;
import java.util.Objects;

import static global.symbio.billing.core.util.constants.CategoryConstants.isPaymentCategory;
import static global.symbio.billing.core.util.constants.EnrichmentConstants.FIELD_PLATFORM;
import static global.symbio.billing.core.util.constants.EnrichmentConstants.FIELD_PROVIDER;
import static global.symbio.billing.core.util.constants.MetricsConstants.PLATFORM_BILLING;

public final class TransactionDataExtractor {

    private TransactionDataExtractor() {
    }

    public static String getPlatform(@Nonnull EnrichedKafkaTransactionEvent transaction, int categoryId) {
        if (isPaymentCategory(categoryId)) {
            return PLATFORM_BILLING;
        } else if (CategoryConstants.CATEGORY_CALL_CHARGE == categoryId) {
            return extractPlatformFromEnrichment(transaction.record().value());
        } else {
            return PlexIDConverter.getPlatform(transaction.record().value().getTransaction().getRef());
        }
    }

    public static String extractPlatformFromEnrichment(@Nonnull EnrichedKafkaTransaction transaction) {
        final var provider = (Map<?, ?>) transaction.getEnrichment().get(FIELD_PROVIDER);
        return Objects.toString(provider.get(FIELD_PLATFORM));
    }

    public static String getAuditGroup(@Nonnull EnrichedKafkaTransactionEvent event) {
        return event.record().value().getTransaction().getRef();
    }
}