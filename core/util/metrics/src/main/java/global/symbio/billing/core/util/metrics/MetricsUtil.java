package global.symbio.billing.core.util.metrics;

import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.CloseableThreadContext;

import java.util.Map;
import java.util.Objects;
import java.util.UUID;

import static global.symbio.billing.core.util.constants.MetricsConstants.*;

@Slf4j
@Singleton
public class MetricsUtil {

    public static void emitMetrics(@Nonnull Map<GroupingKey, Long> transactions, @Nonnull Map<String, Long> audits) {
        transactions.forEach((key, count) -> {
            if (key instanceof CallTransactionGroupingKey callKey) {
                emitCallChargeCount(
                    callKey.category(),
                    callKey.country(),
                    callKey.platform(),
                    callKey.callCategory(),
                    callKey.account(),
                    callKey.accountName(),
                    callKey.date(),
                    callKey.filename(),
                    callKey.direction(),
                    count
                );
            } else if (key instanceof TransactionGroupingKey) {
                emitTransactionCount(
                    key.category(),
                    key.country(),
                    key.platform(),
                    count
                );
            }
        });
        audits.forEach(MetricsUtil::emitAudit);
    }

    @Nonnull
    public static CloseableThreadContext.Instance create(@Nonnull Map<String, String> mdc) {
        return CloseableThreadContext.putAll(mdc);
    }

    private static void emitTransactionCount(
        @Nonnull String category,
        @Nonnull String country,
        @Nonnull String platform,
        @Nonnull Long count
    ) {
        try (final var _ = create(Map.of(
            CONTEXT_TAG_TXN_CATEGORY, category,
            CONTEXT_TAG_TXN_COUNTRY, country,
            CONTEXT_TAG_TXN_PLATFORM, platform,
            CONTEXT_TAG_TXN_COUNT, Objects.toString(count)))
        ) {
            log.info("Ingested transactions of type: {}, {}, {} - {}", category, country, platform, count);
        }
    }

    private static void emitCallChargeCount(
        @Nonnull String category,
        @Nonnull String country,
        @Nonnull String platform,
        @Nonnull String callCategory,
        @Nonnull String account,
        @Nonnull String accountName,
        @Nonnull String date,
        @Nonnull String filename,
        @Nonnull String callDirection,
        @Nonnull Long count
    ) {
        try (final var _ = create(Map.of(
            CONTEXT_TAG_TXN_CATEGORY, category,
            CONTEXT_TAG_TXN_COUNTRY, country,
            CONTEXT_TAG_TXN_PLATFORM, platform,
            CONTEXT_TAG_TXN_CALL_CATEGORY, callCategory,
            CONTEXT_TAG_TXN_ACCOUNT, account,
            CONTEXT_TAG_TXN_ACCOUNT_NAME, accountName,
            CONTEXT_TAG_TXN_DATE, date,
            CONTEXT_TAG_TXN_FILENAME, filename,
            CONTEXT_TAG_TXN_CALL_DIRECTION, callDirection,
            CONTEXT_TAG_TXN_COUNT, Objects.toString(count)))
        ) {
            log.info("Ingested call charges of type: {}, {}, {}, {}, {}, {}, {}, {}, {} - {}", category, country, platform, callCategory, account, accountName, date, filename, callDirection, count);
        }
    }

    private static void emitAudit(
        @Nonnull String key,
        @Nonnull Object value
    ) {
        try (final var _ = create(Map.of(
            CONTEXT_TAG_TXN_AUDIT_TYPE, key,
            CONTEXT_TAG_TXN_AUDIT_COUNT, Objects.toString(value)))
        ) {
            log.info("Transaction audit group: {} - {}", key, value);
        }
    }

    public static void emitDuplicateTransaction(
        @Nonnull UUID identifier,
        @Nonnull String details
    ) {
        try (final var _ = create(Map.of(
            CONTEXT_TAG_TXN_DUPLICATE_IDENTIFIER, Objects.toString(identifier),
            CONTEXT_TAG_TXN_DUPLICATE_DETAILS, details))
        ) {
            log.info("Insert query skipped (conflict) for identifier: {}, {}", identifier, details);
        }
    }

    public interface GroupingKey {
        @Nonnull String category();
        @Nonnull String country();
        @Nonnull String platform();
    }

    public record TransactionGroupingKey(String category, String country, String platform) implements GroupingKey {}

    public record CallTransactionGroupingKey(
        String category,
        String country,
        String platform,
        String callCategory,
        String account,
        String accountName,
        String date,
        String direction,
        String filename
    ) implements GroupingKey {}
}