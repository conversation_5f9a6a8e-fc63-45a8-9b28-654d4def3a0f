package global.symbio.billing.core.util.metrics;

import org.apache.logging.log4j.ThreadContext;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;

@ExtendWith(MockitoExtension.class)
public class TestMetricsUtil {

    @Test
    @DisplayName("MetricsUtil::create puts parameters in thread context")
    void metrics_util_create_puts_parameters_in_thread_context() {
        try (final var _ = MetricsUtil.create(Map.of("Key1", "Value1","Key2", "Value2"))) {
            assertEquals("Value1", ThreadContext.get("Key1"));
            assertEquals("Value2", ThreadContext.get("Key2"));
        }
    }
}
