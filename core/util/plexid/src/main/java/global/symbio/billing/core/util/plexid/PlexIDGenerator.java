package global.symbio.billing.core.util.plexid;

import jakarta.annotation.Nonnull;

import java.util.Objects;
import java.util.StringJoiner;
import java.util.UUID;

public class PlexIDGenerator {

    private static final String PLEXID = "plexid";

    @Nonnull
    public static String generate(String service, Object... parameters) {
        final var joiner = new StringJoiner(":").add(PLEXID).add(service);
        if (parameters != null) {
            for (final var parameter : parameters) {
                joiner.add(Objects.toString(parameter));
            }
        }
        return joiner.toString();
    }

    @Nonnull
    public static String generateBusiness(@Nonnull UUID uuid) {
        return generate("customer", uuid);  //Plexus still uses customer in plexid
    }

    @Nonnull
    public static String generateAccount(@Nonnull UUID uuid) {
        return generate("account", uuid);
    }

    @Nonnull
    public static String generatePayment(@Nonnull UUID uuid) {
        return generate("payment", uuid);
    }

    @Nonnull
    public static String generateTransfer(@Nonnull UUID uuid) {
        return generate("transfer", uuid);
    }

    @Nonnull
    public static String generatePaymentCancellation(@Nonnull UUID uuid) {
        return generate("payment", "cancellation", uuid);
    }

    @Nonnull
    public static String generateInvoice(@Nonnull UUID uuid) {
        return generate("invoice", uuid);
    }

    @Nonnull
    public static String generateAccountAllocation(@Nonnull UUID uuid) {
        return generate("account", "allocation", uuid);
    }
}
