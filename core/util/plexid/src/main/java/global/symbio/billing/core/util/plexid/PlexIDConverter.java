package global.symbio.billing.core.util.plexid;

import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

import java.util.UUID;

@Singleton
public class PlexIDConverter {

    @Nonnull
    public UUID getIdentifier(@Nonnull String plexid) {
        final var components = plexid.split(":");
        final var uuid = components[components.length - 1];
        return UUID.fromString(uuid);
    }

    @Nonnull
    public static String getPlatform(@Nonnull String plexid) {
        //"ref": "product-bridge:SG MVP02 SONAR:63:19:573cf732-c94b-4710-b5f2-34a787c318c5",
        final var components = plexid.split(":");
        final var source = components[1];
        final var sourceComponents = source.split(" ");
        return sourceComponents[1];
    }
}