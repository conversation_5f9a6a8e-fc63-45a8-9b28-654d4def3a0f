package global.symbio.billing.core.util.plexid;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

public class TestPlexIDConverter {
    
    private PlexIDConverter plexid;

    @BeforeEach
    public void setup() {
        plexid = new PlexIDConverter();
    }

    @Test
    @DisplayName("PlexIDConverter::getIdentifier extracts UUID from plexid containing valid UUID")
    void plexid_converter_get_identifier_extracts_uuid_from_plexid_containing_uuid() {
        final var expected = UUID.randomUUID();
        final var input = "prefix" + ":" + expected;
        final var actual = plexid.getIdentifier(input);
        assertEquals(expected, actual);
    }

    @Test
    @DisplayName("PlexIDConverter::getIdentifier throws IllegalArgumentException given empty input")
    void plexid_converter_get_identifier_throws_illegal_argument_exception_given_empty_input() {
        assertThrows(IllegalArgumentException.class, () -> plexid.getIdentifier(""));
    }

    @Test
    @DisplayName("PlexIDConverter::getIdentifier throws IllegalArgumentException given input without UUID")
    void plexid_converter_get_identifier_throws_illegal_argument_exception_given_input_without_uuid() {
        assertThrows(IllegalArgumentException.class, () -> plexid.getIdentifier("prefix:"));
    }

    @Test
    @DisplayName("PlexIDConverter::getIdentifier throws IllegalArgumentException given input containing invalid UUID")
    void plexid_converter_get_identifier_throws_illegal_argument_exception_given_input_containing_invalid_uuid() {
        assertThrows(IllegalArgumentException.class, () -> plexid.getIdentifier("prefix:invalid-uuid"));
    }

    @Test
    @DisplayName("PlexIDConverter::getIdentifier throws NullPointerException given null input")
    void plexid_converter_get_identifier_throws_null_pointer_exception_given_null_input() {
        assertThrows(NullPointerException.class, () -> plexid.getIdentifier(null));
    }

    @Test
    @DisplayName("PlexIDConverter::getPlatform extracts platform from plexid")
    void plexid_converter_get_platform_extracts_platform_from_plexid() {
        final var expected = "MVP02";
        final var input = "product-bridge:SG MVP02 SONAR:63:19:573cf732-c94b-4710-b5f2-34a787c318c5";
        final var actual = PlexIDConverter.getPlatform(input);
        assertEquals(expected, actual);
    }

    @Test
    @DisplayName("PlexIDConverter::getPlatform extracts platform from plexid throws NullPointerException given null input")
    void plexid_converter_get_platform_extracts_platform_from_plexid_throws_null_pointer_exception_given_null_input() {
        assertThrows(NullPointerException.class, () -> PlexIDConverter.getPlatform(null));
    }
}
