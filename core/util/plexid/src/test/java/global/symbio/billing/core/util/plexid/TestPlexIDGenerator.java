package global.symbio.billing.core.util.plexid;

import com.fasterxml.uuid.impl.UUIDUtil;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.Objects;
import java.util.StringJoiner;
import java.util.UUID;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class TestPlexIDGenerator {

    @Nonnull
    private static final UUID ID = UUIDUtil.nilUUID();

    @ParameterizedTest
    @MethodSource("arguments")
    @DisplayName("PlexIDGenerator::generate correct plexid given valid parameters")
    public void plexid_generator_generate_creates_plexid_given_valid_parameters(String service, Object[] parameters){
        final var plexid = PlexIDGenerator.generate(service, parameters);

        final var joiner = new StringJoiner(":").add("plexid").add(service);
        if (parameters != null) {
            for (final var parameter : parameters) {
                joiner.add(Objects.toString(parameter));
            }
        }

        assertEquals(joiner.toString(), plexid);
    }

    @Test
    @DisplayName("PlexIDGenerator::generate correct business plexid")
    public void plexid_generator_generate_creates_business_plexid() {
        final var uuid = UUID.randomUUID();
        final var plexid = PlexIDGenerator.generateBusiness(uuid);
        assertEquals("plexid:customer:" + uuid, plexid);
    }

    @Test
    @DisplayName("PlexIDGenerator::generate correct account plexid")
    public void plexid_generator_generate_creates_account_plexid() {
        final var uuid = UUID.randomUUID();
        final var plexid = PlexIDGenerator.generateAccount(uuid);
        assertEquals("plexid:account:" + uuid, plexid);
    }

    @Test
    @DisplayName("PlexIDGenerator::generate correct payment plexid")
    public void plexid_generator_generate_creates_payment_plexid() {
        final var uuid = UUID.randomUUID();
        final var plexid = PlexIDGenerator.generatePayment(uuid);
        assertEquals("plexid:payment:" + uuid, plexid);
    }

    @Test
    @DisplayName("PlexIDGenerator::generate correct transfer plexid")
    public void plexid_generator_generate_creates_transfer_plexid() {
        final var uuid = UUID.randomUUID();
        final var plexid = PlexIDGenerator.generateTransfer(uuid);
        assertEquals("plexid:transfer:" + uuid, plexid);
    }

    @Test
    @DisplayName("PlexIDGenerator::generate correct payment cancellation plexid")
    public void plexid_generator_generate_creates_payment_cancellation_plexid() {
        final var uuid = UUID.randomUUID();
        final var plexid = PlexIDGenerator.generatePaymentCancellation(uuid);
        assertEquals("plexid:payment:cancellation:" + uuid, plexid);
    }

    @Test
    @DisplayName("PlexIDGenerator::generate correct invoice plexid")
    public void plexid_generator_generate_creates_invoice_plexid() {
        final var uuid = UUID.randomUUID();
        final var plexid = PlexIDGenerator.generateInvoice(uuid);
        assertEquals("plexid:invoice:" + uuid, plexid);
    }

    @Test
    @DisplayName("PlexIDGenerator::generate correct account allocation plexid")
    public void plexid_generator_generate_creates_account_allocation_plexid() {
        final var uuid = UUID.randomUUID();
        final var plexid = PlexIDGenerator.generateAccountAllocation(uuid);
        assertEquals("plexid:account:allocation:" + uuid, plexid);
    }

    private static Stream<Arguments> arguments() {
        return Stream.of(
                Arguments.of("product-bridge", new Object[] { "transaction", ID, "type", "Lease", "identifier", "reference", "account", "1", "invoice", "2" }),
                Arguments.of("payment", new Object[] { "transaction", ID, "invoice", ID }),
                Arguments.of("payment", new Object[] { }),
                Arguments.of("payment", null)
        );
    }
}
