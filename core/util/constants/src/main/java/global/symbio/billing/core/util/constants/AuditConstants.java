package global.symbio.billing.core.util.constants;

public final class AuditConstants {

    private AuditConstants() {}

    public static final String ACCOUNT = "account";
    public static final String ACCOUNT_ALLOCATION = "accountAllocation";
    public static final String BUSINESS = "business";
    public static final String INVOICE = "invoice";
    public static final String PAYMENT = "payment";
    public static final String TRANSFER = "transfer";
    public static final String TRANSFER_IN = "transferIn";
    public static final String CANCELLATION = "cancellation";
    public static final String ORIGIN = "origin";
    public static final String FROM_ACCOUNT = "fromAccount";
    public static final String TO_ACCOUNT = "toAccount";
    public static final String ORIGIN_PAYMENT = "originPayment";
    public static final String ORIGIN_PAYMENT_ACCOUNT_ALLOCATION = "originPaymentAccountAllocation";
}
