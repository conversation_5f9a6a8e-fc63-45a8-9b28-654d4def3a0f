package global.symbio.billing.core.util.constants;

public final class EnrichmentConstants {

    private EnrichmentConstants() {}

    public static final String FIELD_TIMESTAMP = "timestamp";

    //PAYMENT CANCELLATION
    public static final String FIELD_ACCOUNT_ALLOCATION_ID = "accountAllocationId";

    //ARBITRARY/ADDITIONAL CHARGE
    public static final String FIELD_BILLING_REFERENCE = "billingReference";

    //LEASE CHARGE
    public static final String FIELD_LEASE_CHARGE_TYPE = "leaseChargeType";
    public static final String FIELD_FROM_DATE = "fromDate";
    public static final String FIELD_TO_DATE = "toDate";

    //PORTING CHARGE
    public static final String FIELD_PORTING_CHARGE_DESCRIPTION = "portingChargeDescription";

    //CALL CHARGE
    public static final String FIELD_CHARGE = "charge";
    public static final String FIELD_CALL_RELEASE_TIMESTAMP = "callReleaseTimestamp";
    public static final String FIELD_START_TIMESTAMP = "callStartTimestamp";
    public static final String FIELD_RATED_TIMESTAMP = "ratedTimestamp";
    public static final String FIELD_TOTAL_CHARGE_DURATION_SECONDS = "totalChargeDuration";
    public static final String FIELD_CALL_CHARGE_DURATION_SECONDS = "chargeDuration";
    public static final String FIELD_CALL_COMPLETION_STATE_IDENTIFIER = "callCompletedStateID";
    public static final String FIELD_CALL_DIRECTION = "callDirection";
    public static final String FIELD_CALL_CATEGORY = "callCategory";
    public static final String FIELD_CHARGE_CATEGORY = "chargeCategory";
    public static final String FIELD_VOICE_SERVICE_TRANSMISSION = "voiceServiceTransmission";
    public static final String FIELD_RATED_TAX_PERCENTAGE = "taxPercentage";
    public static final String FIELD_PEAK_USAGE = "peakUsage";
    public static final String FIELD_CAPPED = "capped";
    public static final String FIELD_FIXED_CLASSIFICATION = "fixedClassification";
    public static final String FIELD_PROVIDER = "provider";
    public static final String FIELD_PLATFORM = "platform";
    public static final String FIELD_CLASSIFICATION = "classification";
    public static final String FIELD_TERMINATING_NUMBERING_AREA = "terminatingNumberingArea";
    public static final String FIELD_CHARGING_DISTRICT = "chargingDistrict";
}
