package global.symbio.billing.core.util.constants;

public final class CategoryConstants {

    private CategoryConstants() {}

    public static final int CATEGORY_CALL_CHARGE = 1;
    public static final int CATEGORY_SERVICE_CHARGE = 2;
    public static final int CATEGORY_PAYMENT = 3;
    public static final int CATEGORY_CREDIT_ADJUSTMENT = 8;
    public static final int CATEGORY_PAYMENT_CANCELLATION = 10;
    public static final int CATEGORY_LEASE_CHARGE = 18;
    public static final int CATEGORY_ARBITRARY_CHARGE = 19;
    public static final int CATEGORY_LNP_CHARGE = 28;
    public static final int CATEGORY_PAYMENT_TRANSFER = 38;

    public static boolean isPaymentCategory(int categoryId) {
        return CATEGORY_PAYMENT == categoryId ||
            CATEGORY_PAYMENT_CANCELLATION == categoryId ||
            CATEGORY_PAYMENT_TRANSFER == categoryId;
    }

}
