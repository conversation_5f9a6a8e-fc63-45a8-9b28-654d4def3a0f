package global.symbio.billing.core.util.constants;


import java.util.regex.Pattern;

public final class APIConstants {

    private APIConstants() {}

    public static final String GROUP_ERROR_STATUS = "status";
    public static final String GROUP_ERROR_DETAILS = "details";
    public static final Pattern GRPC_ERROR_REGEX = Pattern.compile("io.grpc.StatusRuntimeException:\\s+(?<"+ GROUP_ERROR_STATUS +">\\w+)\\s*:\\s*(?<" + GROUP_ERROR_DETAILS + ">.+)");

    public static final String ACCOUNT_SERVICE_MY_HEALTH_NAME = "accountServiceMY";
    public static final String ACCOUNT_SERVICE_SG_HEALTH_NAME = "accountServiceSG";
    public static final String ACCOUNT_SERVICE_AU_HEALTH_NAME = "accountServiceAU";
    public static final String CUSTOMER_SERVICE_HEALTH_NAME = "customerService";
    public static final String SYSTEM_GATEWAY_MY_HEALTH_NAME = "systemGatewayMY";
    public static final String SYSTEM_GATEWAY_SG_HEALTH_NAME = "systemGatewaySG";
    public static final String CUSTOMER_BRIDGE_MY_HEALTH_NAME = "customerBridgeMY";
    public static final String CUSTOMER_BRIDGE_SG_HEALTH_NAME = "customerBridgeSG";
    public static final String CUSTOMER_BRIDGE_AU_HEALTH_NAME = "customerBridgeAU";
    public static final String NOTIFICATION_SERVICE_HEALTH_NAME = "notificationService";
    public static final String INVOICE_SERVICE_HEALTH_NAME = "invoiceService";
    public static final String DOWNSTREAM_SERVICES_HEALTH_NAME = "downstreamServices";
    public static final String HEALTH_RESULT_OK = "\"result\":\"OK\"";
    public static final String HEALTH_STATUS_UP = "\"status\":\"UP\"";
    public static final String PERMISSIONS = "permissions";
    public static final String INVOICE = "invoice";
    public static final String ACCOUNT = "account";
    public static final String PAYMENT = "payment";
    public static final String TRANSFER = "transfer";
    public static final String CREATE = "create";
    public static final String GET = "get";
    public static final String CANCEL = "cancel";
    public static final String SLASH_SIGN = "/";
    public static final String AUDIT = "audit";
    public static final String PAYMENT_CREATE = PAYMENT + SLASH_SIGN + CREATE;
    public static final String PAYMENT_GET = PAYMENT + SLASH_SIGN + GET;
    public static final String PAYMENT_CANCEL = PAYMENT + SLASH_SIGN + CANCEL;
    public static final String ACCOUNT_GET = ACCOUNT + SLASH_SIGN + GET;
    public static final String INVOICE_GET = INVOICE + SLASH_SIGN + GET;
    public static final String TRANSFER_GET = TRANSFER + SLASH_SIGN + GET;
    public static final String TRANSFER_CREATE = TRANSFER + SLASH_SIGN + CREATE;
}
