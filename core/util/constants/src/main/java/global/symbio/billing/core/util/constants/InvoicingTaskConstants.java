package global.symbio.billing.core.util.constants;

import java.time.Duration;

public final class InvoicingTaskConstants {
    private InvoicingTaskConstants() {
        // Prevent instantiation
        throw new AssertionError("No InvoicingTaskConstants instance");
    }

    public static final Duration RETRY_INTERVAL = Duration.ofSeconds(30L);
    public static final int RETRY_ATTEMPTS_THRESHOLD = 15;

    public static final String BUSINESS_UNIT_CONNECT = "CO";

    /**
     * The starting sequence number for the first invoice of an account
     */
    public static final long ABSENT_TRANSACTION_SEQUENCE = 0L;
    public static final int ABSENT_INVOICE_NUMBER = 0;
}
