package global.symbio.billing.core.util.constants;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class MetricsConstants {
    public static final String CONTEXT_TAG_SCOPE = "scope";
    public static final String CONTEXT_TAG_ACCOUNT = "account";
    public static final String CONTEXT_TAG_JOB_ID = "jobId";
    public static final String CONTEXT_TAG_JOB_NAME = "jobName";
    public static final String CONTEXT_TAG_JOB_CORRELATION = "jobCorrelation";
    public static final String CONTEXT_TAG_JOB_STATE = "jobState";
    public static final String CONTEXT_TAG_JOB_TYPE = "jobType";
    public static final String CONTEXT_TAG_JOB_SCOPE = "jobScope";
    public static final String CONTEXT_TAG_JOB_COUNTRY = "jobCountry";
    public static final String CONTEXT_TAG_JOB_DURATION = "jobDuration";
    public static final String CONTEXT_TAG_JOB_ORDER = "jobOrder";
    public static final String CONTEXT_TAG_JOB_DESCRIPTION = "jobDescription";
    public static final String SCOPE_INVOICING = "invoicing";

    public static final String CONTEXT_TAG_TXN_CATEGORY = "txnCategory";
    public static final String CONTEXT_TAG_TXN_COUNTRY = "txnCountry";
    public static final String CONTEXT_TAG_TXN_PLATFORM = "txnPlatform";
    public static final String CONTEXT_TAG_TXN_CALL_CATEGORY = "txnCallCategory";
    public static final String CONTEXT_TAG_TXN_ACCOUNT = "txnAccount";
    public static final String CONTEXT_TAG_TXN_ACCOUNT_NAME = "txnAccountName";
    public static final String CONTEXT_TAG_TXN_DATE = "txnDate";
    public static final String CONTEXT_TAG_TXN_FILENAME = "txnFilename";
    public static final String CONTEXT_TAG_TXN_CALL_DIRECTION = "txnCallDirection";
    public static final String CONTEXT_TAG_TXN_COUNT = "txnCount";
    public static final String CONTEXT_TAG_TXN_AUDIT_TYPE = "txnAuditType";
    public static final String CONTEXT_TAG_TXN_AUDIT_COUNT = "txnAuditCount";
    public static final String CONTEXT_TAG_TXN_DUPLICATE_IDENTIFIER = "txnDuplicateIdentifier";
    public static final String CONTEXT_TAG_TXN_DUPLICATE_DETAILS = "txnDuplicateDetails";

    public static final String CONTEXT_TAG_API_NAME = "apiName";
    public static final String CONTEXT_TAG_API_STATUS = "apiStatus";
    public static final String CONTEXT_TAG_API_STATUS_CODE = "apiStatusCode";
    public static final String CONTEXT_TAG_API_DURATION = "apiDuration";
    public static final String CONTEXT_TAG_API_ERROR_DETAILS = "apiErrorDetails";
    public static final String CONTEXT_TAG_API_DESCRIPTION = "apiDescription";

    public static final String PLATFORM_BILLING = "Billing";

    public static final String JOB_FETCH_BILLABLE_ACCOUNTS_TASK = "FetchBillableAccountsTask";
    public static final String JOB_GENERATE_ACCOUNT_INVOICE_STATE_TASK = "GenerateAccountInvoiceStateTask";
    public static final String JOB_GENERATE_INVOICE_SUMMARY_PARAMETERS_TASK = "GenerateInvoiceSummaryParametersTask";
    public static final String JOB_SUMMARISE_ACCOUNT_INVOICE_TASK = "SummariseAccountInvoiceTask";
    public static final String JOB_SUMMARISE_TASK = "SummariseTask";
    public static final String JOB_GENERATE_CDR_TASK = "GenerateCDRTask";
    public static final String JOB_SEND_CDR_TASK = "SendCDRTask";
    public static final String JOB_UPDATE_BILLING_DATE_TASK = "UpdateBillingDateTask";
    public static final String JOB_SAVE_DYNAMO_SUMMARY_TASK = "SaveDynamoSummaryTask";
    public static final String JOB_POLL_STEP_FUNCTION_TASK = "PollStepFunctionTask";
    public static final String JOB_UPLOAD_ATTACHMENT_TASK = "UploadAttachmentTask";
    public static final String JOB_SEND_INVOICE_NOTIFICATION_TASK = "SendInvoiceNotificationTask";
    public static final String JOB_NOTIFICATION_STATE_UPDATER_TASK = "NotificationStateUpdaterTask";

    public static final String METRIC_GROUP_PREFIX = "task.scheduler.";
    public static final String METRIC_LATENCY = METRIC_GROUP_PREFIX + "latency";
    public static final String METRIC_COUNT = METRIC_GROUP_PREFIX + "count";
    public static final String TAG_JOB_TYPE = METRIC_GROUP_PREFIX + "job.type";
    public static final String TAG_JOB_STATE = METRIC_GROUP_PREFIX + "job.state";
    public static final String METRIC_GROUP_RETRY_LIMIT_EXCEEDED = METRIC_GROUP_PREFIX + "retry.limit.exceeded.count";

    public static final String FAILURE_TYPE_TRANSIENT = "Transient";
    public static final String FAILURE_TYPE_TERMINAL = "Terminal";

    public static final String API_DESCRIPTION_PAYMENT = "Payment";
    public static final String API_DESCRIPTION_CANCELLATION = "Cancellation";
    public static final String API_DESCRIPTION_TRANSFER = "Transfer";
}
