package global.symbio.billing.core.util.checksum.luhn;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

public class TestLuhnGenerator {

    private LuhnGenerator generator;

    @BeforeEach
    public void setup() {
        generator = new LuhnGenerator();
    }

    @Test
    @DisplayName("LuhnGenerator::generate rejects null parameter")
    public void luhn_generator_generate_invoice_reference_null_parameters() {
        assertThrows(NullPointerException.class, () -> generator.generate(null));
    }

    @ParameterizedTest
    @MethodSource("arguments")
    @DisplayName("LuhnGenerator::generate correct invoice reference given valid parameters")
    public void luhn_generator_generate_invoice_reference_given_valid_parameters(String input, int expected){
        final var result = generator.generate(input);
        assertEquals(expected, result);
    }

    @ParameterizedTest
    @MethodSource("arguments_invalid")
    @DisplayName("LuhnGenerator::generate throws an IllegalArgumentException if input is not alphanumeric")
    public void luhn_generator_generate_throws_illegal_argument_exception_given_invalid_input(String input){
        assertThrows(IllegalArgumentException.class, () -> generator.generate(input));
    }

    private static Stream<Arguments> arguments() {
        return Stream.of(
                Arguments.of("MYCO0000000001", 6), //22341224 0000000001 6
                Arguments.of("MYCO1234567890", 1), //22341224 1234567890 1
                Arguments.of("NZCO0000000010", 2), //23341224 0000000010 2
                Arguments.of("AUCO0000000100", 9), //10301224 0000000100 9
                Arguments.of("SGABC0000001000", 7), //2816101112 0000001000 7
                Arguments.of("0000000001", 8) // 0000000001 8
        );
    }

    private static Stream<Arguments> arguments_invalid() {
        return Stream.of(
                Arguments.of("#MYCO0000000001"),
                Arguments.of("MYCO!!0000000001"),
                Arguments.of("MY&CO0000000001")
        );
    }
}
