package global.symbio.billing.core.util.checksum.luhn;

import com.google.common.base.Preconditions;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

@Singleton
public class LuhnGenerator {

    //added numbers in case prefix contains numbers too
    private static final String CODEPOINTS = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";

    public int generate(@Nonnull String input) {
        return generateCheckDigit(getCodePoint(input.toUpperCase()));
    }

    /**
     * Get an equivalent digit for each letter in the alphabet
     */
    @Nonnull
    private static String getCodePoint(@Nonnull String prefix) {
        final var result = new StringBuilder();
        for (var i = 0; i < prefix.length(); i++) {
            final var codePoint = codePointFromCharacter(prefix.charAt(i));
            result.append(codePoint);
        }
        return result.toString();
    }

    /**
     * Luhn_algorithm
     * 1. start from the rightmost digit. moving left, double the value of every second digit (including the rightmost digit).
     * 2. if the product of this doubling operation is greater than 9, then sum the digits of the products or alternatively subtract 9 from the product.
     * 3. sum the values of the resulting digits
     * 4. multiply by 9
     * 5. last digit is the check digit
     */
    private static int generateCheckDigit(@Nonnull String input) {
        int sum = 0;
        int step = 0;

        //step 1
        for (var i = input.length() - 1; i >= 0; i--) {
            var digit = Character.getNumericValue(input.charAt(i));

            if (step % 2 == 0 && digit != 0) {
                digit *= 2;

                //step 2
                if (digit >= 10) {
                    digit -= 9;
                }
            }

            // step 3
            sum += digit;
            step++;
        }

        //step 4
        sum *= 9;

        //step 5
        return sum % 10;
    }

    private static int codePointFromCharacter(char ch) {
        Preconditions.checkArgument(Character.isAlphabetic(ch) || Character.isDigit(ch));
        return CODEPOINTS.indexOf(ch);
    }
}
