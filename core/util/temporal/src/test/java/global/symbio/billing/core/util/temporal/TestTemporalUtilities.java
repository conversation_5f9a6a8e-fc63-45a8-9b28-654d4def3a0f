package global.symbio.billing.core.util.temporal;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.time.*;
import java.time.temporal.Temporal;
import java.time.temporal.TemporalUnit;
import java.util.List;
import java.util.stream.Stream;

import static java.time.temporal.ChronoUnit.*;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class TestTemporalUtilities {

    @ParameterizedTest(name = "{index} TemporalUtilities::isWithinThreshold {0} plus 1 {1} is within threshold")
    @MethodSource("permutations")
    @DisplayName("TemporalUtilities::isWithinThreshold time plus 1 unit is within threshold")
    public void temporal_utilities_origin_plus_1_unit_is_within_threshold(final Temporal origin, final TemporalUnit unit) {
        final var duration = Duration.of(1L, unit);
        final var end = origin.plus(duration);
        assertTrue(TemporalUtilities.isWithinThreshold(origin, end, duration));
    }

    @ParameterizedTest(name = "{index} TemporalUtilities::isWithinThreshold {0} plus 1 {1} and 1 nanosecond is not within threshold")
    @MethodSource("permutations")
    @DisplayName("TemporalUtilities::isWithinThreshold time plus 1 unit and 1 nanosecond is not within threshold")
    public void temporal_utilities_origin_plus_1_unit_and_plus_1_nano_is_not_within_threshold(final Temporal origin, final TemporalUnit unit) {
        final var duration = Duration.of(1L, unit);
        final var end = origin.plus(duration);
        assertFalse(TemporalUtilities.isWithinThreshold(origin, end.plus(1L, NANOS), duration));
    }

    @ParameterizedTest(name = "{index} TemporalUtilities::isWithinThreshold {0} plus 1 {1} and minus 1 nanosecond is within threshold")
    @MethodSource("permutations")
    @DisplayName("TemporalUtilities::isWithinThreshold time plus 1 unit and minus 1 nanosecond is within threshold")
    public void temporal_utilities_origin_plus_1_unit_and_minus_1_nano_is_within_threshold(final Temporal origin, final TemporalUnit unit) {
        final var duration = Duration.of(1L, unit);
        final var end = origin.plus(duration);
        assertTrue(TemporalUtilities.isWithinThreshold(origin, end.minus(1L, NANOS), duration));
    }

    private static Stream<Arguments> permutations() {
        final var times = List.of(Instant.now(), LocalDateTime.now(), ZonedDateTime.now(), OffsetDateTime.now());
        final var units = List.of(DAYS, HOURS, MINUTES, SECONDS, MILLIS, MICROS, NANOS);
        return times.stream().flatMap(t -> units.stream().map(u -> Arguments.of(t, u)));
    }
}