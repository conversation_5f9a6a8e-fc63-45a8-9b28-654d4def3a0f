package global.symbio.billing.core.util.temporal;

import jakarta.annotation.Nonnull;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.time.Duration;
import java.time.temporal.Temporal;

import static java.time.temporal.ChronoUnit.*;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class TemporalUtilities {

    public static final Duration ONE_DAY = Duration.of(1L, DAYS);
    public static final Duration ONE_HOUR = Duration.of(1L, HOURS);
    public static final Duration ONE_MINUTE = Duration.of(1L, MINUTES);
    public static final Duration ONE_SECOND = Duration.of(1L, SECONDS);
    public static final Duration ONE_MILLISECOND = Duration.of(1L, MILLIS);
    public static final Duration ONE_MICROSECOND = Duration.of(1L, MICROS);
    public static final Duration ONE_NANOSECOND = Duration.of(1L, NANOS);

    public static <T extends Temporal> boolean isWithinThreshold(@Nonnull T first, @Nonnull T second, @Nonnull Duration threshold) {
        if (first.equals(second)) {
            return true;
        }
        final var distance = Duration.between(first, second);
        return distance.compareTo(threshold) <= 0;
    }
}