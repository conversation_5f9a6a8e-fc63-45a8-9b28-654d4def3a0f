package global.symbio.billing.core.util.sanitization;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

class TestFileNameUtil {

    @Test
    @DisplayName("FileNameUtil::sanitizeFileName handles null input")
    public void sanitize_file_name_handles_null_input() {
        assertThrows(NullPointerException.class, () -> FileNameUtil.sanitizeFileName(null));
    }

    @Test
    @DisplayName("FileNameUtil::sanitizeFileName removes invalid characters")
    public void sanitize_file_name_removes_invalid_characters() {
        String fileName = "inv:alid/na*me?.pdf";
        String sanitizedFileName = FileNameUtil.sanitizeFileName(fileName);
        assertEquals("invalidname.pdf", sanitizedFileName);
    }

    @Test
    @DisplayName("FileNameUtil::sanitizeFileName replaces spaces with underscores")
    public void sanitize_file_name_replaces_spaces_with_underscores() {
        String fileName = "file name with spaces.pdf";
        String sanitizedFileName = FileNameUtil.sanitizeFileName(fileName);
        assertEquals("file_name_with_spaces.pdf", sanitizedFileName);
    }

    @Test
    @DisplayName("FileNameUtil::sanitizeFileName enforces length limit")
    public void sanitize_file_name_enforces_length_limit() {
        String fileName = "a".repeat(300) + ".pdf";
        String sanitizedFileName = FileNameUtil.sanitizeFileName(fileName);
        assertEquals(255, sanitizedFileName.length());
    }

    @Test
    @DisplayName("FileNameUtil::sanitizeFileName handles no invalid characters")
    public void sanitize_file_name_handles_no_invalid_characters() {
        String fileName = "valid_file_name.pdf";
        String sanitizedFileName = FileNameUtil.sanitizeFileName(fileName);
        assertEquals("valid_file_name.pdf", sanitizedFileName);
    }

    @Test
    @DisplayName("FileNameUtil::sanitizeFileName handles all invalid characters")
    public void sanitize_file_name_handles_all_invalid_characters() {
        String fileName = "<>:\"/|?*";
        String sanitizedFileName = FileNameUtil.sanitizeFileName(fileName);
        assertEquals("", sanitizedFileName);
    }

    @Test
    @DisplayName("FileNameUtil::sanitizeFileName handles mixed invalid characters and spaces")
    public void sanitize_file_name_handles_mixed_invalid_characters_and_spaces() {
        String fileName = "inv:alid /na*me?.pdf";
        String sanitizedFileName = FileNameUtil.sanitizeFileName(fileName);
        assertEquals("invalid_name.pdf", sanitizedFileName);
    }

    @Test
    @DisplayName("FileNameUtil::sanitizeFileName handles empty string")
    public void sanitize_file_name_handles_empty_string() {
        String fileName = "";
        String sanitizedFileName = FileNameUtil.sanitizeFileName(fileName);
        assertEquals("", sanitizedFileName);
    }
}
