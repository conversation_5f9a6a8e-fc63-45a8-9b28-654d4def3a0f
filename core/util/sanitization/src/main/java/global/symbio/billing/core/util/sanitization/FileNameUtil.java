package global.symbio.billing.core.util.sanitization;

public class FileNameUtil {

    private static final String INVALID_CHARACTERS_REGEX = "[<>:\"/|?*]";
    private static final String WHITESPACE_REGEX = "\\s+";
    private static final int MAX_FILENAME_LENGTH = 255;

    public static String sanitizeFileName(String fileName) {

        final var sanitizedFileName = fileName.replaceAll(INVALID_CHARACTERS_REGEX, "")
            .trim()
            .replaceAll(WHITESPACE_REGEX, "_");

        return sanitizedFileName.substring(0, Math.min(sanitizedFileName.length(), MAX_FILENAME_LENGTH));
    }
}
