package global.symbio.billing.core.currency.persistence.api;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.persistence.api.DataAccessObject;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.util.Objects;

public abstract class CurrencyDataAccessObject implements DataAccessObject<Integer, CurrencyDataAccessObject> {

    @Nonnull
    public abstract String getCode();

    @Nonnull
    public abstract CurrencyDataAccessObject code(@Nonnull String code);

    @Nonnull
    public abstract String getDescription();

    @Nonnull
    public abstract CurrencyDataAccessObject description(@Nonnull String description);

    @Nullable
    public abstract String getSymbol();

    @Nonnull
    public abstract CurrencyDataAccessObject symbol(@Nullable String symbol);

    @Override @Nonnull
    public Currency entity() {
        return new Currency(this);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof CurrencyDataAccessObject that)) return false;
        return Objects.equals(getIdentifier(), that.getIdentifier()) && Objects.equals(getCode(), that.getCode()) && Objects.equals(getDescription(), that.getDescription()) && Objects.equals(getSymbol(), that.getSymbol());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getIdentifier(), getCode(), getDescription(), getSymbol());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this).add("identifier", getIdentifier()).add("code", getCode()).add("description", getDescription()).add("symbol", getSymbol()).toString();
    }
}
