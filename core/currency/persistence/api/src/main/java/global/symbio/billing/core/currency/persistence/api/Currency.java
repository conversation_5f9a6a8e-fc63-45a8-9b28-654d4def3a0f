package global.symbio.billing.core.currency.persistence.api;

import global.symbio.billing.core.persistence.api.Entity;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

public class Currency extends Entity<Integer, CurrencyDataAccessObject> {

    public Currency(@Nonnull CurrencyDataAccessObject data) {
        super(data);
    }

    @Nonnull
    public String getCode() {
        return data().getCode();
    }

    @Nonnull
    public String getDescription() {
        return data().getDescription();
    }

    @Nullable
    public String getSymbol() {
        return data().getSymbol();
    }
}
