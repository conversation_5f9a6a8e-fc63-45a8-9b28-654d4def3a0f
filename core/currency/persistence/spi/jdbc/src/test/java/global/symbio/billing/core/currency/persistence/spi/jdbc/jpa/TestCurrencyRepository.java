package global.symbio.billing.core.currency.persistence.spi.jdbc.jpa;

import global.symbio.billing.core.persistence.api.Repositories;
import global.symbio.billing.core.currency.persistence.api.repository.CurrencyRepository;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

public class TestCurrencyRepository {

    @Test
    @DisplayName("ReadOnlyCurrencyRepository is connected to read-only datasource")
    public void test_read_only_repository() {
        Assertions.assertTrue(CurrencyRepository.class.isAssignableFrom(ReadOnlyJPACurrencyRepository.class));
        Assertions.assertTrue(Repositories.isReadOnlyRepository(ReadOnlyJPACurrencyRepository.class));
    }

    @Test
    @DisplayName("ReadWriteCurrencyRepository is connected to read-write datasource")
    public void test_read_write_repository() {
        Assertions.assertTrue(CurrencyRepository.class.isAssignableFrom(ReadWriteJPACurrencyRepository.class));
        Assertions.assertTrue(Repositories.isReadWriteRepository(ReadWriteJPACurrencyRepository.class));
    }
}