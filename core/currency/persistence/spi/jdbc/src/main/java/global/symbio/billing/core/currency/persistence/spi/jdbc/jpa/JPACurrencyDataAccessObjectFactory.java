package global.symbio.billing.core.currency.persistence.spi.jdbc.jpa;

import global.symbio.billing.core.currency.persistence.api.CurrencyDataAccessObject;
import global.symbio.billing.core.currency.persistence.api.CurrencyDataAccessObjectFactory;
import io.micronaut.context.annotation.Primary;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

@Primary
@Singleton
public class JPACurrencyDataAccessObjectFactory implements CurrencyDataAccessObjectFactory {

    @Nonnull
    @Override
    public Class<? extends CurrencyDataAccessObject> type() {
        return JPACurrencyDataAccessObject.class;
    }

    @Nonnull
    @Override
    public CurrencyDataAccessObject create() {
        return new JPACurrencyDataAccessObject();
    }
}
