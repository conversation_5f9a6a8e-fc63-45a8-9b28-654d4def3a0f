package global.symbio.billing.core.currency.persistence.spi.jdbc.jpa;

import global.symbio.billing.core.currency.persistence.api.CurrencyDataAccessObject;
import io.micronaut.core.annotation.Introspected;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Immutable;

@Immutable @Entity
@Table(name = "currency")
@Getter @Setter
@Introspected(includedAnnotations = Entity.class)
public class JPACurrencyDataAccessObject extends CurrencyDataAccessObject {

    @Id @Nonnull
    @Column(name = "id", unique = true, nullable = false, updatable = false)
    private Integer identifier;

    @Nonnull
    @Column(name = "code", unique = true, nullable = false, length = 5)
    private String code;

    @Nonnull
    @Column(name = "description", nullable = false, length = 64)
    private String description;

    @Nullable
    @Column(name = "symbol", length = 5)
    private String symbol;

    @Nonnull
    @Override
    public CurrencyDataAccessObject identifier(@Nonnull Integer identifier) {
        setIdentifier(identifier);
        return this;
    }

    @Nonnull
    @Override
    public CurrencyDataAccessObject code(@Nonnull String code) {
        setCode(code);
        return this;
    }

    @Nonnull
    @Override
    public CurrencyDataAccessObject description(@Nonnull String description) {
        setDescription(description);
        return this;
    }

    @Nonnull
    @Override
    public CurrencyDataAccessObject symbol(@Nullable String symbol) {
        setSymbol(symbol);
        return this;
    }
}
