package global.symbio.billing.core.reference;

import global.symbio.billing.core.reference.persistence.api.reference.ReferenceType;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestDefaultReferenceEncoderRegistry {

    @Mock
    private ReferenceEncoder handler;

    private static ReferenceType UNSUPPORTED;

    private DefaultReferenceEncoderRegistry registry;

    @BeforeAll
    public static void setUpBeforeClass() {
        UNSUPPORTED = mock(ReferenceType.class);
        lenient().when(UNSUPPORTED.ordinal()).thenReturn(3);
        mockStatic(ReferenceType.class);
        lenient().when(ReferenceType.values()).thenReturn(
            new ReferenceType[] {
                ReferenceType.INVOICE, ReferenceType.PAYMENT, ReferenceType.PAYMENT_CANCELLATION, UNSUPPORTED
            });
    }

    @BeforeEach
    public void setup() {
        when(handler.type()).thenReturn(ReferenceType.PAYMENT);
        registry = new DefaultReferenceEncoderRegistry(Set.of(handler));
    }

    @Test
    @DisplayName("DefaultReferenceEncoderRegistry::new rejects null constructor arguments")
    public void default_reference_encoder_registry_registry_null_constructor() {
        assertThrows(NullPointerException.class, () -> new DefaultReferenceEncoderRegistry(null));
    }

    @Test
    @DisplayName("DefaultReferenceEncoderRegistry::lookup retrieves correct handler given mapped category")
    public void default_reference_encoder_registry_lookup_retrieves_correct_handler_given_mapped_category() {
        final var type = ReferenceType.PAYMENT;
        final var result = registry.lookup(type);
        assertNotNull(result);
        assertSame(handler, result);
    }

    @Test
    @DisplayName("DefaultReferenceEncoderRegistry::lookup returns null when no handler is found for given category")
    public void default_reference_encoder_registry_lookup_retrieves_fallback_handler_given_no_handler_found() {
        final var result = registry.lookup(UNSUPPORTED);
        assertNull(result);
    }
}
