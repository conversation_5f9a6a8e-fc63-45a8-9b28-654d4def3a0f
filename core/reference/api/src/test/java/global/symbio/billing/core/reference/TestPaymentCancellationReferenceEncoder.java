package global.symbio.billing.core.reference;

import global.symbio.billing.core.reference.persistence.api.reference.ReferenceType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
public class TestPaymentCancellationReferenceEncoder {

    PaymentCancellationReferenceEncoder encoder;

    @BeforeEach
    public void setup() {
        encoder = new PaymentCancellationReferenceEncoder();
    }


    @Test
    @DisplayName("PaymentCancellationReferenceEncoder::type returns ReferenceType.PAYMENT_CANCELLATION")
    public void payment_cancellation_reference_encoder_type_returns_correct_type() {
        assertEquals(ReferenceType.PAYMENT_CANCELLATION, encoder.type());
    }

    @Test
    @DisplayName("PaymentCancellationReferenceEncoder::generates generates a reference")
    public void payment_cancellation_reference_encoder_generate_reference() {
        final var countryCode = "MY";
        final var businessUnit = "CO";
        final var sequence = 1L;
        final var checkDigit = 6;

       final var result = assertDoesNotThrow(() -> encoder.generate(countryCode, businessUnit, sequence, checkDigit));
        assertEquals("MYCOPYMX00000000016", result);
    }

    @Test
    @DisplayName("PaymentCancellationReferenceEncoder::generates throws an exception if checkDigit is null")
    public void payment_cancellation_reference_encoder_throws_exception_check_digit_null() {
        final var countryCode = "MY";
        final var businessUnit = "CO";
        final var sequence = 1L;

        final var result = assertThrows(IllegalArgumentException.class, () -> encoder.generate(countryCode, businessUnit, sequence, null));
        assertEquals("Check digit is required for type PAYMENT_CANCELLATION", result.getMessage());
    }

    @ParameterizedTest
    @MethodSource("arguments_invalid")
    @DisplayName("PaymentCancellationReferenceEncoder::generate throws an exception if countryCode or businessUnit is empty")
    public void payment_cancellation_reference_encoder_throws_exception(final String countryCode, final String businessUnit) {
        final var sequence = 1L;
        final var result = assertThrows(IllegalArgumentException.class, () -> encoder.generate(countryCode, businessUnit, sequence, null));
        assertEquals("countryCode and businessUnit are required", result.getMessage());
    }

    private static Stream<Arguments> arguments_invalid() {
        return Stream.of(
            Arguments.of("", "CO"),
            Arguments.of("MY", ""),
            Arguments.of("", "")
        );
    }

}

