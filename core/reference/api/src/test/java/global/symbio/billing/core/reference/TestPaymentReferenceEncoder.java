package global.symbio.billing.core.reference;

import global.symbio.billing.core.reference.persistence.api.reference.ReferenceType;
import global.symbio.billing.core.util.checksum.luhn.LuhnGenerator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class TestPaymentReferenceEncoder {

    @Mock
    private LuhnGenerator luhn;

    PaymentReferenceEncoder encoder;

    @BeforeEach
    public void setup() {
        encoder = new PaymentReferenceEncoder(luhn);
    }

    @Test
    @DisplayName("PaymentReferenceEncoder::new rejects null arguments")
    public void payment_reference_encoder_new_rejects_null_arguments() {
        assertThrows(NullPointerException.class, () -> new PaymentReferenceEncoder(null));
    }

    @Test
    @DisplayName("PaymentReferenceEncoder::type returns ReferenceType.PAYMENT")
    public void payment_reference_encoder_type_returns_correct_type() {
        assertEquals(ReferenceType.PAYMENT, encoder.type());
    }

    @Test
    @DisplayName("PaymentReferenceEncoder::generates generates a reference")
    public void payment_reference_encoder_generate_reference() {
        final var countryCode = "MY";
        final var businessUnit = "CO";
        final var sequence = 1L;
        final var checkDigit = 6;

        when(luhn.generate(anyString())).thenReturn(checkDigit);

        final var result = assertDoesNotThrow(() -> encoder.generate(countryCode, businessUnit, sequence, null));
        assertEquals("MYCOPYMT00000000016", result);
    }

    @ParameterizedTest
    @MethodSource("arguments_invalid")
    @DisplayName("PaymentReferenceEncoder::generate throws an exception if countryCode or businessUnit is empty")
    public void payment_reference_encoder_throws_exception(final String countryCode, final String businessUnit) {
        final var sequence = 1L;
        final var result = assertThrows(IllegalArgumentException.class, () -> encoder.generate(countryCode, businessUnit, sequence, null));
        assertEquals("countryCode and businessUnit are required", result.getMessage());
    }

    private static Stream<Arguments> arguments_invalid() {
        return Stream.of(
            Arguments.of("", "CO"),
            Arguments.of("MY", ""),
            Arguments.of("", "")
        );
    }

}

