package global.symbio.billing.core.reference;

import global.symbio.billing.core.reference.persistence.api.reference.ReferenceType;
import global.symbio.billing.core.util.checksum.luhn.LuhnGenerator;
import io.micrometer.common.util.StringUtils;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.util.Objects;

@Singleton
public class PaymentReferenceEncoder implements ReferenceEncoder {

    private static final String PAYMENT_TYPE_CODE = "PYMT";

    @Nonnull
    private final LuhnGenerator luhn;

    @Inject
    public PaymentReferenceEncoder(@Nonnull LuhnGenerator luhn) {
        this.luhn = Objects.requireNonNull(luhn, "luhn");
    }

    @Nonnull
    @Override
    public ReferenceType type() {
        return ReferenceType.PAYMENT;
    }

    @Nonnull
    @Override
    public String generate(@Nonnull String countryCode, @Nonnull String businessUnit, @Nonnull Long sequence, @Nullable Integer digit) {
        Objects.requireNonNull(countryCode, "countryCode");
        Objects.requireNonNull(businessUnit, "businessUnit");
        Objects.requireNonNull(sequence, "sequence");

        if (StringUtils.isEmpty(countryCode) || StringUtils.isEmpty(businessUnit)) {
            throw new IllegalArgumentException("countryCode and businessUnit are required");
        }

        final var prefix = getPrefix(countryCode, businessUnit, PAYMENT_TYPE_CODE, sequence);
        final var checkDigit = luhn.generate(prefix);
        return prefix + checkDigit;
    }
}
