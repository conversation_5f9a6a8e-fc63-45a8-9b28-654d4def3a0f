package global.symbio.billing.core.reference;

import global.symbio.billing.core.reference.persistence.api.reference.ReferenceType;
import io.micrometer.common.util.StringUtils;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import jakarta.inject.Singleton;

import java.util.Objects;

@Singleton
public class PaymentCancellationReferenceEncoder implements ReferenceEncoder {

    private static final String PAYMENT_CANCELLATION_TYPE_CODE = "PYMX";

    @Nonnull
    @Override
    public ReferenceType type() {
        return ReferenceType.PAYMENT_CANCELLATION;
    }

    @Nonnull
    @Override
    public String generate(@Nonnull String countryCode, @Nonnull String businessUnit, @Nonnull Long sequence, @Nullable Integer digit) {
        Objects.requireNonNull(countryCode, "countryCode");
        Objects.requireNonNull(businessUnit, "businessUnit");
        Objects.requireNonNull(sequence, "sequence");

        if (StringUtils.isEmpty(countryCode) || StringUtils.isEmpty(businessUnit)) {
            throw new IllegalArgumentException("countryCode and businessUnit are required");
        }

        if (digit == null) {
            throw new IllegalArgumentException("Check digit is required for type " + this.type());
        }

        final var prefix = getPrefix(countryCode, businessUnit, PAYMENT_CANCELLATION_TYPE_CODE, sequence);
        return prefix + digit;
    }
}
