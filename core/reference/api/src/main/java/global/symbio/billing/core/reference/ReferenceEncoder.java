package global.symbio.billing.core.reference;

import com.google.common.base.Strings;
import global.symbio.billing.core.reference.persistence.api.reference.ReferenceType;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

public interface ReferenceEncoder {

    char PADDING_CHAR = '0';
    int PADDING_LENGTH = 10;

    @Nonnull
    ReferenceType type();

    @Nonnull
    String generate(
        @Nonnull String countryCode,
        @Nonnull String businessUnit,
        @Nonnull Long sequence,
        @Nullable Integer digit
    );

    default String getPrefix(
        @Nonnull String countryCode,
        @Nonnull String businessUnit,
        @Nonnull String referenceCode,
        @Nonnull Long sequence
    ) {
        final var number = generateNumber(sequence);
        return countryCode + businessUnit + referenceCode + number;
    }

    default String generateNumber(@Nonnull Long sequence) {
        return Strings.padStart(String.valueOf(sequence), PADDING_LENGTH, PADDING_CHAR);
    }

}
