package global.symbio.billing.core.reference;

import global.symbio.billing.core.reference.persistence.api.reference.ReferenceType;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Singleton
public class DefaultReferenceEncoderRegistry implements ReferenceEncoderRegistry {

    @Nonnull
    private final Map<ReferenceType, ReferenceEncoder> handlers;

    @Inject
    public DefaultReferenceEncoderRegistry(
        @Nonnull Set<ReferenceEncoder> handlers
    ) {
        this.handlers = Objects.requireNonNull(handlers, "handlers").stream().collect(Collectors.toUnmodifiableMap(ReferenceEncoder::type, Function.identity()));
    }

    @Nullable
    @Override
    public ReferenceEncoder lookup(@Nonnull ReferenceType type) {
        return handlers.get(type);
    }
}
