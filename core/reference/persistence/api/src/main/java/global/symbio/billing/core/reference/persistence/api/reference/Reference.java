package global.symbio.billing.core.reference.persistence.api.reference;

import global.symbio.billing.core.persistence.api.Entity;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.util.UUID;

public class Reference extends Entity<UUID, ReferenceDataAccessObject> {

    public Reference(@Nonnull ReferenceDataAccessObject data) {
        super(data);
    }

    @Nonnull
    @Override
    public UUID getIdentifier() {
        return data().getIdentifier();
    }

    @Nonnull
    public Long getSequence() {
        return data().getSequence();
    }

    @Nonnull
    public String getCountryCode() {
        return data().getCountryCode();
    }

    @Nonnull
    public String getBusinessUnit() {
        return data().getBusinessUnit();
    }

    @Nullable
    public Integer getCheckDigit() {
        return data().getCheckDigit();
    }

    @Nonnull
    public ReferenceType getType() {
        return data().getType();
    }

    @Nullable
    public String getReference() {
        return data().getReference();
    }
}