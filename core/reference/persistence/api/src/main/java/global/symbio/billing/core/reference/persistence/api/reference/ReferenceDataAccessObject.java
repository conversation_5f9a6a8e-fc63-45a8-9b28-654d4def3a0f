package global.symbio.billing.core.reference.persistence.api.reference;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.country.persistence.api.Country;
import global.symbio.billing.core.country.persistence.api.CountryDataAccessObject;
import global.symbio.billing.core.persistence.api.DataAccessObject;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.util.Objects;
import java.util.UUID;

public abstract class ReferenceDataAccessObject implements DataAccessObject<UUID, ReferenceDataAccessObject> {

    @Nonnull
    public abstract Long getSequence();

    @Nonnull
    public abstract ReferenceDataAccessObject sequence(@Nonnull Long sequence);

    @Nonnull
    public abstract String getCountryCode();

    @Nonnull
    public abstract ReferenceDataAccessObject countryCode(@Nonnull String countryCode);

    @Nonnull
    public ReferenceDataAccessObject countryCode(@Nonnull CountryDataAccessObject country) {
        return countryCode(country.getCode());
    }

    @Nonnull
    public ReferenceDataAccessObject countryCode(@Nonnull Country country) {
        return countryCode(country.data());
    }

    @Nonnull
    public abstract String getBusinessUnit();

    @Nonnull
    public abstract ReferenceDataAccessObject businessUnit(@Nonnull String businessUnit);

    @Nullable
    public abstract Integer getCheckDigit();

    @Nonnull
    public abstract ReferenceDataAccessObject checkDigit(@Nullable Integer checkDigit);

    @Nonnull
    public abstract ReferenceType getType();

    @Nonnull
    public abstract ReferenceDataAccessObject type(@Nonnull ReferenceType type);

    @Nullable
    public abstract String getReference();

    @Nonnull
    public abstract ReferenceDataAccessObject reference(@Nullable String reference);

    @Override @Nonnull
    public Reference entity() {
        return new Reference(this);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof ReferenceDataAccessObject reference)) return false;
        return Objects.equals(getIdentifier(), reference.getIdentifier())
            && Objects.equals(getSequence(), reference.getSequence())
            && Objects.equals(getCountryCode(), reference.getCountryCode())
            && Objects.equals(getBusinessUnit(), reference.getBusinessUnit())
            && Objects.equals(getCheckDigit(), reference.getCheckDigit())
            && Objects.equals(getType(), reference.getType())
            && Objects.equals(getReference(), reference.getReference());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getIdentifier(), getSequence(), getCountryCode(), getBusinessUnit(), getCheckDigit(), getType(), getReference());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("identifier", getIdentifier())
            .add("sequence", getSequence())
            .add("countryCode", getCountryCode())
            .add("businessUnit", getBusinessUnit())
            .add("checkDigit", getCheckDigit())
            .add("type", getType())
            .add("reference", getReference()).toString();
    }
}
