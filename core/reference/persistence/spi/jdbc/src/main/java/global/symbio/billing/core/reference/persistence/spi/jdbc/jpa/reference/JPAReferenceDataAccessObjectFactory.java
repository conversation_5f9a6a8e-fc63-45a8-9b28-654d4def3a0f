package global.symbio.billing.core.reference.persistence.spi.jdbc.jpa.reference;

import global.symbio.billing.core.reference.persistence.api.reference.ReferenceDataAccessObject;
import global.symbio.billing.core.reference.persistence.api.reference.ReferenceDataAccessObjectFactory;
import io.micronaut.context.annotation.Primary;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

@Primary
@Singleton
public class JPAReferenceDataAccessObjectFactory implements ReferenceDataAccessObjectFactory {

    @Nonnull
    @Override
    public Class<? extends ReferenceDataAccessObject> type() {
        return JPAReferenceDataAccessObject.class;
    }

    @Nonnull
    @Override
    public ReferenceDataAccessObject create() {
        return new JPAReferenceDataAccessObject();
    }
}