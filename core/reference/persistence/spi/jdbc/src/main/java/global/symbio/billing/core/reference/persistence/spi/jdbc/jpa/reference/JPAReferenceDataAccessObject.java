package global.symbio.billing.core.reference.persistence.spi.jdbc.jpa.reference;

import global.symbio.billing.core.reference.persistence.api.reference.ReferenceDataAccessObject;
import global.symbio.billing.core.reference.persistence.api.reference.ReferenceType;
import io.micronaut.core.annotation.Introspected;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Generated;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.util.UUID;

@Entity
@Table(name = "reference")
@Getter
@Setter
@Introspected(includedAnnotations = Entity.class)
public class JPAReferenceDataAccessObject extends ReferenceDataAccessObject {

    @Id
    @Nonnull
    @Column(name = "id", unique = true, nullable = false, updatable = false)
    private UUID identifier;

    @Nonnull
    @Generated
    @Column(name = "sequence", unique = true, nullable = false, updatable = false, insertable = false)
    private Long sequence;

    @Nonnull
    @Column(name = "country_code", nullable = false, updatable = false, length = 2)
    private String countryCode;

    @Nonnull
    @Column(name = "business_unit", nullable = false, updatable = false, length = 10)
    private String businessUnit;

    @Nullable
    @Column(name = "check_digit")
    private Integer checkDigit;

    @Nonnull
    @Column(name = "type", nullable = false)
    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    private ReferenceType type;

    @Nullable
    @Column(name = "reference")
    private String reference;

    @Nonnull
    @Override
    public ReferenceDataAccessObject identifier(@Nonnull UUID identifier) {
        setIdentifier(identifier);
        return this;
    }

    @Nonnull
    @Override
    public ReferenceDataAccessObject sequence(@Nonnull Long sequence) {
        setSequence(sequence);
        return this;
    }

    @Nonnull
    @Override
    public ReferenceDataAccessObject countryCode(@Nonnull String countryCode) {
        setCountryCode(countryCode);
        return this;
    }

    @Nonnull
    @Override
    public ReferenceDataAccessObject businessUnit(@Nonnull String businessUnit) {
        setBusinessUnit(businessUnit);
        return this;
    }

    @Nonnull
    @Override
    public ReferenceDataAccessObject checkDigit(@Nullable Integer checkDigit) {
        throw new IllegalStateException("Unable to explicitly set `checkDigit` in JDBC SPI layer.");
    }

    @Nonnull
    @Override
    public ReferenceDataAccessObject type(@Nonnull ReferenceType type) {
        setType(type);
        return this;
    }

    @Nonnull
    @Override
    public ReferenceDataAccessObject reference(@Nullable String reference) {
        setReference(reference);
        final var digit = reference != null ? Character.getNumericValue(reference.charAt(reference.length() - 1)) : null;
        setCheckDigit(digit);
        return this;
    }
}
