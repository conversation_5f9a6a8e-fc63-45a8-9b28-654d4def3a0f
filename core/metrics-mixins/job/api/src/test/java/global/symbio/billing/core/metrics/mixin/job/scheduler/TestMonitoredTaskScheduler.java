package global.symbio.billing.core.metrics.mixin.job.scheduler;

import global.symbio.billing.core.country.persistence.api.Country;
import global.symbio.billing.core.job.persistence.api.job.Job;
import global.symbio.billing.core.job.persistence.api.job.state.Failure;
import global.symbio.billing.core.job.scheduler.TaskScheduler;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.LongTaskTimer;
import io.micrometer.core.instrument.MeterRegistry;
import jakarta.annotation.Nonnull;
import org.apache.logging.log4j.ThreadContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Answers;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Map;
import java.util.UUID;
import java.util.function.Supplier;
import java.util.stream.Stream;

import static global.symbio.billing.core.util.constants.MetricsConstants.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestMonitoredTaskScheduler {

    @Mock(answer = Answers.RETURNS_DEEP_STUBS)
    private MeterRegistry meterRegistry;

    @Mock
    private TaskScheduler taskScheduler;

    private MonitoredTaskScheduler scheduler;

    @BeforeEach
    public void setup() {
        scheduler = new MonitoredTaskScheduler(meterRegistry, taskScheduler);
    }

    @Test
    @DisplayName("MonitoredTaskScheduler::new rejects null constructor arguments")
    public void monitored_task_scheduler_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new MonitoredTaskScheduler(null, taskScheduler));
        assertThrows(NullPointerException.class, () -> new MonitoredTaskScheduler(meterRegistry, null));
    }

    @Test
    @DisplayName("MonitoredTaskScheduler::parameters needed for context are correct")
    public void monitored_task_scheduler_parameters_are_correct() {
        final var job = mock(Job.class);
        final var id = UUID.randomUUID();
        final var country = mock(Country.class);
        final var correlation = UUID.randomUUID();
        final var tags = mock(Map.class);
        when(tags.get(anyString())).thenReturn("invoicing");
        when(country.getCode()).thenReturn("SG");
        when(job.getCountry()).thenReturn(country);
        when(job.getIdentifier()).thenReturn(id);
        when(job.getType()).thenReturn("job.type.FetchBillableAccountsTask");
        when(job.getCorrelation()).thenReturn(correlation);
        when(job.getJob()).thenReturn("FetchBillableAccountsTask");
        when(job.getTags()).thenReturn(tags);

        final var result = MonitoredTaskScheduler.parameters(job);
        assertEquals(id.toString(), result.get(CONTEXT_TAG_JOB_ID));
        assertEquals("FetchBillableAccountsTask", result.get(CONTEXT_TAG_JOB_NAME));
        assertEquals(correlation.toString(), result.get(CONTEXT_TAG_JOB_CORRELATION));
        assertEquals("FetchBillableAccountsTask", result.get(CONTEXT_TAG_JOB_TYPE));
        assertEquals("1", result.get(CONTEXT_TAG_JOB_ORDER));
        assertEquals("Fetch Billable Accounts", result.get(CONTEXT_TAG_JOB_DESCRIPTION));
        assertEquals("invoicing", result.get(CONTEXT_TAG_JOB_SCOPE));
        assertEquals("SG", result.get(CONTEXT_TAG_JOB_COUNTRY));
        assertEquals(8, result.entrySet().size());
    }

    @Test
    @DisplayName("MonitoredTaskScheduler::execute")
    public void monitored_task_scheduler_execute() {
        final var job = mock(Job.class);
        final var id = UUID.randomUUID();
        final var country = mock(Country.class);
        final var correlation = UUID.randomUUID();
        final var tags = mock(Map.class);
        final var count = mock(Counter.class, RETURNS_DEEP_STUBS);

        final var jobState = mock(Failure.class);
        when(jobState.getRetry()).thenReturn(null);
        when(jobState.getType()).thenReturn(Failure.TYPE);

        final var more = mock(MeterRegistry.More.class);
        final var timer = mock(LongTaskTimer.class);
        when(more.longTaskTimer(anyString(), anyString(), anyString())).thenReturn(timer);
        when(timer.record(any(Supplier.class))).thenReturn(jobState);
        when(meterRegistry.more()).thenReturn(more);
        when(job.getState()).thenReturn(jobState);

        when(tags.get(anyString())).thenReturn("invoicing");
        when(country.getCode()).thenReturn("SG");
        when(job.getCountry()).thenReturn(country);
        when(job.getIdentifier()).thenReturn(id);
        when(job.getType()).thenReturn("job.type.FetchBillableAccountsTask");
        when(job.getCorrelation()).thenReturn(correlation);
        when(job.getJob()).thenReturn("FetchBillableAccountsTask");
        when(job.getTags()).thenReturn(tags);

        when(meterRegistry.counter(METRIC_COUNT, TAG_JOB_TYPE, job.getType(), TAG_JOB_STATE, jobState.getType())).thenReturn(count);

        final var result = assertDoesNotThrow(() -> scheduler.execute(job));
        assertEquals(Failure.TYPE, result.getType());
        assertNotNull(ThreadContext.get(CONTEXT_TAG_JOB_DURATION));
        assertEquals(FAILURE_TYPE_TERMINAL, ThreadContext.get(CONTEXT_TAG_JOB_STATE));
    }

    @ParameterizedTest
    @MethodSource("order")
    @DisplayName("MonitoredTaskScheduler::getJobOrder returns the correct order")
    public void monitored_task_scheduler_get_job_order_returns_correct_order(final String order, final String job) {
        final var result = MonitoredTaskScheduler.getJobOrder(job);
        assertEquals(order, result);
    }

    @ParameterizedTest
    @MethodSource("description")
    @DisplayName("MonitoredTaskScheduler::getJobDescription returns the correct description")
    public void monitored_task_scheduler_get_job_description_returns_correct_description(final String description, final String job) {
        final var result = MonitoredTaskScheduler.getJobDescription(job);
        assertEquals(description, result);
    }

    @Nonnull
    private static Stream<Arguments> order() {
        return Stream.of(
            Arguments.of("1", JOB_FETCH_BILLABLE_ACCOUNTS_TASK),
            Arguments.of("2", JOB_GENERATE_ACCOUNT_INVOICE_STATE_TASK),
            Arguments.of("3", JOB_GENERATE_INVOICE_SUMMARY_PARAMETERS_TASK),
            Arguments.of("4", JOB_SUMMARISE_ACCOUNT_INVOICE_TASK),
            Arguments.of("5", JOB_SUMMARISE_TASK),
            Arguments.of("6", JOB_GENERATE_CDR_TASK),
            Arguments.of("7", JOB_SEND_CDR_TASK),
            Arguments.of("8", JOB_UPDATE_BILLING_DATE_TASK),
            Arguments.of("9", JOB_SAVE_DYNAMO_SUMMARY_TASK),
            Arguments.of("10", JOB_POLL_STEP_FUNCTION_TASK),
            Arguments.of("11", JOB_UPLOAD_ATTACHMENT_TASK),
            Arguments.of("12", JOB_SEND_INVOICE_NOTIFICATION_TASK),
            Arguments.of("13", JOB_NOTIFICATION_STATE_UPDATER_TASK),
            Arguments.of("999", "Unknown")
        );
    }

    @Nonnull
    private static Stream<Arguments> description() {
        return Stream.of(
            Arguments.of("Fetch Billable Accounts", JOB_FETCH_BILLABLE_ACCOUNTS_TASK),
            Arguments.of("Create Invoice Records", JOB_GENERATE_ACCOUNT_INVOICE_STATE_TASK),
            Arguments.of("Generate Invoice Summary Parameters", JOB_GENERATE_INVOICE_SUMMARY_PARAMETERS_TASK),
            Arguments.of("Summarise Account Invoice", JOB_SUMMARISE_ACCOUNT_INVOICE_TASK),
            Arguments.of("Summarise Invoice", JOB_SUMMARISE_TASK),
            Arguments.of("Generate Invoice Report", JOB_GENERATE_CDR_TASK),
            Arguments.of("Send Invoice Report", JOB_SEND_CDR_TASK),
            Arguments.of("Update Billing Date", JOB_UPDATE_BILLING_DATE_TASK),
            Arguments.of("Trigger Invoice Templating", JOB_SAVE_DYNAMO_SUMMARY_TASK),
            Arguments.of("Invoice Generation", JOB_POLL_STEP_FUNCTION_TASK),
            Arguments.of("Upload Invoice Attachment", JOB_UPLOAD_ATTACHMENT_TASK),
            Arguments.of("Send Email Notification", JOB_SEND_INVOICE_NOTIFICATION_TASK),
            Arguments.of("Check Notification Status", JOB_NOTIFICATION_STATE_UPDATER_TASK),
            Arguments.of("Unknown", "Unknown")
        );
    }

}
