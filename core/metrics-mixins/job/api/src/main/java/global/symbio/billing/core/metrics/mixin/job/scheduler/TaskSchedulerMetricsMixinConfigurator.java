package global.symbio.billing.core.metrics.mixin.job.scheduler;

import global.symbio.billing.core.job.scheduler.TaskScheduler;
import global.symbio.billing.core.metrics.MetricsMixinConfigurator;
import io.micrometer.core.instrument.MeterRegistry;
import io.micronaut.configuration.metrics.annotation.RequiresMetrics;
import io.micronaut.context.annotation.Factory;
import io.micronaut.inject.BeanDefinition;
import io.micronaut.inject.BeanIdentifier;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;

@Factory
@RequiresMetrics
public class TaskSchedulerMetricsMixinConfigurator extends MetricsMixinConfigurator<TaskScheduler> {

    @Inject
    public TaskSchedulerMetricsMixinConfigurator(@Nonnull MeterRegistry registry) {
        super(registry);
    }

    @Nonnull
    @Override
    public TaskScheduler mixin(@Nonnull MeterRegistry registry, @Nonnull TaskScheduler bean, @Nonnull String name) {
        return new MonitoredTaskScheduler(registry, bean);
    }

    @Nonnull
    @Override
    protected String getName(@Nonnull TaskScheduler bean, @Nonnull BeanDefinition<TaskScheduler> definition, @Nonnull BeanIdentifier identifier) {
        return identifier.getName();
    }
}

