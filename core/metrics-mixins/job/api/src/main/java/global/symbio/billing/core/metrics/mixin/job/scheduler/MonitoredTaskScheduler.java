package global.symbio.billing.core.metrics.mixin.job.scheduler;

import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.job.persistence.api.job.Job;
import global.symbio.billing.core.job.persistence.api.job.state.Failure;
import global.symbio.billing.core.job.persistence.api.job.state.JobState;
import global.symbio.billing.core.job.scheduler.TaskScheduler;
import global.symbio.billing.core.util.metrics.MetricsUtil;
import io.micrometer.core.instrument.MeterRegistry;
import io.micronaut.core.util.StringUtils;
import jakarta.annotation.Nonnull;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.CloseableThreadContext;

import java.time.Duration;
import java.time.Instant;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static global.symbio.billing.core.util.constants.MetricsConstants.*;

@Slf4j
public record MonitoredTaskScheduler(
    @Nonnull MeterRegistry registry,
    @Nonnull TaskScheduler scheduler
) implements TaskScheduler {

    private static final String COUNTRY_GLOBAL = "global";

    // just add new jobs here in the correct order
    private static final List<String> JOB_ORDER = List.of(
        JOB_FETCH_BILLABLE_ACCOUNTS_TASK,
        JOB_GENERATE_ACCOUNT_INVOICE_STATE_TASK,
        JOB_GENERATE_INVOICE_SUMMARY_PARAMETERS_TASK,
        JOB_SUMMARISE_ACCOUNT_INVOICE_TASK,
        JOB_SUMMARISE_TASK,
        JOB_GENERATE_CDR_TASK,
        JOB_SEND_CDR_TASK,
        JOB_UPDATE_BILLING_DATE_TASK,
        JOB_SAVE_DYNAMO_SUMMARY_TASK,
        JOB_POLL_STEP_FUNCTION_TASK,
        JOB_UPLOAD_ATTACHMENT_TASK,
        JOB_SEND_INVOICE_NOTIFICATION_TASK,
        JOB_NOTIFICATION_STATE_UPDATER_TASK
    );

    public MonitoredTaskScheduler {
        Objects.requireNonNull(registry, "registry");
        Objects.requireNonNull(scheduler, "scheduler");
    }

    @Override
    public boolean schedule(@Nonnull Job job) {
        return scheduler.schedule(job);
    }

    @Override
    public boolean schedule(@Nonnull Collection<Job> jobs) {
        return scheduler.schedule(jobs);
    }

    @Nonnull
    @Override
    public JobState execute(@Nonnull Job job) {
        try (final var _ = MetricsUtil.create(parameters(job))) {
            final var start = Instant.now();
            final var state = registry.more().longTaskTimer(METRIC_LATENCY, TAG_JOB_TYPE, job.getType()).record(() -> scheduler.execute(job));
            registry.counter(METRIC_COUNT, TAG_JOB_TYPE, job.getType(), TAG_JOB_STATE, state.getType()).increment();
            
            String failureType = null;
            if (state instanceof Failure failure) {
                failure.onLimitExceeded(() -> registry.counter(METRIC_GROUP_RETRY_LIMIT_EXCEEDED, TAG_JOB_TYPE, job.getType(), TAG_JOB_STATE, state.getType()).increment());
                failureType = failure.getRetry() == null ? FAILURE_TYPE_TERMINAL : FAILURE_TYPE_TRANSIENT;
            }

            final var duration = Duration.between(start, Instant.now());
            final var finalState = failureType == null ? StringUtils.capitalize(state.getType().toLowerCase()) : failureType;
            final var additional = Map.of(
                CONTEXT_TAG_JOB_DURATION, String.valueOf(duration.toMillis()),
                CONTEXT_TAG_JOB_STATE, finalState
            );
            CloseableThreadContext.putAll(additional);
            log.info("Job: {}, Id: {}, Correlation: {}, Duration: {}, StateType: {}, FailureType: {}",
                job.getJob(), job.getIdentifier(), job.getCorrelation(), duration.toMillis(), job.getState().getType(), failureType);
            return state;
        }
    }

    @VisibleForTesting
    @Nonnull
    static Map<String, String> parameters(@Nonnull Job job) {
        final var scope = job.getTags() == null ? null : job.getTags().get(CONTEXT_TAG_SCOPE);
        final var country = job.getCountry() == null ? COUNTRY_GLOBAL : job.getCountry().getCode();
        final var type = job.getType().substring(job.getType().lastIndexOf('.') + 1);
        return Map.of(
            CONTEXT_TAG_JOB_ID, Objects.toString(job.getIdentifier()),
            CONTEXT_TAG_JOB_NAME, job.getJob(),
            CONTEXT_TAG_JOB_CORRELATION, Objects.toString(job.getCorrelation()),
            CONTEXT_TAG_JOB_TYPE, type,
            CONTEXT_TAG_JOB_ORDER, getJobOrder(type),
            CONTEXT_TAG_JOB_DESCRIPTION, getJobDescription(type),
            CONTEXT_TAG_JOB_SCOPE, Objects.toString(scope),
            CONTEXT_TAG_JOB_COUNTRY, country
        );
    }

    @Nonnull
    static String getJobOrder(@Nonnull String type) {
        final var index = JOB_ORDER.indexOf(type);
        return Objects.toString(index >= 0 ? index + 1 : 999);
    }

    @Nonnull
    static String getJobDescription(@Nonnull String type) {
        return switch (type) {
            case JOB_FETCH_BILLABLE_ACCOUNTS_TASK -> "Fetch Billable Accounts";
            case JOB_GENERATE_ACCOUNT_INVOICE_STATE_TASK -> "Create Invoice Records";
            case JOB_GENERATE_INVOICE_SUMMARY_PARAMETERS_TASK -> "Generate Invoice Summary Parameters";
            case JOB_SUMMARISE_ACCOUNT_INVOICE_TASK -> "Summarise Account Invoice";
            case JOB_SUMMARISE_TASK -> "Summarise Invoice";
            case JOB_GENERATE_CDR_TASK -> "Generate Invoice Report";
            case JOB_SEND_CDR_TASK -> "Send Invoice Report";
            case JOB_UPDATE_BILLING_DATE_TASK -> "Update Billing Date";
            case JOB_SAVE_DYNAMO_SUMMARY_TASK -> "Trigger Invoice Templating";
            case JOB_POLL_STEP_FUNCTION_TASK -> "Invoice Generation";
            case JOB_UPLOAD_ATTACHMENT_TASK -> "Upload Invoice Attachment";
            case JOB_SEND_INVOICE_NOTIFICATION_TASK -> "Send Email Notification";
            case JOB_NOTIFICATION_STATE_UPDATER_TASK -> "Check Notification Status";
            default -> "Unknown";
        };
    }
}
