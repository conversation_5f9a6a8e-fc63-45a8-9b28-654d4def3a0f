package global.symbio.billing.core.metrics.mixin.pipeline;

import global.symbio.billing.core.metrics.MetricsMixinConfigurator;
import global.symbio.billing.core.pipeline.api.Pipeline;
import io.micrometer.core.instrument.MeterRegistry;
import io.micronaut.configuration.metrics.annotation.RequiresMetrics;
import io.micronaut.context.annotation.Factory;
import io.micronaut.core.naming.Named;
import io.micronaut.inject.BeanDefinition;
import io.micronaut.inject.BeanIdentifier;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;

@Factory
@RequiresMetrics
public class PipelineMetricsMixinConfigurator extends MetricsMixinConfigurator<Pipeline<?, ?, ?>> {

    @Inject
    public PipelineMetricsMixinConfigurator(@Nonnull MeterRegistry registry) {
        super(registry);
    }

    @Nonnull
    @Override
    public Pipeline<?, ?, ?> mixin(@Nonnull MeterRegistry registry, @Nonnull Pipeline<?, ?, ?> bean, @Nonnull String name) {
        return Pipeline.monitor(name, registry, bean);
    }

    @Nonnull
    @Override
    protected String getName(@Nonnull Pipeline<?, ?, ?> bean, @Nonnull BeanDefinition<Pipeline<?, ?, ?>> definition, @Nonnull BeanIdentifier identifier) {
        if (bean instanceof Named named) {
            return named.getName();
        }
        return identifier.getName();
    }
}
