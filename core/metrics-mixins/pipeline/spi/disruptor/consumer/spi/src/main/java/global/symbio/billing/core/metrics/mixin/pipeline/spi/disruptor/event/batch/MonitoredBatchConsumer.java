package global.symbio.billing.core.metrics.mixin.pipeline.spi.disruptor.event.batch;

import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.pipeline.spi.disruptor.event.batch.BatchConsumer;
import io.micrometer.core.instrument.MeterRegistry;
import jakarta.annotation.Nonnull;

import java.util.Collection;
import java.util.Objects;

//TODO: implement on top of MetricsAwareBatchConsumer
@VisibleForTesting
record MonitoredBatchConsumer<T>(
    @Nonnull String name,
    @Nonnull MeterRegistry registry,
    @Nonnull BatchConsumer<T> consumer
) implements BatchConsumer<T> {

    private static final String METRIC_GROUP_PREFIX = "batch.consumer.";
    private static final String METRIC_LATENCY = METRIC_GROUP_PREFIX + "latency";
    private static final String METRIC_EVENTS = METRIC_GROUP_PREFIX + "events";
    private static final String METRIC_INVOCATIONS = METRIC_GROUP_PREFIX + "invocations";
    private static final String TAG_CONSUMER_NAME = METRIC_GROUP_PREFIX + "name";

    public MonitoredBatchConsumer {
        Objects.requireNonNull(name, "name");
        Objects.requireNonNull(registry, "registry");
        Objects.requireNonNull(consumer, "consumer");
    }

    @Override
    public void consume(@Nonnull final Collection<T> elements) throws Exception {
        registry.timer(METRIC_LATENCY, TAG_CONSUMER_NAME, name).recordCallable(() -> {
            consumer.consume(elements);
            return null;
        });
        registry.counter(METRIC_EVENTS, TAG_CONSUMER_NAME, name).increment(elements.size());
        registry.counter(METRIC_INVOCATIONS, TAG_CONSUMER_NAME, name).increment();
    }

    @Override
    public boolean isConsumable(@Nonnull Collection<T> elements) {
        return consumer.isConsumable(elements);
    }

    @Override
    public boolean isRetryable(@Nonnull Throwable cause) {
        return consumer.isRetryable(cause);
    }

    @Override
    public boolean isInterested(@Nonnull T element) {
        return consumer.isInterested(element);
    }
}
