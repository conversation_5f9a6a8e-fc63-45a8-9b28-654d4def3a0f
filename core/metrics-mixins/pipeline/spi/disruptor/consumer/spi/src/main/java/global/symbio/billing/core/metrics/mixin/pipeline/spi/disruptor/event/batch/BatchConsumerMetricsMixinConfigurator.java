package global.symbio.billing.core.metrics.mixin.pipeline.spi.disruptor.event.batch;

import global.symbio.billing.core.metrics.MetricsMixinConfigurator;
import global.symbio.billing.core.pipeline.spi.disruptor.event.batch.BatchConsumer;
import io.micrometer.core.instrument.MeterRegistry;
import io.micronaut.configuration.metrics.annotation.RequiresMetrics;
import io.micronaut.context.annotation.Factory;
import io.micronaut.inject.BeanDefinition;
import io.micronaut.inject.BeanIdentifier;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;

@Factory
@RequiresMetrics
public class BatchConsumerMetricsMixinConfigurator extends MetricsMixinConfigurator<BatchConsumer<?>> {

    @Inject
    public BatchConsumerMetricsMixinConfigurator(@Nonnull MeterRegistry registry) {
        super(registry);
    }

    @Nonnull
    @Override
    public BatchConsumer<?> mixin(@Nonnull MeterRegistry registry, @Nonnull BatchConsumer<?> bean, @Nonnull String name) {
        return new MonitoredBatchConsumer<>(name, registry, bean);
    }

    @Nonnull
    @Override
    protected String getName(@Nonnull BatchConsumer<?> bean, @Nonnull BeanDefinition<BatchConsumer<?>> definition, @Nonnull BeanIdentifier identifier) {
        return identifier.getName();
    }
}
