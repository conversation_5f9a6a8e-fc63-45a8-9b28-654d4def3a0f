package global.symbio.billing.core.metrics.mixin.cache.caffeine;

import com.github.benmanes.caffeine.cache.Cache;
import global.symbio.billing.core.metrics.MetricsMixinConfigurator;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.binder.cache.CaffeineCacheMetrics;
import io.micronaut.configuration.metrics.annotation.RequiresMetrics;
import io.micronaut.context.annotation.Factory;
import io.micronaut.inject.BeanDefinition;
import io.micronaut.inject.BeanIdentifier;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;

@Factory
@RequiresMetrics
public class CaffeineCacheMetricsMixinConfigurator extends MetricsMixinConfigurator<Cache<?, ?>> {

    @Inject
    public CaffeineCacheMetricsMixinConfigurator(@Nonnull MeterRegistry registry) {
        super(registry);
    }

    @Nonnull
    @Override
    public Cache<?, ?> mixin(@Nonnull MeterRegistry registry, @Nonnull Cache<?, ?> bean, @Nonnull String name) {
        return CaffeineCacheMetrics.monitor(registry, bean, name);
    }

    @Nonnull
    @Override
    protected String getName(@Nonnull Cache<?, ?> bean, @Nonnull BeanDefinition<Cache<?, ?>> definition, @Nonnull BeanIdentifier identifier) {
        return identifier.getName();
    }
}
