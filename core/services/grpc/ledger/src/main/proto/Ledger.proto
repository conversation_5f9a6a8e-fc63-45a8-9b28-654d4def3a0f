syntax = "proto3";
option java_package = "global.symbio.billing.core.services.grpc.ledger";
option java_outer_classname = "LedgerService";
option java_multiple_files = true;

import "UUID.proto";
import "Temporal.proto";
import "BigDecimal.proto";
import "Country.proto";
import "Currency.proto";

service Ledger {
  rpc getSequence(GetSequenceRequest) returns (GetSequenceResponse) {}
  rpc getTransactions(GetTransactionsRequest) returns (GetTransactionsResponse) {}
  rpc getTransaction(GetTransactionRequest) returns (GetTransactionResponse) {}
  rpc getTransactionsByIdentifiers(GetTransactionsByIdentifiersRequest) returns (GetTransactionsByIdentifiersResponse) {}
  rpc getChunkedTransactions(GetChunkedTransactionsRequest) returns (GetChunkedTransactionsResponse) {}
  rpc unlinkInvoiceFromTransactions(UnlinkInvoiceTransactionsRequest) returns (InvoiceTransactionLinkageResponse) {}
  rpc linkInvoiceToTransactions(LinkInvoiceTransactionsRequest) returns (InvoiceTransactionLinkageResponse) {}
  rpc getCategory(GetCategoryRequest) returns (GetCategoryResponse) {}
}

message CategoryMessage {
  uint32 id = 1;
  string name = 2;
}

message TransactionMessage {
  UUIDMessage id = 1;
  uint64 sequence = 2;
  CurrencyMessage currency = 3;
  CategoryMessage category = 4;
  BigDecimalMessage amount = 5;
  BigDecimalMessage taxation = 6;
  UUIDMessage debit = 7;
  UUIDMessage credit = 8;
  optional string description = 9;
  string ref = 10;
  CountryMessage country = 11;
  ZonedDateTimeMessage timestamp = 12;
  optional ZonedDateTimeMessage reported_timestamp = 13;
}

message GetSequenceRequest {
  optional UUIDMessage account = 1;
  optional ZonedDateTimeMessage timestamp = 2;
}

message GetSequenceResponse {
  uint64 sequence = 1;
}

message GetTransactionsRequest {
  UUIDMessage account = 1;
  uint64 sequence_start = 2;
  uint64 sequence_end = 3;
}

message GetTransactionsResponse {
  repeated UUIDMessage transactions = 1;
}

message GetTransactionRequest {
  UUIDMessage identifier = 1;
}

message GetTransactionResponse {
  optional TransactionMessage transaction = 1;
}

message GetTransactionsByIdentifiersRequest {
  repeated UUIDMessage identifiers = 1;
}

message TransactionLookupMessage {
  UUIDMessage identifier = 1;
  optional TransactionMessage transaction = 2;
}

message GetTransactionsByIdentifiersResponse {
  repeated TransactionLookupMessage transactions = 1;
}

message GetChunkedTransactionsRequest {
  UUIDMessage invoice = 1;
  UUIDMessage account = 2;
  uint64 sequence_start = 3;
  uint64 sequence_end = 4;
  uint32 limit = 5;
}

message GetChunkedTransactionsResponse {
  bool complete = 1;
  uint64 sequence_max = 2;
  repeated UUIDMessage identifiers = 3;
}

message InvoiceTransactionLinkageResponse {
  uint64 count = 1;
}

message UnlinkInvoiceTransactionsRequest {
  UUIDMessage invoice = 1;
}

message LinkInvoiceTransactionsRequest {
  UUIDMessage invoice = 1;
  UUIDMessage account = 2;
  uint64 sequence_start = 3;
  uint64 sequence_end = 4;
}

message GetCategoryRequest {
  uint32 id = 1;
}

message GetCategoryResponse {
  optional CategoryMessage category = 1;
}