package global.symbio.billing.core.services.ledger.codec;

import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.ledger.CategoryMessage;
import global.symbio.billing.ledger.persistence.api.transaction.category.TransactionCategory;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

@Singleton
public class CategoryCodec implements GRPCCodec<TransactionCategory, CategoryMessage> {

    @Nonnull
    @Override
    public CategoryMessage encode(@Nonnull TransactionCategory bean) {
        return CategoryMessage.newBuilder()
            .setId(bean.getIdentifier())
            .setName(bean.getName())
            .build();
    }

    @Nonnull
    @Override
    public TransactionCategory decode(@Nonnull CategoryMessage protobuf) {
        final var identifier = protobuf.getId();
        final var name = protobuf.getName();
        return new GRPCTransactionCategoryDataAccessObject(identifier, name).entity();
    }
}