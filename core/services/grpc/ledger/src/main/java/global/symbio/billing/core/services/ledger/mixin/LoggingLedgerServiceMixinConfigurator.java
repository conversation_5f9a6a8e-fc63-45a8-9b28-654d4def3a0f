package global.symbio.billing.core.services.ledger.mixin;

import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.mixin.MixinConfigurator;
import global.symbio.billing.core.services.ledger.LedgerService;
import io.micronaut.context.annotation.Factory;
import io.micronaut.core.order.Ordered;
import io.micronaut.inject.BeanDefinition;
import io.micronaut.inject.BeanIdentifier;
import jakarta.annotation.Nonnull;

@Factory
@VisibleForTesting
class LoggingLedgerServiceMixinConfigurator extends MixinConfigurator<LedgerService> {

    private static final String COMPONENT = "logging";

    @Nonnull
    @Override
    public LedgerService mixin(@Nonnull LedgerService bean, @Nonnull String name) {
        return new LoggingLedgerService(bean);
    }

    @Nonnull
    @Override
    protected String component() {
        return COMPONENT;
    }

    @Nonnull
    @Override
    protected String getName(@Nonnull LedgerService bean, @Nonnull BeanDefinition<LedgerService> definition, @Nonnull BeanIdentifier identifier) {
        return identifier.getName();
    }

    @Override
    protected boolean isEnabled() {
        return true;
    }

    @Override
    public int getOrder() {
        return Ordered.LOWEST_PRECEDENCE;
    }
}