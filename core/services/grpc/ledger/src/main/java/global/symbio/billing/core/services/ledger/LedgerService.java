package global.symbio.billing.core.services.ledger;

import global.symbio.billing.ledger.persistence.api.transaction.Transaction;
import global.symbio.billing.ledger.persistence.api.transaction.category.TransactionCategory;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;
import java.util.*;

public interface LedgerService {

    long getSequence(@Nullable UUID account, @Nullable ZonedDateTime timestamp);

    @Nonnull
    Collection<UUID> getTransactions(@Nonnull UUID account, long start, long end);

    @Nonnull
    Optional<Transaction> getTransaction(@Nonnull UUID identifier);

    @Nonnull
    Map<UUID, Transaction> getTransactionsByIdentifiers(@Nonnull Set<UUID> identifiers);

    @Nonnull
    TransactionChunk getChunkedTransactions(@Nonnull UUID invoice, @Nonnull UUID account, long start, long end, int limit);

    long unlinkInvoiceFromTransactions(@Nonnull UUID invoice);

    long linkInvoiceToTransactions(@Nonnull UUID invoice, @Nonnull UUID account, long start, long end);

    @Nonnull
    Optional<TransactionCategory> getCategory(@Nonnull Integer category);
}