package global.symbio.billing.core.services.ledger.codec;

import global.symbio.billing.core.country.persistence.api.Country;
import global.symbio.billing.core.currency.persistence.api.Currency;
import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.ledger.CategoryMessage;
import global.symbio.billing.core.services.grpc.ledger.TransactionMessage;
import global.symbio.billing.core.services.grpc.message.*;
import global.symbio.billing.ledger.persistence.api.transaction.Transaction;
import global.symbio.billing.ledger.persistence.api.transaction.category.TransactionCategory;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Objects;
import java.util.UUID;

@Singleton
public class TransactionCodec implements GRPCCodec<Transaction, TransactionMessage> {

    @Nonnull
    private final Codec<UUID, UUIDMessage> uuid;

    @Nonnull
    private final Codec<Currency, CurrencyMessage> currency;

    @Nonnull
    private final Codec<TransactionCategory, CategoryMessage> category;

    @Nonnull
    private final Codec<BigDecimal, BigDecimalMessage> decimal;

    @Nonnull
    private final Codec<Country, CountryMessage> country;

    @Nonnull
    private final Codec<ZonedDateTime, ZonedDateTimeMessage> timestamp;

    @Inject
    public TransactionCodec(
        @Nonnull Codec<UUID, UUIDMessage> uuid,
        @Nonnull Codec<Currency, CurrencyMessage> currency,
        @Nonnull Codec<TransactionCategory, CategoryMessage> category,
        @Nonnull Codec<BigDecimal, BigDecimalMessage> decimal,
        @Nonnull Codec<Country, CountryMessage> country,
        @Nonnull Codec<ZonedDateTime, ZonedDateTimeMessage> timestamp
    ) {
        this.uuid = Objects.requireNonNull(uuid, "uuid");
        this.currency = Objects.requireNonNull(currency, "currency");
        this.category = Objects.requireNonNull(category, "category");
        this.decimal = Objects.requireNonNull(decimal, "decimal");
        this.country = Objects.requireNonNull(country, "country");
        this.timestamp = Objects.requireNonNull(timestamp, "timestamp");
    }

    @Nonnull
    @Override
    public TransactionMessage encode(@Nonnull Transaction bean) {
        final var builder = TransactionMessage.newBuilder()
            .setId(uuid.encode(bean.getIdentifier()))
            .setSequence(bean.getSequence())
            .setCurrency(currency.encode(bean.getCurrency()))
            .setCategory(category.encode(bean.getCategory()))
            .setAmount(decimal.encode(bean.getAmount()))
            .setTaxation(decimal.encode(bean.getTaxation()))
            .setDebit(uuid.encode(bean.getDebit()))
            .setCredit(uuid.encode(bean.getCredit()))
            .setRef(bean.getRef())
            .setCountry(country.encode(bean.getCountry()))
            .setTimestamp(timestamp.encode(bean.getTimestamp()));

        if (bean.getDescription() != null) {
            builder.setDescription(bean.getDescription());
        }
        if (bean.getReportedTimestamp() != null) {
            builder.setReportedTimestamp(timestamp.encode(bean.getReportedTimestamp()));
        }

        return builder.build();
    }

    @Nonnull
    @Override
    public Transaction decode(@Nonnull TransactionMessage protobuf) {
        final var identifier = uuid.decode(protobuf.getId());
        final var sequence = protobuf.getSequence();
        final var currency = this.currency.decode(protobuf.getCurrency()).data();
        final var category = this.category.decode(protobuf.getCategory()).data();
        final var amount = decimal.decode(protobuf.getAmount());
        final var taxation = decimal.decode(protobuf.getTaxation());
        final var debit = uuid.decode(protobuf.getDebit());
        final var credit = uuid.decode(protobuf.getCredit());
        final var description = protobuf.hasDescription() ? protobuf.getDescription() : null;
        final var ref = protobuf.getRef();
        final var country = this.country.decode(protobuf.getCountry()).data();
        final var timestamp = this.timestamp.decode(protobuf.getTimestamp());
        final var reported = protobuf.hasReportedTimestamp() ? this.timestamp.decode(protobuf.getReportedTimestamp()) : null;
        return new GRPCTransactionDataAccessObject(identifier, sequence, currency, category, amount, taxation, debit, credit, description, ref, country, timestamp, reported).entity();
    }
}
