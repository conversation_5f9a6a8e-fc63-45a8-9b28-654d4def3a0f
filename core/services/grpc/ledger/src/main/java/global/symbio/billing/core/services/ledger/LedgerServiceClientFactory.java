package global.symbio.billing.core.services.ledger;

import global.symbio.billing.core.services.grpc.ledger.LedgerGrpc;
import io.grpc.ManagedChannel;
import io.micronaut.context.annotation.Bean;
import io.micronaut.context.annotation.Factory;
import io.micronaut.context.annotation.Requires;
import io.micronaut.grpc.annotation.GrpcChannel;
import jakarta.annotation.Nonnull;

@Factory
public class LedgerServiceClientFactory {

    public static final String SERVICE_GRPC_CHANNEL = "ledger";

    @Bean
    @Nonnull
    @Requires("grpc.channels." + SERVICE_GRPC_CHANNEL)
    public LedgerGrpc.LedgerFutureStub ledger(@Nonnull @GrpcChannel(SERVICE_GRPC_CHANNEL) ManagedChannel channel) {
        return LedgerGrpc.newFutureStub(channel);
    }
}