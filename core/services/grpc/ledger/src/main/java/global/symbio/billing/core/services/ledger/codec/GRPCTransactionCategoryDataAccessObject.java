package global.symbio.billing.core.services.ledger.codec;

import global.symbio.billing.core.exception.sdk.BillingEntityUnmodifiableException;
import global.symbio.billing.ledger.persistence.api.transaction.category.TransactionCategory;
import global.symbio.billing.ledger.persistence.api.transaction.category.TransactionCategoryDataAccessObject;
import jakarta.annotation.Nonnull;
import lombok.Getter;

import java.util.Objects;

@Getter
public class GRPCTransactionCategoryDataAccessObject extends TransactionCategoryDataAccessObject {

    @Nonnull
    private final Integer identifier;

    @Nonnull
    private final String name;

    public GRPCTransactionCategoryDataAccessObject(@Nonnull Integer identifier, @Nonnull String name) {
        this.identifier = Objects.requireNonNull(identifier, "identifier");
        this.name = Objects.requireNonNull(name, "name");
    }

    @Nonnull
    @Override
    public TransactionCategoryDataAccessObject identifier(@Nonnull Integer identifier) {
        throw new BillingEntityUnmodifiableException(TransactionCategory.class, this.identifier, "identifier");
    }

    @Nonnull
    @Override
    public TransactionCategoryDataAccessObject name(@Nonnull String name) {
        throw new BillingEntityUnmodifiableException(TransactionCategory.class, this.identifier, "name");
    }
}
