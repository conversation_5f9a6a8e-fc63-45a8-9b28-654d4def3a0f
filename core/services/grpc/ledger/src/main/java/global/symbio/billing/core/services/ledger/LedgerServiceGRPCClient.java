package global.symbio.billing.core.services.ledger;

import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.ledger.*;
import global.symbio.billing.core.services.grpc.message.UUIDMessage;
import global.symbio.billing.core.services.grpc.message.ZonedDateTimeMessage;
import global.symbio.billing.ledger.persistence.api.transaction.Transaction;
import global.symbio.billing.ledger.persistence.api.transaction.category.TransactionCategory;
import io.micronaut.context.annotation.Requires;
import io.micronaut.context.annotation.Secondary;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.time.ZonedDateTime;
import java.util.*;

@Slf4j
@Secondary
@Singleton
@Requires(beans = LedgerGrpc.LedgerFutureStub.class)
public class LedgerServiceGRPCClient implements LedgerService {

    @Nonnull
    private final LedgerGrpc.LedgerFutureStub ledger;

    @Nonnull
    private final Codec<UUID, UUIDMessage> uuid;

    @Nonnull
    private final Codec<ZonedDateTime, ZonedDateTimeMessage> timestamp;

    @Nonnull
    private final Codec<Transaction, TransactionMessage> transaction;

    @Nonnull
    private final Codec<TransactionCategory, CategoryMessage> category;

    @Inject
    public LedgerServiceGRPCClient(
        @Nonnull LedgerGrpc.LedgerFutureStub ledger,
        @Nonnull Codec<UUID, UUIDMessage> uuid,
        @Nonnull Codec<ZonedDateTime, ZonedDateTimeMessage> timestamp,
        @Nonnull Codec<Transaction, TransactionMessage> transaction,
        @Nonnull Codec<TransactionCategory, CategoryMessage> category
    ) {
        this.ledger = Objects.requireNonNull(ledger, "ledger");
        this.uuid = Objects.requireNonNull(uuid, "uuid");
        this.timestamp = Objects.requireNonNull(timestamp, "timestamp");
        this.transaction = Objects.requireNonNull(transaction, "transaction");
        this.category = Objects.requireNonNull(category, "category");
    }

    @Override
    public long getSequence(@Nullable UUID account, @Nullable ZonedDateTime timestamp) {
        try {
            final var request = GetSequenceRequest.newBuilder();
            if (account != null) {
                request.setAccount(uuid.encode(account));
            }
            if (timestamp != null) {
                request.setTimestamp(this.timestamp.encode(timestamp));
            }
            final var response = ledger.getSequence(request.build()).get();
            return response.getSequence();
        } catch (Throwable cause) {
            log.info("Exception fetching sequence: account - {}, timestamp - {}", account, timestamp, cause);
            throw new RuntimeException(cause);
        }
    }

    @Nonnull
    @Override
    public Collection<UUID> getTransactions(@Nonnull UUID account, long start, long end) {
        try {
            final var request = GetTransactionsRequest.newBuilder().setAccount(uuid.encode(account)).setSequenceStart(start).setSequenceEnd(end).build();
            final var response = ledger.getTransactions(request).get();
            return response.getTransactionsList().stream().map(uuid::decode).toList();
        } catch (Throwable cause) {
            log.info("Exception fetching transactions: account - {}, start - {}, end - {}", account, start, end, cause);
            throw new RuntimeException(cause);
        }
    }

    @Nonnull
    @Override
    public Optional<Transaction> getTransaction(@Nonnull UUID identifier) {
        try {
            final var request = GetTransactionRequest.newBuilder().setIdentifier(uuid.encode(identifier)).build();
            final var response = ledger.getTransaction(request).get();
            if (response.hasTransaction()) {
                return Optional.of(transaction.decode(response.getTransaction()));
            }
            return Optional.empty();
        } catch (Throwable cause) {
            log.info("Exception fetching transaction: identifier - {}", identifier, cause);
            throw new RuntimeException(cause);
        }
    }

    @Nonnull
    @Override
    public Map<UUID, Transaction> getTransactionsByIdentifiers(@Nonnull Set<UUID> identifiers) {
        try {
            final var request = GetTransactionsByIdentifiersRequest.newBuilder().addAllIdentifiers(identifiers.stream().map(uuid::encode).toList()).build();
            final var response = ledger.getTransactionsByIdentifiers(request).get();
            final var transactions = new HashMap<UUID, Transaction>(identifiers.size());
            for (final var message : response.getTransactionsList()) {
                final var identifier = uuid.decode(message.getIdentifier());
                final var transaction = message.hasTransaction() ? this.transaction.decode(message.getTransaction()) : null;
                transactions.put(identifier, transaction);
            }
            return Collections.unmodifiableMap(transactions);
        } catch (Throwable cause) {
            log.info("Exception fetching transactions: size - {}", identifiers.size(), cause);
            throw new RuntimeException(cause);
        }
    }

    @Nonnull
    @Override
    public TransactionChunk getChunkedTransactions(@Nonnull UUID invoice, @Nonnull UUID account, long start, long end, int limit) {
        try {
            final var request = GetChunkedTransactionsRequest.newBuilder().setInvoice(uuid.encode(invoice)).setAccount(uuid.encode(account)).setSequenceStart(start).setSequenceEnd(end).setLimit(limit).build();
            final var response = ledger.getChunkedTransactions(request).get();
            final var identifiers = response.getIdentifiersList().stream().map(uuid::decode).toList();
            return new TransactionChunk(response.getComplete(), response.getSequenceMax(), identifiers);
        } catch (Throwable cause) {
            log.info("Exception fetching chunked transactions: account - {}, start - {}, end - {}, limit - {}", account, start, end, limit, cause);
            throw new RuntimeException(cause);
        }
    }

    @Override
    public long unlinkInvoiceFromTransactions(@Nonnull UUID invoice) {
        try {
            final var request = UnlinkInvoiceTransactionsRequest.newBuilder().setInvoice(uuid.encode(invoice)).build();
            final var response = ledger.unlinkInvoiceFromTransactions(request).get();
            return response.getCount();
        } catch (Throwable cause) {
            log.info("Exception unlinking transactions for invoice: invoice - {}", invoice, cause);
            throw new RuntimeException(cause);
        }
    }

    @Override
    public long linkInvoiceToTransactions(@Nonnull UUID invoice, @Nonnull UUID account, long start, long end) {
        try {
            final var request = LinkInvoiceTransactionsRequest.newBuilder().setInvoice(uuid.encode(invoice)).setAccount(uuid.encode(account)).setSequenceStart(start).setSequenceEnd(end).build();
            final var response = ledger.linkInvoiceToTransactions(request).get();
            return response.getCount();
        } catch (Throwable cause) {
            log.info("Exception linking transactions for invoice: invoice - {}, account - {}, start - {}, end - {}", invoice, account, start, end, cause);
            throw new RuntimeException(cause);
        }
    }

    @Nonnull
    @Override
    public Optional<TransactionCategory> getCategory(@Nonnull Integer category) {
        try {
            final var request = GetCategoryRequest.newBuilder().setId(category).build();
            final var response = ledger.getCategory(request).get();
            if (response.hasCategory()) {
                return Optional.of(this.category.decode(response.getCategory()));
            }
            return Optional.empty();
        } catch (Throwable cause) {
            log.info("Exception fetching category: id - {}", category, cause);
            throw new RuntimeException(cause);
        }
    }
}
