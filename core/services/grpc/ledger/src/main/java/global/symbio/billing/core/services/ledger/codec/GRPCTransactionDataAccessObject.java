package global.symbio.billing.core.services.ledger.codec;

import global.symbio.billing.core.country.persistence.api.CountryDataAccessObject;
import global.symbio.billing.core.currency.persistence.api.CurrencyDataAccessObject;
import global.symbio.billing.core.exception.sdk.BillingEntityUnmodifiableException;
import global.symbio.billing.ledger.persistence.api.transaction.Transaction;
import global.symbio.billing.ledger.persistence.api.transaction.TransactionDataAccessObject;
import global.symbio.billing.ledger.persistence.api.transaction.category.TransactionCategoryDataAccessObject;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.Getter;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Objects;
import java.util.UUID;

@Getter
public final class GRPCTransactionDataAccessObject extends TransactionDataAccessObject {

    @Nonnull
    private final UUID identifier;

    @Nonnull
    private final Long sequence;

    @Nonnull
    private final CurrencyDataAccessObject currency;

    @Nonnull
    private final TransactionCategoryDataAccessObject category;

    @Nonnull
    private final BigDecimal amount;

    @Nonnull
    private final BigDecimal taxation;

    @Nonnull
    private final UUID debit;

    @Nonnull
    private final UUID credit;

    @Nullable
    private final String description;

    @Nonnull
    private final String ref;

    @Nonnull
    private final CountryDataAccessObject country;

    @Nonnull
    private final ZonedDateTime timestamp;

    @Nullable
    private final ZonedDateTime reportedTimestamp;

    public GRPCTransactionDataAccessObject(
        @Nonnull UUID identifier,
        @Nonnull Long sequence,
        @Nonnull CurrencyDataAccessObject currency,
        @Nonnull TransactionCategoryDataAccessObject category,
        @Nonnull BigDecimal amount,
        @Nonnull BigDecimal taxation,
        @Nonnull UUID debit,
        @Nonnull UUID credit,
        @Nullable String description,
        @Nonnull String ref,
        @Nonnull CountryDataAccessObject country,
        @Nonnull ZonedDateTime timestamp,
        @Nullable ZonedDateTime reportedTimestamp
    ) {
        this.identifier = Objects.requireNonNull(identifier, "identifier");
        this.sequence = Objects.requireNonNull(sequence, "sequence");
        this.currency = Objects.requireNonNull(currency, "currency");
        this.category = Objects.requireNonNull(category, "category");
        this.amount = Objects.requireNonNull(amount, "amount");
        this.taxation = Objects.requireNonNull(taxation, "taxation");
        this.debit = Objects.requireNonNull(debit, "debit");
        this.credit = Objects.requireNonNull(credit, "credit");
        this.description = description;
        this.ref = Objects.requireNonNull(ref, "ref");
        this.country = Objects.requireNonNull(country, "country");
        this.timestamp = Objects.requireNonNull(timestamp, "timestamp");
        this.reportedTimestamp = reportedTimestamp;
    }

    @Nonnull
    @Override
    public TransactionDataAccessObject identifier(@Nonnull UUID identifier) {
        throw new BillingEntityUnmodifiableException(Transaction.class, this.identifier, "identifier");
    }

    @Nonnull
    @Override
    public TransactionDataAccessObject sequence(@Nonnull Long sequence) {
        throw new BillingEntityUnmodifiableException(Transaction.class, this.identifier, "sequence");
    }

    @Nonnull
    @Override
    public TransactionDataAccessObject currency(@Nonnull CurrencyDataAccessObject currency) {
        throw new BillingEntityUnmodifiableException(Transaction.class, this.identifier, "currency");
    }

    @Nonnull
    @Override
    public TransactionDataAccessObject category(@Nonnull TransactionCategoryDataAccessObject category) {
        throw new BillingEntityUnmodifiableException(Transaction.class, this.identifier, "category");
    }

    @Nonnull
    @Override
    public TransactionDataAccessObject amount(@Nonnull BigDecimal amount) {
        throw new BillingEntityUnmodifiableException(Transaction.class, this.identifier, "amount");
    }

    @Nonnull
    @Override
    public TransactionDataAccessObject taxation(@Nonnull BigDecimal taxation) {
        throw new BillingEntityUnmodifiableException(Transaction.class, this.identifier, "taxation");
    }

    @Nonnull
    @Override
    public TransactionDataAccessObject debit(@Nonnull UUID account) {
        throw new BillingEntityUnmodifiableException(Transaction.class, this.identifier, "debit");
    }

    @Nonnull
    @Override
    public TransactionDataAccessObject credit(@Nonnull UUID account) {
        throw new BillingEntityUnmodifiableException(Transaction.class, this.identifier, "credit");
    }

    @Nonnull
    @Override
    public TransactionDataAccessObject description(@Nullable String description) {
        throw new BillingEntityUnmodifiableException(Transaction.class, this.identifier, "description");
    }

    @Nonnull
    @Override
    public TransactionDataAccessObject ref(@Nonnull String plexid) {
        throw new BillingEntityUnmodifiableException(Transaction.class, this.identifier, "ref");
    }

    @Nonnull
    @Override
    public TransactionDataAccessObject country(@Nonnull CountryDataAccessObject country) {
        throw new BillingEntityUnmodifiableException(Transaction.class, this.identifier, "country");
    }

    @Nonnull
    @Override
    public TransactionDataAccessObject timestamp(@Nonnull ZonedDateTime timestamp) {
        throw new BillingEntityUnmodifiableException(Transaction.class, this.identifier, "timestamp");
    }

    @Nonnull
    @Override
    public TransactionDataAccessObject reportedTimestamp(@Nullable ZonedDateTime timestamp) {
        throw new BillingEntityUnmodifiableException(Transaction.class, this.identifier, "reportedTimestamp");
    }
}