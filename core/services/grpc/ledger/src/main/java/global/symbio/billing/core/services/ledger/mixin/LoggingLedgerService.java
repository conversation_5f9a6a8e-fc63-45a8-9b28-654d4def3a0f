package global.symbio.billing.core.services.ledger.mixin;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.MoreObjects;
import global.symbio.billing.core.services.ledger.LedgerService;
import global.symbio.billing.core.services.ledger.TransactionChunk;
import global.symbio.billing.ledger.persistence.api.transaction.Transaction;
import global.symbio.billing.ledger.persistence.api.transaction.category.TransactionCategory;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.AccessLevel;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.ZonedDateTime;
import java.util.*;

@VisibleForTesting
class LoggingLedgerService implements LedgerService {

    @Nonnull
    private final Logger log;

    @Nonnull
    @Getter(AccessLevel.PACKAGE)
    private final LedgerService ledger;

    public LoggingLedgerService(@Nonnull LedgerService ledger) {
        this(LoggerFactory.getLogger(ledger.getClass()), ledger);
    }

    public LoggingLedgerService(@Nonnull Logger log, @Nonnull LedgerService ledger) {
        this.log = Objects.requireNonNull(log, "log");
        this.ledger = Objects.requireNonNull(ledger, "ledger");
    }

    @Override
    public long getSequence(@Nullable UUID account, @Nullable ZonedDateTime timestamp) {
        log.info("LedgerService::getSequence: account - {}, timestamp - {}", account, timestamp);
        return ledger.getSequence(account, timestamp);
    }

    @Nonnull
    @Override
    public Collection<UUID> getTransactions(@Nonnull UUID account, long start, long end) {
        log.info("LedgerService::getTransactions: account - {}, start - {}, end - {}", account, start, end);
        return ledger.getTransactions(account, start, end);
    }

    @Nonnull
    @Override
    public Optional<Transaction> getTransaction(@Nonnull UUID identifier) {
        log.info("LedgerService::getTransaction: transaction - {}", identifier);
        return ledger.getTransaction(identifier);
    }

    @Nonnull
    @Override
    public Map<UUID, Transaction> getTransactionsByIdentifiers(@Nonnull Set<UUID> identifiers) {
        log.info("LedgerService::getTransactionsByIdentifiers: transactions - {}", identifiers.size());
        return ledger.getTransactionsByIdentifiers(identifiers);
    }

    @Nonnull
    @Override
    public TransactionChunk getChunkedTransactions(@Nonnull UUID invoice, @Nonnull UUID account, long start, long end, int limit) {
        log.info("LedgerService::getChunkedTransactions: invoice - {}, account - {}, start - {}, end - {}, limit - {}", invoice, account, start, end, limit);
        return ledger.getChunkedTransactions(invoice, account, start, end, limit);
    }

    @Override
    public long unlinkInvoiceFromTransactions(@Nonnull UUID invoice) {
        log.info("LedgerService::unlinkInvoiceFromTransactions: invoice - {}", invoice);
        return ledger.unlinkInvoiceFromTransactions(invoice);
    }

    @Override
    public long linkInvoiceToTransactions(@Nonnull UUID invoice, @Nonnull UUID account, long start, long end) {
        log.info("LedgerService::linkInvoiceToTransactions: invoice - {}, account - {}, start - {}, end - {}", invoice, account, start, end);
        return ledger.linkInvoiceToTransactions(invoice, account, start, end);
    }

    @Nonnull
    @Override
    public Optional<TransactionCategory> getCategory(@Nonnull Integer id) {
        log.debug("LedgerService::getCategory: category - {}", id);
        return ledger.getCategory(id);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof LoggingLedgerService service)) return false;
        return Objects.equals(getLedger(), service.getLedger());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getLedger());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("ledger", getLedger())
            .toString();
    }
}