package global.symbio.billing.core.services.ledger.codec;

import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.ledger.CategoryMessage;
import global.symbio.billing.ledger.persistence.api.transaction.category.TransactionCategory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class TestCategoryCodec {

    private Codec<TransactionCategory, CategoryMessage> codec;

    @BeforeEach
    public void setup() {
        codec = new CategoryCodec();
    }

    @Test
    @DisplayName("CategoryCodec::encode bean to protobuf")
    public void codec_bean_to_protobuf() {
        final var bean = new GRPCTransactionCategoryDataAccessObject(1, "Test-Transaction-Category-Data-Object-Name").entity();
        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));

        assertEquals(bean.getIdentifier(), protobuf.getId());
        assertEquals(bean.getName(), protobuf.getName());
    }

    @Test
    @DisplayName("CategoryCodec::decode protobuf to bean")
    public void codec_protobuf_to_bean() {
        final var protobuf = CategoryMessage.newBuilder()
            .setId(1)
            .setName("Test-Protobuf-Name")
            .build();
        final var bean = assertDoesNotThrow(() -> codec.decode(protobuf));

        assertEquals(bean.getIdentifier(), protobuf.getId());
        assertEquals(bean.getName(), protobuf.getName());
    }
}
