package global.symbio.billing.core.services.ledger.mixin;

import com.fasterxml.uuid.impl.UUIDUtil;
import global.symbio.billing.core.services.ledger.LedgerService;
import global.symbio.billing.core.services.ledger.TransactionChunk;
import global.symbio.billing.ledger.persistence.api.transaction.Transaction;
import global.symbio.billing.ledger.persistence.api.transaction.category.TransactionCategory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;

import java.time.ZonedDateTime;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.anySet;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestLoggingLedgerService {

    @Mock
    private Logger log;

    @Mock
    private LedgerService delegate;

    private LoggingLedgerService service;

    @BeforeEach
    public void setup() {
        service = new LoggingLedgerService(log, delegate);
    }

    @Test
    @DisplayName("LoggingLedgerService::new rejects null constructor arguments")
    public void logging_ledger_service_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new LoggingLedgerService(null));
        assertThrows(NullPointerException.class, () -> new LoggingLedgerService(log, null));
        assertThrows(NullPointerException.class, () -> new LoggingLedgerService(null, delegate));
    }

    @Test
    @DisplayName("LoggingLedgerService::getSequence logs and invokes method on delegate")
    public void logging_ledger_service_get_sequence_logs_and_invokes_delegate_method() {
        final var account = UUIDUtil.nilUUID();
        final var timestamp = ZonedDateTime.now();
        when(delegate.getSequence(any(UUID.class), any(ZonedDateTime.class))).thenReturn(1337L);
        final var result = assertDoesNotThrow(() -> service.getSequence(account, timestamp));
        assertEquals(1337L, result);
        verify(log, times(1)).info(anyString(), eq(account), eq(timestamp));
        verify(delegate, times(1)).getSequence(eq(account), eq(timestamp));
    }

    @Test
    @DisplayName("LoggingLedgerService::getTransactions logs and invokes method on delegate")
    public void logging_ledger_service_get_transactions_logs_and_invokes_delegate_method() {
        final var account = UUIDUtil.nilUUID();
        final var start = 0L;
        final var end = 10L;
        final var response = Set.of(UUIDUtil.nilUUID());
        when(delegate.getTransactions(any(UUID.class), anyLong(), anyLong())).thenReturn(response);
        final var result = assertDoesNotThrow(() -> service.getTransactions(account, start, end));
        assertSame(response, result);
        verify(log, times(1)).info(anyString(), eq(account), eq(start), eq(end));
        verify(delegate, times(1)).getTransactions(eq(account), eq(start), eq(end));
    }

    @Test
    @DisplayName("LoggingLedgerService::getTransaction logs and invokes method on delegate")
    public void logging_ledger_service_get_transaction_logs_and_invokes_delegate_method() {
        final var transaction = UUIDUtil.nilUUID();
        final var response = Optional.of(mock(Transaction.class));
        when(delegate.getTransaction(any(UUID.class))).thenReturn(response);
        final var result = assertDoesNotThrow(() -> service.getTransaction(transaction));
        assertSame(response, result);
        verify(log, times(1)).info(anyString(), eq(transaction));
        verify(delegate, times(1)).getTransaction(eq(transaction));
    }

    @Test
    @DisplayName("LoggingLedgerService::getTransactionsByIdentifiers logs and invokes method on delegate")
    public void logging_ledger_service_get_transactions_by_identifiers_logs_and_invokes_delegate_method() {
        final var identifiers = Set.of(UUIDUtil.nilUUID());
        final var response = Map.of(UUIDUtil.nilUUID(), mock(Transaction.class));
        when(delegate.getTransactionsByIdentifiers(anySet())).thenReturn(response);
        final var result = assertDoesNotThrow(() -> service.getTransactionsByIdentifiers(identifiers));
        assertSame(response, result);
        verify(log, times(1)).info(anyString(), eq(1));
        verify(delegate, times(1)).getTransactionsByIdentifiers(eq(identifiers));
    }

    @Test
    @DisplayName("LoggingLedgerService::getChunkedTransactions logs and invokes method on delegate")
    public void logging_ledger_service_get_chunked_transactions_logs_and_invokes_delegate_method() {
        final var invoice = UUIDUtil.nilUUID();
        final var account = UUIDUtil.maxUUID();
        final var start = 0L;
        final var end = 10L;
        final var limit = 100;
        final var response = mock(TransactionChunk.class);
        when(delegate.getChunkedTransactions(any(UUID.class), any(UUID.class), anyLong(), anyLong(), anyInt())).thenReturn(response);
        final var result = assertDoesNotThrow(() -> service.getChunkedTransactions(invoice, account, start, end, limit));
        assertSame(response, result);
        verify(log, times(1)).info(anyString(), eq(invoice), eq(account), eq(start), eq(end), eq(limit));
        verify(delegate, times(1)).getChunkedTransactions(eq(invoice), eq(account), eq(start), eq(end), eq(limit));
    }

    @Test
    @DisplayName("LoggingLedgerService::unlinkInvoiceFromTransactions logs and invokes method on delegate")
    public void logging_ledger_service_unlink_invoice_from_transactions_logs_and_invokes_delegate_method() {
        final var invoice = UUIDUtil.nilUUID();
        final var response = 1337L;
        when(delegate.unlinkInvoiceFromTransactions(any(UUID.class))).thenReturn(response);
        final var result = assertDoesNotThrow(() -> service.unlinkInvoiceFromTransactions(invoice));
        assertEquals(response, result);
        verify(log, times(1)).info(anyString(), eq(invoice));
        verify(delegate, times(1)).unlinkInvoiceFromTransactions(eq(invoice));
    }

    @Test
    @DisplayName("LoggingLedgerService::linkInvoiceToTransactions logs and invokes method on delegate")
    public void logging_ledger_service_link_invoice_to_transactions_logs_and_invokes_delegate_method() {
        final var invoice = UUIDUtil.nilUUID();
        final var account = UUIDUtil.maxUUID();
        final var start = 0L;
        final var end = 10L;
        final var response = 1337L;
        when(delegate.linkInvoiceToTransactions(any(UUID.class), any(UUID.class), anyLong(), anyLong())).thenReturn(response);
        final var result = assertDoesNotThrow(() -> service.linkInvoiceToTransactions(invoice, account, start, end));
        assertEquals(response, result);
        verify(log, times(1)).info(anyString(), eq(invoice), eq(account), eq(start), eq(end));
        verify(delegate, times(1)).linkInvoiceToTransactions(eq(invoice), eq(account), eq(start), eq(end));
    }

    @Test
    @DisplayName("LoggingLedgerService::getCategory logs and invokes method on delegate")
    public void logging_ledger_service_get_category_logs_and_invokes_method() {
        final var id = 1;
        final var category = mock(TransactionCategory.class);
        when(delegate.getCategory(anyInt())).thenReturn(Optional.of(category));
        final var result = assertDoesNotThrow(() -> service.getCategory(id));
        result.ifPresent(txCategory -> assertEquals(category, txCategory));
        verify(log, times(1)).debug(anyString(), eq(id));
        verify(delegate, times(1)).getCategory(eq(id));
    }
}