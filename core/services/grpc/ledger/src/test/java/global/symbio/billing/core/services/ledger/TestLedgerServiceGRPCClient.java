package global.symbio.billing.core.services.ledger;

import com.fasterxml.uuid.impl.UUIDUtil;
import com.google.common.util.concurrent.Futures;
import global.symbio.billing.core.country.persistence.api.Country;
import global.symbio.billing.core.currency.persistence.api.Currency;
import global.symbio.billing.core.services.codec.uuid.UUIDCodec;
import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.codec.country.CountryCodec;
import global.symbio.billing.core.services.grpc.codec.country.GRPCCountryDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.currency.CurrencyCodec;
import global.symbio.billing.core.services.grpc.codec.currency.GRPCCurrencyDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.decimal.BigDecimalCodec;
import global.symbio.billing.core.services.grpc.codec.temporal.*;
import global.symbio.billing.core.services.grpc.ledger.*;
import global.symbio.billing.core.services.grpc.message.*;
import global.symbio.billing.core.services.ledger.codec.CategoryCodec;
import global.symbio.billing.core.services.ledger.codec.GRPCTransactionCategoryDataAccessObject;
import global.symbio.billing.core.services.ledger.codec.GRPCTransactionDataAccessObject;
import global.symbio.billing.core.services.ledger.codec.TransactionCodec;
import global.symbio.billing.ledger.persistence.api.transaction.Transaction;
import global.symbio.billing.ledger.persistence.api.transaction.category.TransactionCategory;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.*;
import java.util.UUID;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestLedgerServiceGRPCClient {

    @Mock
    private LedgerGrpc.LedgerFutureStub ledger;

    private Codec<UUID, UUIDMessage> uuid;

    private Codec<LocalDate, LocalDateMessage> date;

    private Codec<LocalTime, LocalTimeMessage> time;

    private Codec<LocalDateTime, LocalDateTimeMessage> datetime;

    private Codec<ZoneOffset, ZoneOffsetMessage> offset;

    private Codec<ZoneId, ZoneIdMessage> zone;

    private Codec<ZonedDateTime, ZonedDateTimeMessage> timestamp;

    private Codec<Country, CountryMessage> country;

    private Codec<Transaction, TransactionMessage> transaction;

    private Codec<Currency, CurrencyMessage> currency;

    private Codec<TransactionCategory, CategoryMessage> category;

    private Codec<BigDecimal, BigDecimalMessage> decimal;

    private LedgerServiceGRPCClient client;

    @Nonnull
    private static final UUID ID = UUIDUtil.nilUUID();

    @Nonnull
    private static final Instant EPOCH = Instant.EPOCH;

    @BeforeEach
    public void setup() {
        uuid = new UUIDCodec();
        currency = new CurrencyCodec();
        category = new CategoryCodec();
        date = new LocalDateCodec();
        time = new LocalTimeCodec();
        datetime = new LocalDateTimeCodec(date, time);
        offset = new ZoneOffsetCodec();
        zone = new ZoneIdCodec();
        timestamp = new ZonedDateTimeCodec(datetime, offset, zone);
        country = new CountryCodec();
        decimal = new BigDecimalCodec();
        transaction = new TransactionCodec(uuid, currency, category, decimal, country, timestamp);
        client = new LedgerServiceGRPCClient(ledger, uuid, timestamp, transaction, category);
    }


    @Test
    @DisplayName("LedgerServiceGRPCClient::new rejects null constructor arguments")
    public void ledger_service_grpc_client_null_ledger_constructor() {
        assertThrows(NullPointerException.class, () -> new LedgerServiceGRPCClient(null, uuid, timestamp, transaction, category));
        assertThrows(NullPointerException.class, () -> new LedgerServiceGRPCClient(ledger, null, timestamp, transaction, category));
        assertThrows(NullPointerException.class, () -> new LedgerServiceGRPCClient(ledger, uuid, null, transaction, category));
        assertThrows(NullPointerException.class, () -> new LedgerServiceGRPCClient(ledger, uuid, timestamp, null, category));
        assertThrows(NullPointerException.class, () -> new LedgerServiceGRPCClient(ledger, uuid, timestamp, transaction, null));
    }

    @Test
    @DisplayName("LedgerServiceGRPCClient::getSequence invokes gRPC service and returns sequence value")
    public void ledger_service_grpc_client_get_sequence() {
        final var expected = mock(GetSequenceResponse.class);

        when(ledger.getSequence(any(GetSequenceRequest.class))).thenReturn(Futures.immediateFuture(expected));
        when(expected.getSequence()).thenReturn(100L);

        final var result = assertDoesNotThrow(() -> client.getSequence(ID, ZonedDateTime.ofInstant(EPOCH, ZoneId.systemDefault())));

        verify(expected, times(1)).getSequence();
        assertEquals(100L, result);
    }

    @Test
    @DisplayName("LedgerServiceGRPCClient::getTransactions invokes gRPC service and returns list of Transactions")
    public void ledger_service_grpc_client_get_transactions() {
        final var identifier = UUID.randomUUID();
        final var expected = mock(GetTransactionsResponse.class);

        when(ledger.getTransactions(any(GetTransactionsRequest.class))).thenReturn(Futures.immediateFuture(expected));
        when(expected.getTransactionsList()).thenReturn(List.of(uuid.encode(identifier)));

        final var result = assertDoesNotThrow(() -> client.getTransactions(ID, 0L, 1L));

        verify(expected, times(1)).getTransactionsList();
        assertEquals(List.of(identifier), result);
    }

    @ParameterizedTest(name = "{index} LedgerServiceGRPCClient::getTransaction when transaction is present in database: {0}")
    @ValueSource(booleans = { true, false })
    @DisplayName("LedgerServiceGRPCClient::getTransaction invokes gRPC service and returns Transaction if present in database")
    public void ledger_service_grpc_client_get_transaction(final boolean present) {
        final var expected = mock(GetTransactionResponse.class);

        final Optional<Transaction> transaction;
        if (present) {
            final var currency = new GRPCCurrencyDataAccessObject(36, "USD", "US Dollar", "$");
            final var category = new GRPCTransactionCategoryDataAccessObject(3, "Payment");
            final var country = new GRPCCountryDataAccessObject(36, "Australia", "AU");
            transaction = Optional.of(new GRPCTransactionDataAccessObject(ID, 1L, currency, category, BigDecimal.TEN, BigDecimal.ZERO, UUID.randomUUID(), UUID.randomUUID(), "description", "ref", country, ZonedDateTime.now(), ThreadLocalRandom.current().nextBoolean() ? ZonedDateTime.now() : null).entity());
        } else {
            transaction = Optional.empty();
        }

        when(ledger.getTransaction(any(GetTransactionRequest.class))).thenReturn(Futures.immediateFuture(expected));
        when(expected.hasTransaction()).thenReturn(transaction.isPresent());
        if (transaction.isPresent()) {
            when(expected.getTransaction()).thenReturn(transaction.map(this.transaction::encode).orElse(null));
        }

        final var result = assertDoesNotThrow(() -> client.getTransaction(ID));

        assertEquals(present, result.isPresent());
        verify(expected, result.isPresent() ? times(1) : never()).getTransaction();
        result.ifPresent(tx -> assertEquals(ID, tx.getIdentifier()));
    }

    @Test
    @DisplayName("LedgerServiceGRPCClient::getTransactionsByIdentifiers invokes gRPC service and returns a map of UUIDs and Transactions")
    public void ledger_service_grpc_client_get_transactions_by_identifiers() {
        final var expected = mock(GetTransactionsByIdentifiersResponse.class);
        final var currency = new GRPCCurrencyDataAccessObject(36, "USD", "US Dollar", "$");
        final var category = new GRPCTransactionCategoryDataAccessObject(3, "Payment");
        final var country = new GRPCCountryDataAccessObject(36, "Australia", "AU");
        final var transaction = new GRPCTransactionDataAccessObject(ID, 1L, currency, category, BigDecimal.TEN, BigDecimal.ZERO, UUID.randomUUID(), UUID.randomUUID(), "description", "ref", country, ZonedDateTime.now(), ThreadLocalRandom.current().nextBoolean() ? ZonedDateTime.now() : null).entity();
        final var map = Map.of(ID, transaction);
        final var lookup = TransactionLookupMessage.newBuilder().setIdentifier(this.uuid.encode(ID)).setTransaction(this.transaction.encode(transaction)).build();

        when(ledger.getTransactionsByIdentifiers(any(GetTransactionsByIdentifiersRequest.class))).thenReturn(Futures.immediateFuture(expected));
        when(expected.getTransactionsList()).thenReturn(List.of(lookup));

        final var result = assertDoesNotThrow(() -> client.getTransactionsByIdentifiers(Set.of(ID)));

        verify(expected, times(1)).getTransactionsList();
        assertEquals(map, result);
    }

    @Test
    @DisplayName("LedgerServiceGRPCClient::getChunkedTransactions invokes gRPC service and returns a Transaction Chunk")
    public void ledger_service_grpc_client_get_chunked_transactions() {
        final var account = UUID.randomUUID();
        final var expected = mock(GetChunkedTransactionsResponse.class);
        when(expected.getComplete()).thenReturn(true);
        when(expected.getSequenceMax()).thenReturn(0L);
        when(expected.getIdentifiersList()).thenReturn(List.of());
        when(ledger.getChunkedTransactions(any(GetChunkedTransactionsRequest.class))).thenReturn(Futures.immediateFuture(expected));

        final var result = assertDoesNotThrow(() -> client.getChunkedTransactions(ID, account, 0L, 10L, 100));
        verify(ledger, times(1)).getChunkedTransactions(any(GetChunkedTransactionsRequest.class));

        assertEquals(new TransactionChunk(true, 0L, List.of()), result);
    }

    @Test
    @DisplayName("LedgerServiceGRPCClient::unlinkInvoiceFromTransactions invokes gRPC service and returns the number of transactions unlinked")
    public void ledger_service_grpc_client_unlink_invoice_from_transactions() {
        final var invoice = UUID.randomUUID();
        final var expected = mock(InvoiceTransactionLinkageResponse.class);
        when(expected.getCount()).thenReturn(1337L);
        when(ledger.unlinkInvoiceFromTransactions(any(UnlinkInvoiceTransactionsRequest.class))).thenReturn(Futures.immediateFuture(expected));

        final var result = assertDoesNotThrow(() -> client.unlinkInvoiceFromTransactions(invoice));
        verify(ledger, times(1)).unlinkInvoiceFromTransactions(any(UnlinkInvoiceTransactionsRequest.class));

        assertEquals(1337L, result);
    }

    @Test
    @DisplayName("LedgerServiceGRPCClient::linkInvoiceToTransactions invokes gRPC service and returns the number of transactions linked")
    public void ledger_service_grpc_client_link_invoice_to_transactions() {
        final var invoice = UUID.randomUUID();
        final var account = UUID.randomUUID();
        final var start = 0L;
        final var end = 10L;
        final var expected = mock(InvoiceTransactionLinkageResponse.class);
        when(expected.getCount()).thenReturn(end - start);
        when(ledger.linkInvoiceToTransactions(any(LinkInvoiceTransactionsRequest.class))).thenReturn(Futures.immediateFuture(expected));

        final var result = assertDoesNotThrow(() -> client.linkInvoiceToTransactions(invoice, account, start, end));
        verify(ledger, times(1)).linkInvoiceToTransactions(any(LinkInvoiceTransactionsRequest.class));

        assertEquals(10L, result);
    }

    @Test
    @DisplayName("LedgerServiceGRPCClient::getCategory invokes gRPC service and returns the transaction category")
    public void ledger_service_grpc_client_get_category() {
        final var category = 1;
        final var expected = new GRPCTransactionCategoryDataAccessObject(category, "CALL CHARGE").entity();
        final var response = mock(GetCategoryResponse.class);
        when(ledger.getCategory(any(GetCategoryRequest.class))).thenReturn(Futures.immediateFuture(response));
        when(response.hasCategory()).thenReturn(true);
        when(response.getCategory()).thenReturn(this.category.encode(expected));

        final var result = assertDoesNotThrow(() -> client.getCategory(category));

        result.ifPresent(txCategory -> assertEquals(expected, txCategory));
    }
}
