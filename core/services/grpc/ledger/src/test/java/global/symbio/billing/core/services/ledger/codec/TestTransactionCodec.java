package global.symbio.billing.core.services.ledger.codec;

import global.symbio.billing.core.country.persistence.api.Country;
import global.symbio.billing.core.currency.persistence.api.Currency;
import global.symbio.billing.core.services.codec.uuid.UUIDCodec;
import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.codec.country.CountryCodec;
import global.symbio.billing.core.services.grpc.codec.country.GRPCCountryDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.currency.CurrencyCodec;
import global.symbio.billing.core.services.grpc.codec.currency.GRPCCurrencyDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.decimal.BigDecimalCodec;
import global.symbio.billing.core.services.grpc.codec.temporal.*;
import global.symbio.billing.core.services.grpc.ledger.CategoryMessage;
import global.symbio.billing.core.services.grpc.ledger.TransactionMessage;
import global.symbio.billing.core.services.grpc.message.*;
import global.symbio.billing.ledger.persistence.api.transaction.Transaction;
import global.symbio.billing.ledger.persistence.api.transaction.category.TransactionCategory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.*;
import java.util.UUID;
import java.util.concurrent.ThreadLocalRandom;

import static org.junit.jupiter.api.Assertions.*;

public class TestTransactionCodec {

    private GRPCCodec<Transaction, TransactionMessage> codec;

    private GRPCCodec<UUID, UUIDMessage> uuid;

    private GRPCCodec<Currency, CurrencyMessage> currency;

    private GRPCCodec<TransactionCategory, CategoryMessage> category;

    private GRPCCodec<BigDecimal, BigDecimalMessage> decimal;

    private Codec<LocalDate, LocalDateMessage> date;

    private Codec<LocalTime, LocalTimeMessage> time;

    private Codec<LocalDateTime, LocalDateTimeMessage> datetime;

    private Codec<ZoneOffset, ZoneOffsetMessage> offset;

    private Codec<ZoneId, ZoneIdMessage> zone;

    private Codec<ZonedDateTime, ZonedDateTimeMessage> timestamp;

    private Codec<Country, CountryMessage> country;

    @BeforeEach
    public void setup() {
        uuid = new UUIDCodec();
        currency = new CurrencyCodec();
        category = new CategoryCodec();
        decimal = new BigDecimalCodec();
        date = new LocalDateCodec();
        time = new LocalTimeCodec();
        datetime = new LocalDateTimeCodec(date, time);
        offset = new ZoneOffsetCodec();
        zone = new ZoneIdCodec();
        timestamp = new ZonedDateTimeCodec(datetime, offset, zone);
        country = new CountryCodec();
        codec = new TransactionCodec(uuid, currency, category, decimal, country, timestamp);
    }

    @Test
    @DisplayName("TransactionCodec::encode bean to protobuf")
    public void codec_bean_to_protobuf() {
        final var currency = new GRPCCurrencyDataAccessObject(36, "USD", "US Dollar", "$");
        final var category = new GRPCTransactionCategoryDataAccessObject(3, "Payment");
        final var country = new GRPCCountryDataAccessObject(36, "Australia", "AU");
        final var bean = new GRPCTransactionDataAccessObject(UUID.randomUUID(), 1L, currency, category, BigDecimal.TEN, BigDecimal.ZERO, UUID.randomUUID(), UUID.randomUUID(), "description", "ref", country, ZonedDateTime.now(), ThreadLocalRandom.current().nextBoolean() ? ZonedDateTime.now() : null).entity();
        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));

        assertEquals(bean.getIdentifier(), uuid.decode(protobuf.getId()));
        assertEquals(bean.getSequence(), protobuf.getSequence());
        assertEquals(bean.getCurrency(), this.currency.decode(protobuf.getCurrency()));
        assertEquals(bean.getCategory(), this.category.decode(protobuf.getCategory()));
        assertEquals(bean.getAmount(), decimal.decode(protobuf.getAmount()));
        assertEquals(bean.getTaxation(), decimal.decode(protobuf.getTaxation()));
        assertEquals(bean.getDebit(), uuid.decode(protobuf.getDebit()));
        assertEquals(bean.getCredit(), uuid.decode(protobuf.getCredit()));
        assertEquals(bean.getDescription(), protobuf.getDescription());
        assertEquals(bean.getRef(), protobuf.getRef());
        assertEquals(bean.getCountry(), this.country.decode(protobuf.getCountry()));
        assertEquals(bean.getTimestamp(), timestamp.decode(protobuf.getTimestamp()));
        if (bean.getReportedTimestamp() == null) {
            assertFalse(protobuf.hasReportedTimestamp());
        } else {
            assertEquals(bean.getReportedTimestamp(), timestamp.decode(protobuf.getReportedTimestamp()));
        }
    }

    @Test
    @DisplayName("TransactionCodec::decode protobuf to bean")
    public void codec_protobuf_to_bean() {
        final var currency = new GRPCCurrencyDataAccessObject(36, "USD", "US Dollar", "$");
        final var category = new GRPCTransactionCategoryDataAccessObject(3, "Payment");
        final var country = new GRPCCountryDataAccessObject(36, "Australia", "AU");
        final var reported = ThreadLocalRandom.current().nextBoolean() ? ZonedDateTime.now() : null;
        final var transaction = new GRPCTransactionDataAccessObject(UUID.randomUUID(), 1L, currency, category, BigDecimal.TEN, BigDecimal.ZERO, UUID.randomUUID(), UUID.randomUUID(), "description", "ref", country, ZonedDateTime.now(), reported).entity();

        final var builder = TransactionMessage.newBuilder()
            .setId(uuid.encode(transaction.getIdentifier()))
            .setSequence(transaction.getSequence())
            .setCurrency(this.currency.encode(transaction.getCurrency()))
            .setCategory(this.category.encode(transaction.getCategory()))
            .setAmount(decimal.encode(transaction.getAmount()))
            .setTaxation(decimal.encode(transaction.getTaxation()))
            .setDebit(uuid.encode(transaction.getDebit()))
            .setCredit(uuid.encode(transaction.getCredit()))
            .setRef(transaction.getRef())
            .setCountry(this.country.encode(transaction.getCountry()))
            .setTimestamp(this.timestamp.encode(transaction.getTimestamp()));
        if (transaction.getDescription() != null) {
            builder.setDescription(transaction.getDescription());
        }
        if (transaction.getReportedTimestamp() != null) {
            builder.setReportedTimestamp(this.timestamp.encode(reported));
        }

        final var protobuf = builder.build();
        final var bean = assertDoesNotThrow(() -> codec.decode(protobuf));
        assertEquals(transaction, bean);
    }
}
