package global.symbio.billing.core.services.grpc.server;

import com.google.protobuf.Any;
import com.google.protobuf.Message;
import com.google.rpc.ErrorInfo;
import com.google.rpc.Status;
import global.symbio.billing.core.exception.sdk.BillingEntityNotFoundException;
import io.grpc.StatusRuntimeException;
import io.grpc.protobuf.StatusProto;
import io.grpc.stub.StreamObserver;
import io.micronaut.context.exceptions.NoSuchBeanException;
import jakarta.annotation.Nonnull;
import org.slf4j.Logger;

import java.util.function.Function;

import static com.google.rpc.Code.*;

public interface GRPCServerBase {

    @Nonnull
    Logger logger();

    default <T> void complete(@Nonnull T response, @Nonnull StreamObserver<T> observer) {
        observer.onNext(response);
        observer.onCompleted();
    }

    default <T> void error(@Nonnull Throwable cause, @Nonnull StreamObserver<T> observer) {
        logger().warn("Exception in gRPC server", cause);
        final var exception = exception(cause);
        observer.onError(exception);
    }

    default <REQUEST extends Message, RESPONSE extends Message> void handle(
        @Nonnull REQUEST request,
        @Nonnull StreamObserver<RESPONSE> observer,
        @Nonnull Function<REQUEST, RESPONSE> handler
    ) {
        try {
            final var response = handler.apply(request);
            complete(response, observer);
        } catch (Throwable cause) {
            error(cause, observer);
        }
    }

    @Nonnull
    default Status.Builder status(@Nonnull Status.Builder status, @Nonnull Throwable cause) {
        final var message = cause.getMessage();
        final var code = switch (cause) {
            case BillingEntityNotFoundException _, NoSuchBeanException _ -> NOT_FOUND_VALUE;
            case IllegalStateException _, IllegalArgumentException _ -> INVALID_ARGUMENT_VALUE;
            default -> UNKNOWN_VALUE;
        };
        return status.setCode(code).setMessage(message);
    }

    @Nonnull
    default StatusRuntimeException exception(@Nonnull Throwable cause) {
        final var status = status(Status.newBuilder(), cause)
            .addDetails(Any.pack(ErrorInfo.newBuilder()
                .setReason(cause.getMessage())
                .setDomain(cause.getClass().getName())
                .build()))
            .build();
        return StatusProto.toStatusRuntimeException(status);
    }
}
