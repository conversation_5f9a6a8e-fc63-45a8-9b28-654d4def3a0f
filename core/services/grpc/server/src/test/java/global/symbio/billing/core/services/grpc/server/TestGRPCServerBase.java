package global.symbio.billing.core.services.grpc.server;

import com.google.rpc.Code;
import com.google.rpc.Status;
import global.symbio.billing.core.exception.sdk.BillingEntityNotFoundException;
import io.micronaut.context.exceptions.NoSuchBeanException;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class TestGRPCServerBase {

    @ParameterizedTest
    @MethodSource("scenarios")
    @DisplayName("GRPCServerBase::status returns appropriate status depending on exception")
    public void grpc_server_base_status(final String clazz, final String message, final int code ) {
        final var server = mock(GRPCServerBase.class);
        final var throwable = switch (clazz) {
            case "BillingEntityNotFoundException" -> mock(BillingEntityNotFoundException.class);
            case "IllegalStateException" -> mock(IllegalStateException.class);
            case "IllegalArgumentException" -> mock(IllegalArgumentException.class);
            case "NoSuchBeanException" -> mock(NoSuchBeanException.class);
            default -> mock(Throwable.class);
        };

        when(throwable.getMessage()).thenReturn(message);
        when(server.status(any(Status.Builder.class), any(Throwable.class))).thenCallRealMethod();
        final var result = server.status(Status.newBuilder(), throwable);
        assertEquals(code, result.getCode());
        assertEquals(message, result.getMessage());
    }

    @Nonnull
    private static Stream<Arguments> scenarios() {
        return Stream.of(
            Arguments.of("BillingEntityNotFoundException", "Entity of type PlexusAccount with identifier 0ec64160-d3c8-49de-aac0-059f8989cb8e could not be found.", Code.NOT_FOUND_VALUE),
            Arguments.of("IllegalStateException", "Unable to create an allocation greater than the unallocated amount. Unallocated: 1000, Requested: 5000.", Code.INVALID_ARGUMENT_VALUE),
            Arguments.of("IllegalArgumentException", "The input currency code must have a length of 3 characters", Code.INVALID_ARGUMENT_VALUE),
            Arguments.of("NoSuchBeanException", "No bean of type [global.symbio.billing.core.services.rest.plexus.service.PlexusService] exists for the given qualifier: @Named('AU').", Code.NOT_FOUND_VALUE),
            Arguments.of("RuntimeException", "This is an exception.", Code.UNKNOWN_VALUE)
        );
    }
}
