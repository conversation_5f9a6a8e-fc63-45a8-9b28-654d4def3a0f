package global.symbio.billing.core.services.invoice.codec;

import global.symbio.billing.core.payment.persistence.api.payment.method.PaymentMethod;
import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.codec.payment.method.GRPCPaymentMethodDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.payment.method.PaymentMethodCodec;
import global.symbio.billing.core.services.grpc.message.PaymentMethodMessage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class TestPaymentMethodCodec {

    private Codec<PaymentMethod, PaymentMethodMessage> codec;

    @BeforeEach
    public void setup() {
        codec = new PaymentMethodCodec();
    }

    @Test
    @DisplayName("PaymentMethodCodec::encode bean to protobuf")
    public void codec_bean_to_protobuf() {
        final var bean = new GRPCPaymentMethodDataAccessObject(1, "BANK_TRANSFER", "Bank Transfer").entity();
        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));
        assertEquals(bean.getIdentifier(), protobuf.getId());
        assertEquals(bean.getName(), protobuf.getName());
        assertEquals(bean.getDisplayName(), protobuf.getDisplayName());
    }

    @Test
    @DisplayName("PaymentMethodCodec::decode protobuf to bean")
    public void codec_protobuf_to_bean() {
        final var protobuf = PaymentMethodMessage.newBuilder()
            .setId(1)
            .setName("BANK_TRANSFER")
            .setDisplayName("Bank Transfer")
            .build();
        final var bean = assertDoesNotThrow(() -> codec.decode(protobuf));
        assertEquals(protobuf.getId(), bean.getIdentifier());
        assertEquals(protobuf.getName(), bean.getName());
        assertEquals(protobuf.getDisplayName(), bean.getDisplayName());
    }
}
