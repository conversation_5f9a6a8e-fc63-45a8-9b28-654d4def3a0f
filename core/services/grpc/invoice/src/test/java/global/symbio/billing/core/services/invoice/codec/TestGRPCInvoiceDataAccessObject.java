package global.symbio.billing.core.services.invoice.codec;

import com.google.common.collect.Range;
import global.symbio.billing.core.country.persistence.api.CountryDataAccessObject;
import global.symbio.billing.core.exception.sdk.BillingEntityUnmodifiableException;
import global.symbio.billing.core.reference.persistence.api.reference.ReferenceDataAccessObject;
import global.symbio.billing.core.reference.persistence.api.reference.ReferenceType;
import global.symbio.billing.core.services.grpc.codec.country.GRPCCountryDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.reference.GRPCReferenceDataAccessObject;
import global.symbio.billing.invoice.persistence.api.invoice.Invoice;
import global.symbio.billing.invoice.persistence.api.invoice.state.generation.GenerationState;
import global.symbio.billing.invoice.persistence.api.invoice.state.generation.Pending;
import global.symbio.billing.invoice.persistence.api.invoice.state.payment.PaymentState;
import global.symbio.billing.invoice.persistence.api.invoice.summary.InvoiceSummaryDataAccessObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

public class TestGRPCInvoiceDataAccessObject {

    private static final UUID ID = UUID.randomUUID();
    private static final UUID ACCOUNT = UUID.randomUUID();
    private static final Integer NUMBER = 1;
    private static final Range<Long> SEQUENCES = Range.closedOpen(0L,10L);
    private static final Range<ZonedDateTime> PERIOD = Range.closedOpen(ZonedDateTime.now().minusDays(1), ZonedDateTime.now().plusDays(1));
    private static final BigDecimal OPENING = BigDecimal.ZERO;
    private static final BigDecimal CLOSING = BigDecimal.ONE;
    private static final PaymentState PAYMENT_STATE = PaymentState.create(BigDecimal.ONE, BigDecimal.ONE);
    private static final GenerationState GENERATION_STATE = new Pending();
    private static final CountryDataAccessObject COUNTRY = new GRPCCountryDataAccessObject(458, "Malaysia", "MYR");
    private static final InvoiceSummaryDataAccessObject INVOICE_SUMMARY =new GRPCInvoiceSummaryDataAccessObject(ID, "summary", ZonedDateTime.now());
    private static final ReferenceDataAccessObject REFERENCE = new GRPCReferenceDataAccessObject(UUID.randomUUID(), 1L, "MY", "CO", 2, ReferenceType.INVOICE, "MYCO00000000012");
    private static final ZonedDateTime DUE_DATE = ZonedDateTime.now();
    private static final ZonedDateTime TIMESTAMP = ZonedDateTime.now();
    private static final ZonedDateTime REPORTED_TIMESTAMP = ZonedDateTime.now();

    private GRPCInvoiceDataAccessObject invoice;

    @BeforeEach
    public void setup() {
        invoice = new GRPCInvoiceDataAccessObject(ID, ACCOUNT, NUMBER, SEQUENCES, PERIOD, OPENING, CLOSING, PAYMENT_STATE, GENERATION_STATE, COUNTRY, INVOICE_SUMMARY, REFERENCE, DUE_DATE, TIMESTAMP, REPORTED_TIMESTAMP);
    }

    @Test
    @DisplayName("GRPCInvoiceDataAccessObject::new rejects null constructor arguments")
    public void grpc_invoice_data_access_object_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new GRPCInvoiceDataAccessObject(null, ACCOUNT, NUMBER, SEQUENCES, PERIOD, OPENING, CLOSING, PAYMENT_STATE, GENERATION_STATE, COUNTRY, INVOICE_SUMMARY, REFERENCE, DUE_DATE, TIMESTAMP, REPORTED_TIMESTAMP));
        assertThrows(NullPointerException.class, () -> new GRPCInvoiceDataAccessObject(ID, null, NUMBER, SEQUENCES, PERIOD, OPENING, CLOSING, PAYMENT_STATE, GENERATION_STATE, COUNTRY, INVOICE_SUMMARY, REFERENCE, DUE_DATE, TIMESTAMP, REPORTED_TIMESTAMP));
        assertThrows(NullPointerException.class, () -> new GRPCInvoiceDataAccessObject(ID, ACCOUNT, null, SEQUENCES, PERIOD, OPENING, CLOSING, PAYMENT_STATE, GENERATION_STATE, COUNTRY, INVOICE_SUMMARY, REFERENCE, DUE_DATE, TIMESTAMP, REPORTED_TIMESTAMP));
        assertThrows(NullPointerException.class, () -> new GRPCInvoiceDataAccessObject(ID, ACCOUNT, NUMBER, null, PERIOD, OPENING, CLOSING, PAYMENT_STATE, GENERATION_STATE, COUNTRY, INVOICE_SUMMARY, REFERENCE, DUE_DATE, TIMESTAMP, REPORTED_TIMESTAMP));
        assertThrows(NullPointerException.class, () -> new GRPCInvoiceDataAccessObject(ID, ACCOUNT, NUMBER, SEQUENCES, null, OPENING, CLOSING, PAYMENT_STATE, GENERATION_STATE, COUNTRY, INVOICE_SUMMARY, REFERENCE, DUE_DATE, TIMESTAMP, REPORTED_TIMESTAMP));
        assertThrows(NullPointerException.class, () -> new GRPCInvoiceDataAccessObject(ID, ACCOUNT, NUMBER, SEQUENCES, PERIOD, null, CLOSING, PAYMENT_STATE, GENERATION_STATE, COUNTRY, INVOICE_SUMMARY, REFERENCE, DUE_DATE, TIMESTAMP, REPORTED_TIMESTAMP));
        assertThrows(NullPointerException.class, () -> new GRPCInvoiceDataAccessObject(ID, ACCOUNT, NUMBER, SEQUENCES, PERIOD, OPENING, null, PAYMENT_STATE, GENERATION_STATE, COUNTRY, INVOICE_SUMMARY, REFERENCE, DUE_DATE, TIMESTAMP, REPORTED_TIMESTAMP));
        assertThrows(NullPointerException.class, () -> new GRPCInvoiceDataAccessObject(ID, ACCOUNT, NUMBER, SEQUENCES, PERIOD, OPENING, CLOSING, null, GENERATION_STATE, COUNTRY, INVOICE_SUMMARY, REFERENCE, DUE_DATE, TIMESTAMP, REPORTED_TIMESTAMP));
        assertThrows(NullPointerException.class, () -> new GRPCInvoiceDataAccessObject(ID, ACCOUNT, NUMBER, SEQUENCES, PERIOD, OPENING, CLOSING, PAYMENT_STATE, null, COUNTRY, INVOICE_SUMMARY, REFERENCE, DUE_DATE, TIMESTAMP, REPORTED_TIMESTAMP));
        assertThrows(NullPointerException.class, () -> new GRPCInvoiceDataAccessObject(ID, ACCOUNT, NUMBER, SEQUENCES, PERIOD, OPENING, CLOSING, PAYMENT_STATE, GENERATION_STATE, null, INVOICE_SUMMARY, REFERENCE, DUE_DATE, TIMESTAMP, REPORTED_TIMESTAMP));
        assertDoesNotThrow(() -> new GRPCInvoiceDataAccessObject(ID, ACCOUNT, NUMBER, SEQUENCES, PERIOD, OPENING, CLOSING, PAYMENT_STATE, GENERATION_STATE, COUNTRY, null, REFERENCE, DUE_DATE, TIMESTAMP, REPORTED_TIMESTAMP));
        assertDoesNotThrow(() -> new GRPCInvoiceDataAccessObject(ID, ACCOUNT, NUMBER, SEQUENCES, PERIOD, OPENING, CLOSING, PAYMENT_STATE, GENERATION_STATE, COUNTRY, INVOICE_SUMMARY, null, DUE_DATE, TIMESTAMP, REPORTED_TIMESTAMP));
        assertDoesNotThrow(() -> new GRPCInvoiceDataAccessObject(ID, ACCOUNT, NUMBER, SEQUENCES, PERIOD, OPENING, CLOSING, PAYMENT_STATE, GENERATION_STATE, COUNTRY, INVOICE_SUMMARY, REFERENCE, null, TIMESTAMP, REPORTED_TIMESTAMP));
        assertThrows(NullPointerException.class, () -> new GRPCInvoiceDataAccessObject(ID, ACCOUNT, NUMBER, SEQUENCES, PERIOD, OPENING, CLOSING, PAYMENT_STATE, GENERATION_STATE, COUNTRY, INVOICE_SUMMARY, REFERENCE, DUE_DATE, null, REPORTED_TIMESTAMP));
        assertDoesNotThrow(() -> new GRPCInvoiceDataAccessObject(ID, ACCOUNT, NUMBER, SEQUENCES, PERIOD, OPENING, CLOSING, PAYMENT_STATE, GENERATION_STATE, COUNTRY, INVOICE_SUMMARY, REFERENCE, DUE_DATE, TIMESTAMP, null));
    }

    @Test
    @DisplayName("GRPCInvoiceDataAccessObject::identifier is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_invoice_data_access_object_identifier_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(Invoice.class, ID, "identifier");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> invoice.identifier(ID));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCInvoiceDataAccessObject::account is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_invoice_data_access_object_account_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(Invoice.class, ID, "account");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> invoice.account(ACCOUNT));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCInvoiceDataAccessObject::number is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_invoice_data_access_object_number_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(Invoice.class, ID, "number");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> invoice.number(NUMBER));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCInvoiceDataAccessObject::sequences is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_invoice_data_access_object_sequences_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(Invoice.class, ID, "sequences");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> invoice.sequences(SEQUENCES));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCInvoiceDataAccessObject::period is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_invoice_data_access_object_period_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(Invoice.class, ID, "period");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> invoice.period(PERIOD));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCInvoiceDataAccessObject::openingBalance is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_invoice_data_access_object_opening_balance_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(Invoice.class, ID, "openingBalance");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> invoice.openingBalance(OPENING));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCInvoiceDataAccessObject::closingBalance is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_invoice_data_access_object_closing_balance_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(Invoice.class, ID, "closingBalance");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> invoice.closingBalance(CLOSING));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCInvoiceDataAccessObject::payment is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_invoice_data_access_object_payment_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(Invoice.class, ID, "payment");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> invoice.payment(PAYMENT_STATE));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCInvoiceDataAccessObject::generation is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_invoice_data_access_object_generation_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(Invoice.class, ID, "generation");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> invoice.generation(GENERATION_STATE));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCInvoiceDataAccessObject::country is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_invoice_data_access_object_country_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(Invoice.class, ID, "country");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> invoice.country(COUNTRY));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCInvoiceDataAccessObject::reference is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_invoice_data_access_object_reference_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(Invoice.class, ID, "reference");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> invoice.reference(REFERENCE));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCInvoiceDataAccessObject::summary is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_invoice_data_access_object_summary_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(Invoice.class, ID, "summary");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> invoice.summary(INVOICE_SUMMARY));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCInvoiceDataAccessObject::dueDate is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_invoice_data_access_object_due_date_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(Invoice.class, ID, "dueDate");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> invoice.dueDate(DUE_DATE));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCInvoiceDataAccessObject::timestamp is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_invoice_data_access_object_timestamp_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(Invoice.class, ID, "timestamp");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> invoice.timestamp(TIMESTAMP));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCInvoiceDataAccessObject::reportedTimestamp is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_invoice_data_access_object_reported_timestamp_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(Invoice.class, ID, "reportedTimestamp");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> invoice.reportedTimestamp(REPORTED_TIMESTAMP));
        assertEquals(expected.getMessage(), cause.getMessage());
    }
}
