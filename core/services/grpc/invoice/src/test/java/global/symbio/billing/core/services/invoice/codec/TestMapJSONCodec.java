package global.symbio.billing.core.services.invoice.codec;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.protobuf.Struct;
import com.google.protobuf.Value;
import global.symbio.billing.core.services.grpc.codec.Codec;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static java.util.Map.entry;
import static org.junit.jupiter.api.Assertions.*;

public class TestMapJSONCodec {

    private Codec<Map, Struct> codec;

    @BeforeEach
    public void setup() {
        codec = new MapJSONCodec(new ObjectMapper());
    }

    @Test
    @DisplayName("MapJSONCodec::encode bean to protobuf")
    public void codec_bean_to_protobuf() {
        final Map<String, Object> bean = Map.ofEntries(
            entry("boolean", true),
            entry("Boolean", Boolean.TRUE),
            entry("string", "string"),
            entry("char", 'a'),
            entry("Character", Character.valueOf('a')),
            entry("byte", (byte) 1),
            entry("Byte", Byte.valueOf((byte) 1)),
            entry("short", (short) 2),
            entry("Short", Short.valueOf((short) 2)),
            entry("int", 4),
            entry("Integer", Integer.valueOf(4)),
            entry("float", 42.0F),
            entry("Float", Float.valueOf(42.0F)),
            entry("long", 8L),
            entry("Long", Long.valueOf(8L)),
            entry("double", 69.0D),
            entry("Double", Double.valueOf(69.0D)),
            entry("BigInteger", BigInteger.TEN),
            entry("BigDecimal", BigDecimal.TEN),
            entry("Map", Map.of("hello", "world", "life", 42)),
            entry("List", List.of(1, 2, 3))
        );
        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));
        final var decoded = assertDoesNotThrow(() -> codec.decode(protobuf));

        assertEquals(true, decoded.get("boolean"));
        assertEquals(Boolean.TRUE, decoded.get("Boolean"));
        assertEquals("string", decoded.get("string"));
        assertEquals("a", decoded.get("char"));
        assertEquals("a", decoded.get("Character"));
        assertEquals(1.0, decoded.get("byte"));
        assertEquals(1.0, decoded.get("Byte"));
        assertEquals(2.0, decoded.get("short"));
        assertEquals(2.0, decoded.get("Short"));
        assertEquals(4.0, decoded.get("int"));
        assertEquals(4.0, decoded.get("Integer"));
        assertEquals(42.0, decoded.get("float"));
        assertEquals(42.0, decoded.get("Float"));
        assertEquals(8.0, decoded.get("long"));
        assertEquals(8.0, decoded.get("Long"));
        assertEquals(69.0, decoded.get("double"));
        assertEquals(69.0, decoded.get("Double"));
        assertEquals(10.0, decoded.get("BigInteger"));
        assertEquals(10.0, decoded.get("BigDecimal"));
        assertEquals(Map.of("hello", "world", "life", 42.0), decoded.get("Map"));
        assertEquals(List.of(1.0, 2.0, 3.0), decoded.get("List"));
    }

    @Test
    @DisplayName("MapJSONCodec::encode/decode serialises null values")
    public void codec_encode_decode_serialises_null_values() {
        final var bean = new HashMap<String, String>() {{
            put("null", null);
            put("nonnull", "nonnull");
        }};
        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));
        final var decoded = assertDoesNotThrow(() -> codec.decode(protobuf));
        assertEquals(bean, decoded);
    }

    @Test
    @DisplayName("MapJSONCodec::decode protobuf to bean")
    public void codec_protobuf_to_bean() {
        final var value = Value.newBuilder().setStringValue("value").build();
        final var struct = Struct.newBuilder().putFields("key", value).build();
        final var bean = assertDoesNotThrow(() -> codec.decode(struct));
        assertEquals(struct, codec.encode(bean));
    }

    @Test
    @DisplayName("MapJSONCodec::encode throws exception")
    public void codec_bean_to_protobuf_throws_exception() {
        // This map has a value that cannot be encoded as a Protobuf message.
        final var bean = Map.of("key", new Object());
        assertThrows(RuntimeException.class, () -> codec.encode(bean));
    }

    @Test
    @DisplayName("MapJSONCodec::decode throws exception")
    public void codec_protobuf_to_bean_throws_exception() {
        // This Struct has an invalid field type that cannot be decoded to a Map object.
        final var encoded = Struct.newBuilder()
            .putFields("key", Value.newBuilder().setNumberValue(Double.POSITIVE_INFINITY).build()).build();
        assertThrows(RuntimeException.class, () -> codec.decode(encoded));
    }
}
