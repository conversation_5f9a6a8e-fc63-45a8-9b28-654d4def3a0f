package global.symbio.billing.core.services.invoice.codec;

import com.google.common.collect.Range;
import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.invoice.RangeMessage;
import global.symbio.billing.core.util.temporal.TemporalUtilities;
import io.hypersistence.utils.hibernate.type.range.guava.PostgreSQLGuavaRangeType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.time.temporal.Temporal;
import java.util.stream.Stream;

import static java.time.ZoneOffset.UTC;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestRangeCodec {

    private Codec<Range<?>, RangeMessage> codec;

    @Mock
    private PostgreSQLGuavaRangeType encoder;

    @BeforeEach
    public void setup() {
        codec = new RangeCodec(encoder);
    }

    @Test
    @DisplayName("RangeCodec::new rejects null constructor arguments")
    public void range_codec_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new RangeCodec(null));
    }

    @Test
    @DisplayName("RangeCodec::new with no-arg constructor initialises encoder to PostgreSQLGuavaRangeType::INSTANCE")
    public void range_codec_no_arg_constructor_uses_shared_singleton() {
        final var codec = new RangeCodec();
        assertSame(PostgreSQLGuavaRangeType.INSTANCE, codec.getEncoder());
    }

    @ParameterizedTest
    @DisplayName("RangeCodec::encode bean to protobuf")
    @MethodSource("ranges")
    public void codec_bean_to_protobuf(final Range<?> bean, final String type) {
        when(encoder.asString(any(Range.class))).thenCallRealMethod();
        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));
        verify(encoder, times(1)).asString(any(Range.class));
        assertEquals(type, protobuf.getType());
        assertEquals(PostgreSQLGuavaRangeType.INSTANCE.asString(bean), protobuf.getRange());
    }

    @ParameterizedTest
    @DisplayName("RangeCodec::decode protobuf to bean")
    @MethodSource("ranges")
    public void codec_protobuf_to_bean(final Range<?> state, final String type) {
        when(encoder.asString(any(Range.class))).thenCallRealMethod();
        final var protobuf = RangeMessage.newBuilder()
            .setType(type)
            .setRange(encoder.asString(state))
            .build();
        final var bean = assertDoesNotThrow(() -> codec.decode(protobuf));
        verify(encoder, times(1)).asString(any(Range.class));
        assertEquals(type, RangeCodec.type(RangeCodec.bound(bean)));

        switch (type) {
            case "tsrange", "tstzrange" -> {
                // Some clocks support microsecond precision whereas other support nanosecond precision.
                // Ensure bounds are within one microsecond of each other, at most, losing a millionth of a second in precision.
                assertTrue(TemporalUtilities.isWithinThreshold((Temporal) state.lowerEndpoint(), (Temporal) bean.lowerEndpoint(), TemporalUtilities.ONE_MICROSECOND));
                assertTrue(TemporalUtilities.isWithinThreshold((Temporal) state.upperEndpoint(), (Temporal) bean.upperEndpoint(), TemporalUtilities.ONE_MICROSECOND));
            }
            default -> assertEquals(state, bean);
        }
    }

    @Test
    @DisplayName("RangeCodec::decode protobuf to bean with unknown range type throws IllegalStateException")
    public void codec_protobuf_to_bean_unknown_type_throws_illegal_state_exception() {
        final var protobuf = RangeMessage.newBuilder()
            .setType("UNKNOWN")
            .build();
        final var cause = assertThrows(IllegalStateException.class, () -> codec.decode(protobuf));
        assertEquals("The range type [UNKNOWN] is not supported!", cause.getMessage());
    }

    private static Stream<Arguments> ranges() {
        return Stream.of(
            Arguments.of(Range.closed(1, 10), "int4range"),
            Arguments.of(Range.closed(1L, 10L), "int8range"),
            Arguments.of(Range.closed(BigDecimal.ONE, BigDecimal.TEN), "numrange"),
            Arguments.of(Range.closed(LocalDateTime.now(), LocalDateTime.now().plusDays(10L)), "tsrange"),
            Arguments.of(Range.closed(ZonedDateTime.now(UTC), ZonedDateTime.now(UTC).plusDays(10L)), "tstzrange"),
            Arguments.of(Range.closed(LocalDate.now(), LocalDate.now().plusDays(10L)), "daterange")
        );
    }
}