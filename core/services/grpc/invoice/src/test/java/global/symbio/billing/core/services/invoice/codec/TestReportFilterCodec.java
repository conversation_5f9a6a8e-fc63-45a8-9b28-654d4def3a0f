package global.symbio.billing.core.services.invoice.codec;

import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.invoice.ReportFilterMessage;
import global.symbio.billing.core.services.invoice.dto.ReportFilter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class TestReportFilterCodec {

    private Codec<ReportFilter, ReportFilterMessage> codec;

    @BeforeEach
    public void setup() {
        codec = new ReportFilterCodec();
    }

    @ParameterizedTest
    @DisplayName("ReportFilterCodec::encode bean to protobuf")
    @MethodSource("states")
    public void codec_bean_to_protobuf(final ReportFilter bean) {
        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));
        assertEquals(bean, codec.decode(protobuf));
    }

    @ParameterizedTest
    @DisplayName("ReportFilterCodec::decode protobuf to bean")
    @MethodSource("messages")
    public void codec_protobuf_to_bean(final ReportFilterMessage message) {
        final var bean = assertDoesNotThrow(() -> codec.decode(message));
        assertEquals(message, codec.encode(bean));
    }

    private static Stream<Arguments> states() {
        return Stream.of(
            Arguments.of(ReportFilter.BILLING_PERIOD),
            Arguments.of(ReportFilter.GENERATED_DATE)
        );
    }

    private static Stream<Arguments> messages() {
        return Stream.of(
            Arguments.of(ReportFilterMessage.BILLING_PERIOD),
            Arguments.of(ReportFilterMessage.GENERATED_DATE)
        );
    }
}
