package global.symbio.billing.core.services.invoice.codec;

import com.google.common.collect.Range;
import global.symbio.billing.core.country.persistence.api.Country;
import global.symbio.billing.core.reference.persistence.api.reference.Reference;
import global.symbio.billing.core.reference.persistence.api.reference.ReferenceType;
import global.symbio.billing.core.services.codec.uuid.UUIDCodec;
import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.codec.country.CountryCodec;
import global.symbio.billing.core.services.grpc.codec.country.GRPCCountryDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.decimal.BigDecimalCodec;
import global.symbio.billing.core.services.grpc.codec.reference.GRPCReferenceDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.reference.ReferenceCodec;
import global.symbio.billing.core.services.grpc.codec.reference.ReferenceTypeCodec;
import global.symbio.billing.core.services.grpc.codec.temporal.*;
import global.symbio.billing.core.services.grpc.invoice.*;
import global.symbio.billing.core.services.grpc.message.*;
import global.symbio.billing.core.util.temporal.TemporalUtilities;
import global.symbio.billing.invoice.persistence.api.invoice.Invoice;
import global.symbio.billing.invoice.persistence.api.invoice.state.generation.GenerationState;
import global.symbio.billing.invoice.persistence.api.invoice.state.generation.Pending;
import global.symbio.billing.invoice.persistence.api.invoice.state.payment.PaymentState;
import global.symbio.billing.invoice.persistence.api.invoice.state.payment.Uninvoiced;
import global.symbio.billing.invoice.persistence.api.invoice.summary.InvoiceSummary;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.*;
import java.util.UUID;
import java.util.concurrent.ThreadLocalRandom;

import static org.junit.jupiter.api.Assertions.*;

public class TestInvoiceCodec {

    @Nonnull
    private static final UUID ID = UUID.randomUUID();

    private Codec<UUID, UUIDMessage> uuid;

    private Codec<Range<?>, RangeMessage> range;

    private Codec<BigDecimal, BigDecimalMessage> decimal;

    private Codec<PaymentState, PaymentStateMessage> payment;

    private Codec<GenerationState, GenerationStateMessage> generation;

    private Codec<LocalDate, LocalDateMessage> date;

    private Codec<LocalTime, LocalTimeMessage> time;

    private Codec<LocalDateTime, LocalDateTimeMessage> datetime;

    private Codec<ZoneOffset, ZoneOffsetMessage> offset;

    private Codec<ZoneId, ZoneIdMessage> zone;

    private Codec<ZonedDateTime, ZonedDateTimeMessage> timestamp;

    private Codec<Invoice, InvoiceMessage> codec;

    private Codec<Country, CountryMessage> country;

    private Codec<Reference, ReferenceMessage> reference;

    private Codec<InvoiceSummary, InvoiceSummaryMessage> summary;

    private Codec<ReferenceType, ReferenceTypeMessage> type;

    @BeforeEach
    public void setup() {
        uuid = new UUIDCodec();
        range = new RangeCodec();
        decimal = new BigDecimalCodec();
        payment = new PaymentStateCodec(decimal);
        date = new LocalDateCodec();
        time = new LocalTimeCodec();
        datetime = new LocalDateTimeCodec(date, time);
        offset = new ZoneOffsetCodec();
        zone = new ZoneIdCodec();
        timestamp = new ZonedDateTimeCodec(datetime, offset, zone);
        generation = new GenerationStateCodec(timestamp);
        country = new CountryCodec();
        type = new ReferenceTypeCodec();
        reference = new ReferenceCodec(uuid, type);
        summary = new InvoiceSummaryCodec(uuid, timestamp);
        codec = new InvoiceCodec(uuid, range, decimal, payment, generation, timestamp, country, reference, summary);
    }

    @Test
    @DisplayName("InvoiceCodec::new rejects null parameters")
    public void codec_rejects_null_constructor_parameters() {
        assertThrows(NullPointerException.class, () -> new InvoiceCodec(null, range, decimal, payment, generation, timestamp, country, reference, summary));
        assertThrows(NullPointerException.class, () -> new InvoiceCodec(uuid, null, decimal, payment, generation, timestamp, country, reference, summary));
        assertThrows(NullPointerException.class, () -> new InvoiceCodec(uuid, range, null, payment, generation, timestamp, country, reference, summary));
        assertThrows(NullPointerException.class, () -> new InvoiceCodec(uuid, range, decimal, null, generation, timestamp, country, reference, summary));
        assertThrows(NullPointerException.class, () -> new InvoiceCodec(uuid, range, decimal, payment, null, timestamp, country, reference, summary));
        assertThrows(NullPointerException.class, () -> new InvoiceCodec(uuid, range, decimal, payment, generation, null, country, reference, summary));
        assertThrows(NullPointerException.class, () -> new InvoiceCodec(uuid, range, decimal, payment, generation, timestamp, null, reference, summary));
        assertThrows(NullPointerException.class, () -> new InvoiceCodec(uuid, range, decimal, payment, generation, timestamp, country, null, summary));
        assertThrows(NullPointerException.class, () -> new InvoiceCodec(uuid, range, decimal, payment, generation, timestamp, country, reference, null));
    }

    @Test
    @DisplayName("InvoiceCodec::encode bean to protobuf")
    public void codec_bean_to_protobuf() {
        final var reference = new GRPCReferenceDataAccessObject(ID, 1L, "MY", "CO", 2, ReferenceType.INVOICE, "MYCO00000000012");
        final var country = new GRPCCountryDataAccessObject(458, "Malaysia", "MY");
        final var summary = new GRPCInvoiceSummaryDataAccessObject(ID, "summary", ZonedDateTime.now());
        final var bean = new GRPCInvoiceDataAccessObject(ID, UUID.randomUUID(), 1, Range.closed(1L, 10L),
            Range.closed(ZonedDateTime.now(), ZonedDateTime.now().plusDays(10)), BigDecimal.ONE, BigDecimal.TWO,
            Uninvoiced.getInstance(), Pending.getInstance(), country, summary, reference, ZonedDateTime.now().plusDays(7), ZonedDateTime.now(), ThreadLocalRandom.current().nextBoolean() ? ZonedDateTime.now() : null).entity();
        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));

        assertEquals(bean.getIdentifier(), uuid.decode(protobuf.getInvoice()));
        assertEquals(bean.getAccount(), uuid.decode(protobuf.getAccount()));
        assertEquals(bean.getNumber(), protobuf.getNumber());
        assertEquals(bean.getSequences(), range.decode(protobuf.getSequences()));

        // Some clocks support microsecond precision whereas other support nanosecond precision.
        // Ensure bounds are within one microsecond of each other, at most, losing a millionth of a second in precision.
        final var period = (Range<ZonedDateTime>) range.decode(protobuf.getPeriod());
        assertTrue(TemporalUtilities.isWithinThreshold(bean.getPeriod().lowerEndpoint(), period.lowerEndpoint(), TemporalUtilities.ONE_MICROSECOND));
        assertTrue(TemporalUtilities.isWithinThreshold(bean.getPeriod().upperEndpoint(), period.upperEndpoint(), TemporalUtilities.ONE_MICROSECOND));

        assertEquals(bean.getOpeningBalance(), decimal.decode(protobuf.getOpeningBalance()));
        assertEquals(bean.getClosingBalance(), decimal.decode(protobuf.getClosingBalance()));
        assertEquals(bean.getPayment(), payment.decode(protobuf.getPayment()));
        assertEquals(bean.getTimestamp(), timestamp.decode(protobuf.getTimestamp()));

        if (bean.getDueDate() == null) {
            assertFalse(protobuf.hasDueDate());
        } else {
            assertEquals(bean.getDueDate(), timestamp.decode(protobuf.getDueDate()));
        }

        if (bean.getReportedTimestamp() == null) {
            assertFalse(protobuf.hasReportedTimestamp());
        } else {
            assertEquals(bean.getReportedTimestamp(), timestamp.decode(protobuf.getReportedTimestamp()));
        }
        assertEquals(bean.getReference(), this.reference.decode(protobuf.getReference()));
        assertEquals(bean.getSummary(), this.summary.decode(protobuf.getSummary()));
    }

    @Test
    @DisplayName("InvoiceCodec::decode protobuf to bean")
    public void codec_protobuf_to_bean() {
        final var timestamp = ZonedDateTime.now();
        final var reported = ThreadLocalRandom.current().nextBoolean() ? ZonedDateTime.now() : null;
        final var account = UUID.randomUUID();
        final var number = 1;
        final var sequences = Range.closed(1L, 10L);
        final var period = Range.closed(timestamp, timestamp.plusDays(10L));
        final var openingBalance = BigDecimal.ONE;
        final var closingBalance = BigDecimal.TWO;
        final var payment = Uninvoiced.getInstance();
        final var generation = Pending.getInstance();
        final var reference = new GRPCReferenceDataAccessObject(ID, 1L, "MY", "CO", 2, ReferenceType.INVOICE, "MYCO00000000012");
        final var summary = new GRPCInvoiceSummaryDataAccessObject(ID, "summary", ZonedDateTime.now());

        final var builder = InvoiceMessage.newBuilder()
            .setInvoice(uuid.encode(ID))
            .setAccount(uuid.encode(account))
            .setNumber(number)
            .setSequences(range.encode(sequences))
            .setPeriod(range.encode(period))
            .setOpeningBalance(decimal.encode(openingBalance))
            .setClosingBalance(decimal.encode(closingBalance))
            .setPayment(this.payment.encode(payment))
            .setGeneration(this.generation.encode(generation))
            .setTimestamp(this.timestamp.encode(timestamp))
            .setReference(this.reference.encode(reference.entity()))
            .setSummary(this.summary.encode(summary.entity()));

        if (reported != null) {
            builder.setReportedTimestamp(this.timestamp.encode(reported));
        }

        final var protobuf = builder.build();

        final var bean = assertDoesNotThrow(() -> codec.decode(protobuf));

        assertEquals(ID, bean.getIdentifier());
        assertEquals(account, bean.getAccount());
        assertEquals(number, bean.getNumber());
        assertEquals(sequences, bean.getSequences());

        // Some clocks support microsecond precision whereas other support nanosecond precision.
        // Ensure bounds are within one microsecond of each other, at most, losing a millionth of a second in precision.
        assertTrue(TemporalUtilities.isWithinThreshold(period.lowerEndpoint(), bean.getPeriod().lowerEndpoint(), TemporalUtilities.ONE_MICROSECOND));
        assertTrue(TemporalUtilities.isWithinThreshold(period.upperEndpoint(), bean.getPeriod().upperEndpoint(), TemporalUtilities.ONE_MICROSECOND));

        assertEquals(openingBalance, bean.getOpeningBalance());
        assertEquals(closingBalance, bean.getClosingBalance());
        assertEquals(payment, bean.getPayment());
        assertEquals(timestamp.toInstant(), bean.getTimestamp().toInstant());
        assertEquals(reference.entity(), bean.getReference());
        assertEquals(summary.entity(), bean.getSummary());
        if (reported == null) {
            assertNull(bean.getReportedTimestamp());
        } else {
            assertEquals(reported.toInstant(), bean.getReportedTimestamp().toInstant());
        }
    }
}
