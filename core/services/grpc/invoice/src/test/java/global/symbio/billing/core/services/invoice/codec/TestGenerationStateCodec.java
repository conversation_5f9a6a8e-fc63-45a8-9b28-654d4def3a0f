package global.symbio.billing.core.services.invoice.codec;

import global.symbio.billing.core.exception.sdk.BillingEntityNotFoundException;
import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.codec.temporal.*;
import global.symbio.billing.core.services.grpc.invoice.GenerationStateMessage;
import global.symbio.billing.core.services.grpc.message.*;
import global.symbio.billing.invoice.persistence.api.invoice.state.generation.Failure;
import global.symbio.billing.invoice.persistence.api.invoice.state.generation.GenerationState;
import global.symbio.billing.invoice.persistence.api.invoice.state.generation.Pending;
import global.symbio.billing.invoice.persistence.api.invoice.state.generation.Success;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.time.*;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;

public class TestGenerationStateCodec {

    private Codec<LocalDate, LocalDateMessage> date;

    private Codec<LocalTime, LocalTimeMessage> time;

    private Codec<LocalDateTime, LocalDateTimeMessage> datetime;

    private Codec<ZoneOffset, ZoneOffsetMessage> offset;

    private Codec<ZoneId, ZoneIdMessage> zone;

    private Codec<ZonedDateTime, ZonedDateTimeMessage> timestamp;
    private Codec<GenerationState, GenerationStateMessage> codec;

    @BeforeEach
    public void setup() {
        date = new LocalDateCodec();
        time = new LocalTimeCodec();
        datetime = new LocalDateTimeCodec(date, time);
        offset = new ZoneOffsetCodec();
        zone = new ZoneIdCodec();
        timestamp = new ZonedDateTimeCodec(datetime, offset, zone);
        codec = new GenerationStateCodec(timestamp);
    }

    @ParameterizedTest
    @DisplayName("GenerationStateCodec::encode bean to protobuf")
    @MethodSource("states")
    public void codec_bean_to_protobuf(final GenerationState bean) {
        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));
        assertEquals(bean.getState(), protobuf.getState());

        if (bean instanceof Failure failure) {
            assertEquals(failure.getReason(), protobuf.getReason());
            assertEquals(failure.getTimestamp(), timestamp.decode(protobuf.getTimestamp()));
        } else if (bean instanceof Success success) {
            assertEquals(success.getTimestamp(), timestamp.decode(protobuf.getTimestamp()));
        }
    }

    @ParameterizedTest
    @DisplayName("GenerationStateCodec::decode protobuf to bean")
    @MethodSource("states")
    public void codec_protobuf_to_bean(final GenerationState state) {
        final var protobuf = GenerationStateMessage.newBuilder().setState(state.getState());
        String reason = null;
        ZonedDateTime timestamp = null;
        if (state instanceof Failure failure) {
            reason = failure.getReason();
            timestamp = failure.getTimestamp();
            protobuf.setReason(reason);
            protobuf.setTimestamp(this.timestamp.encode(timestamp));
        } else if (state instanceof Success success) {
            timestamp = success.getTimestamp();
            protobuf.setTimestamp(this.timestamp.encode(timestamp));
        }

        final var bean = assertDoesNotThrow(() -> codec.decode(protobuf.build()));

        assertEquals(state.getState(), bean.getState());
        if (bean instanceof Failure failure) {
            assertEquals(reason, failure.getReason());
            assertEquals(timestamp, failure.getTimestamp());
        } else if (bean instanceof Success success) {
            assertEquals(timestamp, success.getTimestamp());
        }
        assertInstanceOf(state.getClass(), bean);
    }

    @Test
    @DisplayName("GenerationStateCodec::decode protobuf to bean with unknown payment type throws BillingEntityNotFoundException")
    public void codec_protobuf_to_bean_unknown_type_throws_billing_entity_not_found_exception() {
        final var protobuf = GenerationStateMessage.newBuilder().setState("UNKNOWN").build();
        final var cause = assertThrows(BillingEntityNotFoundException.class, () -> codec.decode(protobuf));
        assertEquals("Entity of type GenerationState with identifier UNKNOWN could not be found.", cause.getMessage());
    }

    private static Stream<Arguments> states() {
        return Stream.of(
            Arguments.of(new Pending()),
            Arguments.of(new Failure("reason", ZonedDateTime.now())),
            Arguments.of(new Success(ZonedDateTime.now()))
        );
    }
}
