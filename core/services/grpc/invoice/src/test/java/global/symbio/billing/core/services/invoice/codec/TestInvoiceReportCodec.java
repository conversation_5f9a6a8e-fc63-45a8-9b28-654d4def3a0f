package global.symbio.billing.core.services.invoice.codec;

import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.invoice.InvoiceReportMessage;
import global.symbio.billing.core.services.invoice.dto.InvoiceReport;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class TestInvoiceReportCodec {

    private Codec<InvoiceReport, InvoiceReportMessage> codec;

    @BeforeEach
    public void setup() {
        codec = new InvoiceReportCodec();
    }

    @Test
    @DisplayName("InvoiceReportCodec::encode bean to protobuf")
    public void codec_bean_to_protobuf() {
        final var bean = new InvoiceReport(
            "MY",
            "Mar-2024",
            "1234567",
            "Account Name",
            "MYCO00000000867",
            "Yes",
            "Yes",
            "Delivered"
        );
        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));

        assertEquals(bean.country(), protobuf.getCountry());
        assertEquals(bean.billingPeriod(), protobuf.getPeriod());
        assertEquals(bean.accountNumber(), protobuf.getAccountNumber());
        assertEquals(bean.accountName(), protobuf.getAccountName());
        assertEquals(bean.invoiceReference(), protobuf.getReference());
        assertEquals(bean.invoiceGenerated(), protobuf.getInvoiceGenerated());
        assertEquals(bean.invoiceEmailed(), protobuf.getInvoiceEmailed());
        assertEquals(bean.deliveryStatus(), protobuf.getDeliveryStatus());

    }

    @Test
    @DisplayName("InvoiceReportCodec::decode protobuf to bean")
    public void codec_protobuf_to_bean() {
        final var report = new InvoiceReport(
            "MY",
            "Mar-2024",
            "1234567",
            "Account Name",
            "MYCO00000000867",
            "Yes",
            "Yes",
            "Delivered"
        );
        final var protobuf = InvoiceReportMessage.newBuilder()
            .setCountry(report.country())
            .setPeriod(report.billingPeriod())
            .setAccountNumber(report.accountNumber())
            .setAccountName(report.accountName())
            .setReference(report.invoiceReference())
            .setInvoiceGenerated(report.invoiceGenerated())
            .setInvoiceEmailed(report.invoiceEmailed())
            .setDeliveryStatus(report.deliveryStatus())
            .build();

        final var bean = assertDoesNotThrow(() -> codec.decode(protobuf));
        assertEquals(report, bean);
    }
}
