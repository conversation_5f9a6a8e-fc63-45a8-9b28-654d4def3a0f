package global.symbio.billing.core.services.invoice.codec;

import global.symbio.billing.core.exception.sdk.BillingEntityNotFoundException;
import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.codec.decimal.BigDecimalCodec;
import global.symbio.billing.core.services.grpc.invoice.PaymentStateMessage;
import global.symbio.billing.core.services.grpc.message.BigDecimalMessage;
import global.symbio.billing.invoice.persistence.api.invoice.state.payment.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.math.BigDecimal;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;

public class TestPaymentStateCodec {

    private Codec<BigDecimal, BigDecimalMessage> decimal;

    private Codec<PaymentState, PaymentStateMessage> codec;

    @BeforeEach
    public void setup() {
        decimal = new BigDecimalCodec();
        codec = new PaymentStateCodec(decimal);
    }

    @ParameterizedTest
    @DisplayName("PaymentStateCodec::encode bean to protobuf")
    @MethodSource("states")
    public void codec_bean_to_protobuf(final PaymentState bean) {
        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));
        assertEquals(bean.getState(), protobuf.getState());
        assertEquals(bean.getTotal(), decimal.decode(protobuf.getTotal()));
        assertEquals(bean.getPaid(), decimal.decode(protobuf.getPaid()));
        assertEquals(bean.getDue(), decimal.decode(protobuf.getDue()));
    }

    @ParameterizedTest
    @DisplayName("PaymentStateCodec::decode protobuf to bean")
    @MethodSource("states")
    public void codec_protobuf_to_bean(final PaymentState state) {
        final var protobuf = PaymentStateMessage.newBuilder()
            .setState(state.getState())
            .setTotal(decimal.encode(state.getTotal()))
            .setPaid(decimal.encode(state.getPaid()))
            .setDue(decimal.encode(state.getDue()))
            .build();
        final var bean = assertDoesNotThrow(() -> codec.decode(protobuf));

        assertEquals(state.getState(), bean.getState());
        assertEquals(state.getTotal(), bean.getTotal());
        assertEquals(state.getPaid(), bean.getPaid());
        assertEquals(state.getDue(), bean.getDue());
        assertInstanceOf(state.getClass(), bean);
    }

    @Test
    @DisplayName("PaymentStateCodec::decode protobuf to bean with unknown payment type throws IllegalStateException")
    public void codec_protobuf_to_bean_unknown_type_throws_illegal_state_exception() {
        final var protobuf = PaymentStateMessage.newBuilder()
            .setState("UNKNOWN")
            .setTotal(decimal.encode(BigDecimal.TEN))
            .setDue(decimal.encode(BigDecimal.TEN))
            .setPaid(decimal.encode(BigDecimal.ZERO))
            .build();
        final var cause = assertThrows(BillingEntityNotFoundException.class, () -> codec.decode(protobuf));
        assertEquals("Entity of type PaymentState with identifier UNKNOWN could not be found.", cause.getMessage());
    }

    private static Stream<Arguments> states() {
        final var total = new BigDecimal(ThreadLocalRandom.current().nextInt());
        final var paid = new BigDecimal(ThreadLocalRandom.current().nextInt());
        final var due = new BigDecimal(ThreadLocalRandom.current().nextInt());
        return Stream.of(
            Arguments.of(new Uninvoiced()),
            Arguments.of(new Unpaid(total, due)),
            Arguments.of(new PartiallyPaid(total, paid, due)),
            Arguments.of(new Paid(total, paid))
        );
    }
}