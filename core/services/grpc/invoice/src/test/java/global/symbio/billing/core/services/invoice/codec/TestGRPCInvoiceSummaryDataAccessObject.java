package global.symbio.billing.core.services.invoice.codec;

import global.symbio.billing.core.exception.sdk.BillingEntityUnmodifiableException;
import global.symbio.billing.invoice.persistence.api.invoice.summary.InvoiceSummary;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.ZonedDateTime;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

public class TestGRPCInvoiceSummaryDataAccessObject {

    private static final UUID ID = UUID.randomUUID();
    private static final String SUMMARY = "summary";
    private static final ZonedDateTime TIMESTAMP = ZonedDateTime.now();

    private GRPCInvoiceSummaryDataAccessObject summary;

    @BeforeEach
    public void setup() {
        summary = new GRPCInvoiceSummaryDataAccessObject(ID, SUMMARY, TIMESTAMP);
    }

    @Test
    @DisplayName("GRPCInvoiceDataAccessObject::new rejects null constructor arguments")
    public void grpc_invoice_data_access_object_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new GRPCInvoiceSummaryDataAccessObject(null, SUMMARY, TIMESTAMP));
        assertThrows(NullPointerException.class, () -> new GRPCInvoiceSummaryDataAccessObject(ID, null, TIMESTAMP));
        assertThrows(NullPointerException.class, () -> new GRPCInvoiceSummaryDataAccessObject(ID, SUMMARY, null));
    }

    @Test
    @DisplayName("GRPCInvoiceSummaryDataAccessObject::identifier is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_invoice_summary_data_access_object_identifier_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(InvoiceSummary.class, ID, "identifier");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> summary.identifier(ID));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCInvoiceSummaryDataAccessObject::summary is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_invoice_summary_data_access_object_summary_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(InvoiceSummary.class, ID, "summary");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> summary.summary(SUMMARY));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCInvoiceSummaryDataAccessObject::timestamp is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_invoice_summary_data_access_object_timestamp_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(InvoiceSummary.class, ID, "timestamp");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> summary.timestamp(TIMESTAMP));
        assertEquals(expected.getMessage(), cause.getMessage());
    }
}
