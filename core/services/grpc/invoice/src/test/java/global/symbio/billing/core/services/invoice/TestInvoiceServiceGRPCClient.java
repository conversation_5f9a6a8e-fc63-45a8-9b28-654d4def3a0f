package global.symbio.billing.core.services.invoice;

import com.fasterxml.uuid.impl.UUIDUtil;
import com.google.common.collect.Range;
import com.google.common.util.concurrent.Futures;
import global.symbio.billing.core.country.persistence.api.Country;
import global.symbio.billing.core.payment.persistence.api.payment.method.PaymentMethod;
import global.symbio.billing.core.reference.persistence.api.reference.Reference;
import global.symbio.billing.core.reference.persistence.api.reference.ReferenceType;
import global.symbio.billing.core.services.codec.uuid.UUIDCodec;
import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.codec.country.CountryCodec;
import global.symbio.billing.core.services.grpc.codec.country.GRPCCountryDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.decimal.BigDecimalCodec;
import global.symbio.billing.core.services.grpc.codec.pagination.PageableCodec;
import global.symbio.billing.core.services.grpc.codec.pagination.SortCodec;
import global.symbio.billing.core.services.grpc.codec.pagination.SortDirectionCodec;
import global.symbio.billing.core.services.grpc.codec.pagination.SortOrderCodec;
import global.symbio.billing.core.services.grpc.codec.payment.method.GRPCPaymentMethodDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.payment.method.PaymentMethodCodec;
import global.symbio.billing.core.services.grpc.codec.reference.GRPCReferenceDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.reference.ReferenceCodec;
import global.symbio.billing.core.services.grpc.codec.reference.ReferenceTypeCodec;
import global.symbio.billing.core.services.grpc.codec.temporal.*;
import global.symbio.billing.core.services.grpc.invoice.*;
import global.symbio.billing.core.services.grpc.message.*;
import global.symbio.billing.core.services.invoice.codec.*;
import global.symbio.billing.core.services.invoice.dto.InvoiceReport;
import global.symbio.billing.core.services.invoice.dto.ReportFilter;
import global.symbio.billing.invoice.persistence.api.invoice.Invoice;
import global.symbio.billing.invoice.persistence.api.invoice.state.generation.GenerationState;
import global.symbio.billing.invoice.persistence.api.invoice.state.generation.Pending;
import global.symbio.billing.invoice.persistence.api.invoice.state.payment.*;
import global.symbio.billing.invoice.persistence.api.invoice.summary.InvoiceSummary;
import io.micronaut.data.model.Page;
import io.micronaut.data.model.Pageable;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.*;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestInvoiceServiceGRPCClient {

    @Nonnull
    private static final UUID ID = UUIDUtil.nilUUID();

    @Mock
    private InvoiceGrpc.InvoiceFutureStub invoices;

    private Codec<UUID, UUIDMessage> uuid;

    private Codec<Invoice, InvoiceMessage> invoice;

    private Codec<Range<?>, RangeMessage> range;

    private Codec<BigDecimal, BigDecimalMessage> decimal;

    private Codec<PaymentState, PaymentStateMessage> state;

    private Codec<PaymentMethod, PaymentMethodMessage> method;

    private Codec<GenerationState, GenerationStateMessage> generation;

    private Codec<LocalDate, LocalDateMessage> date;

    private Codec<LocalTime, LocalTimeMessage> time;

    private Codec<LocalDateTime, LocalDateTimeMessage> datetime;

    private Codec<ZoneOffset, ZoneOffsetMessage> offset;

    private Codec<ZoneId, ZoneIdMessage> zone;

    private Codec<ZonedDateTime, ZonedDateTimeMessage> timestamp;

    private Codec<Country, CountryMessage> country;

    private Codec<Reference, ReferenceMessage> reference;

    private Codec<ReferenceType, ReferenceTypeMessage> referenceType;

    private Codec<InvoiceSummary, InvoiceSummaryMessage> summary;

    private Codec<YearMonth, YearMonthMessage> yearMonth;

    private Codec<InvoiceReport, InvoiceReportMessage> invoiceReport;

    private Codec<ReportFilter, ReportFilterMessage> filter;

    private Codec<Pageable, PaginationParametersMessage> pagination;

    private InvoiceServiceGRPCClient client;

    @BeforeEach
    public void setup() {
        uuid = new UUIDCodec();
        range = new RangeCodec();
        decimal = new BigDecimalCodec();
        state = new PaymentStateCodec(decimal);
        date = new LocalDateCodec();
        time = new LocalTimeCodec();
        datetime = new LocalDateTimeCodec(date, time);
        offset = new ZoneOffsetCodec();
        zone = new ZoneIdCodec();
        timestamp = new ZonedDateTimeCodec(datetime, offset, zone);
        method = new PaymentMethodCodec();
        generation = new GenerationStateCodec(timestamp);
        country = new CountryCodec();
        referenceType = new ReferenceTypeCodec();
        reference = new ReferenceCodec(uuid, referenceType);
        summary = new InvoiceSummaryCodec(uuid, timestamp);
        yearMonth = new YearMonthCodec();
        invoiceReport = new InvoiceReportCodec();
        filter = new ReportFilterCodec();
        invoice = new InvoiceCodec(uuid, range, decimal, state, generation, timestamp, country, reference, summary);
        pagination = new PageableCodec(new SortCodec(new SortOrderCodec(new SortDirectionCodec())));
        client = new InvoiceServiceGRPCClient(invoices, uuid, invoice, method, timestamp, yearMonth, invoiceReport, filter, pagination);
    }

    @Test
    @DisplayName("InvoiceServiceGRPCClient::new rejects null constructor arguments")
    public void invoice_service_new_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new InvoiceServiceGRPCClient(null, uuid, invoice, method, timestamp, yearMonth, invoiceReport, filter, pagination));
        assertThrows(NullPointerException.class, () -> new InvoiceServiceGRPCClient(invoices, null, invoice, method, timestamp, yearMonth, invoiceReport, filter, pagination));
        assertThrows(NullPointerException.class, () -> new InvoiceServiceGRPCClient(invoices, uuid, null, method, timestamp, yearMonth, invoiceReport, filter, pagination));
        assertThrows(NullPointerException.class, () -> new InvoiceServiceGRPCClient(invoices, uuid, invoice, null, timestamp, yearMonth, invoiceReport, filter, pagination));
        assertThrows(NullPointerException.class, () -> new InvoiceServiceGRPCClient(invoices, uuid, invoice, method, null, yearMonth, invoiceReport, filter, pagination));
        assertThrows(NullPointerException.class, () -> new InvoiceServiceGRPCClient(invoices, uuid, invoice, method, timestamp, null, invoiceReport, filter, pagination));
        assertThrows(NullPointerException.class, () -> new InvoiceServiceGRPCClient(invoices, uuid, invoice, method, timestamp, yearMonth, null, filter, pagination));
        assertThrows(NullPointerException.class, () -> new InvoiceServiceGRPCClient(invoices, uuid, invoice, method, timestamp, yearMonth, invoiceReport, null, pagination));
        assertThrows(NullPointerException.class, () -> new InvoiceServiceGRPCClient(invoices, uuid, invoice, method, timestamp, yearMonth, invoiceReport, filter, null));
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @DisplayName("InvoiceServiceGRPCClient::getInvoice invokes gRPC service and retrieves Invoice")
    public void invoice_service_grpc_client_get_invoice(final boolean present) {
        final var expected = mock(GetInvoiceResponse.class);
        final var country = new GRPCCountryDataAccessObject(458, "Malaysia", "MY");
        final var reference = new GRPCReferenceDataAccessObject(UUID.randomUUID(), 1L, "MY", "CO", 2, ReferenceType.INVOICE, "MYCO00000000012");
        final var summary = new GRPCInvoiceSummaryDataAccessObject(ID, "summary", ZonedDateTime.now());
        final var invoice = new GRPCInvoiceDataAccessObject(ID, UUID.randomUUID(), 1, Range.closed(1L, 10L),
            Range.closed(ZonedDateTime.now(), ZonedDateTime.now().plusDays(10)), BigDecimal.ONE, BigDecimal.TWO,
            Uninvoiced.getInstance(), Pending.getInstance(), country, summary, reference, ZonedDateTime.now().plusDays(7), ZonedDateTime.now(), ThreadLocalRandom.current().nextBoolean() ? ZonedDateTime.now() : null).entity();

        final var protobuf = this.invoice.encode(invoice);

        when(invoices.getInvoice(any(GetInvoiceRequest.class))).thenReturn(Futures.immediateFuture(expected));
        when(expected.hasInvoice()).thenReturn(present);
        if (present) {
            when(expected.getInvoice()).thenReturn(protobuf);
        }

        final var result = assertDoesNotThrow(() -> client.getInvoice(ID));
        verify(expected, times(1)).hasInvoice();
        verify(expected, present ? times(1) : never()).getInvoice();
        assertEquals(result.isPresent(), present);
        result.ifPresent(bean -> assertEquals(this.invoice.decode(protobuf), bean));
    }

    @ParameterizedTest
    @MethodSource("arguments")
    @DisplayName("InvoiceServiceGRPCClient::getInvoices invokes gRPC service and retrieves Invoices")
    public void invoice_service_grpc_client_get_invoices(@Nullable Set<String> states) {
        final var expected = mock(GetInvoicesResponse.class);
        final var country = new GRPCCountryDataAccessObject(458, "Malaysia", "MY");
        final var reference = new GRPCReferenceDataAccessObject(UUID.randomUUID(), 1L, "MY", "CO", 2, ReferenceType.INVOICE, "MYCO00000000012");
        final var summary = new GRPCInvoiceSummaryDataAccessObject(ID, "summary", ZonedDateTime.now());
        final var invoice = new GRPCInvoiceDataAccessObject(ID, UUID.randomUUID(), 1, Range.closed(1L, 10L),
            Range.closed(ZonedDateTime.now(), ZonedDateTime.now().plusDays(10)), BigDecimal.ONE, BigDecimal.TWO,
            Uninvoiced.getInstance(), Pending.getInstance(), country, summary, reference, ZonedDateTime.now().plusDays(7), ZonedDateTime.now(), ThreadLocalRandom.current().nextBoolean() ? ZonedDateTime.now() : null).entity();

        final var pagination = this.pagination.encode(Pageable.unpaged());
        final var protobuf = this.invoice.encode(invoice);

        when(invoices.getInvoices(any(GetInvoicesRequest.class))).thenReturn(Futures.immediateFuture(expected));
        when(expected.getInvoicesList()).thenReturn(List.of(protobuf));
        when(expected.getPagination()).thenReturn(pagination);

        final var result = client.getInvoices(ID, states, ThreadLocalRandom.current().nextBoolean() ? null : "reference", ZonedDateTime.now(), ZonedDateTime.now(), Pageable.unpaged()); //TODO: different permutations of states OR states == null
        verify(expected, times(1)).getInvoicesList();
        assertEquals(Page.of(List.of(this.invoice.decode(protobuf)), Pageable.unpaged(), 0L), result);
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @DisplayName("InvoiceServiceGRPCClient::getPaymentMethod invokes gRPC service and retrieves PaymentMethod if present")
    public void invoice_service_grpc_client_get_payment_method(final boolean present) {
        final var response = mock(GetPaymentMethodResponse.class);
        final var entity = new GRPCPaymentMethodDataAccessObject(1, "BANK_TRANSFER", "Bank Transfer").entity();
        when(response.hasMethod()).thenReturn(present);
        if (present) {
            when(response.getMethod()).thenReturn(this.method.encode(entity));
        }
        when(invoices.getPaymentMethod(any(GetPaymentMethodRequest.class))).thenReturn(Futures.immediateFuture(response));
        final var method = assertDoesNotThrow(() -> client.getPaymentMethod(1));
        verify(response, times(1)).hasMethod();
        verify(response, present ? times(1) : never()).getMethod();
        assertEquals(present, method.isPresent());
        method.ifPresent(bean -> assertEquals(entity, bean));
    }

    @Test
    @DisplayName("InvoiceServiceGRPCClient::getPaymentMethods invoices gRPC service and retrieves collection of PaymentMethods")
    public void invoice_service_grpc_client_get_payment_methods() {
        final var response = mock(GetPaymentMethodsResponse.class);
        final var method = new GRPCPaymentMethodDataAccessObject(1, "BANK_TRANSFER", "Bank Transfer").entity();
        when(response.getMethodsList()).thenReturn(List.of(this.method.encode(method)));
        when(invoices.getPaymentMethods(any(GetPaymentMethodsRequest.class))).thenReturn(Futures.immediateFuture(response));
        final var methods = assertDoesNotThrow(() -> client.getPaymentMethods());
        verify(response, times(1)).getMethodsList();
        assertEquals(List.of(method), methods);
    }

    @Test
    @DisplayName("InvoiceServiceGRPCClient::getInvoiceReport invoices gRPC service and retrieves collection of InvoiceReports")
    public void invoice_service_grpc_client_get_invoice_report() {
        final var response = mock(GetInvoiceReportResponse.class);
        final var report = new InvoiceReport(
            "MY",
            "Mar-2024",
            "1234567",
            "Account Name",
            "MYCO00000000867",
            "Yes",
            "Yes",
            "Delivered"
        );
        when(response.getInvoicesList()).thenReturn(List.of(this.invoiceReport.encode(report)));
        when(invoices.getInvoiceReport(any(GetInvoiceReportRequest.class))).thenReturn(Futures.immediateFuture(response));
        final var reports = assertDoesNotThrow(() -> client.getInvoiceReport("MY", YearMonth.of(2024, 3), ReportFilter.BILLING_PERIOD));
        verify(response, times(1)).getInvoicesList();
        assertEquals(List.of(report), reports);
    }

    private static Stream<Arguments> arguments() {
        return Stream.of(
            Arguments.of(Set.of(Uninvoiced.TYPE)),
            Arguments.of(Set.of(Paid.TYPE)),
            Arguments.of(Set.of(Unpaid.TYPE, PartiallyPaid.TYPE)),
            null
        );
    }
}