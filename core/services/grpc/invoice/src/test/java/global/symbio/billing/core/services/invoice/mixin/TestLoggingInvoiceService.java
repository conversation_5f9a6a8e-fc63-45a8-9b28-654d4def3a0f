package global.symbio.billing.core.services.invoice.mixin;

import com.fasterxml.uuid.impl.UUIDUtil;
import global.symbio.billing.core.payment.persistence.api.payment.method.PaymentMethod;
import global.symbio.billing.core.services.invoice.InvoiceService;
import global.symbio.billing.core.services.invoice.dto.InvoiceReport;
import global.symbio.billing.core.services.invoice.dto.ReportFilter;
import global.symbio.billing.invoice.persistence.api.invoice.Invoice;
import io.micronaut.data.model.Page;
import io.micronaut.data.model.Pageable;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;

import java.time.YearMonth;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.anySet;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestLoggingInvoiceService {

    @Mock
    private Logger log;

    @Mock
    private InvoiceService delegate;

    private LoggingInvoiceService service;

    @BeforeEach
    public void setup() {
        service = new LoggingInvoiceService(log, delegate);
    }

    @Test
    @DisplayName("LoggingInvoiceService::new rejects null constructor arguments")
    public void logging_invoice_service_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new LoggingInvoiceService(null));
        assertThrows(NullPointerException.class, () -> new LoggingInvoiceService(log, null));
        assertThrows(NullPointerException.class, () -> new LoggingInvoiceService(null, delegate));
    }

    @Test
    @DisplayName("LoggingInvoiceService::getInvoice logs and invokes method on delegate")
    public void logging_invoice_service_get_invoice_logs_and_invokes_delegate_method() {
        final var invoice = UUIDUtil.nilUUID();
        final var response = Optional.of(mock(Invoice.class));
        when(delegate.getInvoice(any(UUID.class))).thenReturn(response);
        final var result = assertDoesNotThrow(() -> service.getInvoice(invoice));
        assertSame(response, result);
        verify(log, times(1)).info(anyString(), eq(invoice));
        verify(delegate, times(1)).getInvoice(eq(invoice));
    }

    @Test
    @DisplayName("LoggingInvoiceService::getInvoices logs and invokes method on delegate")
    public void logging_invoice_service_get_invoices_logs_and_invokes_delegate_method() {
        final var account = UUIDUtil.nilUUID();
        final var states = Set.of("a", "b", "c");
        final var reference = "ref";
        final var timestamp = ZonedDateTime.now();
        final var pagination = Pageable.unpaged();
        final var response = Page.of(List.of(mock(Invoice.class)), Pageable.unpaged(), 1L);
        when(delegate.getInvoices(any(UUID.class), anySet(), anyString(), any(ZonedDateTime.class), any(ZonedDateTime.class), any(Pageable.class))).thenReturn(response);
        final var result = assertDoesNotThrow(() -> service.getInvoices(account, states, reference, timestamp, timestamp, pagination));
        assertSame(response, result);
        verify(log, times(1)).info(anyString(), eq(account), eq(states), eq(reference), eq(timestamp), eq(timestamp), eq(pagination));
        verify(delegate, times(1)).getInvoices(eq(account), eq(states), eq(reference), eq(timestamp), eq(timestamp), eq(pagination));
    }

    @Test
    @DisplayName("LoggingInvoiceService::getPaymentMethod logs and invokes method on delegate")
    public void logging_invoice_service_get_payment_method_logs_and_invokes_delegate_method() {
        final var method = 123;
        final var response = Optional.of(mock(PaymentMethod.class));
        when(delegate.getPaymentMethod(anyInt())).thenReturn(response);
        final var result = assertDoesNotThrow(() -> service.getPaymentMethod(method));
        assertSame(response, result);
        verify(log, times(1)).info(anyString(), eq(method));
        verify(delegate, times(1)).getPaymentMethod(eq(method));
    }

    @Test
    @DisplayName("LoggingInvoiceService::getPaymentMethods logs and invokes method on delegate")
    public void logging_invoice_service_get_payment_methods_logs_and_invokes_delegate_method() {
        final var response = Set.of(mock(PaymentMethod.class));
        when(delegate.getPaymentMethods()).thenReturn(response);
        final var result = assertDoesNotThrow(() -> service.getPaymentMethods());
        assertSame(response, result);
        verify(log, times(1)).info(anyString());
        verify(delegate, times(1)).getPaymentMethods();
    }

    @Test
    @DisplayName("LoggingInvoiceService::getInvoiceReport logs and invokes method on delegate")
    public void logging_invoice_service_get_invoice_report_and_invokes_delegate_method() {
        final var country = "MY";
        final var yearMonth = YearMonth.of(2024,3);
        final var response = Set.of(mock(InvoiceReport.class));
        final var filter = ReportFilter.BILLING_PERIOD;
        when(delegate.getInvoiceReport(anyString(), any(YearMonth.class), any(ReportFilter.class))).thenReturn(response);
        final var result = assertDoesNotThrow(() -> service.getInvoiceReport(country, yearMonth, filter));
        assertSame(response, result);
        verify(log, times(1)).info(anyString(), eq(country), eq(yearMonth), eq(filter));
        verify(delegate, times(1)).getInvoiceReport(anyString(), any(YearMonth.class), any(ReportFilter.class));
    }
}