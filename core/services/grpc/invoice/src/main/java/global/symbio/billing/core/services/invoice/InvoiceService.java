package global.symbio.billing.core.services.invoice;

import global.symbio.billing.core.payment.persistence.api.payment.method.PaymentMethod;
import global.symbio.billing.core.services.invoice.dto.InvoiceReport;
import global.symbio.billing.core.services.invoice.dto.ReportFilter;
import global.symbio.billing.invoice.persistence.api.invoice.Invoice;
import io.micronaut.data.model.Page;
import io.micronaut.data.model.Pageable;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.time.YearMonth;
import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

public interface InvoiceService {

    @Nonnull
    Optional<Invoice> getInvoice(@Nonnull UUID identifier);

    @Nonnull
    Page<Invoice> getInvoices(@Nonnull UUID account, @Nullable Set<String> states, @Nullable String reference, @Nullable ZonedDateTime start, @Nullable ZonedDateTime end, @Nonnull Pageable pageable);

    @Nonnull
    Optional<PaymentMethod> getPaymentMethod(@Nonnull Integer identifier);

    @Nonnull
    Collection<PaymentMethod> getPaymentMethods();

    @Nonnull
    Collection<InvoiceReport> getInvoiceReport(@Nonnull String country, @Nonnull YearMonth period, @Nonnull ReportFilter filter);
}