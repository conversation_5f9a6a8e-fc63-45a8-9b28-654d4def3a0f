package global.symbio.billing.core.services.invoice.dto;

import io.micronaut.core.annotation.Introspected;
import jakarta.annotation.Nonnull;

import java.util.Objects;

@Introspected
public record InvoiceReport(
    @Nonnull String country,
    @Nonnull String billingPeriod,
    @Nonnull String accountNumber,
    @Nonnull String accountName,
    @Nonnull String invoiceReference,
    @Nonnull String invoiceGenerated,
    @Nonnull String invoiceEmailed,
    @Nonnull String deliveryStatus
) {

    public InvoiceReport {
        Objects.requireNonNull(country, "country");
        Objects.requireNonNull(billingPeriod, "billingPeriod");
        Objects.requireNonNull(accountNumber, "accountNumber");
        Objects.requireNonNull(accountName, "accountName");
        Objects.requireNonNull(invoiceReference, "invoiceReference");
        Objects.requireNonNull(invoiceGenerated, "invoiceGenerated");
        Objects.requireNonNull(invoiceEmailed, "invoiceEmailed");
        Objects.requireNonNull(deliveryStatus, "deliveryStatus");
    }
}
