package global.symbio.billing.core.services.invoice.codec;

import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.invoice.InvoiceReportMessage;
import global.symbio.billing.core.services.invoice.dto.InvoiceReport;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

@Singleton
public class InvoiceReportCodec implements GRPCCodec<InvoiceReport, InvoiceReportMessage> {

    @Nonnull
    @Override
    public InvoiceReportMessage encode(@Nonnull InvoiceReport bean) {
        final var report = InvoiceReportMessage.newBuilder()
            .setCountry(bean.country())
            .setPeriod(bean.billingPeriod())
            .setAccountNumber(bean.accountNumber())
            .setAccountName(bean.accountName())
            .setReference(bean.invoiceReference())
            .setInvoiceGenerated(bean.invoiceGenerated())
            .setInvoiceEmailed(bean.invoiceEmailed())
            .setDeliveryStatus(bean.deliveryStatus());
        return report.build();
    }

    @Nonnull
    @Override
    public InvoiceReport decode(@Nonnull InvoiceReportMessage protobuf) {
        return new InvoiceReport(
            protobuf.getCountry(),
            protobuf.getPeriod(),
            protobuf.getAccountNumber(),
            protobuf.getAccountName(),
            protobuf.getReference(),
            protobuf.getInvoiceGenerated(),
            protobuf.getInvoiceEmailed(),
            protobuf.getDeliveryStatus()
        );
    }
}
