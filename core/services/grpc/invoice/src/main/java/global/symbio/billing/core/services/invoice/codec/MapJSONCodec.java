package global.symbio.billing.core.services.invoice.codec;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.util.Map;

@Singleton
public class MapJSONCodec extends JSONCodec<Map> {

    @Inject
    public MapJSONCodec(@Nonnull ObjectMapper mapper) {
        super(Map.class, mapper);
    }
}