package global.symbio.billing.core.services.invoice.codec;

import com.google.common.collect.Range;
import global.symbio.billing.core.country.persistence.api.CountryDataAccessObject;
import global.symbio.billing.core.exception.sdk.BillingEntityUnmodifiableException;
import global.symbio.billing.core.reference.persistence.api.reference.ReferenceDataAccessObject;
import global.symbio.billing.invoice.persistence.api.invoice.Invoice;
import global.symbio.billing.invoice.persistence.api.invoice.InvoiceDataAccessObject;
import global.symbio.billing.invoice.persistence.api.invoice.state.generation.GenerationState;
import global.symbio.billing.invoice.persistence.api.invoice.state.payment.PaymentState;
import global.symbio.billing.invoice.persistence.api.invoice.summary.InvoiceSummaryDataAccessObject;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.Getter;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Objects;
import java.util.UUID;

@Getter
public final class GRPCInvoiceDataAccessObject extends InvoiceDataAccessObject {

    @Nonnull
    private final UUID identifier, account;

    @Nonnull
    private final Integer number;

    @Nonnull
    private final Range<Long> sequences;

    @Nonnull
    private final Range<ZonedDateTime> period;

    @Nonnull
    private final BigDecimal openingBalance;

    @Nonnull
    private final BigDecimal closingBalance;

    @Nonnull
    private final PaymentState payment;

    @Nonnull
    private final GenerationState generation;

    @Nonnull
    private final CountryDataAccessObject country;

    @Nullable
    private final InvoiceSummaryDataAccessObject summary;

    @Nullable
    private final ReferenceDataAccessObject reference;

    @Nullable
    private final ZonedDateTime dueDate;

    @Nonnull
    private final ZonedDateTime timestamp;

    @Nullable
    private final ZonedDateTime reportedTimestamp;

    public GRPCInvoiceDataAccessObject(
        @Nonnull UUID identifier,
        @Nonnull UUID account,
        @Nonnull Integer number,
        @Nonnull Range<Long> sequences,
        @Nonnull Range<ZonedDateTime> period,
        @Nonnull BigDecimal openingBalance,
        @Nonnull BigDecimal closingBalance,
        @Nonnull PaymentState payment,
        @Nonnull GenerationState generation,
        @Nonnull CountryDataAccessObject country,
        @Nullable InvoiceSummaryDataAccessObject summary,
        @Nullable ReferenceDataAccessObject reference,
        @Nullable ZonedDateTime dueDate,
        @Nonnull ZonedDateTime timestamp,
        @Nullable ZonedDateTime reportedTimestamp
    ) {
        this.identifier = Objects.requireNonNull(identifier, "identifier");
        this.account = Objects.requireNonNull(account, "account");
        this.number = Objects.requireNonNull(number, "number");
        this.sequences = Objects.requireNonNull(sequences, "sequences");
        this.period = Objects.requireNonNull(period, "period");
        this.openingBalance = Objects.requireNonNull(openingBalance, "openingBalance");
        this.closingBalance = Objects.requireNonNull(closingBalance, "closingBalance");
        this.payment = Objects.requireNonNull(payment, "payment");
        this.generation = Objects.requireNonNull(generation, "generation");
        this.country = Objects.requireNonNull(country, "country");
        this.summary = summary;
        this.reference = reference;
        this.dueDate = dueDate;
        this.timestamp = Objects.requireNonNull(timestamp, "timestamp");
        this.reportedTimestamp = reportedTimestamp;
    }

    @Nonnull
    @Override
    public InvoiceDataAccessObject identifier(@Nonnull UUID identifier) {
        throw new BillingEntityUnmodifiableException(Invoice.class, this.identifier, "identifier");
    }

    @Nonnull
    @Override
    public InvoiceDataAccessObject account(@Nonnull UUID account) {
        throw new BillingEntityUnmodifiableException(Invoice.class, this.identifier, "account");
    }

    @Nonnull
    @Override
    public InvoiceDataAccessObject number(@Nonnull Integer number) {
        throw new BillingEntityUnmodifiableException(Invoice.class, this.identifier, "number");
    }

    @Nonnull
    @Override
    public InvoiceDataAccessObject sequences(@Nonnull Range<Long> range) {
        throw new BillingEntityUnmodifiableException(Invoice.class, this.identifier, "sequences");
    }

    @Nonnull
    @Override
    public InvoiceDataAccessObject period(@Nonnull Range<ZonedDateTime> range) {
        throw new BillingEntityUnmodifiableException(Invoice.class, this.identifier, "period");
    }

    @Nonnull
    @Override
    public InvoiceDataAccessObject openingBalance(@Nonnull BigDecimal balance) {
        throw new BillingEntityUnmodifiableException(Invoice.class, this.identifier, "openingBalance");
    }

    @Nonnull
    @Override
    public InvoiceDataAccessObject closingBalance(@Nonnull BigDecimal balance) {
        throw new BillingEntityUnmodifiableException(Invoice.class, this.identifier, "closingBalance");
    }

    @Nonnull
    @Override
    public InvoiceDataAccessObject payment(@Nonnull PaymentState payment) {
        throw new BillingEntityUnmodifiableException(Invoice.class, this.identifier, "payment");
    }

    @Nonnull
    @Override
    public InvoiceDataAccessObject generation(@Nonnull GenerationState generation) {
        throw new BillingEntityUnmodifiableException(Invoice.class, this.identifier, "generation");
    }

    @Nonnull
    @Override
    public InvoiceDataAccessObject country(@Nonnull CountryDataAccessObject country) {
        throw new BillingEntityUnmodifiableException(Invoice.class, this.identifier, "country");
    }

    @Nonnull
    @Override
    public InvoiceDataAccessObject reference(@Nullable ReferenceDataAccessObject reference) {
        throw new BillingEntityUnmodifiableException(Invoice.class, this.identifier, "reference");
    }

    @Nonnull
    @Override
    public InvoiceDataAccessObject summary(@Nullable InvoiceSummaryDataAccessObject summary) {
        throw new BillingEntityUnmodifiableException(Invoice.class, this.identifier, "summary");
    }

    @Nonnull
    @Override
    public InvoiceDataAccessObject dueDate(@Nullable ZonedDateTime dueDate) {
        throw new BillingEntityUnmodifiableException(Invoice.class, this.identifier, "dueDate");
    }

    @Nonnull
    @Override
    public InvoiceDataAccessObject timestamp(@Nonnull ZonedDateTime timestamp) {
        throw new BillingEntityUnmodifiableException(Invoice.class, this.identifier, "timestamp");
    }

    @Nonnull
    @Override
    public InvoiceDataAccessObject reportedTimestamp(@Nullable ZonedDateTime timestamp) {
        throw new BillingEntityUnmodifiableException(Invoice.class, this.identifier, "reportedTimestamp");
    }
}