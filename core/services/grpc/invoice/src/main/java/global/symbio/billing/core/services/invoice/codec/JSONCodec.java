package global.symbio.billing.core.services.invoice.codec;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.Struct;
import com.google.protobuf.util.JsonFormat;
import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import jakarta.annotation.Nonnull;

import java.util.Objects;

public abstract class JSONCodec<T> implements GRPCCodec<T, Struct> {

    private static final JsonFormat.Parser DEFAULT_ENCODER = JsonFormat.parser().ignoringUnknownFields();
    private static final JsonFormat.Printer DEFAULT_DECODER = JsonFormat.printer();

    @Nonnull
    private final Class<T> type;

    @Nonnull
    private final ObjectMapper mapper;

    @Nonnull
    private final JsonFormat.Parser encoder;

    @Nonnull
    private final JsonFormat.Printer decoder;

    protected JSONCodec(@Nonnull Class<T> type, @Nonnull ObjectMapper mapper) {
        this(type, mapper, DEFAULT_ENCODER, DEFAULT_DECODER);
    }

    protected JSONCodec(@Nonnull Class<T> type, @Nonnull ObjectMapper mapper, @Nonnull JsonFormat.Parser parser, @Nonnull JsonFormat.Printer printer) {
        this.type = Objects.requireNonNull(type, "type");
        this.mapper = Objects.requireNonNull(mapper, "mapper");
        this.encoder = Objects.requireNonNull(parser, "encoder");
        this.decoder = Objects.requireNonNull(printer, "decoder");
    }

    @Nonnull
    @Override
    public Struct encode(@Nonnull T bean) {
        try {
            final var json = mapper.writeValueAsString(bean);
            final var builder = Struct.newBuilder();
            encoder.merge(json, builder);
            return builder.build();
        } catch (JsonProcessingException | InvalidProtocolBufferException cause) {
            throw new RuntimeException(cause);
        }
    }

    @Nonnull
    @Override
    public T decode(@Nonnull Struct encoded) {
        try {
            final var json = decoder.print(encoded);
            return mapper.readValue(json, type);
        } catch (JsonProcessingException | InvalidProtocolBufferException cause) {
            throw new RuntimeException(cause);
        }
    }
}
