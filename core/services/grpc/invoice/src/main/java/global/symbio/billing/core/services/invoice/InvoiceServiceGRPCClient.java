package global.symbio.billing.core.services.invoice;

import com.google.common.base.Strings;
import global.symbio.billing.core.payment.persistence.api.payment.method.PaymentMethod;
import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.invoice.*;
import global.symbio.billing.core.services.grpc.message.*;
import global.symbio.billing.core.services.invoice.dto.InvoiceReport;
import global.symbio.billing.core.services.invoice.dto.ReportFilter;
import global.symbio.billing.invoice.persistence.api.invoice.Invoice;
import io.micronaut.context.annotation.Requires;
import io.micronaut.context.annotation.Secondary;
import io.micronaut.data.model.Page;
import io.micronaut.data.model.Pageable;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.time.YearMonth;
import java.time.ZonedDateTime;
import java.util.UUID;
import java.util.*;

@Slf4j
@Secondary
@Singleton
@Requires(beans = InvoiceGrpc.InvoiceFutureStub.class)
public class InvoiceServiceGRPCClient implements InvoiceService {

    @Nonnull
    private final InvoiceGrpc.InvoiceFutureStub invoices;

    @Nonnull
    private final Codec<UUID, UUIDMessage> uuid;

    @Nonnull
    private final Codec<Invoice, InvoiceMessage> invoice;

    @Nonnull
    private final Codec<PaymentMethod, PaymentMethodMessage> method;

    @Nonnull
    private final Codec<ZonedDateTime, ZonedDateTimeMessage> timestamp;

    @Nonnull
    private final Codec<YearMonth, YearMonthMessage> yearMonth;

    @Nonnull
    private final Codec<InvoiceReport, InvoiceReportMessage> invoiceReport;

    @Nonnull
    private final Codec<ReportFilter, ReportFilterMessage> filter;

    @Nonnull
    private final Codec<Pageable, PaginationParametersMessage> pagination;

    @Inject
    public InvoiceServiceGRPCClient(
        @Nonnull InvoiceGrpc.InvoiceFutureStub invoices,
        @Nonnull Codec<UUID, UUIDMessage> uuid,
        @Nonnull Codec<Invoice, InvoiceMessage> invoice,
        @Nonnull Codec<PaymentMethod, PaymentMethodMessage> method,
        @Nonnull Codec<ZonedDateTime, ZonedDateTimeMessage> timestamp,
        @Nonnull Codec<YearMonth, YearMonthMessage> yearMonth,
        @Nonnull Codec<InvoiceReport, InvoiceReportMessage> invoiceReport,
        @Nonnull Codec<ReportFilter, ReportFilterMessage> filter,
        @Nonnull Codec<Pageable, PaginationParametersMessage> pagination
    ) {
        this.invoices = Objects.requireNonNull(invoices, "invoices");
        this.uuid = Objects.requireNonNull(uuid, "uuid");
        this.invoice = Objects.requireNonNull(invoice, "invoice");
        this.method = Objects.requireNonNull(method, "method");
        this.timestamp = Objects.requireNonNull(timestamp, "timestamp");
        this.yearMonth = Objects.requireNonNull(yearMonth, "yearMonth");
        this.invoiceReport = Objects.requireNonNull(invoiceReport, "invoiceReport");
        this.filter = Objects.requireNonNull(filter, "filter");
        this.pagination = Objects.requireNonNull(pagination, "pagination");
    }

    @Nonnull
    @Override
    public Optional<Invoice> getInvoice(@Nonnull UUID identifier) {
        try {
            final var request = GetInvoiceRequest.newBuilder().setInvoice(uuid.encode(identifier)).build();
            final var response = invoices.getInvoice(request).get();
            if (response.hasInvoice()) {
                return Optional.of(invoice.decode(response.getInvoice()));
            }
            return Optional.empty();
        } catch (Throwable cause) {
            log.info("Exception fetching invoice: {}", identifier, cause);
            throw new RuntimeException(cause);
        }
    }

    @Nonnull
    @Override
    public Page<Invoice> getInvoices(@Nonnull UUID account, @Nullable Set<String> states, @Nullable String reference, @Nullable ZonedDateTime start, @Nullable ZonedDateTime end, @Nonnull Pageable pageable) {
        try {
            final var request = GetInvoicesRequest.newBuilder()
                .setAccount(uuid.encode(account))
                .setPagination(pagination.encode(pageable));
            if (states != null) {
                request.addAllStates(states);
            }
            if (!Strings.isNullOrEmpty(reference)) {
                request.setReference(reference);
            }
            if (start != null) {
                request.setStart(timestamp.encode(start));
            }
            if (end != null) {
                request.setEnd(timestamp.encode(end));
            }
            final var response = invoices.getInvoices(request.build()).get();
            final var invoices = response.getInvoicesList().stream().map(invoice::decode).toList();
            final var pagination = this.pagination.decode(response.getPagination());
            final var total = response.getTotalSize();
            return Page.of(invoices, pagination, total);
        } catch (Throwable cause) {
            log.info("Exception fetching invoices: account - {}, states - {}, reference - {}, start - {}, end - {}, pagination - {}", account, states, reference, start, end, pageable, cause);
            throw new RuntimeException(cause);
        }
    }

    @Nonnull
    @Override
    public Optional<PaymentMethod> getPaymentMethod(@Nonnull Integer identifier) {
        try {
            final var request = GetPaymentMethodRequest.newBuilder().setIdentifier(identifier).build();
            final var response = invoices.getPaymentMethod(request).get();
            if (response.hasMethod()) {
                return Optional.of(method.decode(response.getMethod()));
            }
            return Optional.empty();
        } catch (Throwable cause) {
            log.info("Exception fetching payment method: identifier - {}", identifier, cause);
            throw new RuntimeException(cause);
        }
    }

    @Nonnull
    @Override
    public Collection<PaymentMethod> getPaymentMethods() {
        try {
            final var request = GetPaymentMethodsRequest.newBuilder().build();
            final var response = invoices.getPaymentMethods(request).get();
            return response.getMethodsList().stream().map(method::decode).toList();
        } catch (Throwable cause) {
            log.info("Exception fetching payment methods", cause);
            throw new RuntimeException(cause);
        }
    }

    @Nonnull
    @Override
    public Collection<InvoiceReport> getInvoiceReport(@Nonnull String country, @Nonnull YearMonth period, @Nonnull ReportFilter filter) {
        try {
            final var request = GetInvoiceReportRequest.newBuilder().setCountry(country).setYearMonth(this.yearMonth.encode(period)).setFilter(this.filter.encode(filter));
            final var response = invoices.getInvoiceReport(request.build()).get();
            return response.getInvoicesList().stream().map(invoiceReport::decode).toList();
        } catch (Throwable cause) {
            log.info("Exception getting invoice report: country - {}, yearMonth - {}", country, period);
            throw new RuntimeException(cause);
        }
    }
}