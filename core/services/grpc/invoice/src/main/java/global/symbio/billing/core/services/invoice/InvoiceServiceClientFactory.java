package global.symbio.billing.core.services.invoice;

import global.symbio.billing.core.services.grpc.invoice.InvoiceGrpc;
import io.grpc.ManagedChannel;
import io.micronaut.context.annotation.Bean;
import io.micronaut.context.annotation.Factory;
import io.micronaut.context.annotation.Requires;
import io.micronaut.grpc.annotation.GrpcChannel;
import jakarta.annotation.Nonnull;

@Factory
public class InvoiceServiceClientFactory {

    public static final String SERVICE_GRPC_CHANNEL = "invoice";

    @Bean
    @Nonnull
    @Requires("grpc.channels." + SERVICE_GRPC_CHANNEL)
    public InvoiceGrpc.InvoiceFutureStub invoice(@Nonnull @GrpcChannel(SERVICE_GRPC_CHANNEL) ManagedChannel channel) {
        return InvoiceGrpc.newFutureStub(channel);
    }
}