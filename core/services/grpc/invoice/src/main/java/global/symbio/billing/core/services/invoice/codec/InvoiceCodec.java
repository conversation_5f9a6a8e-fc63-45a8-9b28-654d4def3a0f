package global.symbio.billing.core.services.invoice.codec;

import com.google.common.collect.Range;
import global.symbio.billing.core.country.persistence.api.Country;
import global.symbio.billing.core.reference.persistence.api.reference.Reference;
import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.invoice.*;
import global.symbio.billing.core.services.grpc.message.*;
import global.symbio.billing.invoice.persistence.api.invoice.Invoice;
import global.symbio.billing.invoice.persistence.api.invoice.state.generation.GenerationState;
import global.symbio.billing.invoice.persistence.api.invoice.state.payment.PaymentState;
import global.symbio.billing.invoice.persistence.api.invoice.summary.InvoiceSummary;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Objects;
import java.util.UUID;

@Singleton
public class InvoiceCodec implements GRPCCodec<Invoice, InvoiceMessage> {

    @Nonnull
    private final Codec<UUID, UUIDMessage> uuid;

    @Nonnull
    private final Codec<Range<?>, RangeMessage> range;

    @Nonnull
    private final Codec<BigDecimal, BigDecimalMessage> decimal;

    @Nonnull
    private final Codec<PaymentState, PaymentStateMessage> payment;

    @Nonnull
    private final Codec<GenerationState, GenerationStateMessage> generation;

    @Nonnull
    private final Codec<ZonedDateTime, ZonedDateTimeMessage> timestamp;

    @Nonnull
    private final Codec<Country, CountryMessage> country;

    @Nonnull
    private final Codec<Reference, ReferenceMessage> reference;

    @Nonnull
    private final Codec<InvoiceSummary, InvoiceSummaryMessage> summary;

    @Inject
    public InvoiceCodec(
        @Nonnull Codec<UUID, UUIDMessage> uuid,
        @Nonnull Codec<Range<?>, RangeMessage> range,
        @Nonnull Codec<BigDecimal, BigDecimalMessage> decimal,
        @Nonnull Codec<PaymentState, PaymentStateMessage> payment,
        @Nonnull Codec<GenerationState, GenerationStateMessage> generation,
        @Nonnull Codec<ZonedDateTime, ZonedDateTimeMessage> timestamp,
        @Nonnull Codec<Country, CountryMessage> country,
        @Nonnull Codec<Reference, ReferenceMessage> reference,
        @Nonnull Codec<InvoiceSummary, InvoiceSummaryMessage> summary
    ) {
        this.uuid = Objects.requireNonNull(uuid, "uuid");
        this.range = Objects.requireNonNull(range, "range");
        this.decimal = Objects.requireNonNull(decimal, "decimal");
        this.payment = Objects.requireNonNull(payment, "payment");
        this.generation = Objects.requireNonNull(generation, "generation");
        this.timestamp = Objects.requireNonNull(timestamp, "timestamp");
        this.country = Objects.requireNonNull(country, "country");
        this.reference = Objects.requireNonNull(reference, "reference");
        this.summary = Objects.requireNonNull(summary, "summary");
    }

    @Nonnull
    @Override
    public InvoiceMessage encode(@Nonnull Invoice bean) {
        final var invoice = InvoiceMessage.newBuilder()
            .setInvoice(uuid.encode(bean.getIdentifier()))
            .setAccount(uuid.encode(bean.getAccount()))
            .setNumber(bean.getNumber())
            .setSequences(range.encode(bean.getSequences()))
            .setPeriod(range.encode(bean.getPeriod()))
            .setOpeningBalance(decimal.encode(bean.getOpeningBalance()))
            .setClosingBalance(decimal.encode(bean.getClosingBalance()))
            .setPayment(payment.encode(bean.getPayment()))
            .setGeneration(generation.encode(bean.getGeneration()))
            .setCountry(country.encode(bean.getCountry()))
            .setTimestamp(timestamp.encode(bean.getTimestamp()));

        if (bean.getReference() != null) {
            invoice.setReference(reference.encode(bean.getReference()));
        }
        if (bean.getSummary() != null) {
            invoice.setSummary(summary.encode(bean.getSummary()));
        }
        if (bean.getDueDate() != null) {
            invoice.setDueDate(timestamp.encode(bean.getDueDate()));
        }
        if (bean.getReportedTimestamp() != null) {
            invoice.setReportedTimestamp(timestamp.encode(bean.getReportedTimestamp()));
        }
        return invoice.build();
    }

    @Nonnull
    @Override
    public Invoice decode(@Nonnull InvoiceMessage protobuf) {
        final var identifier = uuid.decode(protobuf.getInvoice());
        final var account = uuid.decode(protobuf.getAccount());
        final var number = protobuf.getNumber();
        final var sequences = range.decode(protobuf.getSequences());
        final var period = range.decode(protobuf.getPeriod());
        final var openingBalance = decimal.decode(protobuf.getOpeningBalance());
        final var closingBalance = decimal.decode(protobuf.getClosingBalance());
        final var payment = this.payment.decode(protobuf.getPayment());
        final var generation = this.generation.decode(protobuf.getGeneration());
        final var country = this.country.decode(protobuf.getCountry()).data();
        final var reference = protobuf.hasReference() ? this.reference.decode(protobuf.getReference()).data() : null;
        final var summary = protobuf.hasSummary() ? this.summary.decode(protobuf.getSummary()).data() : null;
        final var dueDate = protobuf.hasDueDate() ? this.timestamp.decode(protobuf.getDueDate()) : null;
        final var timestamp = this.timestamp.decode(protobuf.getTimestamp());
        final var reported = protobuf.hasReportedTimestamp() ? this.timestamp.decode(protobuf.getReportedTimestamp()) : null;
        return new GRPCInvoiceDataAccessObject(identifier, account, number, (Range<Long>) sequences, (Range<ZonedDateTime>) period, openingBalance, closingBalance, payment, generation, country, summary, reference, dueDate, timestamp, reported).entity();
    }
}
