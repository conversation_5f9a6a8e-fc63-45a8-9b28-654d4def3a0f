package global.symbio.billing.core.services.invoice.codec;

import global.symbio.billing.core.exception.sdk.BillingEntityNotFoundException;
import global.symbio.billing.core.services.grpc.codec.GRPCEnumCodec;
import global.symbio.billing.core.services.grpc.invoice.ReportFilterMessage;
import global.symbio.billing.core.services.invoice.dto.ReportFilter;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

import static global.symbio.billing.core.services.invoice.dto.ReportFilter.BILLING_PERIOD;
import static global.symbio.billing.core.services.invoice.dto.ReportFilter.GENERATED_DATE;

@Singleton
public record ReportFilterCodec() implements GRPCEnumCodec<ReportFilter, ReportFilterMessage> {

    @Nonnull
    @Override
    public ReportFilterMessage encode(@Nonnull ReportFilter bean) {
        return switch (bean) {
            case BILLING_PERIOD -> ReportFilterMessage.BILLING_PERIOD;
            case GENERATED_DATE -> ReportFilterMessage.GENERATED_DATE;
        };
    }

    @Nonnull
    @Override
    public ReportFilter decode(@Nonnull ReportFilterMessage protobuf) {
        return switch (protobuf) {
            case ReportFilterMessage.BILLING_PERIOD -> BILLING_PERIOD;
            case ReportFilterMessage.GENERATED_DATE -> GENERATED_DATE;
            default -> throw new BillingEntityNotFoundException(ReportFilter.class, protobuf);
        };
    }
}
