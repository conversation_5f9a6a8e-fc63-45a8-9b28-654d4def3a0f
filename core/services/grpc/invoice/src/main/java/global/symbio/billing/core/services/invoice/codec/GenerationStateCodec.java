package global.symbio.billing.core.services.invoice.codec;

import global.symbio.billing.core.exception.sdk.BillingEntityNotFoundException;
import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.invoice.GenerationStateMessage;
import global.symbio.billing.core.services.grpc.message.ZonedDateTimeMessage;
import global.symbio.billing.invoice.persistence.api.invoice.state.generation.Failure;
import global.symbio.billing.invoice.persistence.api.invoice.state.generation.GenerationState;
import global.symbio.billing.invoice.persistence.api.invoice.state.generation.Pending;
import global.symbio.billing.invoice.persistence.api.invoice.state.generation.Success;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.time.ZonedDateTime;
import java.util.Objects;

@Singleton
public class GenerationStateCodec implements GRPCCodec<GenerationState, GenerationStateMessage> {

    @Nonnull
    private final Codec<ZonedDateTime, ZonedDateTimeMessage> timestamp;

    @Inject
    public GenerationStateCodec(@Nonnull Codec<ZonedDateTime, ZonedDateTimeMessage> timestamp) {
        this.timestamp = Objects.requireNonNull(timestamp, "timestamp");
    }

    @Nonnull
    @Override
    public GenerationStateMessage encode(@Nonnull GenerationState bean) {
        final var builder = GenerationStateMessage.newBuilder().setState(bean.getState());
        if (bean instanceof Success success) {
            builder.setTimestamp(timestamp.encode(success.getTimestamp()));
        } else if (bean instanceof Failure failure) {
            builder.setReason(failure.getReason()).setTimestamp(timestamp.encode(failure.getTimestamp()));
        }
        return builder.build();
    }

    @Nonnull
    @Override
    public GenerationState decode(@Nonnull GenerationStateMessage protobuf) {
        final var state = protobuf.getState();
        final var reason = protobuf.hasReason() ? protobuf.getReason() : null;
        final var timestamp = protobuf.hasTimestamp() ? this.timestamp.decode(protobuf.getTimestamp()) : null;
        return switch (state) {
            case Pending.TYPE -> new Pending();
            case Failure.TYPE -> new Failure(reason, timestamp);
            case Success.TYPE -> new Success(timestamp);
            default -> throw new BillingEntityNotFoundException(GenerationState.class, state);
        };
    }
}
