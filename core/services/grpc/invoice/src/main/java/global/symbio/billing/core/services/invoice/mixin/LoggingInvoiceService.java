package global.symbio.billing.core.services.invoice.mixin;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.MoreObjects;
import global.symbio.billing.core.payment.persistence.api.payment.method.PaymentMethod;
import global.symbio.billing.core.services.invoice.InvoiceService;
import global.symbio.billing.core.services.invoice.dto.InvoiceReport;
import global.symbio.billing.core.services.invoice.dto.ReportFilter;
import global.symbio.billing.invoice.persistence.api.invoice.Invoice;
import io.micronaut.data.model.Page;
import io.micronaut.data.model.Pageable;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.AccessLevel;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.YearMonth;
import java.time.ZonedDateTime;
import java.util.*;

@VisibleForTesting
class LoggingInvoiceService implements InvoiceService {

    @Nonnull
    private final Logger log;

    @Nonnull
    @Getter(AccessLevel.PACKAGE)
    private final InvoiceService invoice;

    public LoggingInvoiceService(@Nonnull InvoiceService invoice) {
        this(LoggerFactory.getLogger(invoice.getClass()), invoice);
    }

    public LoggingInvoiceService(@Nonnull Logger log, @Nonnull InvoiceService invoice) {
        this.log = Objects.requireNonNull(log, "log");
        this.invoice = Objects.requireNonNull(invoice, "invoice");
    }

    @Nonnull
    @Override
    public Optional<Invoice> getInvoice(@Nonnull UUID identifier) {
        log.info("InvoiceService::getInvoice: invoice - {}", identifier);
        return invoice.getInvoice(identifier);
    }

    @Nonnull
    @Override
    public Page<Invoice> getInvoices(@Nonnull UUID account, @Nullable Set<String> states, @Nullable String reference, @Nullable ZonedDateTime start, @Nullable ZonedDateTime end, @Nonnull Pageable pageable) {
        log.info("InvoiceService::getInvoices: account - {}, states - {}, reference - {}, start - {}, end - {}, pagination - {}", account, states, reference, start, end, pageable);
        return invoice.getInvoices(account, states, reference, start, end, pageable);
    }

    @Nonnull
    @Override
    public Optional<PaymentMethod> getPaymentMethod(@Nonnull Integer identifier) {
        log.info("InvoiceService::getPaymentMethod: method - {}", identifier);
        return invoice.getPaymentMethod(identifier);
    }

    @Nonnull
    @Override
    public Collection<PaymentMethod> getPaymentMethods() {
        log.info("InvoiceService::getPaymentMethods");
        return invoice.getPaymentMethods();
    }

    @Nonnull
    @Override
    public Collection<InvoiceReport> getInvoiceReport(@Nonnull String country, @Nonnull YearMonth period, @Nonnull ReportFilter filter) {
        log.info("InvoiceService::getInvoiceReport: country - {}, yearMonth - {}, filter - {}", country, period, filter);
        return invoice.getInvoiceReport(country, period, filter);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof LoggingInvoiceService service)) return false;
        return Objects.equals(getInvoice(), service.getInvoice());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getInvoice());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("invoice", getInvoice())
            .toString();
    }
}