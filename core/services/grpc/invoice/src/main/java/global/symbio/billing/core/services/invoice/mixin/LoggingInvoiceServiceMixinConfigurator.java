package global.symbio.billing.core.services.invoice.mixin;

import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.mixin.MixinConfigurator;
import global.symbio.billing.core.services.invoice.InvoiceService;
import io.micronaut.context.annotation.Factory;
import io.micronaut.core.order.Ordered;
import io.micronaut.inject.BeanDefinition;
import io.micronaut.inject.BeanIdentifier;
import jakarta.annotation.Nonnull;

@Factory
@VisibleForTesting
class LoggingInvoiceServiceMixinConfigurator extends MixinConfigurator<InvoiceService> {

    private static final String COMPONENT = "logging";

    @Nonnull
    @Override
    public InvoiceService mixin(@Nonnull InvoiceService bean, @Nonnull String name) {
        return new LoggingInvoiceService(bean);
    }

    @Nonnull
    @Override
    protected String component() {
        return COMPONENT;
    }

    @Nonnull
    @Override
    protected String getName(@Nonnull InvoiceService bean, @Nonnull BeanDefinition<InvoiceService> definition, @Nonnull BeanIdentifier identifier) {
        return identifier.getName();
    }

    @Override
    protected boolean isEnabled() {
        return true;
    }

    @Override
    public int getOrder() {
        return Ordered.LOWEST_PRECEDENCE;
    }
}