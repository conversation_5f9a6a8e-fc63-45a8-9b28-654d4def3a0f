package global.symbio.billing.core.services.invoice.codec;

import com.google.common.collect.Range;
import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.invoice.RangeMessage;
import io.hypersistence.utils.hibernate.type.range.guava.PostgreSQLGuavaRangeType;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.AccessLevel;
import lombok.Getter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZonedDateTime;
import java.util.Objects;

@Singleton
@Getter(AccessLevel.PACKAGE)
public class RangeCodec implements GRPCCodec<Range<?>, RangeMessage> {

    @Nonnull
    private final PostgreSQLGuavaRangeType encoder;

    @Inject
    public RangeCodec() {
        this(PostgreSQLGuavaRangeType.INSTANCE);
    }

    @Inject
    public RangeCodec(@Nonnull PostgreSQLGuavaRangeType encoder) {
        this.encoder = Objects.requireNonNull(encoder, "encoder");
    }

    @Nonnull
    @Override
    public RangeMessage encode(@Nonnull Range<?> bean) {
        final var bound = bound(bean);
        final var type = type(bound);
        final var range = encoder.asString(bean);
        return RangeMessage.newBuilder()
                .setType(type)
                .setRange(range)
                .build();
    }

    @Nonnull
    @Override
    public Range<?> decode(@Nonnull RangeMessage protobuf) {
        final var value = protobuf.getRange();
        return switch (protobuf.getType()) {
            case "int4range" -> encoder.integerRange(value);
            case "int8range" -> encoder.longRange(value);
            case "numrange" -> encoder.bigDecimalRange(value);
            case "tsrange" -> encoder.localDateTimeRange(value);
            case "tstzrange" -> encoder.zonedDateTimeRange(value);
            case "daterange" -> encoder.localDateRange(value);
            default -> throw new IllegalStateException("The range type [" + protobuf.getType() + "] is not supported!");
        };
    }

    @Nonnull
    public static String type(@Nonnull Class<?> type) {
        if (Integer.class.equals(type)) {
            return "int4range";
        } else if (Long.class.equals(type)) {
            return "int8range";
        } else if (BigDecimal.class.equals(type)) {
            return "numrange";
        } else if (LocalDateTime.class.equals(type)) {
            return "tsrange";
        } else if (ZonedDateTime.class.equals(type) || OffsetDateTime.class.equals(type)) {
            return "tstzrange";
        } else if (LocalDate.class.equals(type)) {
            return "daterange";
        } else {
            throw new IllegalStateException("The range type [" + type + "] is not supported!");
        }
    }

    @Nonnull
    public static Class<?> bound(@Nonnull Range<?> range) {
        if (range.hasUpperBound()) {
            return range.upperEndpoint().getClass();
        } else if (range.hasLowerBound()) {
            return range.lowerEndpoint().getClass();
        }
        throw new IllegalStateException("Unbounded or empty range");
    }
}