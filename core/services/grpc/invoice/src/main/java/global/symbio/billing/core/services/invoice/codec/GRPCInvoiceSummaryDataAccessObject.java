package global.symbio.billing.core.services.invoice.codec;

import global.symbio.billing.core.exception.sdk.BillingEntityUnmodifiableException;
import global.symbio.billing.invoice.persistence.api.invoice.summary.InvoiceSummary;
import global.symbio.billing.invoice.persistence.api.invoice.summary.InvoiceSummaryDataAccessObject;
import jakarta.annotation.Nonnull;
import lombok.Getter;

import java.time.ZonedDateTime;
import java.util.Objects;
import java.util.UUID;

@Getter
public final class GRPCInvoiceSummaryDataAccessObject extends InvoiceSummaryDataAccessObject {

    @Nonnull
    private final UUID identifier;

    @Nonnull
    private final String summary;

    @Nonnull
    private final ZonedDateTime timestamp;

    public GRPCInvoiceSummaryDataAccessObject(
        @Nonnull UUID identifier,
        @Nonnull String summary,
        @Nonnull ZonedDateTime timestamp
    ) {
        this.identifier = Objects.requireNonNull(identifier, "identifier");
        this.summary = Objects.requireNonNull(summary, "summary");
        this.timestamp = Objects.requireNonNull(timestamp, "timestamp");
    }

    @Nonnull
    @Override
    public InvoiceSummaryDataAccessObject identifier(@Nonnull UUID identifier) {
        throw new BillingEntityUnmodifiableException(InvoiceSummary.class, this.identifier, "identifier");
    }

    @Nonnull
    @Override
    public InvoiceSummaryDataAccessObject summary(@Nonnull String summary) {
        throw new BillingEntityUnmodifiableException(InvoiceSummary.class, this.identifier, "summary");
    }

    @Nonnull
    @Override
    public InvoiceSummaryDataAccessObject timestamp(@Nonnull ZonedDateTime timestamp) {
        throw new BillingEntityUnmodifiableException(InvoiceSummary.class, this.identifier, "timestamp");
    }
}