package global.symbio.billing.core.services.invoice.codec;

import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.invoice.InvoiceSummaryMessage;
import global.symbio.billing.core.services.grpc.message.UUIDMessage;
import global.symbio.billing.core.services.grpc.message.ZonedDateTimeMessage;
import global.symbio.billing.invoice.persistence.api.invoice.summary.InvoiceSummary;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.time.ZonedDateTime;
import java.util.Objects;
import java.util.UUID;

@Singleton
public class InvoiceSummaryCodec implements GRPCCodec<InvoiceSummary, InvoiceSummaryMessage> {

    @Nonnull
    private final Codec<UUID, UUIDMessage> uuid;

    @Nonnull
    private final Codec<ZonedDateTime, ZonedDateTimeMessage> timestamp;

    @Inject
    public InvoiceSummaryCodec(
        @Nonnull Codec<UUID, UUIDMessage> uuid,
        @Nonnull Codec<ZonedDateTime, ZonedDateTimeMessage> timestamp
    ) {
        this.uuid = Objects.requireNonNull(uuid, "uuid");
        this.timestamp = Objects.requireNonNull(timestamp, "timestamp");
    }

    @Nonnull
    @Override
    public InvoiceSummaryMessage encode(@Nonnull InvoiceSummary bean) {
        return InvoiceSummaryMessage.newBuilder()
            .setInvoice(uuid.encode(bean.getIdentifier()))
            .setSummary(bean.getSummary())
            .setTimestamp(timestamp.encode(bean.getTimestamp()))
            .build();
    }

    @Nonnull
    @Override
    public InvoiceSummary decode(@Nonnull InvoiceSummaryMessage protobuf) {
        final var identifier = uuid.decode(protobuf.getInvoice());
        final var summary = protobuf.getSummary();
        final var timestamp = this.timestamp.decode(protobuf.getTimestamp());
        return new GRPCInvoiceSummaryDataAccessObject(identifier, summary, timestamp).entity();
    }
}
