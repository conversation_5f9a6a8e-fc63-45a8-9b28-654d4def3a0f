package global.symbio.billing.core.services.invoice.codec;

import global.symbio.billing.core.exception.sdk.BillingEntityNotFoundException;
import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.invoice.PaymentStateMessage;
import global.symbio.billing.core.services.grpc.message.BigDecimalMessage;
import global.symbio.billing.invoice.persistence.api.invoice.state.payment.*;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.math.BigDecimal;
import java.util.Objects;

@Singleton
public class PaymentStateCodec implements GRPCCodec<PaymentState, PaymentStateMessage> {

    @Nonnull
    private final Codec<BigDecimal, BigDecimalMessage> decimal;

    @Inject
    public PaymentStateCodec(@Nonnull Codec<BigDecimal, BigDecimalMessage> decimal) {
        this.decimal = Objects.requireNonNull(decimal, "decimal");
    }

    @Nonnull
    @Override
    public PaymentStateMessage encode(@Nonnull PaymentState bean) {
        return PaymentStateMessage.newBuilder()
            .setState(bean.getState())
            .setTotal(decimal.encode(bean.getTotal()))
            .setPaid(decimal.encode(bean.getPaid()))
            .setDue(decimal.encode(bean.getDue()))
            .build();
    }

    @Nonnull
    @Override
    public PaymentState decode(@Nonnull PaymentStateMessage protobuf) {
        final var type = protobuf.getState();
        final var total = decimal.decode(protobuf.getTotal());
        final var paid = decimal.decode(protobuf.getPaid());
        final var due = decimal.decode(protobuf.getDue());
        return switch (type) {
            case Uninvoiced.TYPE -> new Uninvoiced();
            case Unpaid.TYPE -> new Unpaid(total, due);
            case PartiallyPaid.TYPE -> new PartiallyPaid(total, paid, due);
            case Paid.TYPE -> new Paid(total, paid);
            default -> throw new BillingEntityNotFoundException(PaymentState.class, type);
        };
    }
}
