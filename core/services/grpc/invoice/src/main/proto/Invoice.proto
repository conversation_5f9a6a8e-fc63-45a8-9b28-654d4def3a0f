syntax = "proto3";
option java_package = "global.symbio.billing.core.services.grpc.invoice";
option java_outer_classname = "InvoiceService";
option java_multiple_files = true;

import "UUID.proto";
import "BigDecimal.proto";
import "Temporal.proto";
import "Country.proto";
import "PaymentMethod.proto";
import "Reference.proto";
import "Pagination.proto";

service Invoice {
  rpc getInvoice(GetInvoiceRequest) returns (GetInvoiceResponse) {}
  rpc getInvoices(GetInvoicesRequest) returns (GetInvoicesResponse) {}
  rpc getPaymentMethod(GetPaymentMethodRequest) returns (GetPaymentMethodResponse) {}
  rpc getPaymentMethods(GetPaymentMethodsRequest) returns (GetPaymentMethodsResponse) {}
  rpc getInvoiceReport(GetInvoiceReportRequest) returns (GetInvoiceReportResponse) {}
}

message RangeMessage {
  string type = 1;
  string range = 2;
}

enum ReportFilterMessage {
  REPORT_FILTER_UNSPECIFIED = 0;
  BILLING_PERIOD = 1;
  GENERATED_DATE = 2;
}

message PaymentStateMessage {
  string state = 1;
  BigDecimalMessage total = 2;
  BigDecimalMessage paid = 3;
  BigDecimalMessage due = 4;
}

message GenerationStateMessage {
  string state = 1;
  optional string reason = 2;
  optional ZonedDateTimeMessage timestamp = 3;
}

message InvoiceMessage {
  UUIDMessage invoice = 1;
  UUIDMessage account = 2;
  uint32 number = 3;
  RangeMessage sequences = 4;
  RangeMessage period = 5;
  BigDecimalMessage opening_balance = 6;
  BigDecimalMessage closing_balance = 7;
  PaymentStateMessage payment = 8;
  GenerationStateMessage generation = 9;
  CountryMessage country = 10;
  optional ReferenceMessage reference = 11;
  optional InvoiceSummaryMessage summary = 12;
  optional ZonedDateTimeMessage due_date = 13;
  ZonedDateTimeMessage timestamp = 14;
  optional ZonedDateTimeMessage reported_timestamp = 15;
}

message InvoiceSummaryMessage {
  UUIDMessage invoice = 1;
  string summary = 2;
  ZonedDateTimeMessage timestamp = 3;
}

message GetInvoiceRequest {
  UUIDMessage invoice = 1;
}

message GetInvoiceResponse {
  optional InvoiceMessage invoice = 1;
}

message GetInvoicesRequest {
  UUIDMessage account = 1;
  repeated string states = 2;
  optional string reference = 3;
  optional ZonedDateTimeMessage start = 4;
  optional ZonedDateTimeMessage end = 5;
  PaginationParametersMessage pagination = 6;
}

message GetInvoicesResponse {
  repeated InvoiceMessage invoices = 1;
  PaginationParametersMessage pagination = 2;
  uint64 total_size = 3;
}

message GetPaymentMethodRequest {
  int32 identifier = 1;
}

message GetPaymentMethodResponse {
  optional PaymentMethodMessage method = 1;
}

message GetPaymentMethodsRequest {}

message GetPaymentMethodsResponse {
  repeated PaymentMethodMessage methods = 1;
}

message InvoiceReportMessage {
  string country = 1;
  string period = 2;
  string account_number = 3;
  string account_name = 4;
  string reference = 5;
  string invoice_generated = 6;
  string invoice_emailed = 7;
  string delivery_status = 8;
}

message GetInvoiceReportRequest {
  string country = 1;
  YearMonthMessage yearMonth = 2;
  ReportFilterMessage filter = 3;
}

message GetInvoiceReportResponse {
  repeated InvoiceReportMessage invoices = 1;
}
