package global.symbio.billing.core.services.codec.uuid;

import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.message.UUIDMessage;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

import java.util.UUID;

@Singleton
public class UUIDCodec implements GRPCCodec<UUID, UUIDMessage> {

    @Nonnull
    @Override
    public UUIDMessage encode(@Nonnull UUID bean) {
        return UUIDMessage.newBuilder()
            .setMsb(bean.getMostSignificantBits())
            .setLsb(bean.getLeastSignificantBits())
            .build();
    }

    @Nonnull
    @Override
    public UUID decode(@Nonnull UUIDMessage protobuf) {
        return new UUID(protobuf.getMsb(), protobuf.getLsb());
    }
}