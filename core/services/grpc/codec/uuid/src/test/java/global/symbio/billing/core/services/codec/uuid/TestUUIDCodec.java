package global.symbio.billing.core.services.codec.uuid;

import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.message.UUIDMessage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class TestUUIDCodec {

    private Codec<UUID, UUIDMessage> codec;

    @BeforeEach
    public void setup() {
        codec = new UUIDCodec();
    }

    @Test
    @DisplayName("UUIDCodec::encode bean to protobuf")
    public void codec_bean_to_protobuf() {
        final var bean = UUID.randomUUID();
        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));
        assertEquals(bean.getMostSignificantBits(), protobuf.getMsb());
        assertEquals(bean.getLeastSignificantBits(), protobuf.getLsb());
    }

    @Test
    @DisplayName("UUIDCodec::decode protobuf to bean")
    public void codec_protobuf_to_bean() {
        final var source = UUID.randomUUID();
        final var protobuf = UUIDMessage.newBuilder().setMsb(source.getMostSignificantBits()).setLsb(source.getLeastSignificantBits()).build();
        final var bean = assertDoesNotThrow(() -> codec.decode(protobuf));
        assertEquals(protobuf.getMsb(), bean.getMostSignificantBits());
        assertEquals(protobuf.getLsb(), bean.getLeastSignificantBits());
        assertEquals(source, bean);
    }
}