syntax = "proto3";
option java_package = "global.symbio.billing.core.services.grpc.message";
option java_multiple_files = true;

// These structures are modelled upon Micronauts concept of pagination.

enum SortDirectionMessage {
  // Intentionally specifies `ASC` as `0` so that the default value is `ASC`.
  ASC = 0;
  DESC = 1;
}

message SortOrderMessage {
  string property = 1;
  SortDirectionMessage direction = 2;
  bool ignore_case = 3;
}

message SortMessage {
  repeated SortOrderMessage order_by = 1;
}

message PaginationParametersMessage {
  uint32 page = 1;
  uint32 size = 2;
  optional SortMessage sort = 3;
}