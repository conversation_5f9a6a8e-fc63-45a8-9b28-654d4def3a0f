package global.symbio.billing.core.services.grpc.codec.pagination;

import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.message.SortMessage;
import global.symbio.billing.core.services.grpc.message.SortOrderMessage;
import io.micronaut.data.model.Sort;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.util.Objects;

@Singleton
public class SortCodec implements GRPCCodec<Sort, SortMessage> {

    @Nonnull
    private final Codec<Sort.Order, SortOrderMessage> order;

    @Inject
    public SortCodec(@Nonnull Codec<Sort.Order, SortOrderMessage> order) {
        this.order = Objects.requireNonNull(order, "order");
    }

    @Nonnull
    @Override
    public SortMessage encode(@Nonnull Sort bean) {
        return SortMessage.newBuilder()
            .addAllOrderBy(bean.getOrderBy().stream().map(order::encode).toList())
            .build();
    }

    @Nonnull
    @Override
    public Sort decode(@Nonnull SortMessage protobuf) {
        final var orderBy = protobuf.getOrderByList().stream().map(order::decode).toList();
        return Sort.of(orderBy);
    }
}