package global.symbio.billing.core.services.grpc.codec.pagination;

import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.message.SortDirectionMessage;
import global.symbio.billing.core.services.grpc.message.SortOrderMessage;
import io.micronaut.data.model.Sort;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.util.Objects;

@Singleton
public class SortOrderCodec implements GRPCCodec<Sort.Order, SortOrderMessage> {

    @Nonnull
    private final Codec<Sort.Order.Direction, SortDirectionMessage> direction;

    @Inject
    public SortOrderCodec(@Nonnull Codec<Sort.Order.Direction, SortDirectionMessage> direction) {
        this.direction = Objects.requireNonNull(direction, "direction");
    }

    @Nonnull
    @Override
    public SortOrderMessage encode(@Nonnull Sort.Order bean) {
        return SortOrderMessage.newBuilder()
            .setProperty(bean.getProperty())
            .setDirection(this.direction.encode(bean.getDirection()))
            .setIgnoreCase(bean.isIgnoreCase())
            .build();
    }

    @Nonnull
    @Override
    public Sort.Order decode(@Nonnull SortOrderMessage protobuf) {
        final var property = protobuf.getProperty();
        final var direction = this.direction.decode(protobuf.getDirection());
        final var ignoreCase = protobuf.getIgnoreCase();
        return new Sort.Order(property, direction, ignoreCase);
    }
}