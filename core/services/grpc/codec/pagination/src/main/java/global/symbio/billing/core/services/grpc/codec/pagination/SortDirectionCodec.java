package global.symbio.billing.core.services.grpc.codec.pagination;

import global.symbio.billing.core.exception.sdk.BillingEntityNotFoundException;
import global.symbio.billing.core.services.grpc.codec.GRPCEnumCodec;
import global.symbio.billing.core.services.grpc.message.SortDirectionMessage;
import io.micronaut.data.model.Sort;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

@Singleton
public class SortDirectionCodec implements GRPCEnumCodec<Sort.Order.Direction, SortDirectionMessage> {

    @Nonnull
    @Override
    public SortDirectionMessage encode(@Nonnull Sort.Order.Direction bean) {
        return switch (bean) {
            case Sort.Order.Direction.ASC -> SortDirectionMessage.ASC;
            case Sort.Order.Direction.DESC -> SortDirectionMessage.DESC;
        };
    }

    @Nonnull
    @Override
    public Sort.Order.Direction decode(@Nonnull SortDirectionMessage protobuf) {
        return switch (protobuf) {
            case SortDirectionMessage.ASC -> Sort.Order.Direction.ASC;
            case SortDirectionMessage.DESC -> Sort.Order.Direction.DESC;
            default -> throw new BillingEntityNotFoundException(Sort.Order.Direction.class, protobuf);
        };
    }
}