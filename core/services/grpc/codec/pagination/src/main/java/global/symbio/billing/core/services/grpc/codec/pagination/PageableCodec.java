package global.symbio.billing.core.services.grpc.codec.pagination;

import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.message.PaginationParametersMessage;
import global.symbio.billing.core.services.grpc.message.SortMessage;
import io.micronaut.data.model.Pageable;
import io.micronaut.data.model.Sort;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.util.Objects;

@Singleton
public class PageableCodec implements GRPCCodec<Pageable, PaginationParametersMessage> {

    @Nonnull
    private final Codec<Sort, SortMessage> sort;

    @Inject
    public PageableCodec(@Nonnull Codec<Sort, SortMessage> sort) {
        this.sort = Objects.requireNonNull(sort, "sort");
    }

    @Nonnull
    @Override
    public PaginationParametersMessage encode(@Nonnull Pageable bean) {
        final var builder = PaginationParametersMessage.newBuilder().setPage(bean.getNumber()).setSize(bean.getSize());
        if (bean.getSort() != null && bean.isSorted()) {
            builder.setSort(sort.encode(bean.getSort()));
        }
        return builder.build();
    }

    @Nonnull
    @Override
    public Pageable decode(@Nonnull PaginationParametersMessage protobuf) {
        final var page = protobuf.getPage();
        final var size = protobuf.getSize();
        final var sort = protobuf.hasSort() ? this.sort.decode(protobuf.getSort()) : null;
        return Pageable.from(page, size, sort);
    }
}