package global.symbio.billing.core.services.grpc.codec.pagination;

import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.message.SortDirectionMessage;
import global.symbio.billing.core.services.grpc.message.SortOrderMessage;
import io.micronaut.data.model.Sort;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.concurrent.ThreadLocalRandom;

import static io.micronaut.data.model.Sort.Order.Direction.ASC;
import static io.micronaut.data.model.Sort.Order.Direction.DESC;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class TestSortOrderCodec {

    private Codec<Sort.Order.Direction, SortDirectionMessage> direction;
    private Codec<Sort.Order, SortOrderMessage> codec;

    @BeforeEach
    public void setup() {
        direction = new SortDirectionCodec();
        codec = new SortOrderCodec(direction);
    }

    @Test
    @DisplayName("SortOrderCodec::encode bean to protobuf")
    public void codec_bean_to_protobuf() {
        final var bean = new Sort.Order("a.b.c", ThreadLocalRandom.current().nextBoolean() ? ASC : DESC, ThreadLocalRandom.current().nextBoolean());
        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));
        assertEquals(bean.getProperty(), protobuf.getProperty());
        assertEquals(bean.getDirection(), direction.decode(protobuf.getDirection()));
        assertEquals(bean.isIgnoreCase(), protobuf.getIgnoreCase());
    }

    @Test
    @DisplayName("SortOrderCodec::decode protobuf to bean")
    public void codec_protobuf_to_bean() {
        final var message = SortOrderMessage.newBuilder()
            .setProperty("d.e.f")
            .setDirection(ThreadLocalRandom.current().nextBoolean() ? SortDirectionMessage.ASC : SortDirectionMessage.DESC)
            .setIgnoreCase(ThreadLocalRandom.current().nextBoolean())
            .build();
        final var bean = assertDoesNotThrow(() -> codec.decode(message));
        assertEquals(message.getProperty(), bean.getProperty());
        assertEquals(message.getDirection(), direction.encode(bean.getDirection()));
        assertEquals(message.getIgnoreCase(), bean.isIgnoreCase());
    }
}