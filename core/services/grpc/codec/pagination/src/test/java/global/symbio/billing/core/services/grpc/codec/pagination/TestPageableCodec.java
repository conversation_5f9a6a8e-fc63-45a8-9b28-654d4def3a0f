package global.symbio.billing.core.services.grpc.codec.pagination;

import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.message.PaginationParametersMessage;
import global.symbio.billing.core.services.grpc.message.SortDirectionMessage;
import global.symbio.billing.core.services.grpc.message.SortMessage;
import global.symbio.billing.core.services.grpc.message.SortOrderMessage;
import io.micronaut.data.model.Pageable;
import io.micronaut.data.model.Sort;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class TestPageableCodec {

    private Codec<Sort.Order.Direction, SortDirectionMessage> direction;
    private Codec<Sort.Order, SortOrderMessage> order;
    private Codec<Sort, SortMessage> sort;
    private Codec<Pageable, PaginationParametersMessage> codec;

    @BeforeEach
    public void setup() {
        direction = new SortDirectionCodec();
        order = new SortOrderCodec(direction);
        sort = new SortCodec(order);
        codec = new PageableCodec(sort);
    }

    @ParameterizedTest
    @MethodSource("parameters")
    @DisplayName("PageableCodec::encode bean to protobuf")
    public void codec_bean_to_protobuf(@Nonnull Pageable bean, @Nonnull PaginationParametersMessage protobuf) {
        final var encoded = assertDoesNotThrow(() -> codec.encode(bean));
        assertEquals(protobuf, encoded);
    }

    @ParameterizedTest
    @MethodSource("parameters")
    @DisplayName("PageableCodec::decode protobuf to bean")
    public void codec_protobuf_to_bean(@Nonnull Pageable bean, @Nonnull PaginationParametersMessage protobuf) {
        final var decoded = assertDoesNotThrow(() -> codec.decode(protobuf));
        assertEquals(bean, decoded);
    }

    @Nonnull
    private static Stream<Arguments> parameters() {
        return Stream.of(
            Arguments.of(
                Pageable.from(1),
                PaginationParametersMessage
                    .newBuilder()
                    .setPage(1)
                    .setSize(10)
                    .build()
            ),
            Arguments.of(
                Pageable.from(3, 10),
                PaginationParametersMessage
                    .newBuilder()
                    .setPage(3)
                    .setSize(10)
                    .build()
            ),
            Arguments.of(
                Pageable.from(100, 123, Sort.of(Sort.Order.asc("abc"))),
                PaginationParametersMessage
                    .newBuilder()
                    .setPage(100)
                    .setSize(123)
                    .setSort(
                        SortMessage
                            .newBuilder()
                            .addOrderBy(
                                SortOrderMessage
                                    .newBuilder()
                                    .setProperty("abc")
                                    .setDirection(SortDirectionMessage.ASC)
                                    .setIgnoreCase(false)
                                    .build()
                            )
                            .build()
                    )
                    .build()
            )
        );
    }
}
