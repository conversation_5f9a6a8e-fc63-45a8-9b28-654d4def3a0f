package global.symbio.billing.core.services.grpc.codec.pagination;

import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.message.SortDirectionMessage;
import io.micronaut.data.model.Sort;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class TestSortDirectionCodec {

    private Codec<Sort.Order.Direction, SortDirectionMessage> codec;

    @BeforeEach
    public void setup() {
        codec = new SortDirectionCodec();
    }

    @ParameterizedTest
    @DisplayName("SortDirectionCodec::encode bean to protobuf")
    @MethodSource("enums")
    public void codec_bean_to_protobuf(final Sort.Order.Direction bean) {
        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));
        assertEquals(bean, codec.decode(protobuf));
    }

    @ParameterizedTest
    @DisplayName("SortDirectionCodec::decode protobuf to bean")
    @MethodSource("protos")
    public void codec_protobuf_to_bean(final SortDirectionMessage message) {
        final var bean = assertDoesNotThrow(() -> codec.decode(message));
        assertEquals(message, codec.encode(bean));
    }

    private static Stream<Arguments> enums() {
        return Stream.of(
            Arguments.of(Sort.Order.Direction.ASC),
            Arguments.of(Sort.Order.Direction.DESC)
        );
    }

    private static Stream<Arguments> protos() {
        return Stream.of(
            Arguments.of(SortDirectionMessage.ASC),
            Arguments.of(SortDirectionMessage.DESC)
        );
    }
}