package global.symbio.billing.core.services.grpc.codec.pagination;

import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.message.SortDirectionMessage;
import global.symbio.billing.core.services.grpc.message.SortMessage;
import global.symbio.billing.core.services.grpc.message.SortOrderMessage;
import io.micronaut.data.model.Sort;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class TestSortCodec {

    private Codec<Sort.Order.Direction, SortDirectionMessage> direction;
    private Codec<Sort.Order, SortOrderMessage> order;
    private Codec<Sort, SortMessage> codec;

    @BeforeEach
    public void setup() {
        direction = new SortDirectionCodec();
        order = new SortOrderCodec(direction);
        codec = new SortCodec(order);
    }

    @ParameterizedTest
    @MethodSource("parameters")
    @DisplayName("SortCodec::encode bean to protobuf")
    public void codec_bean_to_protobuf(@Nonnull Sort bean, @Nonnull SortMessage protobuf) {
        final var encoded = assertDoesNotThrow(() -> codec.encode(bean));
        assertEquals(protobuf, encoded);
    }

    @ParameterizedTest
    @MethodSource("parameters")
    @DisplayName("SortCodec::decode protobuf to bean")
    public void codec_protobuf_to_bean(@Nonnull Sort bean, @Nonnull SortMessage protobuf) {
        final var decoded = assertDoesNotThrow(() -> codec.decode(protobuf));
        assertEquals(bean, decoded);
    }

    @Nonnull
    private static Stream<Arguments> parameters() {
        final var ignoreCase = ThreadLocalRandom.current().nextBoolean();
        return Stream.of(
            Arguments.of(
                Sort.of(
                    Sort.Order.asc("a.b.c", ignoreCase),
                    Sort.Order.desc("d.e.f", !ignoreCase)
                ),
                SortMessage.newBuilder()
                    .addOrderBy(
                        SortOrderMessage.newBuilder()
                            .setProperty("a.b.c")
                            .setDirection(SortDirectionMessage.ASC)
                            .setIgnoreCase(ignoreCase)
                            .build()
                    )
                    .addOrderBy(
                        SortOrderMessage.newBuilder()
                            .setProperty("d.e.f")
                            .setDirection(SortDirectionMessage.DESC)
                            .setIgnoreCase(!ignoreCase)
                            .build()
                    )
                    .build()
            ),
            Arguments.of(
                Sort.unsorted(),
                SortMessage.newBuilder().build()
            )
        );
    }
}