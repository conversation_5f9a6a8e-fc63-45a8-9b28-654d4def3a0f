package global.symbio.billing.core.services.grpc.codec.country;

import global.symbio.billing.core.country.persistence.api.Country;
import global.symbio.billing.core.country.persistence.api.CountryDataAccessObject;
import global.symbio.billing.core.exception.sdk.BillingEntityUnmodifiableException;
import jakarta.annotation.Nonnull;
import lombok.Getter;

import java.util.Objects;

@Getter
public final class GRPCCountryDataAccessObject extends CountryDataAccessObject {

    @Nonnull
    private final Integer identifier;

    @Nonnull
    private final String name;

    @Nonnull
    private final String code;

    public GRPCCountryDataAccessObject(@Nonnull Integer identifier, @Nonnull String name, @Nonnull String code) {
        this.identifier = Objects.requireNonNull(identifier, "identifier");
        this.name = Objects.requireNonNull(name, "name");
        this.code = Objects.requireNonNull(code, "code");
    }

    @Nonnull
    @Override
    public CountryDataAccessObject identifier(@Nonnull Integer identifier) {
        throw new BillingEntityUnmodifiableException(Country.class, this.identifier, "identifier");
    }

    @Nonnull
    @Override
    public CountryDataAccessObject name(@Nonnull String name) {
        throw new BillingEntityUnmodifiableException(Country.class, this.identifier, "name");
    }

    @Nonnull
    @Override
    public CountryDataAccessObject code(@Nonnull String code) {
        throw new BillingEntityUnmodifiableException(Country.class, this.identifier, "code");
    }
}