package global.symbio.billing.core.services.grpc.codec.country;

import global.symbio.billing.core.country.persistence.api.Country;
import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.message.CountryMessage;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

@Singleton
public class CountryCodec implements GRPCCodec<Country, CountryMessage> {

    @Nonnull
    @Override
    public CountryMessage encode(@Nonnull Country bean) {
        return CountryMessage.newBuilder()
            .setId(bean.getIdentifier())
            .setName(bean.getName())
            .setCode(bean.getCode())
            .build();
    }

    @Nonnull
    @Override
    public Country decode(@Nonnull CountryMessage protobuf) {
        final var identifier = protobuf.getId();
        final var name = protobuf.getName();
        final var code = protobuf.getCode();
        return new GRPCCountryDataAccessObject(identifier, name, code).entity();
    }
}