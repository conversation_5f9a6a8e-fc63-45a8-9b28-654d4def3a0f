package global.symbio.billing.core.services.grpc.codec.country;

import global.symbio.billing.core.country.persistence.api.Country;
import global.symbio.billing.core.exception.sdk.BillingEntityUnmodifiableException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

public class TestGRPCCountryDataAccessObject {

    private static final int ID = 36;
    private static final String NAME = "Australia";
    private static final String CODE = "AU";

    private GRPCCountryDataAccessObject country;

    @BeforeEach
    public void setup() {
        country = new GRPCCountryDataAccessObject(ID, NAME, CODE);
    }

    @Test
    @DisplayName("GRPCCountryDataAccessObject::new rejects null constructor arguments")
    public void grpc_country_data_access_object_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new GRPCCountryDataAccessObject(null, NAME, CODE));
        assertThrows(NullPointerException.class, () -> new GRPCCountryDataAccessObject(ID, null, CODE));
        assertThrows(NullPointerException.class, () -> new GRPCCountryDataAccessObject(ID, NAME, null));
    }

    @Test
    @DisplayName("GRPCCountryDataAccessObject::identifier is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_country_data_access_object_identifier_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(Country.class, ID, "identifier");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> country.identifier(69));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCCountryDataAccessObject::name is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_country_data_access_object_name_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(Country.class, ID, "name");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> country.name("ABCDEF"));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCCountryDataAccessObject::code is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_country_data_access_object_code_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(Country.class, ID, "code");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> country.code("XD"));
        assertEquals(expected.getMessage(), cause.getMessage());
    }
}