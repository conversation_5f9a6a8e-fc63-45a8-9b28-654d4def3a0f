package global.symbio.billing.core.services.grpc.codec.country;

import global.symbio.billing.core.country.persistence.api.Country;
import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.message.CountryMessage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class TestCountryCodec {

    private Codec<Country, CountryMessage> codec;

    @BeforeEach
    public void setup() {
        codec = new CountryCodec();
    }

    @Test
    @DisplayName("CountryCodec::encode bean to protobuf")
    public void codec_bean_to_protobuf() {
        final var bean = new GRPCCountryDataAccessObject(36, "Australia", "AU").entity();
        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));

        assertEquals(bean.getIdentifier(), protobuf.getId());
        assertEquals(bean.getName(), protobuf.getName());
        assertEquals(bean.getCode(), protobuf.getCode());
    }

    @Test
    @DisplayName("CountryCodec::decode protobuf to bean")
    public void codec_protobuf_to_bean() {
        final var source = new GRPCCountryDataAccessObject(36, "Australia", "AU").entity();
        final var protobuf = CountryMessage.newBuilder()
            .setId(source.getIdentifier())
            .setName(source.getName())
            .setCode(source.getCode())
            .build();
        final var bean = assertDoesNotThrow(() -> codec.decode(protobuf));

        assertEquals(bean.getIdentifier(), protobuf.getId());
        assertEquals(bean.getName(), protobuf.getName());
        assertEquals(bean.getCode(), protobuf.getCode());
        assertEquals(source, bean);
    }
}