package global.symbio.billing.core.services.grpc.codec.temporal;

import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.message.LocalTimeMessage;
import global.symbio.billing.core.services.grpc.message.OffsetTimeMessage;
import global.symbio.billing.core.services.grpc.message.ZoneOffsetMessage;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.time.LocalTime;
import java.time.OffsetTime;
import java.time.ZoneOffset;
import java.util.Objects;


@Singleton
public class OffsetTimeCodec implements GRPCCodec<OffsetTime, OffsetTimeMessage> {

    @Nonnull
    private final Codec<LocalTime, LocalTimeMessage> time;

    @Nonnull
    private final Codec<ZoneOffset, ZoneOffsetMessage> offset;

    @Inject
    public OffsetTimeCodec(
        @Nonnull Codec<LocalTime, LocalTimeMessage> time,
        @Nonnull Codec<ZoneOffset, ZoneOffsetMessage> offset
    ) {
        this.time = Objects.requireNonNull(time, "time");
        this.offset = Objects.requireNonNull(offset, "offset");
    }

    @Nonnull
    @Override
    public OffsetTimeMessage encode(@Nonnull OffsetTime bean) {
        return OffsetTimeMessage.newBuilder()
            .setTime(time.encode(bean.toLocalTime()))
            .setOffset(offset.encode(bean.getOffset()))
            .build();
    }

    @Nonnull
    @Override
    public OffsetTime decode(@Nonnull OffsetTimeMessage protobuf) {
        final var time = this.time.decode(protobuf.getTime());
        final var offset = this.offset.decode(protobuf.getOffset());
        return OffsetTime.of(time, offset);
    }
}
