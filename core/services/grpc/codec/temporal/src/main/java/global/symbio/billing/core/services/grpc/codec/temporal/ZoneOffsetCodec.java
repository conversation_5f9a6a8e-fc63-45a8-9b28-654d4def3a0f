package global.symbio.billing.core.services.grpc.codec.temporal;

import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.message.ZoneOffsetMessage;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

import java.time.ZoneOffset;


@Singleton
public class ZoneOffsetCodec implements GRPCCodec<ZoneOffset, ZoneOffsetMessage> {

    @Nonnull
    @Override
    public ZoneOffsetMessage encode(@Nonnull ZoneOffset bean) {
        return ZoneOffsetMessage.newBuilder()
            .setId(bean.getId())
            .build();
    }

    @Nonnull
    @Override
    public ZoneOffset decode(@Nonnull ZoneOffsetMessage protobuf) {
        final var id = protobuf.getId();
        return ZoneOffset.of(id);
    }
}
