package global.symbio.billing.core.services.grpc.codec.temporal;

import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.message.LocalDateMessage;
import global.symbio.billing.core.services.grpc.message.LocalDateTimeMessage;
import global.symbio.billing.core.services.grpc.message.LocalTimeMessage;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Objects;

@Singleton
public class LocalDateTimeCodec implements GRPCCodec<LocalDateTime, LocalDateTimeMessage> {

    @Nonnull
    private final Codec<LocalDate, LocalDateMessage> date;

    @Nonnull
    private final Codec<LocalTime, LocalTimeMessage> time;

    @Inject
    public LocalDateTimeCodec(
        @Nonnull Codec<LocalDate, LocalDateMessage> date,
        @Nonnull Codec<LocalTime, LocalTimeMessage> time
    ) {
        this.date = Objects.requireNonNull(date, "date");
        this.time = Objects.requireNonNull(time, "time");
    }

    @Nonnull
    @Override
    public LocalDateTimeMessage encode(@Nonnull LocalDateTime bean) {
        return LocalDateTimeMessage.newBuilder()
            .setDate(date.encode(bean.toLocalDate()))
            .setTime(time.encode(bean.toLocalTime()))
            .build();
    }

    @Nonnull
    @Override
    public LocalDateTime decode(@Nonnull LocalDateTimeMessage protobuf) {
        final var date = this.date.decode(protobuf.getDate());
        final var time = this.time.decode(protobuf.getTime());
        return LocalDateTime.of(date, time);
    }
}