package global.symbio.billing.core.services.grpc.codec.temporal;

import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.message.LocalDateTimeMessage;
import global.symbio.billing.core.services.grpc.message.OffsetDateTimeMessage;
import global.symbio.billing.core.services.grpc.message.ZoneOffsetMessage;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.Objects;


@Singleton
public class OffsetDateTimeCodec implements GRPCCodec<OffsetDateTime, OffsetDateTimeMessage> {

    @Nonnull
    private final Codec<LocalDateTime, LocalDateTimeMessage> datetime;

    @Nonnull
    private final Codec<ZoneOffset, ZoneOffsetMessage> offset;

    @Inject
    public OffsetDateTimeCodec(
        @Nonnull Codec<LocalDateTime, LocalDateTimeMessage> datetime,
        @Nonnull Codec<ZoneOffset, ZoneOffsetMessage> offset
    ) {
        this.datetime = Objects.requireNonNull(datetime, "datetime");
        this.offset = Objects.requireNonNull(offset, "offset");
    }

    @Nonnull
    @Override
    public OffsetDateTimeMessage encode(@Nonnull OffsetDateTime bean) {
        return OffsetDateTimeMessage.newBuilder()
            .setDatetime(datetime.encode(bean.toLocalDateTime()))
            .setOffset(offset.encode(bean.getOffset()))
            .build();
    }

    @Nonnull
    @Override
    public OffsetDateTime decode(@Nonnull OffsetDateTimeMessage protobuf) {
        final var datetime = this.datetime.decode(protobuf.getDatetime());
        final var offset = this.offset.decode(protobuf.getOffset());
        return OffsetDateTime.of(datetime, offset);
    }
}
