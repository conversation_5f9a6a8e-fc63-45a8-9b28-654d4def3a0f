package global.symbio.billing.core.services.grpc.codec.temporal;

import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.message.ZoneIdMessage;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

import java.time.ZoneId;


@Singleton
public class ZoneIdCodec implements GRPCCodec<ZoneId, ZoneIdMessage> {

    @Nonnull
    @Override
    public ZoneIdMessage encode(@Nonnull ZoneId bean) {
        return ZoneIdMessage.newBuilder()
            .setId(bean.getId())
            .build();
    }

    @Nonnull
    @Override
    public ZoneId decode(@Nonnull ZoneIdMessage protobuf) {
        final var id = protobuf.getId();
        return ZoneId.of(id);
    }
}
