package global.symbio.billing.core.services.grpc.codec.temporal;

import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.message.LocalDateTimeMessage;
import global.symbio.billing.core.services.grpc.message.ZoneIdMessage;
import global.symbio.billing.core.services.grpc.message.ZoneOffsetMessage;
import global.symbio.billing.core.services.grpc.message.ZonedDateTimeMessage;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.Objects;


@Singleton
public class ZonedDateTimeCodec implements GRPCCodec<ZonedDateTime, ZonedDateTimeMessage> {

    @Nonnull
    private final Codec<LocalDateTime, LocalDateTimeMessage> datetime;

    @Nonnull
    private final Codec<ZoneOffset, ZoneOffsetMessage> offset;

    @Nonnull
    private final Codec<ZoneId, ZoneIdMessage> zone;

    @Inject
    public ZonedDateTimeCodec(
        @Nonnull Codec<LocalDateTime, LocalDateTimeMessage> datetime,
        @Nonnull Codec<ZoneOffset, ZoneOffsetMessage> offset,
        @Nonnull Codec<ZoneId, ZoneIdMessage> zone
    ) {
        this.datetime = Objects.requireNonNull(datetime, "datetime");
        this.offset = Objects.requireNonNull(offset, "offset");
        this.zone = Objects.requireNonNull(zone, "zone");
    }

    @Nonnull
    @Override
    public ZonedDateTimeMessage encode(@Nonnull ZonedDateTime bean) {
        return ZonedDateTimeMessage.newBuilder()
            .setDatetime(datetime.encode(bean.toLocalDateTime()))
            .setOffset(offset.encode(bean.getOffset()))
            .setZone(zone.encode(bean.getZone()))
            .build();
    }

    @Nonnull
    @Override
    public ZonedDateTime decode(@Nonnull ZonedDateTimeMessage protobuf) {
        final var datetime = this.datetime.decode(protobuf.getDatetime());
        final var offset = this.offset.decode(protobuf.getOffset());
        final var zone = this.zone.decode(protobuf.getZone());
        return ZonedDateTime.ofInstant(datetime, offset, zone);
    }
}
