package global.symbio.billing.core.services.grpc.codec.temporal;

import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.message.DurationMessage;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

import java.time.Duration;


@Singleton
public class DurationCodec implements GRPCCodec<Duration, DurationMessage> {

    @Nonnull
    @Override
    public DurationMessage encode(@Nonnull Duration bean) {
        return DurationMessage.newBuilder()
            .setSeconds(bean.getSeconds())
            .setNanos(bean.getNano())
            .build();
    }

    @Nonnull
    @Override
    public Duration decode(@Nonnull DurationMessage protobuf) {
        final var seconds = protobuf.getSeconds();
        final var nanos = protobuf.getNanos();
        return Duration.ofSeconds(seconds, nanos);
    }
}
