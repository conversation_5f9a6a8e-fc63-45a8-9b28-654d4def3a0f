package global.symbio.billing.core.services.grpc.codec.temporal;

import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.message.MonthDayMessage;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

import java.time.MonthDay;


@Singleton
public class MonthDayCodec implements GRPCCodec<MonthDay, MonthDayMessage> {

    @Nonnull
    @Override
    public MonthDayMessage encode(@Nonnull MonthDay bean) {
        return MonthDayMessage.newBuilder()
            .setMonth(bean.getMonthValue())
            .setDay(bean.getDayOfMonth())
            .build();
    }

    @Nonnull
    @Override
    public MonthDay decode(@Nonnull MonthDayMessage protobuf) {
        final var month = protobuf.getMonth();
        final var day = protobuf.getDay();
        return MonthDay.of(month, day);
    }
}
