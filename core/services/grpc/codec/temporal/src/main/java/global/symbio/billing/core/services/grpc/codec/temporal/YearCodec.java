package global.symbio.billing.core.services.grpc.codec.temporal;

import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.message.YearMessage;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

import java.time.Year;


@Singleton
public class YearCodec implements GRPCCodec<Year, YearMessage> {

    @Nonnull
    @Override
    public YearMessage encode(@Nonnull Year bean) {
        return YearMessage.newBuilder()
            .setYear(bean.getValue())
            .build();
    }

    @Nonnull
    @Override
    public Year decode(@Nonnull YearMessage protobuf) {
        final var year = protobuf.getYear();
        return Year.of(year);
    }
}
