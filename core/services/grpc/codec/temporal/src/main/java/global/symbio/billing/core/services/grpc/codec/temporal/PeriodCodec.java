package global.symbio.billing.core.services.grpc.codec.temporal;

import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.message.PeriodMessage;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

import java.time.Period;

@Singleton
public class PeriodCodec implements GRPCCodec<Period, PeriodMessage> {

    @Nonnull
    @Override
    public PeriodMessage encode(@Nonnull Period bean) {
        return PeriodMessage.newBuilder()
            .setYears(bean.getYears())
            .setMonths(bean.getMonths())
            .setDays(bean.getDays())
            .build();
    }

    @Nonnull
    @Override
    public Period decode(@Nonnull PeriodMessage protobuf) {
        final var years = protobuf.getYears();
        final var months = protobuf.getMonths();
        final var days = protobuf.getDays();
        return Period.of(years, months, days);
    }
}
