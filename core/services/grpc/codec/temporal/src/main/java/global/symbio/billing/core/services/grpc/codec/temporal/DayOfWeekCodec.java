package global.symbio.billing.core.services.grpc.codec.temporal;

import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.message.DayOfWeekMessage;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

import java.time.DayOfWeek;


@Singleton
public class DayOfWeekCodec implements GRPCCodec<DayOfWeek, DayOfWeekMessage> {

    @Nonnull
    @Override
    public DayOfWeekMessage encode(@Nonnull DayOfWeek bean) {
        return DayOfWeekMessage.newBuilder()
            .setValue(bean.getValue())
            .build();
    }

    @Nonnull
    @Override
    public DayOfWeek decode(@Nonnull DayOfWeekMessage protobuf) {
        final var value = protobuf.getValue();
        return DayOfWeek.of(value);
    }
}
