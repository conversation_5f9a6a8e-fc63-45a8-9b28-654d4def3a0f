package global.symbio.billing.core.services.grpc.codec.temporal;

import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.message.InstantMessage;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

import java.time.Instant;


@Singleton
public class InstantCodec implements GRPCCodec<Instant, InstantMessage> {

    @Nonnull
    @Override
    public InstantMessage encode(@Nonnull Instant bean) {
        return InstantMessage.newBuilder()
            .setSeconds(bean.getEpochSecond())
            .setNanos(bean.getNano())
            .build();
    }

    @Nonnull
    @Override
    public Instant decode(@Nonnull InstantMessage protobuf) {
        final var seconds = protobuf.getSeconds();
        final var nanos = protobuf.getNanos();
        return Instant.ofEpochSecond(seconds, nanos);
    }
}
