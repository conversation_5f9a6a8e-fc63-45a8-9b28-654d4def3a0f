package global.symbio.billing.core.services.grpc.codec.temporal;

import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.message.LocalDateMessage;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

import java.time.LocalDate;

@Singleton
public class LocalDateCodec implements GRPCCodec<LocalDate, LocalDateMessage> {

    @Nonnull
    @Override
    public LocalDateMessage encode(@Nonnull LocalDate bean) {
        return LocalDateMessage.newBuilder()
            .setYear(bean.getYear())
            .setMonth(bean.getMonthValue())
            .setDay(bean.getDayOfMonth())
            .build();
    }

    @Nonnull
    @Override
    public LocalDate decode(@Nonnull LocalDateMessage protobuf) {
        final var year = protobuf.getYear();
        final var month = protobuf.getMonth();
        final var day = protobuf.getDay();
        return LocalDate.of(year, month, day);
    }
}
