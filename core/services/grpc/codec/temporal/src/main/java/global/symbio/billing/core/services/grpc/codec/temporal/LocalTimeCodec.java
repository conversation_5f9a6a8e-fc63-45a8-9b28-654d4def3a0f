package global.symbio.billing.core.services.grpc.codec.temporal;

import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.message.LocalTimeMessage;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

import java.time.LocalTime;

@Singleton
public class LocalTimeCodec implements GRPCCodec<LocalTime, LocalTimeMessage> {

    @Nonnull
    @Override
    public LocalTimeMessage encode(@Nonnull LocalTime bean) {
        return LocalTimeMessage.newBuilder()
            .setHour(bean.getHour())
            .setMinute(bean.getMinute())
            .setSecond(bean.getSecond())
            .setNano(bean.getNano())
            .build();
    }

    @Nonnull
    @Override
    public LocalTime decode(@Nonnull LocalTimeMessage protobuf) {
        final var hour = protobuf.getHour();
        final var minute = protobuf.getMinute();
        final var second = protobuf.getSecond();
        final var nano = protobuf.getNano();
        return LocalTime.of(hour, minute, second, nano);
    }
}