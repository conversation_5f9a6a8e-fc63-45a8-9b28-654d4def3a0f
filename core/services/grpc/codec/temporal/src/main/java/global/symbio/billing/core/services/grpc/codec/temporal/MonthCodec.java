package global.symbio.billing.core.services.grpc.codec.temporal;

import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.message.MonthMessage;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

import java.time.Month;


@Singleton
public class MonthCodec implements GRPCCodec<Month, MonthMessage> {

    @Nonnull
    @Override
    public MonthMessage encode(@Nonnull Month bean) {
        return MonthMessage.newBuilder()
            .setValue(bean.getValue())
            .build();
    }

    @Nonnull
    @Override
    public Month decode(@Nonnull MonthMessage protobuf) {
        final var value = protobuf.getValue();
        return Month.of(value);
    }
}
