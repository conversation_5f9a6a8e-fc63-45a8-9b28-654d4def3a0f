package global.symbio.billing.core.services.grpc.codec.temporal;

import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.message.YearMonthMessage;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

import java.time.YearMonth;


@Singleton
public class YearMonthCodec implements GRPCCodec<YearMonth, YearMonthMessage> {

    @Nonnull
    @Override
    public YearMonthMessage encode(@Nonnull YearMonth bean) {
        return YearMonthMessage.newBuilder()
            .setYear(bean.getYear())
            .setMonth(bean.getMonthValue())
            .build();
    }

    @Nonnull
    @Override
    public YearMonth decode(@Nonnull YearMonthMessage protobuf) {
        final var year = protobuf.getYear();
        final var month = protobuf.getMonth();
        return YearMonth.of(year, month);
    }
}
