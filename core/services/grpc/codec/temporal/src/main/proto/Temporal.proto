syntax = "proto3";
option java_package = "global.symbio.billing.core.services.grpc.message";
option java_multiple_files = true;

message LocalDateMessage {
  int32 year = 1;
  uint32 month = 2;
  uint32 day = 3;
}

message LocalTimeMessage {
  uint32 hour = 1;
  uint32 minute = 2;
  uint32 second = 3;
  uint32 nano = 4;
}

message LocalDateTimeMessage {
  LocalDateMessage date = 1;
  LocalTimeMessage time = 2;
}

message DurationMessage {
  int64 seconds = 1;
  uint32 nanos = 2;
}

message InstantMessage {
  int64 seconds = 1;
  uint32 nanos = 2;
}

message PeriodMessage {
  int32 years = 1;
  int32 months = 2;
  int32 days = 3;
}

message DayOfWeekMessage {
  uint32 value = 1;
}

message MonthMessage {
  uint32 value = 1;
}

message MonthDayMessage {
  uint32 month = 1;
  uint32 day = 2;
}

message YearMessage {
  int32 year = 1;
}

message YearMonthMessage {
  int32 year = 1;
  uint32 month = 2;
}

message ZoneIdMessage {
  string id = 1;
}

message ZoneOffsetMessage {
  string id = 1;
}

message ZonedDateTimeMessage {
  LocalDateTimeMessage datetime = 1;
  ZoneOffsetMessage offset = 2;
  ZoneIdMessage zone = 3;
}

message OffsetDateTimeMessage {
  LocalDateTimeMessage datetime = 1;
  ZoneOffsetMessage offset = 2;
}

message OffsetTimeMessage {
  LocalTimeMessage time = 1;
  ZoneOffsetMessage offset = 2;
}