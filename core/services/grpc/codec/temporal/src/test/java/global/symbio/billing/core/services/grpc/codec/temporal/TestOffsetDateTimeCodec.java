package global.symbio.billing.core.services.grpc.codec.temporal;

import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.message.LocalDateTimeMessage;
import global.symbio.billing.core.services.grpc.message.OffsetDateTimeMessage;
import global.symbio.billing.core.services.grpc.message.ZoneOffsetMessage;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;

public class TestOffsetDateTimeCodec {

    private Codec<LocalDateTime, LocalDateTimeMessage> datetime;

    private Codec<ZoneOffset, ZoneOffsetMessage> offset;

    private Codec<OffsetDateTime, OffsetDateTimeMessage> codec;

    @BeforeEach
    public void setup() {
        datetime = new LocalDateTimeCodec(new LocalDateCodec(), new LocalTimeCodec());
        offset = new ZoneOffsetCodec();
        codec = new OffsetDateTimeCodec(datetime, offset);
    }

    @Test
    @DisplayName("OffsetDateTimeCodec::new rejects null constructor arguments")
    public void offset_date_time_codec_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new OffsetDateTimeCodec(null, offset));
        assertThrows(NullPointerException.class, () -> new OffsetDateTimeCodec(datetime, null));
    }

    @ParameterizedTest
    @MethodSource("timestamps")
    @DisplayName("OffsetDateTimeCodec::encode bean to protobuf")
    public void codec_bean_to_protobuf(final OffsetDateTime bean) {
        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));
        assertEquals(bean.toLocalDateTime(), datetime.decode(protobuf.getDatetime()));
        assertEquals(bean.getOffset(), offset.decode(protobuf.getOffset()));
    }

    @ParameterizedTest
    @MethodSource("timestamps")
    @DisplayName("OffsetDateTimeCodec::decode protobuf to bean")
    public void codec_protobuf_to_bean(final OffsetDateTime source) {
        final var protobuf = OffsetDateTimeMessage.newBuilder().setDatetime(datetime.encode(source.toLocalDateTime())).setOffset(offset.encode(source.getOffset())).build();
        final var bean = assertDoesNotThrow(() -> codec.decode(protobuf));
        assertEquals(datetime.decode(protobuf.getDatetime()), bean.toLocalDateTime());
        assertEquals(offset.decode(protobuf.getOffset()), bean.getOffset());
        assertEquals(source, bean);
    }

    @Nonnull
    private static Stream<Arguments> timestamps() {
        return Stream.of(
            OffsetDateTime.ofInstant(Instant.EPOCH, ZoneOffset.MIN),
            OffsetDateTime.now(),
            OffsetDateTime.now(ZoneOffset.UTC),
            OffsetDateTime.ofInstant(Instant.EPOCH, ZoneOffset.MAX)
        ).map(Arguments::of);
    }
}
