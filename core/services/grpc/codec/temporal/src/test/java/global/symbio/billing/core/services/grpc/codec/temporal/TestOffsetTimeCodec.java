package global.symbio.billing.core.services.grpc.codec.temporal;

import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.message.LocalTimeMessage;
import global.symbio.billing.core.services.grpc.message.OffsetTimeMessage;
import global.symbio.billing.core.services.grpc.message.ZoneOffsetMessage;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.time.Instant;
import java.time.LocalTime;
import java.time.OffsetTime;
import java.time.ZoneOffset;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;

public class TestOffsetTimeCodec {

    private Codec<LocalTime, LocalTimeMessage> time;

    private Codec<ZoneOffset, ZoneOffsetMessage> offset;

    private Codec<OffsetTime, OffsetTimeMessage> codec;

    @BeforeEach
    public void setup() {
        time = new LocalTimeCodec();
        offset = new ZoneOffsetCodec();
        codec = new OffsetTimeCodec(time, offset);
    }

    @Test
    @DisplayName("OffsetTimeCodec::new rejects null constructor arguments")
    public void offset_time_codec_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new OffsetTimeCodec(null, offset));
        assertThrows(NullPointerException.class, () -> new OffsetTimeCodec(time, null));
    }

    @ParameterizedTest
    @MethodSource("timestamps")
    @DisplayName("OffsetTimeCodec::encode bean to protobuf")
    public void codec_bean_to_protobuf(final OffsetTime bean) {
        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));
        assertEquals(bean.toLocalTime(), time.decode(protobuf.getTime()));
        assertEquals(bean.getOffset(), offset.decode(protobuf.getOffset()));
    }

    @ParameterizedTest
    @MethodSource("timestamps")
    @DisplayName("OffsetTimeCodec::decode protobuf to bean")
    public void codec_protobuf_to_bean(final OffsetTime source) {
        final var protobuf = OffsetTimeMessage.newBuilder().setTime(time.encode(source.toLocalTime())).setOffset(offset.encode(source.getOffset())).build();
        final var bean = assertDoesNotThrow(() -> codec.decode(protobuf));
        assertEquals(time.decode(protobuf.getTime()), bean.toLocalTime());
        assertEquals(offset.decode(protobuf.getOffset()), bean.getOffset());
        assertEquals(source, bean);
    }

    @Nonnull
    private static Stream<Arguments> timestamps() {
        return Stream.of(
            OffsetTime.ofInstant(Instant.EPOCH, ZoneOffset.MIN),
            OffsetTime.now(),
            OffsetTime.now(ZoneOffset.UTC),
            OffsetTime.ofInstant(Instant.EPOCH, ZoneOffset.MAX)
        ).map(Arguments::of);
    }
}
