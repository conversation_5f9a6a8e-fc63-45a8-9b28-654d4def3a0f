package global.symbio.billing.core.services.grpc.codec.temporal;

import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.message.PeriodMessage;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.time.Period;
import java.util.List;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class TestPeriodCodec {

    private Codec<Period, PeriodMessage> codec;

    @BeforeEach
    public void setup() {
        codec = new PeriodCodec();
    }

    @ParameterizedTest
    @MethodSource("periods")
    @DisplayName("PeriodCodec::encode bean to protobuf")
    public void codec_bean_to_protobuf(final Period bean) {
        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));
        assertEquals(bean.getYears(), protobuf.getYears());
        assertEquals(bean.getMonths(), protobuf.getMonths());
        assertEquals(bean.getDays(), protobuf.getDays());
    }

    @ParameterizedTest
    @MethodSource("periods")
    @DisplayName("PeriodCodec::decode protobuf to bean")
    public void codec_protobuf_to_bean(final Period source) {
        final var protobuf = PeriodMessage.newBuilder().setYears(source.getYears()).setMonths(source.getMonths()).setDays(source.getDays()).build();
        final var bean = assertDoesNotThrow(() -> codec.decode(protobuf));
        assertEquals(protobuf.getYears(), bean.getYears());
        assertEquals(protobuf.getMonths(), bean.getMonths());
        assertEquals(protobuf.getDays(), bean.getDays());
        assertEquals(source, bean);
    }

    @Nonnull
    private static Stream<Arguments> periods() {
        final var periods = List.of(
            Period.ZERO,
            Period.ofDays(42),
            Period.ofMonths(69),
            Period.ofYears(420),
            Period.ofWeeks(1337),
            Period.of(42, 69, 420)
        );
        final var stream = Stream.concat(periods.stream(), periods.stream().map(Period::negated));
        return stream.map(Arguments::of);
    }
}
