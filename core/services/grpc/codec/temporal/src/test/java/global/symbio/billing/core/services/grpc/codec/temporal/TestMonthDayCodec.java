package global.symbio.billing.core.services.grpc.codec.temporal;

import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.message.MonthDayMessage;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.time.Month;
import java.time.MonthDay;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class TestMonthDayCodec {

    private Codec<MonthDay, MonthDayMessage> codec;

    @BeforeEach
    public void setup() {
        codec = new MonthDayCodec();
    }

    @ParameterizedTest
    @MethodSource("values")
    @DisplayName("MonthDayCodec::encode bean to protobuf")
    public void codec_bean_to_protobuf(final MonthDay bean) {
        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));
        assertEquals(bean.getMonthValue(), protobuf.getMonth());
        assertEquals(bean.getDayOfMonth(), protobuf.getDay());
    }

    @ParameterizedTest
    @MethodSource("values")
    @DisplayName("MonthDayCodec::decode protobuf to bean")
    public void codec_protobuf_to_bean(final MonthDay source) {
        final var protobuf = MonthDayMessage.newBuilder().setMonth(source.getMonthValue()).setDay(source.getDayOfMonth()).build();
        final var bean = assertDoesNotThrow(() -> codec.decode(protobuf));
        assertEquals(protobuf.getMonth(), bean.getMonthValue());
        assertEquals(protobuf.getDay(), bean.getDayOfMonth());
        assertEquals(source, bean);
    }

    @Nonnull
    private static Stream<Arguments> values() {
        return Stream.of(
            MonthDay.of(Month.JANUARY, 1),
            MonthDay.of(Month.FEBRUARY, 28),
            MonthDay.of(Month.AUGUST, 18)
        ).map(Arguments::of);
    }
}
