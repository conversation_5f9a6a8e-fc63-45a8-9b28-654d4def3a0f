package global.symbio.billing.core.services.grpc.codec.temporal;

import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.message.ZoneOffsetMessage;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.time.ZoneOffset;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class TestZoneOffsetCodec {

    private Codec<ZoneOffset, ZoneOffsetMessage> codec;

    @BeforeEach
    public void setup() {
        codec = new ZoneOffsetCodec();
    }

    @ParameterizedTest
    @MethodSource("offsets")
    @DisplayName("ZoneOffsetCodec::encode bean to protobuf")
    public void codec_bean_to_protobuf(final ZoneOffset bean) {
        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));
        assertEquals(bean.getId(), protobuf.getId());
    }

    @ParameterizedTest
    @MethodSource("offsets")
    @DisplayName("ZoneOffsetCodec::decode protobuf to bean")
    public void codec_protobuf_to_bean(final ZoneOffset source) {
        final var protobuf = ZoneOffsetMessage.newBuilder().setId(source.getId()).build();
        final var bean = assertDoesNotThrow(() -> codec.decode(protobuf));
        assertEquals(protobuf.getId(), bean.getId());
        assertEquals(source, bean);
    }

    @Nonnull
    private static Stream<Arguments> offsets() {
        return Stream.of(
            ZoneOffset.MIN,
            ZoneOffset.UTC,
            ZoneOffset.MAX
        ).map(Arguments::of);
    }
}
