package global.symbio.billing.core.services.grpc.codec.temporal;

import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.message.ZoneIdMessage;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class TestZoneIdCodec {

    private Codec<ZoneId, ZoneIdMessage> codec;

    @BeforeEach
    public void setup() {
        codec = new ZoneIdCodec();
    }

    @ParameterizedTest
    @MethodSource("offsets")
    @DisplayName("ZoneIdCodec::encode bean to protobuf")
    public void codec_bean_to_protobuf(final ZoneId bean) {
        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));
        assertEquals(bean.getId(), protobuf.getId());
    }

    @ParameterizedTest
    @MethodSource("offsets")
    @DisplayName("ZoneIdCodec::decode protobuf to bean")
    public void codec_protobuf_to_bean(final ZoneId source) {
        final var protobuf = ZoneIdMessage.newBuilder().setId(source.getId()).build();
        final var bean = assertDoesNotThrow(() -> codec.decode(protobuf));
        assertEquals(protobuf.getId(), bean.getId());
        assertEquals(source, bean);
    }

    @Nonnull
    private static Stream<Arguments> offsets() {
        final var zones = ZoneId.getAvailableZoneIds().stream().map(ZoneId::of);
        final var defaults = Stream.of(
            ZoneOffset.MIN,
            ZoneOffset.UTC,
            ZoneOffset.systemDefault(),
            ZoneOffset.MAX
        );
        return Stream.concat(defaults, zones).limit(10).map(Arguments::of);
    }
}
