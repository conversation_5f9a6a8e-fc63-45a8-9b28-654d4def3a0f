package global.symbio.billing.core.services.grpc.codec.temporal;

import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.message.LocalTimeMessage;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.time.LocalTime;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class TestLocalTimeCodec {

    private Codec<LocalTime, LocalTimeMessage> codec;

    @BeforeEach
    public void setup() {
        codec = new LocalTimeCodec();
    }

    @ParameterizedTest
    @MethodSource("times")
    @DisplayName("LocalTimeCodec::encode bean to protobuf")
    public void codec_bean_to_protobuf(final LocalTime bean) {
        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));
        assertEquals(bean.getHour(), protobuf.getHour());
        assertEquals(bean.getMinute(), protobuf.getMinute());
        assertEquals(bean.getSecond(), protobuf.getSecond());
        assertEquals(bean.getNano(), protobuf.getNano());
    }

    @ParameterizedTest
    @MethodSource("times")
    @DisplayName("LocalTimeCodec::decode protobuf to bean")
    public void codec_protobuf_to_bean(final LocalTime source) {
        final var protobuf = LocalTimeMessage.newBuilder().setHour(source.getHour()).setMinute(source.getMinute()).setSecond(source.getSecond()).setNano(source.getNano()).build();
        final var bean = assertDoesNotThrow(() -> codec.decode(protobuf));
        assertEquals(protobuf.getHour(), bean.getHour());
        assertEquals(protobuf.getMinute(), bean.getMinute());
        assertEquals(protobuf.getSecond(), bean.getSecond());
        assertEquals(protobuf.getNano(), bean.getNano());
        assertEquals(source, bean);
    }

    @Nonnull
    private static Stream<Arguments> times() {
        return Stream.of(LocalTime.MIN, LocalTime.MIDNIGHT, LocalTime.NOON, LocalTime.now(), LocalTime.MAX).map(Arguments::of);
    }
}
