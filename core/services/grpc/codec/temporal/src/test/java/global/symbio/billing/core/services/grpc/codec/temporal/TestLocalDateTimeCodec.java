package global.symbio.billing.core.services.grpc.codec.temporal;

import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.message.LocalDateMessage;
import global.symbio.billing.core.services.grpc.message.LocalDateTimeMessage;
import global.symbio.billing.core.services.grpc.message.LocalTimeMessage;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;

public class TestLocalDateTimeCodec {

    private Codec<LocalDate, LocalDateMessage> date;
    private Codec<LocalTime, LocalTimeMessage> time;
    private Codec<LocalDateTime, LocalDateTimeMessage> codec;

    @BeforeEach
    public void setup() {
        date = new LocalDateCodec();
        time = new LocalTimeCodec();
        codec = new LocalDateTimeCodec(date, time);
    }

    @Test
    @DisplayName("LocalDateTimeCodec::new rejects null constructor arguments")
    public void local_date_time_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new LocalDateTimeCodec(null, time));
        assertThrows(NullPointerException.class, () -> new LocalDateTimeCodec(date, null));
    }

    @ParameterizedTest
    @MethodSource("datetimes")
    @DisplayName("LocalDateTimeCodec::encode bean to protobuf")
    public void codec_bean_to_protobuf(final LocalDateTime bean) {
        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));
        assertEquals(bean.toLocalDate(), date.decode(protobuf.getDate()));
        assertEquals(bean.toLocalTime(), time.decode(protobuf.getTime()));
    }

    @ParameterizedTest
    @MethodSource("datetimes")
    @DisplayName("LocalDateTimeCodec::decode protobuf to bean")
    public void codec_protobuf_to_bean(final LocalDateTime source) {
        final var protobuf = LocalDateTimeMessage.newBuilder().setDate(date.encode(source.toLocalDate())).setTime(time.encode(source.toLocalTime())).build();
        final var bean = assertDoesNotThrow(() -> codec.decode(protobuf));
        assertEquals(source, bean);
    }

    @Nonnull
    private static Stream<Arguments> datetimes() {
        return Stream.of(LocalDateTime.MIN, LocalDateTime.now(), LocalDateTime.MAX).map(Arguments::of);
    }
}
