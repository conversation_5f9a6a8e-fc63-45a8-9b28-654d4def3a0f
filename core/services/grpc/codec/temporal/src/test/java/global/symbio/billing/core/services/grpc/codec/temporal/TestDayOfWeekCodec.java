package global.symbio.billing.core.services.grpc.codec.temporal;

import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.message.DayOfWeekMessage;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.ValueSource;

import java.time.DateTimeException;
import java.time.DayOfWeek;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;

public class TestDayOfWeekCodec {

    private Codec<DayOfWeek, DayOfWeekMessage> codec;

    @BeforeEach
    public void setup() {
        codec = new DayOfWeekCodec();
    }

    @ParameterizedTest
    @MethodSource("days")
    @DisplayName("DayOfWeekCodec::encode bean to protobuf")
    public void codec_bean_to_protobuf(final DayOfWeek bean) {
        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));
        assertEquals(bean.getValue(), protobuf.getValue());
    }

    @ParameterizedTest
    @MethodSource("days")
    @DisplayName("DayOfWeekCodec::decode protobuf to bean")
    public void codec_protobuf_to_bean(final DayOfWeek source) {
        final var protobuf = DayOfWeekMessage.newBuilder().setValue(source.getValue()).build();
        final var bean = assertDoesNotThrow(() -> codec.decode(protobuf));
        assertEquals(protobuf.getValue(), bean.getValue());
        assertEquals(source, bean);
    }

    @ParameterizedTest
    @ValueSource(ints = {-1, 0, 8})
    @DisplayName("DayOfWeekCodec::decode protobuf to bean")
    public void codec_protobuf_to_bean(final int value) {
        final var protobuf = DayOfWeekMessage.newBuilder().setValue(value).build();
        assertThrows(DateTimeException.class, () -> codec.decode(protobuf));
    }

    @Nonnull
    private static Stream<Arguments> days() {
        return Stream.of(DayOfWeek.values()).map(Arguments::of);
    }
}
