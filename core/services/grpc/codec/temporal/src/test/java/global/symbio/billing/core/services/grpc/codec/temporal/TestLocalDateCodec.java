package global.symbio.billing.core.services.grpc.codec.temporal;

import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.message.LocalDateMessage;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.time.LocalDate;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class TestLocalDateCodec {

    private Codec<LocalDate, LocalDateMessage> codec;

    @BeforeEach
    public void setup() {
        codec = new LocalDateCodec();
    }

    @ParameterizedTest
    @MethodSource("dates")
    @DisplayName("LocalDateCodec::encode bean to protobuf")
    public void codec_bean_to_protobuf(final LocalDate bean) {
        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));
        assertEquals(bean.getYear(), protobuf.getYear());
        assertEquals(bean.getMonthValue(), protobuf.getMonth());
        assertEquals(bean.getDayOfMonth(), protobuf.getDay());
    }

    @ParameterizedTest
    @MethodSource("dates")
    @DisplayName("LocalDateCodec::decode protobuf to bean")
    public void codec_protobuf_to_bean(final LocalDate source) {
        final var protobuf = LocalDateMessage.newBuilder().setYear(source.getYear()).setMonth(source.getMonthValue()).setDay(source.getDayOfMonth()).build();
        final var bean = assertDoesNotThrow(() -> codec.decode(protobuf));
        assertEquals(protobuf.getYear(), bean.getYear());
        assertEquals(protobuf.getMonth(), bean.getMonthValue());
        assertEquals(protobuf.getDay(), bean.getDayOfMonth());
        assertEquals(source, bean);
    }

    @Nonnull
    private static Stream<Arguments> dates() {
        return Stream.of(LocalDate.MIN, LocalDate.EPOCH, LocalDate.now(), LocalDate.MAX).map(Arguments::of);
    }
}
