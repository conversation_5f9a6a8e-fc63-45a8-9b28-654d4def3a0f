package global.symbio.billing.core.services.grpc.codec.temporal;

import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.message.MonthMessage;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.ValueSource;

import java.time.DateTimeException;
import java.time.Month;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;

public class TestMonthCodec {

    private Codec<Month, MonthMessage> codec;

    @BeforeEach
    public void setup() {
        codec = new MonthCodec();
    }

    @ParameterizedTest
    @MethodSource("months")
    @DisplayName("MonthCodec::encode bean to protobuf")
    public void codec_bean_to_protobuf(final Month bean) {
        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));
        assertEquals(bean.getValue(), protobuf.getValue());
    }

    @ParameterizedTest
    @MethodSource("months")
    @DisplayName("MonthCodec::decode protobuf to bean")
    public void codec_protobuf_to_bean(final Month source) {
        final var protobuf = MonthMessage.newBuilder().setValue(source.getValue()).build();
        final var bean = assertDoesNotThrow(() -> codec.decode(protobuf));
        assertEquals(protobuf.getValue(), bean.getValue());
        assertEquals(source, bean);
    }

    @ParameterizedTest
    @ValueSource(ints = {-1, 0, 13})
    @DisplayName("MonthCodec::decode protobuf to bean")
    public void codec_protobuf_to_bean(final int value) {
        final var protobuf = MonthMessage.newBuilder().setValue(value).build();
        assertThrows(DateTimeException.class, () -> codec.decode(protobuf));
    }

    @Nonnull
    private static Stream<Arguments> months() {
        return Stream.of(Month.values()).map(Arguments::of);
    }
}
