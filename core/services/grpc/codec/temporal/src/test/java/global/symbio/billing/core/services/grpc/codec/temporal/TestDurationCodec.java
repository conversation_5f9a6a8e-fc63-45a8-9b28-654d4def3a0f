package global.symbio.billing.core.services.grpc.codec.temporal;

import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.message.DurationMessage;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.time.Duration;
import java.util.List;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class TestDurationCodec {

    private Codec<Duration, DurationMessage> codec;

    @BeforeEach
    public void setup() {
        codec = new DurationCodec();
    }

    @ParameterizedTest
    @MethodSource("durations")
    @DisplayName("DurationCodec::encode bean to protobuf")
    public void codec_bean_to_protobuf(final Duration bean) {
        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));
        assertEquals(bean.getSeconds(), protobuf.getSeconds());
        assertEquals(bean.getNano(), protobuf.getNanos());
    }

    @ParameterizedTest
    @MethodSource("durations")
    @DisplayName("DurationCodec::decode protobuf to bean")
    public void codec_protobuf_to_bean(final Duration source) {
        final var protobuf = DurationMessage.newBuilder().setSeconds(source.getSeconds()).setNanos(source.getNano()).build();
        final var bean = assertDoesNotThrow(() -> codec.decode(protobuf));
        assertEquals(protobuf.getSeconds(), bean.getSeconds());
        assertEquals(protobuf.getNanos(), bean.getNano());
        assertEquals(source, bean);
    }

    @Nonnull
    private static Stream<Arguments> durations() {
        final var durations = List.of(
            Duration.ZERO,
            Duration.ofNanos(123),
            Duration.ofMillis(42),
            Duration.ofSeconds(69),
            Duration.ofHours(420),
            Duration.ofDays(1337)
        );
        final var stream = Stream.concat(durations.stream(), durations.stream().map(Duration::negated));
        return stream.map(Arguments::of);
    }
}
