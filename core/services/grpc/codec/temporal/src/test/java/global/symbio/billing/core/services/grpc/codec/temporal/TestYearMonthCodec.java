package global.symbio.billing.core.services.grpc.codec.temporal;

import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.message.YearMonthMessage;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.time.Month;
import java.time.Year;
import java.time.YearMonth;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class TestYearMonthCodec {

    private Codec<YearMonth, YearMonthMessage> codec;

    @BeforeEach
    public void setup() {
        codec = new YearMonthCodec();
    }

    @ParameterizedTest
    @MethodSource("values")
    @DisplayName("YearMonthCodec::encode bean to protobuf")
    public void codec_bean_to_protobuf(final YearMonth bean) {
        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));
        assertEquals(bean.getYear(), protobuf.getYear());
        assertEquals(bean.getMonthValue(), protobuf.getMonth());
    }

    @ParameterizedTest
    @MethodSource("values")
    @DisplayName("YearMonthCodec::decode protobuf to bean")
    public void codec_protobuf_to_bean(final YearMonth source) {
        final var protobuf = YearMonthMessage.newBuilder().setYear(source.getYear()).setMonth(source.getMonthValue()).build();
        final var bean = assertDoesNotThrow(() -> codec.decode(protobuf));
        assertEquals(protobuf.getYear(), bean.getYear());
        assertEquals(protobuf.getMonth(), bean.getMonthValue());
        assertEquals(source, bean);
    }

    @Nonnull
    private static Stream<Arguments> values() {
        return Stream.of(
            YearMonth.of(Year.MIN_VALUE, Month.JANUARY),
            YearMonth.now(),
            YearMonth.of(Year.MAX_VALUE, Month.DECEMBER)
        ).map(Arguments::of);
    }
}
