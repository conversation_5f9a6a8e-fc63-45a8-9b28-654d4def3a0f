package global.symbio.billing.core.services.grpc.codec.temporal;

import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.message.InstantMessage;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.time.Instant;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class TestInstantCodec {

    private Codec<Instant, InstantMessage> codec;

    @BeforeEach
    public void setup() {
        codec = new InstantCodec();
    }

    @ParameterizedTest
    @MethodSource("instants")
    @DisplayName("InstantCodec::encode bean to protobuf")
    public void codec_bean_to_protobuf(final Instant bean) {
        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));
        assertEquals(bean.getEpochSecond(), protobuf.getSeconds());
        assertEquals(bean.getNano(), protobuf.getNanos());
    }

    @ParameterizedTest
    @MethodSource("instants")
    @DisplayName("InstantCodec::decode protobuf to bean")
    public void codec_protobuf_to_bean(final Instant source) {
        final var protobuf = InstantMessage.newBuilder().setSeconds(source.getEpochSecond()).setNanos(source.getNano()).build();
        final var bean = assertDoesNotThrow(() -> codec.decode(protobuf));
        assertEquals(protobuf.getSeconds(), bean.getEpochSecond());
        assertEquals(protobuf.getNanos(), bean.getNano());
        assertEquals(source, bean);
    }

    @Nonnull
    private static Stream<Arguments> instants() {
        return Stream.of(Instant.MIN, Instant.EPOCH, Instant.now(), Instant.MAX).map(Arguments::of);
    }
}
