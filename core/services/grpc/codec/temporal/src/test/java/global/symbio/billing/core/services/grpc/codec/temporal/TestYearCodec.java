package global.symbio.billing.core.services.grpc.codec.temporal;

import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.message.YearMessage;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.time.Year;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class TestYearCodec {

    private Codec<Year, YearMessage> codec;

    @BeforeEach
    public void setup() {
        codec = new YearCodec();
    }

    @ParameterizedTest
    @MethodSource("years")
    @DisplayName("YearCodec::encode bean to protobuf")
    public void codec_bean_to_protobuf(final Year bean) {
        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));
        assertEquals(bean.getValue(), protobuf.getYear());
    }

    @ParameterizedTest
    @MethodSource("years")
    @DisplayName("YearCodec::decode protobuf to bean")
    public void codec_protobuf_to_bean(final Year source) {
        final var protobuf = YearMessage.newBuilder().setYear(source.getValue()).build();
        final var bean = assertDoesNotThrow(() -> codec.decode(protobuf));
        assertEquals(protobuf.getYear(), bean.getValue());
        assertEquals(source, bean);
    }

    @Nonnull
    private static Stream<Arguments> years() {
        return Stream.of(
            Year.of(Year.MIN_VALUE),
            Year.now(),
            Year.of(Year.MAX_VALUE)
        ).map(Arguments::of);
    }
}
