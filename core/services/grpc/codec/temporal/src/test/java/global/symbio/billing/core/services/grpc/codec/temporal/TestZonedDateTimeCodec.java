package global.symbio.billing.core.services.grpc.codec.temporal;

import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.message.LocalDateTimeMessage;
import global.symbio.billing.core.services.grpc.message.ZoneIdMessage;
import global.symbio.billing.core.services.grpc.message.ZoneOffsetMessage;
import global.symbio.billing.core.services.grpc.message.ZonedDateTimeMessage;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.time.*;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;

public class TestZonedDateTimeCodec {

    private Codec<LocalDateTime, LocalDateTimeMessage> datetime;

    private Codec<ZoneOffset, ZoneOffsetMessage> offset;

    private Codec<ZoneId, ZoneIdMessage> zone;

    private Codec<ZonedDateTime, ZonedDateTimeMessage> codec;

    @BeforeEach
    public void setup() {
        datetime = new LocalDateTimeCodec(new LocalDateCodec(), new LocalTimeCodec());
        offset = new ZoneOffsetCodec();
        zone = new ZoneIdCodec();
        codec = new ZonedDateTimeCodec(datetime, offset, zone);
    }

    @Test
    @DisplayName("ZonedDateTimeCodec::new rejects null constructor arguments")
    public void zoned_date_time_codec_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new ZonedDateTimeCodec(null, offset, zone));
        assertThrows(NullPointerException.class, () -> new ZonedDateTimeCodec(datetime, null, zone));
        assertThrows(NullPointerException.class, () -> new ZonedDateTimeCodec(datetime, offset, null));
    }

    @ParameterizedTest
    @MethodSource("timestamps")
    @DisplayName("ZonedDateTimeCodec::encode bean to protobuf")
    public void codec_bean_to_protobuf(final ZonedDateTime bean) {
        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));
        assertEquals(bean.toLocalDateTime(), datetime.decode(protobuf.getDatetime()));
        assertEquals(bean.getOffset(), offset.decode(protobuf.getOffset()));
        assertEquals(bean.getZone(), zone.decode(protobuf.getZone()));
    }

    @ParameterizedTest
    @MethodSource("timestamps")
    @DisplayName("ZonedDateTimeCodec::decode protobuf to bean")
    public void codec_protobuf_to_bean(final ZonedDateTime source) {
        final var protobuf = ZonedDateTimeMessage.newBuilder().setDatetime(datetime.encode(source.toLocalDateTime())).setOffset(offset.encode(source.getOffset())).setZone(zone.encode(source.getZone())).build();
        final var bean = assertDoesNotThrow(() -> codec.decode(protobuf));
        assertEquals(datetime.decode(protobuf.getDatetime()), bean.toLocalDateTime());
        assertEquals(offset.decode(protobuf.getOffset()), bean.getOffset());
        assertEquals(zone.decode(protobuf.getZone()), bean.getZone());
        assertEquals(source, bean);
    }

    @Nonnull
    private static Stream<Arguments> timestamps() {
        return Stream.of(
            ZonedDateTime.ofInstant(Instant.EPOCH, ZoneOffset.MIN),
            ZonedDateTime.now(),
            ZonedDateTime.now(ZoneOffset.UTC),
            ZonedDateTime.ofInstant(Instant.EPOCH, ZoneOffset.MAX)
        ).map(Arguments::of);
    }
}
