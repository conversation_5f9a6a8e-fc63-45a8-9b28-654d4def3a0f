package global.symbio.billing.core.services.grpc.codec.decimal;

import com.google.protobuf.ByteString;
import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.message.BigDecimalMessage;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.MathContext;

@Singleton
public class BigDecimalCodec implements GRPCCodec<BigDecimal, BigDecimalMessage> {

    @Nonnull
    @Override
    public BigDecimalMessage encode(@Nonnull BigDecimal bean) {
        return BigDecimalMessage.newBuilder()
            .setScale(bean.scale())
            .setPrecision(bean.precision())
            .setValue(ByteString.copyFrom(bean.unscaledValue().toByteArray()))
            .build();
    }

    @Nonnull
    @Override
    public BigDecimal decode(@Nonnull BigDecimalMessage protobuf) {
        final var scale = protobuf.getScale();
        final var value = new BigInteger(protobuf.getValue().toByteArray());
        final var context = new MathContext(protobuf.getPrecision());
        return new BigDecimal(value, scale, context);
    }
}
