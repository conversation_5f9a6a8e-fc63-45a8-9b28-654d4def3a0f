package global.symbio.billing.core.services.grpc.codec.decimal;

import com.google.protobuf.ByteString;
import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.message.BigDecimalMessage;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.math.BigDecimal;
import java.math.MathContext;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;

public class TestBigDecimalCodec {

    private Codec<BigDecimal, BigDecimalMessage> codec;

    @BeforeEach
    public void setup() {
        codec = new BigDecimalCodec();
    }

    @ParameterizedTest
    @MethodSource("decimals")
    @DisplayName("BigDecimalCodec::encode bean to protobuf")
    public void codec_bean_to_protobuf(@Nonnull final BigDecimal bean) {
        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));
        assertEquals(bean.scale(), protobuf.getScale());
        assertEquals(bean.precision(), protobuf.getPrecision());
        assertArrayEquals(bean.unscaledValue().toByteArray(), protobuf.getValue().toByteArray());
    }

    @ParameterizedTest
    @MethodSource("decimals")
    @DisplayName("BigDecimalCodec::decode protobuf to bean")
    public void codec_protobuf_to_bean(@Nonnull final BigDecimal original) {
        final var protobuf = BigDecimalMessage.newBuilder()
            .setScale(original.scale())
            .setPrecision(original.precision())
            .setValue(ByteString.copyFrom(original.unscaledValue().toByteArray()))
            .build();
        final var bean = assertDoesNotThrow(() -> codec.decode(protobuf));
        assertEquals(original, bean);
    }

    @Nonnull
    private static Stream<Arguments> decimals() {
        final var decimals = Stream.of(BigDecimal.ZERO, BigDecimal.ONE, BigDecimal.TWO, BigDecimal.TEN, BigDecimal.TEN.pow(100), new BigDecimal(HUNDRED_PI_DIGITS, new MathContext(2048))).toList();
        return Stream.concat(decimals.stream(), decimals.stream().map(BigDecimal::negate)).map(Arguments::of);
    }

    private static final String HUNDRED_PI_DIGITS = "3."
        + "1415926535897932384626433832795028841971693993751" // 1 to 49
        + "05820974944592307816406286208998628034825342117067" // 50 to 99
        + "9" // 100
        ;
}
