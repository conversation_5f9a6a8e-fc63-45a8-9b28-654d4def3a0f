package global.symbio.billing.core.services.grpc.codec.reference;

import global.symbio.billing.core.reference.persistence.api.reference.Reference;
import global.symbio.billing.core.reference.persistence.api.reference.ReferenceType;
import global.symbio.billing.core.services.codec.uuid.UUIDCodec;
import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.message.ReferenceMessage;
import global.symbio.billing.core.services.grpc.message.ReferenceTypeMessage;
import global.symbio.billing.core.services.grpc.message.UUIDMessage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

public class TestReferenceCodec {

    private Codec<UUID, UUIDMessage> uuid;

    private Codec<ReferenceType, ReferenceTypeMessage> type;

    private Codec<Reference, ReferenceMessage> codec;

    @BeforeEach
    public void setUp() {
        uuid = new UUIDCodec();
        type = new ReferenceTypeCodec();
        codec = new ReferenceCodec(uuid, type);
    }

    @Test
    @DisplayName("ReferenceCodec::new rejects null parameters")
    public void codec_rejects_null_constructor_parameters() {
        assertThrows(NullPointerException.class, () -> new ReferenceCodec(null, type));
        assertThrows(NullPointerException.class, () -> new ReferenceCodec(uuid, null));
    }

    @Test
    @DisplayName("ReferenceCodec::encode bean to protobuf")
    public void codec_bean_to_protobuf() {
        final var bean = new GRPCReferenceDataAccessObject(UUID.randomUUID(), 1L, "MY", "CO", 2, ReferenceType.PAYMENT, "MYCOPYMT00000000012").entity();
        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));

        assertEquals(bean.getIdentifier(), uuid.decode(protobuf.getIdentifier()));
        assertEquals(bean.getSequence(), protobuf.getSequence());
        assertEquals(bean.getCountryCode(), protobuf.getCountryCode());
        assertEquals(bean.getBusinessUnit(), protobuf.getBusinessUnit());
        assertEquals(bean.getCheckDigit(), protobuf.getCheckDigit());
        assertEquals(bean.getType(), type.decode(protobuf.getType()));
        assertEquals(bean.getReference(), protobuf.getReference());
    }
    @Test
    @DisplayName("ReferenceCodec::decode protobuf to bean")
    public void codec_protobuf_to_bean() {
        final var identifier = UUID.randomUUID();
        final var sequence = 1L;
        final var countryCode = "MY";
        final var businessUnit = "CO";
        final var checkDigit = 2;
        final var type = ReferenceType.PAYMENT;
        final var referenceNumber = "MYCOPYMT00000000012";

        final var builder = ReferenceMessage.newBuilder()
            .setIdentifier(uuid.encode(identifier))
            .setSequence(sequence)
            .setCountryCode(countryCode)
            .setBusinessUnit(businessUnit)
            .setCheckDigit(checkDigit)
            .setType(this.type.encode(type))
            .setReference(referenceNumber);

        final var protobuf = builder.build();
        final var bean = assertDoesNotThrow(() -> codec.decode(protobuf));

        assertEquals(identifier, bean.getIdentifier());
        assertEquals(sequence, bean.getSequence());
        assertEquals(countryCode, bean.getCountryCode());
        assertEquals(businessUnit, bean.getBusinessUnit());
        assertEquals(checkDigit, bean.getCheckDigit());
        assertEquals(type, bean.getType());
        assertEquals(referenceNumber, bean.getReference());
    }

}
