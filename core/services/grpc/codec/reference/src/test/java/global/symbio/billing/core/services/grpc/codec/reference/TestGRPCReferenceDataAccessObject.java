package global.symbio.billing.core.services.grpc.codec.reference;

import global.symbio.billing.core.exception.sdk.BillingEntityUnmodifiableException;
import global.symbio.billing.core.reference.persistence.api.reference.Reference;
import global.symbio.billing.core.reference.persistence.api.reference.ReferenceType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

public class TestGRPCReferenceDataAccessObject {

    private static final UUID ID = UUID.randomUUID();
    private static final Long SEQUENCE = 1L;
    private static final String COUNTRY_CODE = "MY";
    private static final String BUSINESS_UNIT = "CO";
    private static final Integer CHECK_DIGIT = 6;
    private static final ReferenceType REFERENCE_TYPE = ReferenceType.PAYMENT;
    private static final String REFERENCE = "MYCOPYMT00000000016";

    private GRPCReferenceDataAccessObject reference;

    @BeforeEach
    public void setup() {
        reference = new GRPCReferenceDataAccessObject(ID, SEQUENCE, COUNTRY_CODE, BUSINESS_UNIT, CHECK_DIGIT, REFERENCE_TYPE, REFERENCE);
    }

    @Test
    @DisplayName("GRPCReferenceDataAccessObject::new rejects null constructor arguments")
    public void grpc_reference_data_access_object_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new GRPCReferenceDataAccessObject(null, SEQUENCE, COUNTRY_CODE, BUSINESS_UNIT, CHECK_DIGIT, REFERENCE_TYPE, REFERENCE));
        assertThrows(NullPointerException.class, () -> new GRPCReferenceDataAccessObject(ID, null, COUNTRY_CODE, BUSINESS_UNIT, CHECK_DIGIT, REFERENCE_TYPE, REFERENCE));
        assertThrows(NullPointerException.class, () -> new GRPCReferenceDataAccessObject(ID, SEQUENCE, null, BUSINESS_UNIT, CHECK_DIGIT, REFERENCE_TYPE, REFERENCE));
        assertThrows(NullPointerException.class, () -> new GRPCReferenceDataAccessObject(ID, SEQUENCE, COUNTRY_CODE, null, CHECK_DIGIT, REFERENCE_TYPE, REFERENCE));
        assertDoesNotThrow(() -> new GRPCReferenceDataAccessObject(ID, SEQUENCE, COUNTRY_CODE, BUSINESS_UNIT, null, REFERENCE_TYPE, REFERENCE));
        assertThrows(NullPointerException.class, () -> new GRPCReferenceDataAccessObject(ID, SEQUENCE, COUNTRY_CODE, BUSINESS_UNIT, CHECK_DIGIT, null, REFERENCE));
        assertDoesNotThrow(() -> new GRPCReferenceDataAccessObject(ID, SEQUENCE, COUNTRY_CODE, BUSINESS_UNIT, CHECK_DIGIT, REFERENCE_TYPE, null));
    }

    @Test
    @DisplayName("GRPCReferenceDataAccessObject::identifier is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_reference_data_access_object_identifier_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(Reference.class, ID, "identifier");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> reference.identifier(ID));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCReferenceDataAccessObject::sequence is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_reference_data_access_object_sequence_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(Reference.class, ID, "sequence");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> reference.sequence(SEQUENCE));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCReferenceDataAccessObject::countryCode is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_reference_data_access_object_country_code_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(Reference.class, ID, "countryCode");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> reference.countryCode(COUNTRY_CODE));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCReferenceDataAccessObject::businessUnit is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_reference_data_access_object_business_unit_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(Reference.class, ID, "businessUnit");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> reference.businessUnit(BUSINESS_UNIT));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCReferenceDataAccessObject::checkDigit is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_reference_data_access_object_check_digit_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(Reference.class, ID, "checkDigit");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> reference.checkDigit(CHECK_DIGIT));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCReferenceDataAccessObject::type is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_reference_data_access_object_type_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(Reference.class, ID, "type");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> reference.type(REFERENCE_TYPE));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCReferenceDataAccessObject::reference is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_reference_data_access_object_reference_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(Reference.class, ID, "reference");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> reference.reference(REFERENCE));
        assertEquals(expected.getMessage(), cause.getMessage());
    }
}
