package global.symbio.billing.core.services.grpc.codec.reference;

import global.symbio.billing.core.reference.persistence.api.reference.ReferenceType;
import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.message.ReferenceTypeMessage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class TestReferenceTypeCodec {

    private Codec<ReferenceType, ReferenceTypeMessage> codec;

    @BeforeEach
    public void setup() {
        codec = new ReferenceTypeCodec();
    }

    @ParameterizedTest
    @DisplayName("ReferenceTypeCodec::encode bean to protobuf")
    @MethodSource("states")
    public void codec_bean_to_protobuf(final ReferenceType bean) {
        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));
        assertEquals(bean, codec.decode(protobuf));
    }

    @ParameterizedTest
    @DisplayName("ReferenceTypeCodec::decode protobuf to bean")
    @MethodSource("messages")
    public void codec_protobuf_to_bean(final ReferenceTypeMessage message) {
        final var bean = assertDoesNotThrow(() -> codec.decode(message));
        assertEquals(message, codec.encode(bean));
    }

    private static Stream<Arguments> states() {
        return Stream.of(
            Arguments.of(ReferenceType.INVOICE),
            Arguments.of(ReferenceType.PAYMENT)
        );
    }

    private static Stream<Arguments> messages() {
        return Stream.of(
            Arguments.of(ReferenceTypeMessage.INVOICE_REFERENCE),
            Arguments.of(ReferenceTypeMessage.PAYMENT_REFERENCE)
        );
    }
}
