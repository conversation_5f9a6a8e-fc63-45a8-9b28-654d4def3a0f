package global.symbio.billing.core.services.grpc.codec.reference;

import global.symbio.billing.core.reference.persistence.api.reference.Reference;
import global.symbio.billing.core.reference.persistence.api.reference.ReferenceType;
import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.message.ReferenceMessage;
import global.symbio.billing.core.services.grpc.message.ReferenceTypeMessage;
import global.symbio.billing.core.services.grpc.message.UUIDMessage;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.util.Objects;
import java.util.UUID;

@Singleton
public class ReferenceCodec implements GRPCCodec<Reference, ReferenceMessage> {

    @Nonnull
    private final Codec<UUID, UUIDMessage> uuid;

    @Nonnull
    private final Codec<ReferenceType, ReferenceTypeMessage> type;

    @Inject
    public ReferenceCodec(
        @Nonnull Codec<UUID, UUIDMessage> uuid,
        @Nonnull Codec<ReferenceType, ReferenceTypeMessage> type
    ) {
        this.uuid = Objects.requireNonNull(uuid, "uuid");
        this.type = Objects.requireNonNull(type, "type");
    }

    @Nonnull
    @Override
    public ReferenceMessage encode(@Nonnull Reference bean) {
        final var reference = ReferenceMessage.newBuilder()
            .setIdentifier(uuid.encode(bean.getIdentifier()))
            .setSequence(bean.getSequence())
            .setCountryCode(bean.getCountryCode())
            .setBusinessUnit(bean.getBusinessUnit())
            .setType(type.encode(bean.getType()));

        if (bean.getCheckDigit() != null) {
            reference.setCheckDigit(bean.getCheckDigit());
        }
        if (bean.getReference() != null) {
            reference.setReference(bean.getReference());
        }

        return reference.build();
    }

    @Nonnull
    @Override
    public Reference decode(@Nonnull ReferenceMessage protobuf) {
        final var identifier = uuid.decode(protobuf.getIdentifier());
        final var sequence = protobuf.getSequence();
        final var countryCode = protobuf.getCountryCode();
        final var businessUnit = protobuf.getBusinessUnit();
        final var checkDigit = protobuf.hasCheckDigit() ? protobuf.getCheckDigit() : null;
        final var type = this.type.decode(protobuf.getType());
        final var reference = protobuf.hasReference() ? protobuf.getReference() : null;
        return new GRPCReferenceDataAccessObject(identifier, sequence, countryCode, businessUnit, checkDigit, type, reference).entity();
    }
}
