package global.symbio.billing.core.services.grpc.codec.reference;

import global.symbio.billing.core.exception.sdk.BillingEntityUnmodifiableException;
import global.symbio.billing.core.reference.persistence.api.reference.Reference;
import global.symbio.billing.core.reference.persistence.api.reference.ReferenceDataAccessObject;
import global.symbio.billing.core.reference.persistence.api.reference.ReferenceType;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.Getter;

import java.util.Objects;
import java.util.UUID;

@Getter
public final class GRPCReferenceDataAccessObject extends ReferenceDataAccessObject {

    @Nonnull
    private final UUID identifier;

    @Nonnull
    private final Long sequence;

    @Nonnull
    private final String countryCode;

    @Nonnull
    private final String businessUnit;

    @Nullable
    private final Integer checkDigit;

    @Nonnull
    private final ReferenceType type;

    @Nullable
    private final String reference;

    public GRPCReferenceDataAccessObject(
        @Nonnull UUID identifier,
        @Nonnull Long sequence,
        @Nonnull String countryCode,
        @Nonnull String businessUnit,
        @Nullable Integer checkDigit, 
        @Nonnull ReferenceType type,
        @Nullable String reference
    ) {
        this.identifier = Objects.requireNonNull(identifier, "identifier");
        this.sequence = Objects.requireNonNull(sequence, "sequence");
        this.countryCode = Objects.requireNonNull(countryCode, "countryCode");
        this.businessUnit = Objects.requireNonNull(businessUnit, "businessUnit");
        this.checkDigit = checkDigit;
        this.type = Objects.requireNonNull(type, "type");
        this.reference = reference;
    }

    @Nonnull
    @Override
    public ReferenceDataAccessObject identifier(@Nonnull UUID identifier) {
        throw new BillingEntityUnmodifiableException(Reference.class, this.identifier, "identifier");
    }

    @Nonnull
    @Override
    public ReferenceDataAccessObject sequence(@Nonnull Long sequence) {
        throw new BillingEntityUnmodifiableException(Reference.class, this.identifier, "sequence");
    }

    @Nonnull
    @Override
    public ReferenceDataAccessObject countryCode(@Nonnull String countryCode) {
        throw new BillingEntityUnmodifiableException(Reference.class, this.identifier, "countryCode");
    }

    @Nonnull
    @Override
    public ReferenceDataAccessObject businessUnit(@Nonnull String businessUnit) {
        throw new BillingEntityUnmodifiableException(Reference.class, this.identifier, "businessUnit");
    }

    @Nonnull
    @Override
    public ReferenceDataAccessObject checkDigit(@Nullable Integer checkDigit) {
        throw new BillingEntityUnmodifiableException(Reference.class, this.identifier, "checkDigit");
    }

    @Nonnull
    @Override
    public ReferenceDataAccessObject type(@Nullable ReferenceType type) {
        throw new BillingEntityUnmodifiableException(Reference.class, this.identifier, "type");
    }

    @Nonnull
    @Override
    public ReferenceDataAccessObject reference(@Nullable String reference) {
        throw new BillingEntityUnmodifiableException(Reference.class, this.identifier, "reference");
    }
}