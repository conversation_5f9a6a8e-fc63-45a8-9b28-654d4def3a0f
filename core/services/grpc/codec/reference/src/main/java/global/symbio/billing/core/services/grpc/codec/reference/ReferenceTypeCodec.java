package global.symbio.billing.core.services.grpc.codec.reference;

import global.symbio.billing.core.exception.sdk.BillingEntityNotFoundException;
import global.symbio.billing.core.reference.persistence.api.reference.ReferenceType;
import global.symbio.billing.core.services.grpc.codec.GRPCEnumCodec;
import global.symbio.billing.core.services.grpc.message.ReferenceTypeMessage;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

import static global.symbio.billing.core.reference.persistence.api.reference.ReferenceType.*;

@Singleton
public record ReferenceTypeCodec() implements GRPCEnumCodec<ReferenceType, ReferenceTypeMessage> {

    @Nonnull
    @Override
    public ReferenceTypeMessage encode(@Nonnull ReferenceType bean) {
        return switch (bean) {
            case INVOICE -> ReferenceTypeMessage.INVOICE_REFERENCE;
            case PAYMENT -> ReferenceTypeMessage.PAYMENT_REFERENCE;
            case  PAYMENT_CANCELLATION -> ReferenceTypeMessage.PAYMENT_CANCELLATION_REFERENCE;
        };
    }

    @Nonnull
    @Override
    public ReferenceType decode(@Nonnull ReferenceTypeMessage protobuf) {
        return switch (protobuf) {
            case ReferenceTypeMessage.INVOICE_REFERENCE -> INVOICE;
            case ReferenceTypeMessage.PAYMENT_REFERENCE -> PAYMENT;
            case ReferenceTypeMessage.PAYMENT_CANCELLATION_REFERENCE -> PAYMENT_CANCELLATION;
            default -> throw new BillingEntityNotFoundException(ReferenceType.class, protobuf);
        };
    }
}
