syntax = "proto3";
option java_package = "global.symbio.billing.core.services.grpc.message";
option java_multiple_files = true;

import "UUID.proto";

message ReferenceMessage {
  UUIDMessage identifier = 1;
  uint64 sequence = 2;
  string country_code = 3;
  string business_unit = 4;
  optional uint32 check_digit = 5;
  ReferenceTypeMessage type = 6;
  optional string reference = 7;
}

enum ReferenceTypeMessage {
  REFERENCE_TYPE_UNSPECIFIED = 0;
  INVOICE_REFERENCE = 1;
  PAYMENT_REFERENCE = 2;
  PAYMENT_CANCELLATION_REFERENCE = 3;
}