package global.symbio.billing.core.services.grpc.codec.currency;

import global.symbio.billing.core.currency.persistence.api.Currency;
import global.symbio.billing.core.currency.persistence.api.CurrencyDataAccessObject;
import global.symbio.billing.core.exception.sdk.BillingEntityUnmodifiableException;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.Getter;

import java.util.Objects;

@Getter
public class GRPCCurrencyDataAccessObject extends CurrencyDataAccessObject {

    @Nonnull
    private final Integer identifier;

    @Nonnull
    private final String code, description;

    @Nullable
    private final String symbol;

    public GRPCCurrencyDataAccessObject(@Nonnull Integer identifier, @Nonnull String code, @Nonnull String description, @Nullable String symbol) {
        this.identifier = Objects.requireNonNull(identifier, "identifier");
        this.code = Objects.requireNonNull(code, "code");
        this.description = Objects.requireNonNull(description, "description");
        this.symbol = symbol;
    }

    @Nonnull
    @Override
    public CurrencyDataAccessObject identifier(@Nonnull Integer identifier) {
        throw new BillingEntityUnmodifiableException(Currency.class, this.identifier, "identifier");
    }

    @Nonnull
    @Override
    public CurrencyDataAccessObject code(@Nonnull String code) {
        throw new BillingEntityUnmodifiableException(Currency.class, this.identifier, "code");
    }

    @Nonnull
    @Override
    public CurrencyDataAccessObject description(@Nonnull String description) {
        throw new BillingEntityUnmodifiableException(Currency.class, this.identifier, "description");
    }

    @Nonnull
    @Override
    public CurrencyDataAccessObject symbol(@Nullable String symbol) {
        throw new BillingEntityUnmodifiableException(Currency.class, this.identifier, "symbol");
    }
}
