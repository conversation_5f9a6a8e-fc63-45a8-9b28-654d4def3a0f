package global.symbio.billing.core.services.grpc.codec.currency;

import global.symbio.billing.core.currency.persistence.api.Currency;
import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.message.CurrencyMessage;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

@Singleton
public class CurrencyCodec implements GRPCCodec<Currency, CurrencyMessage> {

    @Nonnull
    @Override
    public CurrencyMessage encode(@Nonnull Currency bean) {
        return CurrencyMessage.newBuilder()
            .setId(bean.getIdentifier())
            .setCode(bean.getCode())
            .setDescription(bean.getDescription())
            .setSymbol(bean.getSymbol())
            .build();
    }

    @Nonnull
    @Override
    public Currency decode(@Nonnull CurrencyMessage protobuf) {
        final var identifier = protobuf.getId();
        final var code = protobuf.getCode();
        final var description = protobuf.getDescription();
        final var symbol = protobuf.hasSymbol() ? protobuf.getSymbol() : null;
        return new GRPCCurrencyDataAccessObject(identifier, code, description, symbol).entity();
    }
}