package global.symbio.billing.core.services.grpc.codec.currency;

import global.symbio.billing.core.currency.persistence.api.Currency;
import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.message.CurrencyMessage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class TestCurrencyCodec {

    private GRPCCodec<Currency, CurrencyMessage> codec;

    @BeforeEach
    public void setup() {
        codec = new CurrencyCodec();
    }

    @Test
    @DisplayName("CurrencyCodec::encode bean to protobuf")
    public void codec_bean_to_protobuf() {
        final var bean = new GRPCCurrencyDataAccessObject(1, "AUD", "Australian Dollar", "$").entity();
        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));
        assertEquals(bean.getIdentifier(), protobuf.getId());
        assertEquals(bean.getCode(), protobuf.getCode());
        assertEquals(bean.getDescription(), protobuf.getDescription());
        assertEquals(bean.getSymbol(), protobuf.getSymbol());
    }

    @Test
    @DisplayName("CurrencyCodec::decode protobuf to bean")
    public void codec_protobuf_to_bean() {
        final var protobuf = CurrencyMessage.newBuilder().setId(1).setCode("AUD").setDescription("Australian Dollar").setSymbol("$").build();
        final var bean = assertDoesNotThrow(() -> codec.decode(protobuf));
        assertEquals(protobuf.getId(), bean.getIdentifier());
        assertEquals(protobuf.getCode(), bean.getCode());
        assertEquals(protobuf.getDescription(), bean.getDescription());
        assertEquals(protobuf.getSymbol(), bean.getSymbol());
    }
}
