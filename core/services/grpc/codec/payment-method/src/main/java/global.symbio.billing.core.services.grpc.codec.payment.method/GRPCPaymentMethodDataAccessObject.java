package global.symbio.billing.core.services.grpc.codec.payment.method;

import global.symbio.billing.core.exception.sdk.BillingEntityUnmodifiableException;
import global.symbio.billing.core.payment.persistence.api.payment.method.PaymentMethod;
import global.symbio.billing.core.payment.persistence.api.payment.method.PaymentMethodDataAccessObject;
import jakarta.annotation.Nonnull;
import lombok.Getter;

import java.util.Objects;

@Getter
public class GRPCPaymentMethodDataAccessObject extends PaymentMethodDataAccessObject {

    @Nonnull
    private final Integer identifier;

    @Nonnull
    private final String name;

    @Nonnull
    private final String displayName;

    public GRPCPaymentMethodDataAccessObject(
        @Nonnull Integer identifier,
        @Nonnull String name,
        @Nonnull String displayName
    ) {
        this.identifier = Objects.requireNonNull(identifier, "identifier");
        this.name = Objects.requireNonNull(name, "name");
        this.displayName = Objects.requireNonNull(displayName, "displayName");
    }

    @Nonnull
    @Override
    public PaymentMethodDataAccessObject identifier(@Nonnull Integer identifier) {
        throw new BillingEntityUnmodifiableException(PaymentMethod.class, this.identifier, "identifier");
    }

    @Nonnull
    @Override
    public PaymentMethodDataAccessObject name(@Nonnull String name) {
        throw new BillingEntityUnmodifiableException(PaymentMethod.class, this.identifier, "name");
    }

    @Nonnull
    @Override
    public PaymentMethodDataAccessObject displayName(@Nonnull String name) {
        throw new BillingEntityUnmodifiableException(PaymentMethod.class, this.identifier, "displayName");
    }
}