package global.symbio.billing.core.services.grpc.codec.payment.method;

import global.symbio.billing.core.payment.persistence.api.payment.method.PaymentMethod;
import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.message.PaymentMethodMessage;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

@Singleton
public record PaymentMethodCodec() implements GRPCCodec<PaymentMethod, PaymentMethodMessage> {

    @Nonnull
    @Override
    public PaymentMethodMessage encode(@Nonnull PaymentMethod bean) {
        return PaymentMethodMessage.newBuilder()
            .setId(bean.getIdentifier())
            .setName(bean.getName())
            .setDisplayName(bean.getDisplayName())
            .build();
    }

    @Nonnull
    @Override
    public PaymentMethod decode(@Nonnull PaymentMethodMessage encoded) {
        return new GRPCPaymentMethodDataAccessObject(encoded.getId(), encoded.getName(), encoded.getDisplayName()).entity();
    }
}