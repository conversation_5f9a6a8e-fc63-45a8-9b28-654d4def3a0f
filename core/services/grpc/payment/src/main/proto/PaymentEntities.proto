syntax = "proto3";
option java_package = "global.symbio.billing.core.services.grpc.payment.entities";
option java_outer_classname = "PaymentEntities";
option java_multiple_files = true;

import "UUID.proto";
import "BigDecimal.proto";
import "Temporal.proto";
import "Country.proto";
import "Currency.proto";
import "PaymentMethod.proto";
import "Reference.proto";

enum PaymentStatusMessage {
  PAYMENT_STATUS_UNSPECIFIED = 0;
  ACTIVE = 1;
  CANCELLED = 2;
}

enum PaymentStateMessage {
  PAYMENT_STATE_UNSPECIFIED = 0;
  PAID = 1;
  PARTIALLY_PAID = 2;
  UNPAID = 3;
}

enum AllocationTypeMessage {
  ALLOCATION_TYPE_UNSPECIFIED = 0;
  PAYMENT = 1;
  TRANSFER_OUT = 2;
  TRANSFER_IN = 3;
}

enum AllocationDirectionMessage {
  ALLOCATION_DIRECTION_UNSPECIFIED = 0;
  IN = 1;
  OUT = 2;
}

message AllocationStateMessage {
  enum Type {
    ALLOCATION_STATE_TYPE_UNSPECIFIED = 0;
    UNALLOCATED = 1;
    PARTIALLY_ALLOCATED = 2;
    ALLOCATED = 3;
  }
  Type type = 1;
  BigDecimalMessage total = 2;
  BigDecimalMessage allocated = 3;
  BigDecimalMessage unallocated = 4;
}

message PaymentMessage {
  UUIDMessage identifier = 1;
  UUIDMessage business = 2;
  PaymentMethodMessage method = 3;
  CurrencyMessage currency = 4;
  BigDecimalMessage amount = 5;
  ZonedDateTimeMessage receipt_date = 6;
  PaymentStatusMessage status = 7;
  optional string description = 8;
  CountryMessage country = 9;
  optional ReferenceMessage reference = 10;
  ZonedDateTimeMessage timestamp = 11;
}

message PaymentCancellationMessage {
  UUIDMessage identifier = 1;
  PaymentMessage payment = 2;
  string reason = 3;
  string reference = 4;
  optional ZonedDateTimeMessage sent_timestamp = 5;
  optional ZonedDateTimeMessage settled_timestamp = 6;
  ZonedDateTimeMessage timestamp = 7;
}

message TransferMessage {
  UUIDMessage identifier = 1;
  AccountAllocationMessageApi transfer_out = 2;
  AccountAllocationMessageApi transfer_in = 3;
  CurrencyMessage currency = 4;
  BigDecimalMessage amount = 5;
  optional string description = 6;
  CountryMessage country = 7;
  AccountAllocationMessageApi origin = 8;
  ZonedDateTimeMessage timestamp = 9;
}

message AccountAllocationMessageApi {
  oneof accountAllocation {
    PaymentMessage payment = 1;
    TransferTypeMessage transfer_type = 2;
  }
  UUIDMessage identifier = 3;
  UUIDMessage account = 4;
  AllocationTypeMessage type = 5;
  CountryMessage country = 6;
  CurrencyMessage currency = 7;
  BigDecimalMessage amount = 8;
  AllocationStateMessage allocation_state = 9;
  AllocationDirectionMessage allocation_direction = 10;
  optional ZonedDateTimeMessage sent_timestamp = 11;
  optional ZonedDateTimeMessage settled_timestamp = 12;
  ZonedDateTimeMessage timestamp = 13;
}

message TransferTypeMessage {
  UUIDMessage allocation = 1;
  UUIDMessage parent = 2;
}