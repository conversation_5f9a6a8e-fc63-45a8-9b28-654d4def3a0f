syntax = "proto3";
option java_package = "global.symbio.billing.core.services.grpc.payment";
option java_outer_classname = "PaymentService";
option java_multiple_files = true;

import "UUID.proto";
import "BigDecimal.proto";
import "Temporal.proto";
import "PaymentDTOs.proto";
import "PaymentEntities.proto";

// TODO: modularise the proto files for this service (separating RPC and models)

// https://cloud.google.com/apis/design/naming_convention
service Payment {
  rpc CreatePayment(CreatePaymentRequest) returns (CreatePaymentResponse) {}
  rpc GetPaymentAccountAllocations(GetPaymentAccountAllocationsRequest) returns (GetPaymentAccountAllocationsResponse) {}
  rpc AssignAllocationToInvoices(AssignAllocationToInvoicesRequest) returns (AssignAllocationToInvoicesResponse) {}
  rpc GetPaymentStates(GetPaymentStatesRequest) returns (GetPaymentStatesResponse) {}
  rpc CreateTransfer(CreateTransferRequest) returns (CreateTransferResponse) {}
  rpc GetPaymentAccountAllocation(GetPaymentAccountAllocationRequest) returns (GetPaymentAccountAllocationResponse) {}
  rpc GetInvoiceAllocations(GetInvoiceAllocationsRequest) returns (GetInvoiceAllocationsResponse) {}
  rpc GetTransferByTransferIn(GetTransferByTransferInRequest) returns (GetTransferByTransferInResponse) {}
  rpc GetTransfers(GetTransfersRequest) returns (GetTransfersResponse) {}
  rpc GetAccountAllocation(GetAccountAllocationRequest) returns (GetAccountAllocationResponse) {}
  rpc CancelPayment(CancelPaymentRequest) returns (CancelPaymentResponse) {}
  rpc GetPaymentCancellation(GetPaymentCancellationRequest) returns (GetPaymentCancellationResponse) {}
  rpc GetInvoiceAllocationsByInvoice(GetInvoiceAllocationsByInvoiceRequest) returns (GetInvoiceAllocationsByInvoiceResponse) {}
}

message CreatePaymentRequest {
  UUIDMessage identifier = 1;
  UUIDMessage business = 2;
  int32 method = 3;
  string currency = 4;
  BigDecimalMessage amount = 5;
  ZonedDateTimeMessage receipt_date = 6;
  optional string description = 7;
  string country = 8;
  repeated AccountInvoiceAllocationMessage allocations = 9;
}

message AccountInvoiceAllocationResponseMessage {
  AccountAllocationMessageApi accountAllocation = 1;
  repeated InvoiceAllocationMessage invoiceAllocations = 2;
}

message CreatePaymentResponse {
  PaymentMessage payment = 1;
  repeated AccountInvoiceAllocationResponseMessage allocations = 2;
}

message GetPaymentAccountAllocationsRequest {
  optional UUIDMessage account = 1;
  optional ZonedDateTimeMessage payment_date_start = 2;
  optional ZonedDateTimeMessage payment_date_end = 3;
  optional ZonedDateTimeMessage receipt_date_start = 4;
  optional ZonedDateTimeMessage receipt_date_end = 5;
  repeated string allocation_states = 6;
  repeated int32 methods = 7;
  repeated PaymentStatusMessage status = 8;
}

message GetPaymentAccountAllocationsResponse {
  repeated AccountAllocationMessageApi allocations = 1;
}

message AssignAllocationToInvoicesRequest {
  UUIDMessage identifier = 1;
  repeated InvoiceAllocationMessage allocations = 2;
}

message AssignAllocationToInvoicesResponse {
  AccountAllocationMessageApi allocation = 1;
}

message GetPaymentStatesRequest {
}

message GetPaymentStatesResponse {
  repeated PaymentStateMessage paymentState = 1;
}

message CreateTransferRequest {
  UUIDMessage identifier = 1;
  UUIDMessage account_allocation = 2;
  UUIDMessage account = 3;
  BigDecimalMessage amount = 4;
  optional string description = 5;
}

message CreateTransferResponse {
  TransferMessage transfer = 1;
}

message GetPaymentAccountAllocationRequest {
  UUIDMessage identifier = 1;
}

message GetPaymentAccountAllocationResponse {
  optional AccountAllocationMessageApi allocation = 1;
}

message GetInvoiceAllocationsRequest {
  UUIDMessage accountAllocation = 1;
}

message GetInvoiceAllocationsResponse {
  repeated InvoiceAllocationMessage invoiceAllocations = 1;
}

message GetTransferByTransferInRequest {
  UUIDMessage identifier = 1;
}

message GetTransferByTransferInResponse {
  optional TransferMessage transfer = 1;
}

message GetTransfersRequest {
  UUIDMessage account = 1;
  optional ZonedDateTimeMessage transfer_date_start = 2;
  optional ZonedDateTimeMessage transfer_date_end = 3;
  optional AllocationTypeMessage type = 4;
}

message GetTransfersResponse {
  repeated TransferMessage transfers = 1;
}

message GetAccountAllocationRequest {
  UUIDMessage identifier = 1;
}

message GetAccountAllocationResponse {
  optional AccountAllocationMessageApi allocation = 1;
}

message CancelPaymentRequest {
  UUIDMessage identifier = 1;
  string reason = 2;
}

message CancelPaymentResponse {
  PaymentCancellationMessage cancellation = 1;
}

message GetPaymentCancellationRequest {
  UUIDMessage identifier = 1;
}

message GetPaymentCancellationResponse {
  optional PaymentCancellationMessage cancellation = 1;
}

message GetInvoiceAllocationsByInvoiceRequest {
  UUIDMessage invoice = 1;
}

message GetInvoiceAllocationsByInvoiceResponse {
  repeated PaymentInvoiceAllocationMessage paymentInvoiceAllocations = 1;
}