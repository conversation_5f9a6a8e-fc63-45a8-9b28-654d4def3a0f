syntax = "proto3";
option java_package = "global.symbio.billing.core.services.grpc.payment.dto";
option java_outer_classname = "PaymentDTOs";
option java_multiple_files = true;

import "UUID.proto";
import "BigDecimal.proto";
import "Temporal.proto";
import "PaymentEntities.proto";

// Analogous to global.symbio.billing.core.services.payment.allocation.AccountInvoiceAllocation.InvoiceAllocation subtype
message InvoiceAllocationMessage {
  UUIDMessage invoice = 1;
  string reference = 2;
  BigDecimalMessage amount = 3;
}

// Analogous to global.symbio.billing.core.services.payment.allocation.AccountInvoiceAllocation.InvoiceAllocationResponse
message InvoiceAllocationResponseMessage {
  UUIDMessage invoice = 1;
  string reference = 2;
  BigDecimalMessage amount = 3;
  ZonedDateTimeMessage last_updated = 4;
}

// Analogous to global.symbio.billing.core.services.payment.allocation.AccountInvoiceAllocation subtype
message AccountInvoiceAllocationMessage {
  UUIDMessage account = 1;
  BigDecimalMessage amount = 2;
  repeated InvoiceAllocationMessage invoices = 3;
}

message PaymentInvoiceAllocationMessage {
  PaymentMessage payment = 1;
  InvoiceAllocationResponseMessage allocation = 2;
}