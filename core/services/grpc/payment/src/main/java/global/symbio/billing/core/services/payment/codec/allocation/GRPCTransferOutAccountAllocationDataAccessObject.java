package global.symbio.billing.core.services.payment.codec.allocation;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.country.persistence.api.CountryDataAccessObject;
import global.symbio.billing.core.currency.persistence.api.CurrencyDataAccessObject;
import global.symbio.billing.core.exception.sdk.BillingEntityUnmodifiableException;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AllocationDirection;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AllocationType;
import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.transfer.TransferOutAccountAllocation;
import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.transfer.TransferOutAccountAllocationDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.allocation.state.AllocationState;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.Getter;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Objects;
import java.util.UUID;

@Getter
public final class GRPCTransferOutAccountAllocationDataAccessObject implements TransferOutAccountAllocationDataAccessObject<GRPCTransferOutAccountAllocationDataAccessObject> {

    @Nonnull
    private final UUID identifier;

    @Nonnull
    private final UUID account;

    @Nonnull
    private final AllocationType type;

    @Nonnull
    private final CountryDataAccessObject country;

    @Nonnull
    private final CurrencyDataAccessObject currency;

    @Nonnull
    private final BigDecimal amount;

    @Nonnull
    private final AllocationState allocationState;

    @Nonnull
    private final AllocationDirection direction;

    @Nullable
    private final ZonedDateTime sentTimestamp;

    @Nullable
    private final ZonedDateTime settledTimestamp;

    @Nonnull
    private final ZonedDateTime timestamp;

    @Nonnull
    private final UUID parent;

    public GRPCTransferOutAccountAllocationDataAccessObject(
        @Nonnull UUID identifier,
        @Nonnull UUID account,
        @Nonnull AllocationType type,
        @Nonnull CountryDataAccessObject country,
        @Nonnull CurrencyDataAccessObject currency,
        @Nonnull BigDecimal amount,
        @Nonnull AllocationState allocationState,
        @Nonnull AllocationDirection direction,
        @Nullable ZonedDateTime sentTimestamp,
        @Nullable ZonedDateTime settledTimestamp,
        @Nonnull ZonedDateTime timestamp,
        @Nonnull UUID parent
    ) {
        this.identifier = Objects.requireNonNull(identifier, "identifier");
        this.account = Objects.requireNonNull(account, "account");
        this.type = Objects.requireNonNull(type, "type");
        this.country = Objects.requireNonNull(country, "country");
        this.currency = Objects.requireNonNull(currency, "currency");
        this.amount = Objects.requireNonNull(amount, "amount");
        this.allocationState = Objects.requireNonNull(allocationState, "allocationState");
        this.direction = Objects.requireNonNull(direction, "direction");
        this.sentTimestamp = sentTimestamp;
        this.settledTimestamp = settledTimestamp;
        this.timestamp = Objects.requireNonNull(timestamp, "timestamp");
        this.parent = Objects.requireNonNull(parent, "parent");
    }

    @Nonnull
    @Override
    public GRPCTransferOutAccountAllocationDataAccessObject identifier(@Nonnull UUID identifier) {
        throw new BillingEntityUnmodifiableException(TransferOutAccountAllocation.class, this.identifier, "identifier");
    }

    @Nonnull
    @Override
    public GRPCTransferOutAccountAllocationDataAccessObject account(@Nonnull UUID account) {
        throw new BillingEntityUnmodifiableException(TransferOutAccountAllocation.class, this.identifier, "account");
    }

    @Nonnull
    @Override
    public GRPCTransferOutAccountAllocationDataAccessObject type(@Nonnull AllocationType type) {
        throw new BillingEntityUnmodifiableException(TransferOutAccountAllocation.class, this.identifier, "type");
    }

    @Nonnull
    @Override
    public GRPCTransferOutAccountAllocationDataAccessObject country(@Nonnull CountryDataAccessObject country) {
        throw new BillingEntityUnmodifiableException(TransferOutAccountAllocation.class, this.identifier, "country");
    }

    @Nonnull
    @Override
    public GRPCTransferOutAccountAllocationDataAccessObject currency(@Nonnull CurrencyDataAccessObject currency) {
        throw new BillingEntityUnmodifiableException(TransferOutAccountAllocation.class, this.identifier, "currency");
    }

    @Nonnull
    @Override
    public GRPCTransferOutAccountAllocationDataAccessObject amount(@Nonnull BigDecimal amount) {
        throw new BillingEntityUnmodifiableException(TransferOutAccountAllocation.class, this.identifier, "amount");
    }

    @Nonnull
    @Override
    public GRPCTransferOutAccountAllocationDataAccessObject allocationState(@Nonnull AllocationState state) {
        throw new BillingEntityUnmodifiableException(TransferOutAccountAllocation.class, this.identifier, "allocationState");
    }

    @Nonnull
    @Override
    public GRPCTransferOutAccountAllocationDataAccessObject direction(@Nonnull AllocationDirection direction) {
        throw new BillingEntityUnmodifiableException(TransferOutAccountAllocation.class, this.identifier, "direction");
    }

    @Nonnull
    @Override
    public GRPCTransferOutAccountAllocationDataAccessObject sentTimestamp(@Nonnull ZonedDateTime sentTimestamp) {
        throw new BillingEntityUnmodifiableException(TransferOutAccountAllocation.class, this.identifier, "sentTimestamp");
    }

    @Nonnull
    @Override
    public GRPCTransferOutAccountAllocationDataAccessObject settledTimestamp(@Nonnull ZonedDateTime settledTimestamp) {
        throw new BillingEntityUnmodifiableException(TransferOutAccountAllocation.class, this.identifier, "settledTimestamp");
    }

    @Nonnull
    @Override
    public GRPCTransferOutAccountAllocationDataAccessObject timestamp(@Nonnull ZonedDateTime timestamp) {
        throw new BillingEntityUnmodifiableException(TransferOutAccountAllocation.class, this.identifier, "timestamp");
    }

    @Nonnull
    @Override
    public GRPCTransferOutAccountAllocationDataAccessObject parent(@Nonnull UUID parent) {
        throw new BillingEntityUnmodifiableException(TransferOutAccountAllocation.class, this.identifier, "parent");
    }

    @Override
    public boolean equals(Object other) {
        if (this == other) return true;
        if (!(other instanceof GRPCTransferOutAccountAllocationDataAccessObject allocation)) return false;
        return Objects.equals(getIdentifier(), allocation.getIdentifier()) &&
            Objects.equals(getAccount(), allocation.getAccount()) &&
            getType() == allocation.getType() &&
            Objects.equals(getCountry(), allocation.getCountry()) &&
            Objects.equals(getCurrency(), allocation.getCurrency()) &&
            Objects.equals(getAmount(), allocation.getAmount()) &&
            Objects.equals(getAllocationState(), allocation.getAllocationState()) &&
            Objects.equals(getDirection(), allocation.getDirection()) &&
            Objects.equals(getSentTimestamp(), allocation.getSentTimestamp()) &&
            Objects.equals(getSettledTimestamp(), allocation.getSettledTimestamp()) &&
            Objects.equals(getTimestamp(), allocation.getTimestamp()) &&
            Objects.equals(getParent(), allocation.getParent());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getIdentifier(), getAccount(), getType(), getCountry(), getCurrency(), getAmount(), getAllocationState(), getDirection(), getSentTimestamp(), getSettledTimestamp(), getTimestamp(), getParent());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("identifier", getIdentifier())
            .add("account", getAccount())
            .add("type", getType())
            .add("country", getCountry())
            .add("currency", getCurrency())
            .add("amount", getAmount())
            .add("allocationState", getAllocationState())
            .add("direction", getDirection())
            .add("sentTimestamp", getSentTimestamp())
            .add("settledTimestamp", getSettledTimestamp())
            .add("timestamp", getTimestamp())
            .add("parent", getParent())
            .toString();
    }
}