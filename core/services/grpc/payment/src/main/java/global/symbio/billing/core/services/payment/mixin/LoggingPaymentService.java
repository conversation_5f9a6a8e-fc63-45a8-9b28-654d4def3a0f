package global.symbio.billing.core.services.payment.mixin;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.MoreObjects;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AccountAllocation;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AllocationType;
import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.payment.PaymentAccountAllocation;
import global.symbio.billing.core.payment.persistence.api.cancellation.PaymentCancellation;
import global.symbio.billing.core.payment.persistence.api.payment.PaymentStatus;
import global.symbio.billing.core.payment.persistence.api.payment.state.PaymentState;
import global.symbio.billing.core.payment.persistence.api.transfer.Transfer;
import global.symbio.billing.core.services.payment.PaymentService;
import global.symbio.billing.core.services.payment.codec.allocation.dto.AccountInvoiceAllocation;
import global.symbio.billing.core.services.payment.codec.allocation.dto.InvoiceAllocationDTO;
import global.symbio.billing.core.services.payment.codec.allocation.dto.PaymentInvoiceAllocationDTO;
import global.symbio.billing.core.services.payment.codec.payment.PaymentResponse;
import global.symbio.billing.core.util.metrics.MetricsUtil;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.AccessLevel;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.*;

import static global.symbio.billing.core.util.constants.MetricsConstants.*;

@VisibleForTesting
class LoggingPaymentService implements PaymentService {

    @Nonnull
    private final Logger log;

    @Nonnull
    @Getter(AccessLevel.PACKAGE)
    private final PaymentService payment;

    public LoggingPaymentService(@Nonnull PaymentService payment) {
        this(LoggerFactory.getLogger(payment.getClass()), payment);
    }

    public LoggingPaymentService(@Nonnull Logger log, @Nonnull PaymentService payment) {
        this.log = Objects.requireNonNull(log, "log");
        this.payment = Objects.requireNonNull(payment, "payment");
    }

    @Nonnull
    @Override
    public PaymentResponse create(@Nonnull UUID identifier, @Nonnull UUID business, @Nonnull Integer method, @Nonnull String currency, @Nonnull BigDecimal amount, @Nonnull ZonedDateTime receipt, @Nullable String description, @Nonnull String country, @Nonnull Set<? extends AccountInvoiceAllocation> allocations) {
        log.info("PaymentService::create: identifier - {}, business - {}, method - {}, currency - {}, amount - {}, receipt - {}, description - {}, country - {}, allocations - {}", identifier, business, method, currency, amount, receipt, description, country, allocations.size());
        final var payment = this.payment.create(identifier, business, method, currency, amount, receipt, description, country, allocations);
        //we only log metric if payment response is successful i.e. no error thrown
        final var map = Map.of(CONTEXT_TAG_TXN_COUNTRY, country, CONTEXT_TAG_API_DESCRIPTION, API_DESCRIPTION_PAYMENT);
        logMetrics("PaymentService::create: " + identifier, map);
        return payment;
    }

    @Nonnull
    @Override
    public Collection<PaymentAccountAllocation<?>> getPaymentAccountAllocations(@Nullable UUID account, @Nullable ZonedDateTime paymentDateStart, @Nullable ZonedDateTime paymentDateEnd, @Nullable ZonedDateTime receiptDateStart, @Nullable ZonedDateTime receiptDateEnd, @Nullable Set<String> allocation, @Nullable Set<Integer> methods, @Nullable Set<PaymentStatus> status) {
        log.info("PaymentService::getPaymentAccountAllocations: account - {}, paymentDateStart - {}, paymentDateEnd - {}, receiptDateStart - {}, receiptDateEnd - {}, allocation - {}, methods - {}, status - {}", account, paymentDateStart, paymentDateEnd, receiptDateStart, receiptDateEnd, allocation, methods, status);
        return payment.getPaymentAccountAllocations(account, paymentDateStart, paymentDateEnd, receiptDateStart, receiptDateEnd, allocation, methods, status);
    }

    @Nonnull
    @Override
    public AccountAllocation<?> assignAllocationToInvoices(@Nonnull UUID identifier, @Nonnull Set<? extends InvoiceAllocationDTO> allocations) {
        log.info("PaymentService::assignAllocationToInvoices: identifier - {}, allocations - {}, size allocation - {}", identifier, allocations, allocations.size());
        return payment.assignAllocationToInvoices(identifier, allocations);
    }

    @Nonnull
    @Override
    public Collection<PaymentState> getPaymentStates() {
        log.info("PaymentService::getPaymentStates");
        return payment.getPaymentStates();
    }

    @Nonnull
    @Override
    public Transfer createTransfer(@Nonnull UUID identifier, @Nonnull UUID fromAccountAllocation, @Nonnull UUID toAccount, @Nonnull BigDecimal amount, @Nullable String description) {
        log.info("PaymentService::createTransfer: identifier - {}, fromAccountAllocation - {}, toAccount - {}, amount - {}, description - {}", identifier, fromAccountAllocation, toAccount, amount, description);
        final var transfer = payment.createTransfer(identifier, fromAccountAllocation, toAccount, amount, description);
        final var map = Map.of(CONTEXT_TAG_TXN_COUNTRY, transfer.getCountry().getCode(), CONTEXT_TAG_API_DESCRIPTION, API_DESCRIPTION_TRANSFER);
        logMetrics("PaymentService::createTransfer: " + transfer.getIdentifier(), map);
        return transfer;
    }

    @Nonnull
    @Override
    public Optional<PaymentAccountAllocation<?>> getPaymentAccountAllocation(@Nonnull UUID identifier) {
        log.info("PaymentService::getPaymentAccountAllocation: identifier - {}", identifier);
        return payment.getPaymentAccountAllocation(identifier);
    }

    @Nonnull
    @Override
    public Collection<InvoiceAllocationDTO> getInvoiceAllocations(@Nonnull UUID accountAllocation) {
        log.info("PaymentService::getInvoiceAllocations: accountAllocation - {}", accountAllocation);
        return payment.getInvoiceAllocations(accountAllocation);
    }

    @Nonnull
    @Override
    public Optional<Transfer> getTransferByTransferIn(@Nonnull UUID identifier) {
        log.info("PaymentService::getTransferByTransferIn: identifier - {}", identifier);
        return payment.getTransferByTransferIn(identifier);
    }

    @Nonnull
    @Override
    public Collection<Transfer> getTransfers(@Nonnull UUID account, @Nullable ZonedDateTime transferDateStart, @Nullable ZonedDateTime transferDateEnd, @Nullable AllocationType type) {
        log.info("PaymentService::getTransfers: account - {}, transferDateStart - {}, transferDateEnd - {}, allocationType - {}", account, transferDateStart, transferDateEnd, type);
        return payment.getTransfers(account, transferDateStart, transferDateEnd, type);
    }

    @Nonnull
    @Override
    public Optional<AccountAllocation<?>> getAccountAllocation(@Nonnull UUID identifier) {
        log.info("PaymentService::getAccountAllocation: identifier - {}", identifier);
        return payment.getAccountAllocation(identifier);
    }

    @Nonnull
    @Override
    public PaymentCancellation cancel(@Nonnull UUID identifier, @Nonnull String reason) {
        log.info("PaymentService::cancel: identifier - {}, reason - {}", identifier, reason);
        final var cancellation = this.payment.cancel(identifier, reason);
        final var map = Map.of(CONTEXT_TAG_TXN_COUNTRY, cancellation.getPayment().getCountry().getCode(), CONTEXT_TAG_API_DESCRIPTION, API_DESCRIPTION_CANCELLATION);
        logMetrics("PaymentService::cancel: " + cancellation.getIdentifier(), map);
        return cancellation;
    }

    @Nonnull
    @Override
    public Optional<PaymentCancellation> getPaymentCancellation(@Nonnull UUID identifier) {
        log.info("PaymentService::getPaymentCancellation: identifier - {}", identifier);
        return this.payment.getPaymentCancellation(identifier);
    }

    @Nonnull
    public Collection<PaymentInvoiceAllocationDTO> getInvoiceAllocationsByInvoice(@Nonnull UUID invoice) {
        log.info("PaymentService::getInvoiceAllocationsByInvoice: invoice - {}", invoice);
        return this.payment.getInvoiceAllocationsByInvoice(invoice);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof LoggingPaymentService service)) return false;
        return Objects.equals(getPayment(), service.getPayment());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getPayment());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("payment", getPayment())
            .toString();
    }

    private void logMetrics(@Nonnull String logMessage, @Nonnull Map<String,String> metricsMap) {
        try (final var _ = MetricsUtil.create(metricsMap)) {
            log.info(logMessage);
        }
    }
}