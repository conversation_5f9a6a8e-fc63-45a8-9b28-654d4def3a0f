package global.symbio.billing.core.services.payment.codec.allocation;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.country.persistence.api.CountryDataAccessObject;
import global.symbio.billing.core.currency.persistence.api.CurrencyDataAccessObject;
import global.symbio.billing.core.exception.sdk.BillingEntityUnmodifiableException;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AllocationDirection;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AllocationType;
import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.payment.PaymentAccountAllocation;
import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.payment.PaymentAccountAllocationDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.allocation.state.AllocationState;
import global.symbio.billing.core.payment.persistence.api.payment.PaymentDataAccessObject;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.Getter;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Objects;
import java.util.UUID;

@Getter
public final class GRPCPaymentAccountAllocationDataAccessObject implements PaymentAccountAllocationDataAccessObject<GRPCPaymentAccountAllocationDataAccessObject> {

    @Nonnull
    private final UUID identifier;

    @Nonnull
    private final UUID account;

    @Nonnull
    private final AllocationType type;

    @Nonnull
    private final CountryDataAccessObject country;

    @Nonnull
    private final CurrencyDataAccessObject currency;

    @Nonnull
    private final BigDecimal amount;

    @Nonnull
    private final AllocationState allocationState;

    @Nonnull
    private final AllocationDirection direction;

    @Nullable
    private final ZonedDateTime sentTimestamp;

    @Nullable
    private final ZonedDateTime settledTimestamp;

    @Nonnull
    private final ZonedDateTime timestamp;

    @Nonnull
    private final PaymentDataAccessObject payment;

    public GRPCPaymentAccountAllocationDataAccessObject(
        @Nonnull UUID identifier,
        @Nonnull UUID account,
        @Nonnull AllocationType type,
        @Nonnull CountryDataAccessObject country,
        @Nonnull CurrencyDataAccessObject currency,
        @Nonnull BigDecimal amount,
        @Nonnull AllocationState allocationState,
        @Nonnull AllocationDirection direction,
        @Nullable ZonedDateTime sentTimestamp,
        @Nullable ZonedDateTime settledTimestamp,
        @Nonnull ZonedDateTime timestamp,
        @Nonnull PaymentDataAccessObject payment
    ) {
        this.identifier = Objects.requireNonNull(identifier, "identifier");
        this.account = Objects.requireNonNull(account, "account");
        this.type = Objects.requireNonNull(type, "type");
        this.country = Objects.requireNonNull(country, "country");
        this.currency = Objects.requireNonNull(currency, "currency");
        this.amount = Objects.requireNonNull(amount, "amount");
        this.allocationState = Objects.requireNonNull(allocationState, "allocationState");
        this.direction = Objects.requireNonNull(direction, "direction");
        this.sentTimestamp = sentTimestamp;
        this.settledTimestamp = settledTimestamp;
        this.timestamp = Objects.requireNonNull(timestamp, "timestamp");
        this.payment = Objects.requireNonNull(payment, "payment");
    }

    @Nonnull
    @Override
    public GRPCPaymentAccountAllocationDataAccessObject identifier(@Nonnull UUID identifier) {
        throw new BillingEntityUnmodifiableException(PaymentAccountAllocation.class, this.identifier, "identifier");
    }

    @Nonnull
    @Override
    public GRPCPaymentAccountAllocationDataAccessObject account(@Nonnull UUID account) {
        throw new BillingEntityUnmodifiableException(PaymentAccountAllocation.class, this.identifier, "account");
    }

    @Nonnull
    @Override
    public GRPCPaymentAccountAllocationDataAccessObject type(@Nonnull AllocationType type) {
        throw new BillingEntityUnmodifiableException(PaymentAccountAllocation.class, this.identifier, "type");
    }

    @Nonnull
    @Override
    public GRPCPaymentAccountAllocationDataAccessObject country(@Nonnull CountryDataAccessObject country) {
        throw new BillingEntityUnmodifiableException(PaymentAccountAllocation.class, this.identifier, "country");
    }

    @Nonnull
    @Override
    public GRPCPaymentAccountAllocationDataAccessObject currency(@Nonnull CurrencyDataAccessObject currency) {
        throw new BillingEntityUnmodifiableException(PaymentAccountAllocation.class, this.identifier, "currency");
    }

    @Nonnull
    @Override
    public GRPCPaymentAccountAllocationDataAccessObject amount(@Nonnull BigDecimal amount) {
        throw new BillingEntityUnmodifiableException(PaymentAccountAllocation.class, this.identifier, "amount");
    }

    @Nonnull
    @Override
    public GRPCPaymentAccountAllocationDataAccessObject allocationState(@Nonnull AllocationState state) {
        throw new BillingEntityUnmodifiableException(PaymentAccountAllocation.class, this.identifier, "allocationState");
    }

    @Nonnull
    @Override
    public GRPCPaymentAccountAllocationDataAccessObject direction(@Nonnull AllocationDirection direction) {
        throw new BillingEntityUnmodifiableException(PaymentAccountAllocation.class, this.identifier, "direction");
    }

    @Nonnull
    @Override
    public GRPCPaymentAccountAllocationDataAccessObject sentTimestamp(@Nullable ZonedDateTime sentTimestamp) {
        throw new BillingEntityUnmodifiableException(PaymentAccountAllocation.class, this.identifier, "sentTimestamp");
    }

    @Nonnull
    @Override
    public GRPCPaymentAccountAllocationDataAccessObject settledTimestamp(@Nullable ZonedDateTime settledTimestamp) {
        throw new BillingEntityUnmodifiableException(PaymentAccountAllocation.class, this.identifier, "settledTimestamp");
    }

    @Nonnull
    @Override
    public GRPCPaymentAccountAllocationDataAccessObject timestamp(@Nonnull ZonedDateTime timestamp) {
        throw new BillingEntityUnmodifiableException(PaymentAccountAllocation.class, this.identifier, "timestamp");
    }

    @Nonnull
    @Override
    public GRPCPaymentAccountAllocationDataAccessObject payment(@Nonnull PaymentDataAccessObject payment) {
        throw new BillingEntityUnmodifiableException(PaymentAccountAllocation.class, this.identifier, "payment");
    }

    @Override
    public boolean equals(Object other) {
        if (this == other) return true;
        if (!(other instanceof GRPCPaymentAccountAllocationDataAccessObject allocation)) return false;
        return Objects.equals(getIdentifier(), allocation.getIdentifier()) &&
            Objects.equals(getAccount(), allocation.getAccount()) &&
            getType() == allocation.getType() &&
            Objects.equals(getCountry(), allocation.getCountry()) &&
            Objects.equals(getCurrency(), allocation.getCurrency()) &&
            Objects.equals(getAmount(), allocation.getAmount()) &&
            Objects.equals(getAllocationState(), allocation.getAllocationState()) &&
            Objects.equals(getDirection(), allocation.getDirection()) &&
            Objects.equals(getSentTimestamp(), allocation.getSentTimestamp()) &&
            Objects.equals(getSettledTimestamp(), allocation.getSettledTimestamp()) &&
            Objects.equals(getTimestamp(), allocation.getTimestamp()) &&
            Objects.equals(getPayment(), allocation.getPayment());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getIdentifier(), getAccount(), getType(), getCountry(), getCurrency(), getAmount(), getAllocationState(), getDirection(), getSentTimestamp(), getSettledTimestamp(), getTimestamp(), getPayment());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("identifier", getIdentifier())
            .add("account", getAccount())
            .add("type", getType())
            .add("country", getCountry())
            .add("currency", getCurrency())
            .add("amount", getAmount())
            .add("allocationState", getAllocationState())
            .add("direction", getDirection())
            .add("sentTimestamp", getSentTimestamp())
            .add("settledTimestamp", getSettledTimestamp())
            .add("timestamp", getTimestamp())
            .add("payment", getPayment())
            .toString();
    }
}
