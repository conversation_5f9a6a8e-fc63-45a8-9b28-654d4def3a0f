package global.symbio.billing.core.services.payment.codec.payment;

import global.symbio.billing.core.payment.persistence.api.payment.Payment;
import global.symbio.billing.core.services.payment.codec.allocation.dto.AccountInvoiceAllocationResponse;
import jakarta.annotation.Nonnull;

import java.util.Set;

public interface PaymentResponse {

    @Nonnull
    Payment payment();

    @Nonnull
    Set<AccountInvoiceAllocationResponse> accountAllocations();
}
