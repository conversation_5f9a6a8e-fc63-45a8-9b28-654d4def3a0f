package global.symbio.billing.core.services.payment.codec.allocation.dto;

import global.symbio.billing.core.payment.persistence.api.payment.Payment;
import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.payment.dto.InvoiceAllocationResponseMessage;
import global.symbio.billing.core.services.grpc.payment.dto.PaymentInvoiceAllocationMessage;
import global.symbio.billing.core.services.grpc.payment.entities.PaymentMessage;
import global.symbio.billing.core.services.payment.codec.allocation.dto.impl.GRPCPaymentInvoiceAllocationDTO;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.util.Objects;

@Singleton
public record PaymentInvoiceAllocationDTOCodec (
    @Inject @Nonnull Codec<Payment, PaymentMessage> payment,
    @Inject @Nonnull Codec<InvoiceAllocationResponseDTO, InvoiceAllocationResponseMessage> invoiceAllocation
) implements GRPCCodec<PaymentInvoiceAllocationDTO, PaymentInvoiceAllocationMessage> {

    public PaymentInvoiceAllocationDTOCodec {
        Objects.requireNonNull(payment, "payment");
        Objects.requireNonNull(invoiceAllocation, "invoiceAllocation");
    }

    @Nonnull
    @Override
    public PaymentInvoiceAllocationMessage encode(@Nonnull PaymentInvoiceAllocationDTO bean) {
        final var payment = this.payment.encode(bean.payment());
        final var invoiceAllocation = this.invoiceAllocation.encode(bean.invoiceAllocation());
        return PaymentInvoiceAllocationMessage.newBuilder()
            .setPayment(payment)
            .setAllocation(invoiceAllocation)
            .build();
    }

    @Nonnull
    @Override
    public PaymentInvoiceAllocationDTO decode(@Nonnull PaymentInvoiceAllocationMessage protobuf) {
        final var payment = this.payment.decode(protobuf.getPayment());
        final var invoiceAllocation = this.invoiceAllocation.decode(protobuf.getAllocation());
        return new GRPCPaymentInvoiceAllocationDTO(payment, invoiceAllocation);
    }
}
