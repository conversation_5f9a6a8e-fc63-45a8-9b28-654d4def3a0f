package global.symbio.billing.core.services.payment.codec.payment;

import global.symbio.billing.core.payment.persistence.api.payment.Payment;
import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.payment.AccountInvoiceAllocationResponseMessage;
import global.symbio.billing.core.services.grpc.payment.CreatePaymentResponse;
import global.symbio.billing.core.services.grpc.payment.entities.PaymentMessage;
import global.symbio.billing.core.services.payment.codec.allocation.dto.AccountInvoiceAllocationResponse;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.util.Objects;
import java.util.stream.Collectors;

@Singleton
public record PaymentResponseCodec(
    @Inject @Nonnull Codec<Payment, PaymentMessage> payment,
    @Inject @Nonnull Codec<AccountInvoiceAllocationResponse, AccountInvoiceAllocationResponseMessage> allocations
) implements GRPCCodec<PaymentResponse, CreatePaymentResponse> {

    public PaymentResponseCodec {
        Objects.requireNonNull(payment,"payment");
        Objects.requireNonNull(allocations, "allocations");
    }

    @Nonnull
    @Override
    public CreatePaymentResponse encode(@Nonnull PaymentResponse bean) {
        final var payment = this.payment.encode(bean.payment());
        final var allocations = bean.accountAllocations().stream().map(this.allocations::encode).collect(Collectors.toSet());
        return CreatePaymentResponse.newBuilder()
            .setPayment(payment)
            .addAllAllocations(allocations)
            .build();
    }

    @Nonnull
    @Override
    public PaymentResponse decode(@Nonnull CreatePaymentResponse protobuf) {
        final var payment = this.payment.decode(protobuf.getPayment());
        final var allocations = protobuf.getAllocationsList().stream().map(this.allocations::decode).collect(Collectors.toSet());
        return new GRPCPaymentResponse(payment, allocations);
    }
}