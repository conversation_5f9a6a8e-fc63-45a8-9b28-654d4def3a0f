package global.symbio.billing.core.services.payment.codec.allocation.state;

import global.symbio.billing.core.payment.persistence.api.allocation.state.Allocated;
import global.symbio.billing.core.payment.persistence.api.allocation.state.AllocationState;
import global.symbio.billing.core.payment.persistence.api.allocation.state.PartiallyAllocated;
import global.symbio.billing.core.payment.persistence.api.allocation.state.Unallocated;
import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.message.BigDecimalMessage;
import global.symbio.billing.core.services.grpc.payment.entities.AllocationStateMessage;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.math.BigDecimal;
import java.util.Objects;

@Singleton
public record AllocationStateCodec(
    @Inject @Nonnull Codec<BigDecimal, BigDecimalMessage> decimal
) implements GRPCCodec<AllocationState, AllocationStateMessage> {

    public AllocationStateCodec {
        Objects.requireNonNull(decimal, "decimal");
    }

    @Nonnull
    @Override
    public AllocationStateMessage encode(@Nonnull AllocationState bean) {
        final var type = switch (bean.getType()) {
            case Unallocated.TYPE -> AllocationStateMessage.Type.UNALLOCATED;
            case PartiallyAllocated.TYPE -> AllocationStateMessage.Type.PARTIALLY_ALLOCATED;
            case Allocated.TYPE -> AllocationStateMessage.Type.ALLOCATED;
            default -> throw new IllegalStateException("Unexpected type: " + bean.getType());
        };
        final var total = decimal.encode(bean.getTotal());
        final var allocated = decimal.encode(bean.getAllocated());
        final var unallocated = decimal.encode(bean.getUnallocated());
        return AllocationStateMessage.newBuilder()
            .setType(type)
            .setTotal(total)
            .setAllocated(allocated)
            .setUnallocated(unallocated)
            .build();
    }

    @Nonnull
    @Override
    public AllocationState decode(@Nonnull AllocationStateMessage protobuf) {
        final var total = decimal.decode(protobuf.getTotal());
        final var allocated = decimal.decode(protobuf.getAllocated());
        final var unallocated = decimal.decode(protobuf.getUnallocated());
        return switch (protobuf.getType()) {
            case UNALLOCATED -> new Unallocated(total);
            case PARTIALLY_ALLOCATED -> new PartiallyAllocated(total, allocated, unallocated);
            case ALLOCATED -> new Allocated(total);
            default -> throw new IllegalStateException("Unexpected type: " + protobuf.getType());
        };
    }
}