package global.symbio.billing.core.services.payment.codec.allocation.dto;

import global.symbio.billing.core.exception.sdk.BillingEntityNotFoundException;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AllocationDirection;
import global.symbio.billing.core.services.grpc.codec.GRPCEnumCodec;
import global.symbio.billing.core.services.grpc.payment.entities.AllocationDirectionMessage;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

import static global.symbio.billing.core.payment.persistence.api.allocation.account.AllocationDirection.IN;
import static global.symbio.billing.core.payment.persistence.api.allocation.account.AllocationDirection.OUT;

@Singleton
public record AllocationDirectionCodec() implements GRPCEnumCodec<AllocationDirection, AllocationDirectionMessage> {
    @Nonnull
    @Override
    public AllocationDirectionMessage encode(@Nonnull AllocationDirection bean) {
        return switch (bean) {
            case IN -> AllocationDirectionMessage.IN;
            case OUT -> AllocationDirectionMessage.OUT;
        };
    }

    @Nonnull
    @Override
    public AllocationDirection decode(@Nonnull AllocationDirectionMessage protobuf) {
        return switch (protobuf) {
            case AllocationDirectionMessage.IN -> IN;
            case AllocationDirectionMessage.OUT -> OUT;
            default -> throw new BillingEntityNotFoundException(AllocationDirection.class, protobuf);
        };
    }
}
