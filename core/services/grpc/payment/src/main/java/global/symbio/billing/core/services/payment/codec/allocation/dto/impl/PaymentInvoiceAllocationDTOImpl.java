package global.symbio.billing.core.services.payment.codec.allocation.dto.impl;

import global.symbio.billing.core.payment.persistence.api.payment.Payment;
import global.symbio.billing.core.services.payment.codec.allocation.dto.InvoiceAllocationResponseDTO;
import global.symbio.billing.core.services.payment.codec.allocation.dto.PaymentInvoiceAllocationDTO;
import io.micronaut.core.annotation.Introspected;
import jakarta.annotation.Nonnull;
import jakarta.validation.constraints.NotNull;

import java.util.Objects;

@Introspected
public record PaymentInvoiceAllocationDTOImpl(
    @Nonnull @NotNull Payment payment,
    @Nonnull @NotNull InvoiceAllocationResponseDTO invoiceAllocation
) implements PaymentInvoiceAllocationDTO {

    public PaymentInvoiceAllocationDTOImpl {
        Objects.requireNonNull(payment, "payment");
        Objects.requireNonNull(invoiceAllocation, "invoiceAllocation");
    }
}