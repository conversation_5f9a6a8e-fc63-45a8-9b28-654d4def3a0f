package global.symbio.billing.core.services.payment.codec.payment;

import global.symbio.billing.core.payment.persistence.api.payment.Payment;
import global.symbio.billing.core.services.payment.codec.allocation.dto.AccountInvoiceAllocationResponse;
import jakarta.annotation.Nonnull;

import java.util.Objects;
import java.util.Set;

public record GRPCPaymentResponse (
    @Nonnull Payment payment,
    @Nonnull Set<AccountInvoiceAllocationResponse> accountAllocations
) implements PaymentResponse {

    public GRPCPaymentResponse {
        Objects.requireNonNull(payment, "payment");
        Objects.requireNonNull(accountAllocations, "accountAllocations");
    }
}
