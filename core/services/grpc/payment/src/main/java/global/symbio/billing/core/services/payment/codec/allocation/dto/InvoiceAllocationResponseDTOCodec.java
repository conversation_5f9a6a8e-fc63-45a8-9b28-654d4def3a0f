package global.symbio.billing.core.services.payment.codec.allocation.dto;

import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.message.BigDecimalMessage;
import global.symbio.billing.core.services.grpc.message.UUIDMessage;
import global.symbio.billing.core.services.grpc.message.ZonedDateTimeMessage;
import global.symbio.billing.core.services.grpc.payment.dto.InvoiceAllocationResponseMessage;
import global.symbio.billing.core.services.payment.codec.allocation.dto.impl.GRPCInvoiceAllocationResponseDTO;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Objects;
import java.util.UUID;

@Singleton
public record InvoiceAllocationResponseDTOCodec(
    @Inject @Nonnull Codec<UUID, UUIDMessage> uuid,
    @Inject @Nonnull Codec<BigDecimal, BigDecimalMessage> decimal,
    @Inject @Nonnull Codec<ZonedDateTime, ZonedDateTimeMessage> timestamp
) implements GRPCCodec<InvoiceAllocationResponseDTO, InvoiceAllocationResponseMessage> {

    public InvoiceAllocationResponseDTOCodec {
        Objects.requireNonNull(uuid, "uuid");
        Objects.requireNonNull(decimal, "decimal");
        Objects.requireNonNull(timestamp, "timestamp");
    }

    @Nonnull
    public InvoiceAllocationResponseMessage encode(@Nonnull InvoiceAllocationResponseDTO bean) {
        final var invoice = uuid.encode(bean.invoice());
        final var amount = decimal.encode(bean.amount());
        final var lastUpdated = timestamp.encode(bean.lastUpdated());
        return InvoiceAllocationResponseMessage.newBuilder()
            .setInvoice(invoice)
            .setReference(bean.reference())
            .setAmount(amount)
            .setLastUpdated(lastUpdated)
            .build();
    }

    @Nonnull
    @Override
    public InvoiceAllocationResponseDTO decode(@Nonnull InvoiceAllocationResponseMessage protobuf) {
        final var invoice = uuid.decode(protobuf.getInvoice());
        final var amount = decimal.decode(protobuf.getAmount());
        final var lastUpdated = timestamp.decode(protobuf.getLastUpdated());
        return new GRPCInvoiceAllocationResponseDTO(invoice, protobuf.getReference(), amount, lastUpdated);
    }
}