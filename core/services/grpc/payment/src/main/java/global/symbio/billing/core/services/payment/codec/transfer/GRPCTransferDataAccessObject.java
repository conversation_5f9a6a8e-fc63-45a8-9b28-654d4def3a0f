package global.symbio.billing.core.services.payment.codec.transfer;

import global.symbio.billing.core.country.persistence.api.CountryDataAccessObject;
import global.symbio.billing.core.currency.persistence.api.CurrencyDataAccessObject;
import global.symbio.billing.core.exception.sdk.BillingEntityUnmodifiableException;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AccountAllocationDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.transfer.Transfer;
import global.symbio.billing.core.payment.persistence.api.transfer.TransferDataAccessObject;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.Getter;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Objects;
import java.util.UUID;

@Getter
public class GRPCTransferDataAccessObject extends TransferDataAccessObject {

    @Nonnull
    private final UUID identifier;

    @Nonnull
    private final AccountAllocationDataAccessObject<?> transferIn;

    @Nonnull
    private final AccountAllocationDataAccessObject<?> transferOut;

    @Nonnull
    private final CurrencyDataAccessObject currency;

    @Nonnull
    private final BigDecimal amount;

    @Nullable
    private final String description;

    @Nonnull
    private final CountryDataAccessObject country;

    @Nonnull
    private final AccountAllocationDataAccessObject<?> origin;

    @Nonnull
    private final ZonedDateTime timestamp;

    public GRPCTransferDataAccessObject(
        @Nonnull UUID identifier,
        @Nonnull AccountAllocationDataAccessObject<?> transferIn,
        @Nonnull AccountAllocationDataAccessObject<?> transferOut,
        @Nonnull CurrencyDataAccessObject currency,
        @Nonnull BigDecimal amount,
        @Nullable String description,
        @Nonnull CountryDataAccessObject country,
        @Nonnull AccountAllocationDataAccessObject<?> origin,
        @Nonnull ZonedDateTime timestamp
    ) {
        this.identifier = Objects.requireNonNull(identifier, "identifier");
        this.transferOut = Objects.requireNonNull(transferOut, "transferOut");
        this.transferIn = Objects.requireNonNull(transferIn, "transferIn");
        this.currency = Objects.requireNonNull(currency, "currency");
        this.amount = Objects.requireNonNull(amount, "amount");
        this.description = description;
        this.country = Objects.requireNonNull(country, "country");
        this.origin = Objects.requireNonNull(origin, "origin");
        this.timestamp = Objects.requireNonNull(timestamp, "timestamp");
    }

    @Nonnull
    @Override
    public TransferDataAccessObject identifier(@Nonnull UUID identifier) {
        throw new BillingEntityUnmodifiableException(Transfer.class, this.identifier, "identifier");
    }

    @Nonnull
    @Override
    public TransferDataAccessObject transferIn(@Nonnull AccountAllocationDataAccessObject<?> transferIn) {
        throw new BillingEntityUnmodifiableException(Transfer.class, this.identifier, "transferIn");
    }

    @Nonnull
    @Override
    public TransferDataAccessObject transferOut(@Nonnull AccountAllocationDataAccessObject<?> transferOut) {
        throw new BillingEntityUnmodifiableException(Transfer.class, this.identifier, "transferOut");
    }

    @Nonnull
    @Override
    public TransferDataAccessObject currency(@Nonnull CurrencyDataAccessObject currency) {
        throw new BillingEntityUnmodifiableException(Transfer.class, this.identifier, "currency");
    }

    @Nonnull
    @Override
    public TransferDataAccessObject amount(@Nonnull BigDecimal amount) {
        throw new BillingEntityUnmodifiableException(Transfer.class, this.identifier, "amount");
    }

    @Nonnull
    @Override
    public TransferDataAccessObject description(@Nullable String description) {
        throw new BillingEntityUnmodifiableException(Transfer.class, this.identifier, "description");
    }

    @Nonnull
    @Override
    public TransferDataAccessObject country(@Nonnull CountryDataAccessObject country) {
        throw new BillingEntityUnmodifiableException(Transfer.class, this.identifier, "country");
    }

    @Nonnull
    @Override
    public TransferDataAccessObject origin(@Nonnull AccountAllocationDataAccessObject<?> origin) {
        throw new BillingEntityUnmodifiableException(Transfer.class, this.identifier, "origin");
    }

    @Nonnull
    @Override
    public TransferDataAccessObject timestamp(@Nonnull ZonedDateTime timestamp) {
        throw new BillingEntityUnmodifiableException(Transfer.class, this.identifier, "timestamp");
    }
}