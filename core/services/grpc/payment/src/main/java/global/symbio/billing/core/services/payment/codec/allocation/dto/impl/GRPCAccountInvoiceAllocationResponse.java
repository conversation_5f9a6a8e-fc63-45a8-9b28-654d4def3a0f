package global.symbio.billing.core.services.payment.codec.allocation.dto.impl;

import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.payment.PaymentAccountAllocation;
import global.symbio.billing.core.services.payment.codec.allocation.dto.AccountInvoiceAllocationResponse;
import global.symbio.billing.core.services.payment.codec.allocation.dto.InvoiceAllocationDTO;
import jakarta.annotation.Nonnull;

import java.util.Objects;
import java.util.Set;

public record GRPCAccountInvoiceAllocationResponse(
    @Nonnull PaymentAccountAllocation<?> accountAllocation,
    @Nonnull Set<InvoiceAllocationDTO> invoiceAllocations
) implements AccountInvoiceAllocationResponse {

    public GRPCAccountInvoiceAllocationResponse {
        Objects.requireNonNull(accountAllocation, "accountAllocation");
        Objects.requireNonNull(invoiceAllocations, "invoiceAllocations");
    }
}