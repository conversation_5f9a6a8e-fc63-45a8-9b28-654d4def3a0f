package global.symbio.billing.core.services.payment.codec.payment.impl;

import global.symbio.billing.core.payment.persistence.api.payment.Payment;
import global.symbio.billing.core.services.payment.codec.allocation.dto.AccountInvoiceAllocationResponse;
import global.symbio.billing.core.services.payment.codec.payment.PaymentResponse;
import io.micronaut.core.annotation.Introspected;
import jakarta.annotation.Nonnull;

import java.util.Objects;
import java.util.Set;

@Introspected
public record PaymentResponseImpl(
    @Nonnull Payment payment,
    @Nonnull Set<AccountInvoiceAllocationResponse> accountAllocations
) implements PaymentResponse {

    public PaymentResponseImpl {
        Objects.requireNonNull(payment, "payment");
        Objects.requireNonNull(accountAllocations, "accountAllocation");
    }
}
