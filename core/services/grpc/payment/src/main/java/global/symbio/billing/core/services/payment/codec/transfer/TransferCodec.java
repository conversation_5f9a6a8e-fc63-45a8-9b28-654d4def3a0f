package global.symbio.billing.core.services.payment.codec.transfer;

import global.symbio.billing.core.country.persistence.api.Country;
import global.symbio.billing.core.currency.persistence.api.Currency;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AccountAllocation;
import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.payment.PaymentAccountAllocation;
import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.transfer.TransferInAccountAllocation;
import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.transfer.TransferOutAccountAllocation;
import global.symbio.billing.core.payment.persistence.api.transfer.Transfer;
import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.message.*;
import global.symbio.billing.core.services.grpc.payment.entities.AccountAllocationMessageApi;
import global.symbio.billing.core.services.grpc.payment.entities.TransferMessage;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Objects;
import java.util.UUID;

@Singleton
public record TransferCodec(
    @Inject @Nonnull Codec<UUID, UUIDMessage> uuid,
    @Inject @Nonnull Codec<BigDecimal, BigDecimalMessage> decimal,
    @Inject @Nonnull Codec<Currency, CurrencyMessage> currency,
    @Inject @Nonnull Codec<Country, CountryMessage> country,
    @Inject @Nonnull Codec<ZonedDateTime, ZonedDateTimeMessage> timestamp,
    @Inject @Nonnull Codec<TransferInAccountAllocation<?>, AccountAllocationMessageApi> inbound,
    @Inject @Nonnull Codec<TransferOutAccountAllocation<?>, AccountAllocationMessageApi> outbound,
    @Inject @Nonnull Codec<PaymentAccountAllocation<?>, AccountAllocationMessageApi> payment
) implements GRPCCodec<Transfer, TransferMessage> {

    public TransferCodec {
        Objects.requireNonNull(uuid, "uuid");
        Objects.requireNonNull(decimal, "decimal");
        Objects.requireNonNull(currency, "currency");
        Objects.requireNonNull(country, "country");
        Objects.requireNonNull(timestamp, "timestamp");
        Objects.requireNonNull(inbound, "inbound");
        Objects.requireNonNull(outbound, "outbound");
        Objects.requireNonNull(payment, "payment");
    }

    @Nonnull
    @Override
    public TransferMessage encode(@Nonnull Transfer bean) {
        final var identifier = uuid.encode(bean.getIdentifier());
        final var transferIn = encode(bean.getTransferIn());
        final var transferOut = encode(bean.getTransferOut());
        final var currency = this.currency.encode(bean.getCurrency());
        final var amount = decimal.encode(bean.getAmount());
        final var country = this.country.encode(bean.getCountry());
        final var origin = encode(bean.getOrigin());
        final var timestamp = this.timestamp.encode(bean.getTimestamp());
        final var transfer = TransferMessage.newBuilder()
            .setIdentifier(identifier)
            .setTransferIn(transferIn)
            .setTransferOut(transferOut)
            .setCurrency(currency)
            .setAmount(amount)
            .setCountry(country)
            .setOrigin(origin)
            .setTimestamp(timestamp);

        if (bean.getDescription() != null) {
            transfer.setDescription(bean.getDescription());
        }
        return transfer.build();
    }

    @Nonnull
    @Override
    public Transfer decode(@Nonnull TransferMessage protobuf) {
        final var identifier = uuid.decode(protobuf.getIdentifier());
        final var transferIn = decode(protobuf.getTransferIn()).data();
        final var transferOut = decode(protobuf.getTransferOut()).data();
        final var currency = this.currency.decode(protobuf.getCurrency()).data();
        final var amount = decimal.decode(protobuf.getAmount());
        final var description = protobuf.hasDescription() ? protobuf.getDescription() : null;
        final var country = this.country.decode(protobuf.getCountry()).data();
        final var origin = decode(protobuf.getOrigin()).data();
        final var timestamp = this.timestamp.decode(protobuf.getTimestamp());
        return new GRPCTransferDataAccessObject(identifier, transferIn, transferOut, currency, amount, description, country, origin, timestamp).entity();
    }

    @Nonnull
    private AccountAllocationMessageApi encode(@Nonnull AccountAllocation<?> bean) {
        return switch (bean) {
            case PaymentAccountAllocation<?> allocation -> payment.encode(allocation);
            case TransferInAccountAllocation<?> allocation -> inbound.encode(allocation);
            case TransferOutAccountAllocation<?> allocation -> outbound.encode(allocation);
            default -> throw new IllegalStateException("Unexpected account allocation type: " + bean);
        };
    }

    @Nonnull
    private AccountAllocation<?> decode(@Nonnull AccountAllocationMessageApi protobuf) {
        return switch (protobuf.getType()) {
            case PAYMENT -> payment.decode(protobuf);
            case TRANSFER_IN -> inbound.decode(protobuf);
            case TRANSFER_OUT -> outbound.decode(protobuf);
            default -> throw new IllegalStateException("Unexpected allocation type: " + protobuf.getType());
        };
    }
}
