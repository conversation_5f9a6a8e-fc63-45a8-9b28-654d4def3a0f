package global.symbio.billing.core.services.payment.codec.allocation;

import global.symbio.billing.core.country.persistence.api.Country;
import global.symbio.billing.core.currency.persistence.api.Currency;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AllocationDirection;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AllocationType;
import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.payment.PaymentAccountAllocation;
import global.symbio.billing.core.payment.persistence.api.allocation.state.AllocationState;
import global.symbio.billing.core.payment.persistence.api.payment.Payment;
import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.message.*;
import global.symbio.billing.core.services.grpc.payment.entities.*;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Objects;
import java.util.UUID;

@Singleton
public record PaymentAccountAllocationCodec(
    @Inject @Nonnull Codec<UUID, UUIDMessage> uuid,
    @Inject @Nonnull Codec<BigDecimal, BigDecimalMessage> decimal,
    @Inject @Nonnull Codec<AllocationType, AllocationTypeMessage> type,
    @Inject @Nonnull Codec<AllocationState, AllocationStateMessage> state,
    @Inject @Nonnull Codec<AllocationDirection, AllocationDirectionMessage> direction,
    @Inject @Nonnull Codec<Currency, CurrencyMessage> currency,
    @Inject @Nonnull Codec<Country, CountryMessage> country,
    @Inject @Nonnull Codec<ZonedDateTime, ZonedDateTimeMessage> timestamp,
    @Inject @Nonnull Codec<Payment, PaymentMessage> payment
) implements GRPCCodec<PaymentAccountAllocation<?>, AccountAllocationMessageApi> {

    public PaymentAccountAllocationCodec {
        Objects.requireNonNull(uuid, "uuid");
        Objects.requireNonNull(decimal, "decimal");
        Objects.requireNonNull(type, "type");
        Objects.requireNonNull(state, "state");
        Objects.requireNonNull(direction, "direction");
        Objects.requireNonNull(currency, "currency");
        Objects.requireNonNull(country, "country");
        Objects.requireNonNull(timestamp, "timestamp");
        Objects.requireNonNull(payment, "payment");
    }

    @Nonnull
    @Override
    public AccountAllocationMessageApi encode(@Nonnull PaymentAccountAllocation<?> bean) {
        final var identifier = uuid.encode(bean.getIdentifier());
        final var account = uuid.encode(bean.getAccount());
        final var type = this.type.encode(bean.getType());
        final var country = this.country.encode(bean.getCountry());
        final var currency = this.currency.encode(bean.getCurrency());
        final var amount = decimal.encode(bean.getAmount());
        final var state = this.state.encode(bean.getAllocationState());
        final var direction = this.direction.encode(bean.getDirection());
        final var sentTimestamp = bean.getSentTimestamp() != null ? this.timestamp.encode(bean.getSentTimestamp()) : null;
        final var settledTimestamp = bean.getSettledTimestamp() != null ? this.timestamp.encode(bean.getSettledTimestamp()) : null;
        final var timestamp = this.timestamp.encode(bean.getTimestamp());
        final var payment = this.payment.encode(bean.getPayment());

        final var builder = AccountAllocationMessageApi.newBuilder();
        builder
            .setPayment(payment)
            .setIdentifier(identifier)
            .setAccount(account)
            .setType(type)
            .setCountry(country)
            .setCurrency(currency)
            .setAmount(amount)
            .setAllocationState(state)
            .setAllocationDirection(direction)
            .setTimestamp(timestamp);

        if (sentTimestamp != null) {
            builder.setSentTimestamp(sentTimestamp);
        }

        if (settledTimestamp != null) {
            builder.setSettledTimestamp(settledTimestamp);
        }

        return builder.build();
    }

    @Nonnull
    @Override
    public PaymentAccountAllocation<?> decode(@Nonnull AccountAllocationMessageApi protobuf) {
        final var identifier = uuid.decode(protobuf.getIdentifier());
        final var account = uuid.decode(protobuf.getAccount());
        final var type = this.type.decode(protobuf.getType());
        final var country = this.country.decode(protobuf.getCountry()).data();
        final var currency = this.currency.decode(protobuf.getCurrency()).data();
        final var amount = decimal.decode(protobuf.getAmount());
        final var state = this.state.decode(protobuf.getAllocationState());
        final var direction = this.direction.decode(protobuf.getAllocationDirection());
        final var sentTimestamp = protobuf.hasSentTimestamp() ? this.timestamp.decode(protobuf.getSentTimestamp()) : null;
        final var settledTimestamp = protobuf.hasSettledTimestamp() ? this.timestamp.decode(protobuf.getSettledTimestamp()) : null;
        final var timestamp = this.timestamp.decode(protobuf.getTimestamp());

        if (protobuf.hasPayment()) {
            final var payment = this.payment.decode(protobuf.getPayment());
            return new GRPCPaymentAccountAllocationDataAccessObject(identifier, account, type, country, currency, amount, state, direction, sentTimestamp, settledTimestamp, timestamp, payment.data()).entity();
        } else {
            throw new IllegalStateException("Unexpected allocation type.");
        }
    }
}