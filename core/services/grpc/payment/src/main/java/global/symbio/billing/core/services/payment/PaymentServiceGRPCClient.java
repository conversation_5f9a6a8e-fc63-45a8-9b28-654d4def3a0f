package global.symbio.billing.core.services.payment;

import global.symbio.billing.core.payment.persistence.api.allocation.account.AccountAllocation;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AllocationType;
import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.payment.PaymentAccountAllocation;
import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.transfer.TransferInAccountAllocation;
import global.symbio.billing.core.payment.persistence.api.cancellation.PaymentCancellation;
import global.symbio.billing.core.payment.persistence.api.payment.PaymentStatus;
import global.symbio.billing.core.payment.persistence.api.payment.state.PaymentState;
import global.symbio.billing.core.payment.persistence.api.transfer.Transfer;
import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.message.BigDecimalMessage;
import global.symbio.billing.core.services.grpc.message.UUIDMessage;
import global.symbio.billing.core.services.grpc.message.ZonedDateTimeMessage;
import global.symbio.billing.core.services.grpc.payment.*;
import global.symbio.billing.core.services.grpc.payment.dto.AccountInvoiceAllocationMessage;
import global.symbio.billing.core.services.grpc.payment.dto.InvoiceAllocationMessage;
import global.symbio.billing.core.services.grpc.payment.dto.PaymentInvoiceAllocationMessage;
import global.symbio.billing.core.services.grpc.payment.entities.*;
import global.symbio.billing.core.services.payment.codec.allocation.dto.AccountInvoiceAllocation;
import global.symbio.billing.core.services.payment.codec.allocation.dto.InvoiceAllocationDTO;
import global.symbio.billing.core.services.payment.codec.allocation.dto.PaymentInvoiceAllocationDTO;
import global.symbio.billing.core.services.payment.codec.payment.PaymentResponse;
import io.micronaut.context.annotation.Requires;
import io.micronaut.context.annotation.Secondary;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Secondary
@Singleton
@Requires(beans = PaymentGrpc.PaymentFutureStub.class)
public class PaymentServiceGRPCClient implements PaymentService {

    @Nonnull
    private final PaymentGrpc.PaymentFutureStub service;

    @Nonnull
    private final Codec<UUID, UUIDMessage> uuid;

    @Nonnull
    private final Codec<BigDecimal, BigDecimalMessage> decimal;

    @Nonnull
    private final Codec<ZonedDateTime, ZonedDateTimeMessage> timestamp;

    @Nonnull
    private final Codec<AccountInvoiceAllocation, AccountInvoiceAllocationMessage> allocation;

    @Nonnull
    private final Codec<PaymentStatus, PaymentStatusMessage> status;

    @Nonnull
    private final Codec<PaymentAccountAllocation<?>, AccountAllocationMessageApi> paymentAccountAllocation;

    @Nonnull
    private final Codec<TransferInAccountAllocation<?>, AccountAllocationMessageApi> transferInAccountAllocation;

    @Nonnull
    private final Codec<InvoiceAllocationDTO, InvoiceAllocationMessage> invoiceAllocation;

    @Nonnull
    private final Codec<PaymentInvoiceAllocationDTO, PaymentInvoiceAllocationMessage> paymentInvoiceAllocation;

    @Nonnull
    private final Codec<PaymentState, PaymentStateMessage> paymentState;

    @Nonnull
    private final Codec<Transfer, TransferMessage> transfer;

    @Nonnull
    private final Codec<PaymentResponse, CreatePaymentResponse> paymentResponse;

    @Nonnull
    private final Codec<AllocationType, AllocationTypeMessage> type;

    @Nonnull
    private final Codec<PaymentCancellation, PaymentCancellationMessage> cancellation;

    @Inject
    public PaymentServiceGRPCClient(
        @Nonnull PaymentGrpc.PaymentFutureStub service,
        @Nonnull Codec<UUID, UUIDMessage> uuid,
        @Nonnull Codec<BigDecimal, BigDecimalMessage> decimal,
        @Nonnull Codec<ZonedDateTime, ZonedDateTimeMessage> timestamp,
        @Nonnull Codec<AccountInvoiceAllocation, AccountInvoiceAllocationMessage> allocation,
        @Nonnull Codec<PaymentStatus, PaymentStatusMessage> status,
        @Nonnull Codec<PaymentAccountAllocation<?>, AccountAllocationMessageApi> paymentAccountAllocation,
        @Nonnull Codec<PaymentInvoiceAllocationDTO, PaymentInvoiceAllocationMessage> paymentInvoiceAllocation,
        @Nonnull Codec<PaymentState, PaymentStateMessage> paymentState,
        @Nonnull Codec<TransferInAccountAllocation<?>, AccountAllocationMessageApi> transferInAccountAllocation,
        @Nonnull Codec<InvoiceAllocationDTO, InvoiceAllocationMessage> invoiceAllocation,
        @Nonnull Codec<Transfer, TransferMessage> transfer,
        @Nonnull Codec<PaymentResponse, CreatePaymentResponse> paymentResponse,
        @Nonnull Codec<AllocationType, AllocationTypeMessage> type,
        @Nonnull Codec<PaymentCancellation, PaymentCancellationMessage> cancellation
    ) {
        this.service = Objects.requireNonNull(service, "service");
        this.uuid = Objects.requireNonNull(uuid, "uuid");
        this.decimal = Objects.requireNonNull(decimal, "decimal");
        this.timestamp = Objects.requireNonNull(timestamp, "timestamp");
        this.allocation = Objects.requireNonNull(allocation, "allocation");
        this.status = Objects.requireNonNull(status, "status");
        this.paymentAccountAllocation = Objects.requireNonNull(paymentAccountAllocation, "paymentAccountAllocation");
        this.paymentInvoiceAllocation = Objects.requireNonNull(paymentInvoiceAllocation, "paymentInvoiceAllocation");
        this.transferInAccountAllocation = Objects.requireNonNull(transferInAccountAllocation, "transferInAccountAllocation");
        this.invoiceAllocation = Objects.requireNonNull(invoiceAllocation, "invoiceAllocation");
        this.paymentState = Objects.requireNonNull(paymentState, "paymentState");
        this.transfer = Objects.requireNonNull(transfer, "transfer");
        this.paymentResponse = Objects.requireNonNull(paymentResponse, "paymentResponse");
        this.type = Objects.requireNonNull(type, "type");
        this.cancellation = Objects.requireNonNull(cancellation, "cancellation");
    }

    @Nonnull
    @Override
    public PaymentResponse create(
        @Nonnull UUID _identifier,
        @Nonnull UUID business,
        @Nonnull Integer _method,
        @Nonnull String _currency,
        @Nonnull BigDecimal _amount,
        @Nonnull ZonedDateTime _receipt,
        @Nullable String _description,
        @Nonnull String _country,
        @Nonnull Set<? extends AccountInvoiceAllocation> _allocations
    ) {
        try {
            final var allocations = _allocations.stream().map(allocation::encode).toList();
            final var request = CreatePaymentRequest.newBuilder()
                .setIdentifier(uuid.encode(_identifier))
                .setBusiness(uuid.encode(business))
                .setMethod(_method)
                .setCurrency(_currency)
                .setAmount(decimal.encode(_amount))
                .setReceiptDate(timestamp.encode(_receipt))
                .setCountry(_country)
                .addAllAllocations(allocations);
            if (_description != null) {
                request.setDescription(_description);
            }
            final var response = service.createPayment(request.build()).get();
            return paymentResponse.decode(response);
        } catch (Throwable cause) {
            log.info("Exception creating payment: {}", _identifier, cause);
            throw new RuntimeException(cause);
        }
    }

    @Nonnull
    @Override
    public Collection<PaymentAccountAllocation<?>> getPaymentAccountAllocations(
        @Nullable UUID account,
        @Nullable ZonedDateTime paymentDateStart,
        @Nullable ZonedDateTime paymentDateEnd,
        @Nullable ZonedDateTime receiptDateStart,
        @Nullable ZonedDateTime receiptDateEnd,
        @Nullable Set<String> allocation_states,
        @Nullable Set<Integer> methods,
        @Nullable Set<PaymentStatus> status
    ) {
        try {
            final var request = GetPaymentAccountAllocationsRequest.newBuilder();
            if (account != null) {
                request.setAccount(uuid.encode(account));
            }
            if (paymentDateStart != null) {
                request.setPaymentDateStart(timestamp.encode(paymentDateStart));
            }
            if (paymentDateEnd != null) {
                request.setPaymentDateEnd(timestamp.encode(paymentDateEnd));
            }
            if (receiptDateStart != null) {
                request.setReceiptDateStart(timestamp.encode(receiptDateStart));
            }
            if (receiptDateEnd != null) {
                request.setReceiptDateEnd(timestamp.encode(receiptDateEnd));
            }
            if (allocation_states != null) {
                request.addAllAllocationStates(allocation_states);
            }
            if (methods != null) {
                request.addAllMethods(methods);
            }
            if (status != null) {
                request.addAllStatus(status.stream().map(this.status::encode).toList());
            }
            final var response = service.getPaymentAccountAllocations(request.build()).get();
            return response.getAllocationsList().stream().map(paymentAccountAllocation::decode).collect(Collectors.toList());
        } catch (Throwable cause) {
            log.info("Exception getting payments", cause);
            throw new RuntimeException(cause);
        }
    }

    @Nonnull
    @Override
    public AccountAllocation<?> assignAllocationToInvoices(
        @Nonnull UUID identifier,
        @Nonnull Set<? extends InvoiceAllocationDTO> allocations
    ) {
        try {
            final var invoiceAllocations = allocations.stream().map(invoiceAllocation::encode).toList();
            final var request = AssignAllocationToInvoicesRequest.newBuilder()
                .setIdentifier(uuid.encode(identifier))
                .addAllAllocations(invoiceAllocations)
                .build();
            final var response = service.assignAllocationToInvoices(request).get();
            return decode(response.getAllocation());
        } catch (Throwable cause) {
            log.info("Exception assigning allocations to invoices: {}", identifier, cause);
            throw new RuntimeException(cause);
        }
    }

    @Nonnull
    @Override
    public Collection<PaymentState> getPaymentStates() {
        try {
            final var request = GetPaymentStatesRequest.newBuilder().build();
            final var response = service.getPaymentStates(request).get();
            return response.getPaymentStateList().stream().map(paymentState::decode).toList();
        } catch (Throwable cause) {
            log.info("Exception getting payment states", cause);
            throw new RuntimeException(cause);
        }
    }

    @Nonnull
    @Override
    public Transfer createTransfer(@Nonnull UUID identifier, @Nonnull UUID fromAccountAllocation, @Nonnull UUID toAccount, @Nonnull BigDecimal amount, @Nullable String description) {
        try {
            final var request = CreateTransferRequest.newBuilder()
                .setIdentifier(uuid.encode(identifier))
                .setAccountAllocation(uuid.encode(fromAccountAllocation))
                .setAccount(uuid.encode(toAccount))
                .setAmount(decimal.encode(amount));
            if (description != null) {
                request.setDescription(description);
            }
            final var response = service.createTransfer(request.build()).get();
            return transfer.decode(response.getTransfer());
        } catch (Throwable cause) {
            log.info("Exception creating transfer: {}", identifier, cause);
            throw new RuntimeException(cause);
        }
    }

    @Nonnull
    private AccountAllocation<?> decode(@Nonnull AccountAllocationMessageApi allocation) {
        return switch(allocation.getType()) {
            case PAYMENT -> paymentAccountAllocation.decode(allocation);
            case TRANSFER_IN -> transferInAccountAllocation.decode(allocation);
            default -> throw new IllegalStateException("Unexpected allocation type.");
        };
    }

    @Nonnull
    @Override
    public Optional<PaymentAccountAllocation<?>> getPaymentAccountAllocation(@Nonnull UUID identifier) {
        try {
            final var request = GetPaymentAccountAllocationRequest.newBuilder()
                .setIdentifier(uuid.encode(identifier))
                .build();
            final var response = service.getPaymentAccountAllocation(request).get();
            if (response.hasAllocation()) {
                return Optional.of(paymentAccountAllocation.decode(response.getAllocation()));
            }
            return Optional.empty();
        } catch (Throwable cause) {
            log.info("Exception getting payment account allocation: {}", identifier, cause);
            throw new RuntimeException(cause);
        }
    }

    @Nonnull
    @Override
    public Optional<Transfer> getTransferByTransferIn(@Nonnull UUID identifier) {
        try {
            final var request = GetTransferByTransferInRequest.newBuilder()
                .setIdentifier(uuid.encode(identifier))
                .build();
            final var response = service.getTransferByTransferIn(request).get();
            if (response.hasTransfer()) {
                return Optional.of(transfer.decode(response.getTransfer()));
            }
            return Optional.empty();
        } catch (Throwable cause) {
            log.info("Exception getting transfer: {}", identifier, cause);
            throw new RuntimeException(cause);
        }
    }

    @Nonnull
    @Override
    public Collection<InvoiceAllocationDTO> getInvoiceAllocations(@Nonnull UUID accountAllocation) {
        try {
            final var request = GetInvoiceAllocationsRequest.newBuilder()
                .setAccountAllocation(uuid.encode(accountAllocation))
                .build();
            final var response =  service.getInvoiceAllocations(request).get();
            return response.getInvoiceAllocationsList().stream().map(invoiceAllocation::decode).collect(Collectors.toList());
        } catch (Throwable cause) {
            log.info("Exception getting payments", cause);
            throw new RuntimeException(cause);
        }
    }

    @Nonnull
    @Override
    public Collection<Transfer> getTransfers(@Nonnull UUID account, @Nullable ZonedDateTime transferDateStart, @Nullable ZonedDateTime transferDateEnd, @Nullable AllocationType type) {
        try {
            final var request = GetTransfersRequest.newBuilder().setAccount(uuid.encode(account));
            if (transferDateStart != null) {
                request.setTransferDateStart(timestamp.encode(transferDateStart));
            }
            if (transferDateEnd != null) {
                request.setTransferDateEnd(timestamp.encode(transferDateEnd));
            }
            if (type != null) {
                request.setType(this.type.encode(type));
            }
            final var response = service.getTransfers(request.build()).get();
            return response.getTransfersList().stream().map(transfer::decode).collect(Collectors.toList());
        } catch (Throwable cause) {
            log.info("Exception getting payments", cause);
            throw new RuntimeException(cause);
        }
    }

    @Nonnull
    @Override
    public Optional<AccountAllocation<?>> getAccountAllocation(@Nonnull UUID identifier) {
        try {
            final var request = GetAccountAllocationRequest.newBuilder()
                .setIdentifier(uuid.encode(identifier))
                .build();
            final var response = service.getAccountAllocation(request).get();
            if (response.hasAllocation()) {
                return Optional.of(decode(response.getAllocation()));
            }
            return Optional.empty();
        } catch (Throwable cause) {
            log.info("Exception getting account allocation: {}", identifier, cause);
            throw new RuntimeException(cause);
        }
    }

    @Nonnull
    @Override
    public PaymentCancellation cancel(@Nonnull UUID identifier, @Nonnull String reason) {
        try {
            final var request = CancelPaymentRequest.newBuilder()
                .setIdentifier(uuid.encode(identifier))
                .setReason(reason);
            final var response = service.cancelPayment(request.build()).get();
            return cancellation.decode(response.getCancellation());
        } catch (Throwable cause) {
            log.info("Exception cancelling payment: {}", identifier, cause);
            throw new RuntimeException(cause);
        }
    }

    @Nonnull
    @Override
    public Optional<PaymentCancellation> getPaymentCancellation(@Nonnull UUID identifier) {
        try {
            final var request = GetPaymentCancellationRequest.newBuilder()
                .setIdentifier(uuid.encode(identifier))
                .build();
            final var response = service.getPaymentCancellation(request).get();
            if (response.hasCancellation()) {
                return Optional.of(cancellation.decode(response.getCancellation()));
            }
            return Optional.empty();
        } catch (Throwable cause) {
            log.info("Exception getting payment cancellation: {}", identifier, cause);
            throw new RuntimeException(cause);
        }
    }

    @Nonnull
    @Override
    public Collection<PaymentInvoiceAllocationDTO> getInvoiceAllocationsByInvoice(@Nonnull UUID invoice) {
        try {
            final var request = GetInvoiceAllocationsByInvoiceRequest.newBuilder()
                .setInvoice(uuid.encode(invoice))
                .build();
            final var response = service.getInvoiceAllocationsByInvoice(request).get();
            return response.getPaymentInvoiceAllocationsList().stream().map(paymentInvoiceAllocation::decode).toList();
        } catch (Throwable cause) {
            log.info("Exception getting invoice allocations: {}", invoice, cause);
            throw new RuntimeException(cause);
        }
    }
}