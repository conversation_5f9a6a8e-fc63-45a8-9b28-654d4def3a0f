package global.symbio.billing.core.services.payment.codec.cancellation;

import global.symbio.billing.core.payment.persistence.api.cancellation.PaymentCancellation;
import global.symbio.billing.core.payment.persistence.api.payment.Payment;
import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.message.UUIDMessage;
import global.symbio.billing.core.services.grpc.message.ZonedDateTimeMessage;
import global.symbio.billing.core.services.grpc.payment.entities.PaymentCancellationMessage;
import global.symbio.billing.core.services.grpc.payment.entities.PaymentMessage;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.time.ZonedDateTime;
import java.util.Objects;
import java.util.UUID;

@Singleton
public record PaymentCancellationCodec(
    @Inject @Nonnull Codec<UUID, UUIDMessage> uuid,
    @Inject @Nonnull Codec<Payment, PaymentMessage> payment,
    @Inject @Nonnull Codec<ZonedDateTime, ZonedDateTimeMessage> timestamp
) implements GRPCCodec<PaymentCancellation, PaymentCancellationMessage> {

    public PaymentCancellationCodec {
        Objects.requireNonNull(uuid, "uuid");
        Objects.requireNonNull(payment, "payment");
        Objects.requireNonNull(timestamp, "timestamp");
    }

    @Nonnull
    @Override
    public PaymentCancellationMessage encode(@Nonnull PaymentCancellation bean) {
        final var identifier = uuid.encode(bean.getIdentifier());
        final var payment = this.payment.encode(bean.getPayment());
        final var reference = bean.getReference();
        final var sentTimestamp = bean.getSentTimestamp() != null ? this.timestamp.encode(bean.getSentTimestamp()) : null;
        final var settledTimestamp = bean.getSettledTimestamp() != null ? this.timestamp.encode(bean.getSettledTimestamp()) : null;
        final var timestamp = this.timestamp.encode(bean.getTimestamp());
        final var paymentCancellation = PaymentCancellationMessage.newBuilder()
            .setIdentifier(identifier)
            .setPayment(payment)
            .setReference(reference)
            .setReason(bean.getReason())
            .setTimestamp(timestamp);

        if (sentTimestamp != null) {
            paymentCancellation.setSentTimestamp(sentTimestamp);
        }

        if (settledTimestamp != null) {
            paymentCancellation.setSettledTimestamp(settledTimestamp);
        }

        return paymentCancellation.build();
    }

    @Nonnull
    @Override
    public PaymentCancellation decode(@Nonnull PaymentCancellationMessage protobuf) {
        final var identifier = uuid.decode(protobuf.getIdentifier());
        final var payment = this.payment.decode(protobuf.getPayment()).data();
        final var reason = protobuf.getReason();
        final var reference = protobuf.getReference();
        final var sentTimestamp = protobuf.hasSentTimestamp() ? this.timestamp.decode(protobuf.getSentTimestamp()) : null;
        final var settledTimestamp = protobuf.hasSettledTimestamp() ? this.timestamp.decode(protobuf.getSettledTimestamp()) : null;
        final var timestamp = this.timestamp.decode(protobuf.getTimestamp());
        return new GRPCPaymentCancellationDataAccessObject(identifier, payment, reason, reference, sentTimestamp, settledTimestamp, timestamp).entity();
    }
}
