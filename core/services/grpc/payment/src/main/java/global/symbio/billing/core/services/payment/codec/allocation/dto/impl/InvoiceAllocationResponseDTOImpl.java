package global.symbio.billing.core.services.payment.codec.allocation.dto.impl;

import global.symbio.billing.core.services.payment.codec.allocation.dto.InvoiceAllocationResponseDTO;
import jakarta.annotation.Nonnull;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Objects;
import java.util.UUID;

public record InvoiceAllocationResponseDTOImpl (
    @Nonnull UUID invoice,
    @Nonnull String reference,
    @Nonnull BigDecimal amount,
    @Nonnull ZonedDateTime lastUpdated
) implements InvoiceAllocationResponseDTO {

    public InvoiceAllocationResponseDTOImpl {
        Objects.requireNonNull(invoice, "invoice");
        Objects.requireNonNull(reference, "reference");
        Objects.requireNonNull(amount, "amount");
        Objects.requireNonNull(lastUpdated, "lastUpdated");
    }
}
