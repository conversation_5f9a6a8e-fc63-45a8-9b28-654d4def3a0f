package global.symbio.billing.core.services.payment.codec.allocation.dto;

import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.payment.PaymentAccountAllocation;
import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.payment.AccountInvoiceAllocationResponseMessage;
import global.symbio.billing.core.services.grpc.payment.dto.InvoiceAllocationMessage;
import global.symbio.billing.core.services.grpc.payment.entities.AccountAllocationMessageApi;
import global.symbio.billing.core.services.payment.codec.allocation.dto.impl.GRPCAccountInvoiceAllocationResponse;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.util.Objects;
import java.util.stream.Collectors;

@Singleton
public record AccountInvoiceAllocationResponseCodec(
    @Inject @Nonnull Codec<PaymentAccountAllocation<?>, AccountAllocationMessageApi> account,
    @Inject @Nonnull Codec<InvoiceAllocationDTO, InvoiceAllocationMessage> invoice
) implements GRPCCodec<AccountInvoiceAllocationResponse, AccountInvoiceAllocationResponseMessage> {

    public AccountInvoiceAllocationResponseCodec {
        Objects.requireNonNull(account, "account");
        Objects.requireNonNull(invoice, "invoice");
    }

    @Nonnull
    @Override
    public AccountInvoiceAllocationResponseMessage encode(@Nonnull AccountInvoiceAllocationResponse bean) {
        final var accountAllocation = account.encode(bean.accountAllocation());
        final var invoices = bean.invoiceAllocations().stream().map(invoice::encode).toList();
        return AccountInvoiceAllocationResponseMessage.newBuilder()
            .setAccountAllocation(accountAllocation)
            .addAllInvoiceAllocations(invoices)
            .build();
    }

    @Nonnull
    @Override
    public AccountInvoiceAllocationResponse decode(@Nonnull AccountInvoiceAllocationResponseMessage protobuf) {
        final var accountAllocation = account.decode(protobuf.getAccountAllocation());
        final var invoices = protobuf.getInvoiceAllocationsList().stream().map(invoice::decode).collect(Collectors.toSet());
        return new GRPCAccountInvoiceAllocationResponse(accountAllocation, invoices);
    }
}