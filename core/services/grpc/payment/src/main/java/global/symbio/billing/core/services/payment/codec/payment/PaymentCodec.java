package global.symbio.billing.core.services.payment.codec.payment;

import global.symbio.billing.core.country.persistence.api.Country;
import global.symbio.billing.core.currency.persistence.api.Currency;
import global.symbio.billing.core.payment.persistence.api.payment.Payment;
import global.symbio.billing.core.payment.persistence.api.payment.PaymentStatus;
import global.symbio.billing.core.payment.persistence.api.payment.method.PaymentMethod;
import global.symbio.billing.core.reference.persistence.api.reference.Reference;
import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.message.*;
import global.symbio.billing.core.services.grpc.payment.entities.PaymentMessage;
import global.symbio.billing.core.services.grpc.payment.entities.PaymentStatusMessage;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Objects;
import java.util.UUID;

@Singleton
public record PaymentCodec(
    @Inject @Nonnull Codec<UUID, UUIDMessage> uuid,
    @Inject @Nonnull Codec<BigDecimal, BigDecimalMessage> decimal,
    @Inject @Nonnull Codec<Currency, CurrencyMessage> currency,
    @Inject @Nonnull Codec<Country, CountryMessage> country,
    @Inject @Nonnull Codec<ZonedDateTime, ZonedDateTimeMessage> timestamp,
    @Inject @Nonnull Codec<PaymentMethod, PaymentMethodMessage> method,
    @Inject @Nonnull Codec<PaymentStatus, PaymentStatusMessage> status,
    @Inject @Nonnull Codec<Reference, ReferenceMessage> reference
) implements GRPCCodec<Payment, PaymentMessage> {

    public PaymentCodec {
        Objects.requireNonNull(uuid, "uuid");
        Objects.requireNonNull(decimal, "decimal");
        Objects.requireNonNull(currency, "currency");
        Objects.requireNonNull(country, "country");
        Objects.requireNonNull(timestamp, "timestamp");
        Objects.requireNonNull(method, "method");
        Objects.requireNonNull(status, "status");
        Objects.requireNonNull(reference, "reference");
    }

    @Nonnull
    @Override
    public PaymentMessage encode(@Nonnull Payment bean) {
        final var identifier = uuid.encode(bean.getIdentifier());
        final var business = uuid.encode(bean.getBusiness());
        final var method = this.method.encode(bean.getMethod());
        final var currency = this.currency.encode(bean.getCurrency());
        final var amount = decimal.encode(bean.getAmount());
        final var receipt = timestamp.encode(bean.getReceiptDate());
        final var status = this.status.encode(bean.getStatus());
        final var country = this.country.encode(bean.getCountry());
        final var timestamp = this.timestamp.encode(bean.getTimestamp());
        final var payment = PaymentMessage.newBuilder()
            .setIdentifier(identifier)
            .setBusiness(business)
            .setMethod(method)
            .setCurrency(currency)
            .setAmount(amount)
            .setReceiptDate(receipt)
            .setStatus(status)
            .setCountry(country)
            .setTimestamp(timestamp);

        if (bean.getDescription() != null) {
            payment.setDescription(bean.getDescription());
        }
        if (bean.getReference() != null) {
            payment.setReference(reference.encode(bean.getReference()));
        }
        return payment.build();
    }

    @Nonnull
    @Override
    public Payment decode(@Nonnull PaymentMessage protobuf) {
        final var identifier = uuid.decode(protobuf.getIdentifier());
        final var business = uuid.decode(protobuf.getBusiness());
        final var method = this.method.decode(protobuf.getMethod()).data();
        final var currency = this.currency.decode(protobuf.getCurrency()).data();
        final var amount = decimal.decode(protobuf.getAmount());
        final var receipt = timestamp.decode(protobuf.getReceiptDate());
        final var status = this.status.decode(protobuf.getStatus());
        final var description = protobuf.hasDescription() ? protobuf.getDescription() : null;
        final var country = this.country.decode(protobuf.getCountry()).data();
        final var reference = protobuf.hasReference() ? this.reference.decode(protobuf.getReference()).data() : null;
        final var timestamp = this.timestamp.decode(protobuf.getTimestamp());
        return new GRPCPaymentDataAccessObject(identifier, business, method, currency, amount, receipt, status, description, country, reference, timestamp).entity();
    }
}
