package global.symbio.billing.core.services.payment.codec.payment;

import global.symbio.billing.core.exception.sdk.BillingEntityNotFoundException;
import global.symbio.billing.core.payment.persistence.api.payment.state.PaymentState;
import global.symbio.billing.core.services.grpc.codec.GRPCEnumCodec;
import global.symbio.billing.core.services.grpc.payment.entities.PaymentStateMessage;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

import static global.symbio.billing.core.payment.persistence.api.payment.state.PaymentState.*;


@Singleton
public class PaymentStateCodec implements GRPCEnumCodec<PaymentState, PaymentStateMessage> {

    @Nonnull
    @Override
    public PaymentStateMessage encode(@Nonnull PaymentState bean) {
        return switch (bean) {
            case PAID -> PaymentStateMessage.PAID;
            case PARTIALLY_PAID ->  PaymentStateMessage.PARTIALLY_PAID;
            case UNPAID -> PaymentStateMessage.UNPAID;
            default -> throw new BillingEntityNotFoundException(PaymentState.class, bean);
        };
    }

    @Nonnull
    @Override
    public PaymentState decode(@Nonnull PaymentStateMessage encoded) {
        return switch (encoded) {
            case PaymentStateMessage.PAID -> PAID ;
            case PaymentStateMessage.PARTIALLY_PAID -> PARTIALLY_PAID ;
            case PaymentStateMessage.UNPAID -> UNPAID;
            default -> throw new BillingEntityNotFoundException(PaymentStateMessage.class, encoded);
        };
    }
}
