package global.symbio.billing.core.services.payment.codec.cancellation;

import global.symbio.billing.core.exception.sdk.BillingEntityUnmodifiableException;
import global.symbio.billing.core.payment.persistence.api.cancellation.PaymentCancellation;
import global.symbio.billing.core.payment.persistence.api.cancellation.PaymentCancellationDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.payment.PaymentDataAccessObject;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.Getter;

import java.time.ZonedDateTime;
import java.util.Objects;
import java.util.UUID;

@Getter
public class GRPCPaymentCancellationDataAccessObject extends PaymentCancellationDataAccessObject {

    @Nonnull
    private final UUID identifier;

    @Nonnull
    private final PaymentDataAccessObject payment;

    @Nullable
    private final String reason;

    @Nonnull
    private final String reference;

    @Nullable
    private final ZonedDateTime sentTimestamp;

    @Nullable
    private final ZonedDateTime settledTimestamp;

    @Nonnull
    private final ZonedDateTime timestamp;

    public GRPCPaymentCancellationDataAccessObject(
        @Nonnull UUID identifier,
        @Nonnull PaymentDataAccessObject payment,
        @Nonnull String reason,
        @Nonnull String reference,
        @Nullable ZonedDateTime sentTimestamp,
        @Nullable ZonedDateTime settledTimestamp,
        @Nonnull ZonedDateTime timestamp
    ) {
        this.identifier = Objects.requireNonNull(identifier, "identifier");
        this.payment = Objects.requireNonNull(payment, "payment");
        this.reason = Objects.requireNonNull(reason, "reason");
        this.reference = Objects.requireNonNull(reference, "reference");
        this.sentTimestamp = sentTimestamp;
        this.settledTimestamp = settledTimestamp;
        this.timestamp = Objects.requireNonNull(timestamp, "timestamp");
    }

    @Nonnull
    @Override
    public PaymentCancellationDataAccessObject identifier(@Nonnull UUID identifier) {
        throw new BillingEntityUnmodifiableException(PaymentCancellation.class, this.identifier, "identifier");
    }

    @Nonnull
    @Override
    public PaymentCancellationDataAccessObject payment(@Nonnull PaymentDataAccessObject payment) {
        throw new BillingEntityUnmodifiableException(PaymentCancellation.class, this.identifier, "payment");
    }


    @Nonnull
    @Override
    public PaymentCancellationDataAccessObject reason(@Nonnull String reason) {
        throw new BillingEntityUnmodifiableException(PaymentCancellation.class, this.identifier, "reason");
    }

    @Nonnull
    @Override
    public PaymentCancellationDataAccessObject reference(@Nonnull String reference) {
        throw new BillingEntityUnmodifiableException(PaymentCancellation.class, this.identifier, "reference");
    }

    @Nonnull
    @Override
    public PaymentCancellationDataAccessObject sentTimestamp(@Nullable ZonedDateTime sentTimestamp) {
        throw new BillingEntityUnmodifiableException(PaymentCancellation.class, this.identifier, "sentTimestamp");
    }

    @Nonnull
    @Override
    public PaymentCancellationDataAccessObject settledTimestamp(@Nullable ZonedDateTime settledTimestamp) {
        throw new BillingEntityUnmodifiableException(PaymentCancellation.class, this.identifier, "settledTimestamp");
    }

    @Nonnull
    @Override
    public PaymentCancellationDataAccessObject timestamp(@Nonnull ZonedDateTime timestamp) {
        throw new BillingEntityUnmodifiableException(PaymentCancellation.class, this.identifier, "timestamp");
    }
}