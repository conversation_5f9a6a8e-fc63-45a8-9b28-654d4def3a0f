package global.symbio.billing.core.services.payment;

import global.symbio.billing.core.services.grpc.payment.PaymentGrpc;
import io.grpc.ManagedChannel;
import io.micronaut.context.annotation.Bean;
import io.micronaut.context.annotation.Factory;
import io.micronaut.context.annotation.Requires;
import io.micronaut.grpc.annotation.GrpcChannel;
import jakarta.annotation.Nonnull;

@Factory
public class PaymentServiceClientFactory {

    public static final String SERVICE_GRPC_CHANNEL = "payment";

    @Bean
    @Nonnull
    @Requires("grpc.channels." + SERVICE_GRPC_CHANNEL)
    public PaymentGrpc.PaymentFutureStub payment(@Nonnull @GrpcChannel(SERVICE_GRPC_CHANNEL) ManagedChannel channel) {
        return PaymentGrpc.newFutureStub(channel);
    }
}