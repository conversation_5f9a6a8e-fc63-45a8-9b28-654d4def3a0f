package global.symbio.billing.core.services.payment.mixin;

import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.mixin.MixinConfigurator;
import global.symbio.billing.core.services.payment.PaymentService;
import io.micronaut.context.annotation.Factory;
import io.micronaut.core.order.Ordered;
import io.micronaut.inject.BeanDefinition;
import io.micronaut.inject.BeanIdentifier;
import jakarta.annotation.Nonnull;

@Factory
@VisibleForTesting
class LoggingPaymentServiceMixinConfigurator extends MixinConfigurator<PaymentService> {

    private static final String COMPONENT = "logging";

    @Nonnull
    @Override
    public PaymentService mixin(@Nonnull PaymentService bean, @Nonnull String name) {
        return new LoggingPaymentService(bean);
    }

    @Nonnull
    @Override
    protected String component() {
        return COMPONENT;
    }

    @Nonnull
    @Override
    protected String getName(@Nonnull PaymentService bean, @Nonnull BeanDefinition<PaymentService> definition, @Nonnull BeanIdentifier identifier) {
        return identifier.getName();
    }

    @Override
    protected boolean isEnabled() {
        return true;
    }

    @Override
    public int getOrder() {
        return Ordered.LOWEST_PRECEDENCE;
    }
}