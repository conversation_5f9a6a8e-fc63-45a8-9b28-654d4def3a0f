package global.symbio.billing.core.services.payment.codec.allocation.dto;

import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.message.BigDecimalMessage;
import global.symbio.billing.core.services.grpc.message.UUIDMessage;
import global.symbio.billing.core.services.grpc.payment.dto.AccountInvoiceAllocationMessage;
import global.symbio.billing.core.services.grpc.payment.dto.InvoiceAllocationMessage;
import global.symbio.billing.core.services.payment.codec.allocation.dto.impl.GRPCAccountInvoiceAllocationDTO;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

@Singleton
public record AccountInvoiceAllocationCodec(
    @Inject @Nonnull Codec<UUID, UUIDMessage> uuid,
    @Inject @Nonnull Codec<BigDecimal, BigDecimalMessage> decimal,
    @Inject @Nonnull Codec<AccountInvoiceAllocation.InvoiceAllocation, InvoiceAllocationMessage> invoice
) implements GRPCCodec<AccountInvoiceAllocation, AccountInvoiceAllocationMessage> {

    public AccountInvoiceAllocationCodec {
        Objects.requireNonNull(uuid, "uuid");
        Objects.requireNonNull(decimal, "decimal");
        Objects.requireNonNull(invoice, "invoice");
    }

    @Nonnull
    @Override
    public AccountInvoiceAllocationMessage encode(@Nonnull AccountInvoiceAllocation bean) {
        final var account = uuid.encode(bean.account());
        final var amount = decimal.encode(bean.amount());
        final var invoices = bean.invoices().stream().map(invoice::encode).toList();
        return AccountInvoiceAllocationMessage.newBuilder()
            .setAccount(account)
            .setAmount(amount)
            .addAllInvoices(invoices)
            .build();
    }

    @Nonnull
    @Override
    public AccountInvoiceAllocation decode(@Nonnull AccountInvoiceAllocationMessage protobuf) {
        final var account = uuid.decode(protobuf.getAccount());
        final var amount = decimal.decode(protobuf.getAmount());
        final var invoices = protobuf.getInvoicesList().stream().map(invoice::decode).collect(Collectors.toSet());
        return new GRPCAccountInvoiceAllocationDTO(account, amount, invoices);
    }
}