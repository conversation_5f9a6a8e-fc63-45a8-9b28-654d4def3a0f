package global.symbio.billing.core.services.payment.codec.allocation.dto;

import global.symbio.billing.core.exception.sdk.BillingEntityNotFoundException;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AllocationType;
import global.symbio.billing.core.services.grpc.codec.GRPCEnumCodec;
import global.symbio.billing.core.services.grpc.payment.entities.AllocationTypeMessage;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

import static global.symbio.billing.core.payment.persistence.api.allocation.account.AllocationType.*;

@Singleton
public record AllocationTypeCodec() implements GRPCEnumCodec<AllocationType, AllocationTypeMessage> {
    @Nonnull
    @Override
    public AllocationTypeMessage encode(@Nonnull AllocationType bean) {
        return switch (bean) {
            case PAYMENT -> AllocationTypeMessage.PAYMENT;
            case TRANSFER_OUT -> AllocationTypeMessage.TRANSFER_OUT;
            case TRANSFER_IN -> AllocationTypeMessage.TRANSFER_IN;
        };
    }

    @Nonnull
    @Override
    public AllocationType decode(@Nonnull AllocationTypeMessage protobuf) {
        return switch (protobuf) {
            case AllocationTypeMessage.PAYMENT -> PAYMENT;
            case AllocationTypeMessage.TRANSFER_OUT -> TRANSFER_OUT;
            case AllocationTypeMessage.TRANSFER_IN -> TRANSFER_IN;
            default -> throw new BillingEntityNotFoundException(AllocationType.class, protobuf);
        };
    }
}
