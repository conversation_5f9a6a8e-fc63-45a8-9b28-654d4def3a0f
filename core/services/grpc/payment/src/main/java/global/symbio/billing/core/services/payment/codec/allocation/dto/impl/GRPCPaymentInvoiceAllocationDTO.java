package global.symbio.billing.core.services.payment.codec.allocation.dto.impl;

import global.symbio.billing.core.payment.persistence.api.payment.Payment;
import global.symbio.billing.core.services.payment.codec.allocation.dto.InvoiceAllocationResponseDTO;
import global.symbio.billing.core.services.payment.codec.allocation.dto.PaymentInvoiceAllocationDTO;
import jakarta.annotation.Nonnull;

import java.util.Objects;

public record GRPCPaymentInvoiceAllocationDTO(
    @Nonnull Payment payment,
    @Nonnull InvoiceAllocationResponseDTO invoiceAllocation
) implements PaymentInvoiceAllocationDTO {

    public GRPCPaymentInvoiceAllocationDTO {
        Objects.requireNonNull(payment, "payment");
        Objects.requireNonNull(invoiceAllocation, "invoiceAllocation");
    }
}
