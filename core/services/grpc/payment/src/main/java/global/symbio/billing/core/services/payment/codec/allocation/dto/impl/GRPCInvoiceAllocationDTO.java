package global.symbio.billing.core.services.payment.codec.allocation.dto.impl;

import global.symbio.billing.core.services.payment.codec.allocation.dto.InvoiceAllocationDTO;
import jakarta.annotation.Nonnull;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.UUID;

public record GRPCInvoiceAllocationDTO(
    @Nonnull UUID invoice,
    @Nonnull String reference,
    @Nonnull BigDecimal amount
) implements InvoiceAllocationDTO {

    public GRPCInvoiceAllocationDTO {
        Objects.requireNonNull(invoice, "invoice");
        Objects.requireNonNull(reference, "reference");
        Objects.requireNonNull(amount, "amount");
    }
}
