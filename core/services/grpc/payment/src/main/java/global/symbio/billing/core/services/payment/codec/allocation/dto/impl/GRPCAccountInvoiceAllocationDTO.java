package global.symbio.billing.core.services.payment.codec.allocation.dto.impl;

import global.symbio.billing.core.services.payment.codec.allocation.dto.AccountInvoiceAllocation;
import jakarta.annotation.Nonnull;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;

public record GRPCAccountInvoiceAllocationDTO(
    @Nonnull UUID account,
    @Nonnull BigDecimal amount,
    @Nonnull Set<? extends AccountInvoiceAllocation.InvoiceAllocation> invoices
) implements AccountInvoiceAllocation {

    public GRPCAccountInvoiceAllocationDTO {
        Objects.requireNonNull(account, "account");
        Objects.requireNonNull(amount, "amount");
        Objects.requireNonNull(invoices, "invoices");
    }

    public record GRPCInvoiceAllocationDTO(
        @Nonnull UUID invoice,
        @Nonnull String reference,
        @Nonnull BigDecimal amount
    ) implements AccountInvoiceAllocation.InvoiceAllocation {

        public GRPCInvoiceAllocationDTO {
            Objects.requireNonNull(invoice, "invoice");
            Objects.requireNonNull(amount, "amount");
        }
    }
}