package global.symbio.billing.core.services.payment.codec.allocation.dto;

import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.payment.PaymentAccountAllocation;
import jakarta.annotation.Nonnull;

import java.util.Set;

public interface AccountInvoiceAllocationResponse {

    @Nonnull
    PaymentAccountAllocation<?> accountAllocation();

    @Nonnull
    Set<InvoiceAllocationDTO> invoiceAllocations();
}
