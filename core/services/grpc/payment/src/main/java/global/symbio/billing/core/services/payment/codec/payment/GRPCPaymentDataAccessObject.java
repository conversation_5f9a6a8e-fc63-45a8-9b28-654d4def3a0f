package global.symbio.billing.core.services.payment.codec.payment;

import global.symbio.billing.core.country.persistence.api.CountryDataAccessObject;
import global.symbio.billing.core.currency.persistence.api.CurrencyDataAccessObject;
import global.symbio.billing.core.exception.sdk.BillingEntityUnmodifiableException;
import global.symbio.billing.core.payment.persistence.api.payment.Payment;
import global.symbio.billing.core.payment.persistence.api.payment.PaymentDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.payment.PaymentStatus;
import global.symbio.billing.core.payment.persistence.api.payment.method.PaymentMethodDataAccessObject;
import global.symbio.billing.core.reference.persistence.api.reference.ReferenceDataAccessObject;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.Getter;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Objects;
import java.util.UUID;

@Getter
public class GRPCPaymentDataAccessObject extends PaymentDataAccessObject {

    @Nonnull
    private final UUID identifier;

    @Nonnull
    private final UUID business;

    @Nonnull
    private final PaymentMethodDataAccessObject method;

    @Nonnull
    private final CurrencyDataAccessObject currency;

    @Nonnull
    private final BigDecimal amount;

    @Nonnull
    private final ZonedDateTime receiptDate;

    @Nonnull
    private final PaymentStatus status;

    @Nullable
    private final String description;

    @Nonnull
    private final CountryDataAccessObject country;

    @Nullable
    private final ReferenceDataAccessObject reference;

    @Nonnull
    private final ZonedDateTime timestamp;

    public GRPCPaymentDataAccessObject(
        @Nonnull UUID identifier,
        @Nonnull UUID business,
        @Nonnull PaymentMethodDataAccessObject method,
        @Nonnull CurrencyDataAccessObject currency,
        @Nonnull BigDecimal amount,
        @Nonnull ZonedDateTime receiptDate,
        @Nonnull PaymentStatus status,
        @Nullable String description,
        @Nonnull CountryDataAccessObject country,
        @Nullable ReferenceDataAccessObject reference,
        @Nonnull ZonedDateTime timestamp
    ) {
        this.identifier = Objects.requireNonNull(identifier, "identifier");
        this.business = Objects.requireNonNull(business, "business");
        this.method = Objects.requireNonNull(method, "method");
        this.currency = Objects.requireNonNull(currency, "currency");
        this.amount = Objects.requireNonNull(amount, "amount");
        this.receiptDate = Objects.requireNonNull(receiptDate, "receiptDate");
        this.status = Objects.requireNonNull(status, "status");
        this.description = description;
        this.country = Objects.requireNonNull(country, "country");
        this.reference = reference;
        this.timestamp = Objects.requireNonNull(timestamp, "timestamp");
    }

    @Nonnull
    @Override
    public PaymentDataAccessObject identifier(@Nonnull UUID identifier) {
        throw new BillingEntityUnmodifiableException(Payment.class, this.identifier, "identifier");
    }

    @Nonnull
    @Override
    public PaymentDataAccessObject business(@Nonnull UUID business) {
        throw new BillingEntityUnmodifiableException(Payment.class, this.identifier, "business");
    }

    @Nonnull
    @Override
    public PaymentDataAccessObject method(@Nonnull PaymentMethodDataAccessObject method) {
        throw new BillingEntityUnmodifiableException(Payment.class, this.identifier, "method");
    }

    @Nonnull
    @Override
    public PaymentDataAccessObject currency(@Nonnull CurrencyDataAccessObject currency) {
        throw new BillingEntityUnmodifiableException(Payment.class, this.identifier, "currency");
    }

    @Nonnull
    @Override
    public PaymentDataAccessObject amount(@Nonnull BigDecimal amount) {
        throw new BillingEntityUnmodifiableException(Payment.class, this.identifier, "amount");
    }

    @Nonnull
    @Override
    public PaymentDataAccessObject receiptDate(@Nonnull ZonedDateTime receiptDate) {
        throw new BillingEntityUnmodifiableException(Payment.class, this.identifier, "receiptDate");
    }

    @Nonnull
    @Override
    public PaymentDataAccessObject status(@Nonnull PaymentStatus status) {
        throw new BillingEntityUnmodifiableException(Payment.class, this.identifier, "status");
    }

    @Nonnull
    @Override
    public PaymentDataAccessObject description(@Nullable String description) {
        throw new BillingEntityUnmodifiableException(Payment.class, this.identifier, "description");
    }

    @Nonnull
    @Override
    public PaymentDataAccessObject country(@Nonnull CountryDataAccessObject country) {
        throw new BillingEntityUnmodifiableException(Payment.class, this.identifier, "country");
    }

    @Nonnull
    @Override
    public PaymentDataAccessObject reference(@Nullable ReferenceDataAccessObject reference) {
        throw new BillingEntityUnmodifiableException(Payment.class, this.identifier, "reference");
    }

    @Nonnull
    @Override
    public PaymentDataAccessObject timestamp(@Nonnull ZonedDateTime timestamp) {
        throw new BillingEntityUnmodifiableException(Payment.class, this.identifier, "timestamp");
    }
}