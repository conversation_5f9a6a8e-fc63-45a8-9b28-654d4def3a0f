package global.symbio.billing.core.services.payment.codec.payment;

import global.symbio.billing.core.exception.sdk.BillingEntityNotFoundException;
import global.symbio.billing.core.payment.persistence.api.payment.PaymentStatus;
import global.symbio.billing.core.services.grpc.codec.GRPCEnumCodec;
import global.symbio.billing.core.services.grpc.payment.entities.PaymentStatusMessage;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

import static global.symbio.billing.core.payment.persistence.api.payment.PaymentStatus.ACTIVE;
import static global.symbio.billing.core.payment.persistence.api.payment.PaymentStatus.CANCELLED;

@Singleton
public record PaymentStatusCodec() implements GRPCEnumCodec<PaymentStatus, PaymentStatusMessage> {

    @Nonnull
    @Override
    public PaymentStatusMessage encode(@Nonnull PaymentStatus bean) {
        return switch (bean) {
            case ACTIVE -> PaymentStatusMessage.ACTIVE;
            case CANCELLED -> PaymentStatusMessage.CANCELLED;
        };
    }

    @Nonnull
    @Override
    public PaymentStatus decode(@Nonnull PaymentStatusMessage protobuf) {
        return switch (protobuf) {
            case PaymentStatusMessage.ACTIVE -> ACTIVE;
            case PaymentStatusMessage.CANCELLED -> CANCELLED;
            default -> throw new BillingEntityNotFoundException(PaymentStatus.class, protobuf);
        };
    }
}