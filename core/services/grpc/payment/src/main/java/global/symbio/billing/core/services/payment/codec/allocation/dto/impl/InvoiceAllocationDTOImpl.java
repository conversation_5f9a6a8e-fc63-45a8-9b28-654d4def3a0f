package global.symbio.billing.core.services.payment.codec.allocation.dto.impl;

import global.symbio.billing.core.services.payment.codec.allocation.dto.InvoiceAllocationDTO;
import io.micronaut.core.annotation.Introspected;
import jakarta.annotation.Nonnull;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.UUID;

@Introspected
public record InvoiceAllocationDTOImpl(
    @Nonnull @NotNull UUID invoice,
    @Nonnull @NotNull String reference,
    @Nonnull @NotNull @Positive BigDecimal amount
) implements InvoiceAllocationDTO {

    public InvoiceAllocationDTOImpl {
        Objects.requireNonNull(invoice, "invoice");
        Objects.requireNonNull(reference, "reference");
        Objects.requireNonNull(amount, "amount");
    }
}