package global.symbio.billing.core.services.payment;

import global.symbio.billing.core.payment.persistence.api.allocation.account.AccountAllocation;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AllocationType;
import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.payment.PaymentAccountAllocation;
import global.symbio.billing.core.payment.persistence.api.cancellation.PaymentCancellation;
import global.symbio.billing.core.payment.persistence.api.payment.PaymentStatus;
import global.symbio.billing.core.payment.persistence.api.payment.state.PaymentState;
import global.symbio.billing.core.payment.persistence.api.transfer.Transfer;
import global.symbio.billing.core.services.payment.codec.allocation.dto.AccountInvoiceAllocation;
import global.symbio.billing.core.services.payment.codec.allocation.dto.InvoiceAllocationDTO;
import global.symbio.billing.core.services.payment.codec.allocation.dto.PaymentInvoiceAllocationDTO;
import global.symbio.billing.core.services.payment.codec.payment.PaymentResponse;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

public interface PaymentService {

    @Nonnull
    PaymentResponse create(
        @Nonnull UUID identifier,
        @Nonnull UUID business,
        @Nonnull Integer method,
        @Nonnull String currency,
        @Nonnull BigDecimal amount,
        @Nonnull ZonedDateTime receipt,
        @Nullable String description,
        @Nonnull String country,
        @Nonnull Set<? extends AccountInvoiceAllocation> allocations
    );

    @Nonnull
    Collection<PaymentAccountAllocation<?>> getPaymentAccountAllocations(
        @Nullable UUID account,
        @Nullable ZonedDateTime paymentDateStart,
        @Nullable ZonedDateTime paymentDateEnd,
        @Nullable ZonedDateTime receiptDateStart,
        @Nullable ZonedDateTime receiptDateEnd,
        @Nullable Set<String> allocation,
        @Nullable Set<Integer> methods,
        @Nullable Set<PaymentStatus> status
    );

    @Nonnull
    AccountAllocation<?> assignAllocationToInvoices(
        @Nonnull UUID identifier,
        @Nonnull Set<? extends InvoiceAllocationDTO> allocations
    );

    @Nonnull
    Collection<PaymentState> getPaymentStates();

    @Nonnull
    Transfer createTransfer(
        @Nonnull UUID identifier,
        @Nonnull UUID fromAccountAllocation,
        @Nonnull UUID toAccount,
        @Nonnull BigDecimal amount,
        @Nullable String description
    );

    @Nonnull
    Optional<PaymentAccountAllocation<?>> getPaymentAccountAllocation(@Nonnull UUID identifier);

    @Nonnull
    Collection<InvoiceAllocationDTO> getInvoiceAllocations(@Nonnull UUID accountAllocation);

    @Nonnull
    Optional<Transfer> getTransferByTransferIn(@Nonnull UUID identifier);

    @Nonnull
    Collection<Transfer> getTransfers(
        @Nonnull UUID account,
        @Nullable ZonedDateTime transferDateStart,
        @Nullable ZonedDateTime transferDateEnd,
        @Nullable AllocationType type
    );

    @Nonnull
    Optional<AccountAllocation<?>> getAccountAllocation(@Nonnull UUID identifier);

    @Nonnull
    PaymentCancellation cancel(
        @Nonnull UUID identifier,
        @Nonnull String reason
    );

    @Nonnull
    Optional<PaymentCancellation> getPaymentCancellation(@Nonnull UUID identifier);

    @Nonnull
    Collection<PaymentInvoiceAllocationDTO> getInvoiceAllocationsByInvoice(@Nonnull UUID invoice);
}