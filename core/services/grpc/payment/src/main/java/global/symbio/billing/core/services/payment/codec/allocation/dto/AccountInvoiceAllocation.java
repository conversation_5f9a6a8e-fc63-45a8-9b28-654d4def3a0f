package global.symbio.billing.core.services.payment.codec.allocation.dto;

import jakarta.annotation.Nonnull;

import java.math.BigDecimal;
import java.util.Set;
import java.util.UUID;

public interface AccountInvoiceAllocation {

    @Nonnull
    UUID account();

    @Nonnull
    BigDecimal amount();

    @Nonnull
    Set<? extends InvoiceAllocation> invoices();

    interface InvoiceAllocation {

        @Nonnull
        UUID invoice();

        @Nonnull
        String reference();

        @Nonnull
        BigDecimal amount();
    }
}