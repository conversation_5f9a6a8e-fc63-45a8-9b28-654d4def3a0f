package global.symbio.billing.core.services.payment.codec.allocation.dto;

import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.codec.GRPCCodec;
import global.symbio.billing.core.services.grpc.message.BigDecimalMessage;
import global.symbio.billing.core.services.grpc.message.UUIDMessage;
import global.symbio.billing.core.services.grpc.payment.dto.InvoiceAllocationMessage;
import global.symbio.billing.core.services.payment.codec.allocation.dto.impl.GRPCInvoiceAllocationDTO;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.UUID;

@Singleton
public record InvoiceAllocationDTOCodec(
    @Inject @Nonnull Codec<UUID, UUIDMessage> uuid,
    @Inject @Nonnull Codec<BigDecimal, BigDecimalMessage> decimal
) implements GRPCCodec<InvoiceAllocationDTO, InvoiceAllocationMessage> {

    public InvoiceAllocationDTOCodec {
        Objects.requireNonNull(uuid, "uuid");
        Objects.requireNonNull(decimal, "decimal");
    }

    @Nonnull
    @Override
    public InvoiceAllocationMessage encode(@Nonnull InvoiceAllocationDTO bean) {
        final var invoice = uuid.encode(bean.invoice());
        final var amount = decimal.encode(bean.amount());
        return InvoiceAllocationMessage.newBuilder()
            .setInvoice(invoice)
            .setReference(bean.reference())
            .setAmount(amount)
            .build();
    }

    @Nonnull
    @Override
    public InvoiceAllocationDTO decode(@Nonnull InvoiceAllocationMessage protobuf) {
        final var invoice = uuid.decode(protobuf.getInvoice());
        final var amount = decimal.decode(protobuf.getAmount());
        return new GRPCInvoiceAllocationDTO(invoice, protobuf.getReference(), amount);
    }
}