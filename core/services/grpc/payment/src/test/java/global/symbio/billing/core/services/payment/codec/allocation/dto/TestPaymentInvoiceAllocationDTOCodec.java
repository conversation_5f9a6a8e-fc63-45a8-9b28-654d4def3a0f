package global.symbio.billing.core.services.payment.codec.allocation.dto;

import global.symbio.billing.core.country.persistence.api.Country;
import global.symbio.billing.core.currency.persistence.api.Currency;
import global.symbio.billing.core.payment.persistence.api.payment.Payment;
import global.symbio.billing.core.payment.persistence.api.payment.PaymentStatus;
import global.symbio.billing.core.payment.persistence.api.payment.method.PaymentMethod;
import global.symbio.billing.core.reference.persistence.api.reference.Reference;
import global.symbio.billing.core.reference.persistence.api.reference.ReferenceType;
import global.symbio.billing.core.services.codec.uuid.UUIDCodec;
import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.codec.country.CountryCodec;
import global.symbio.billing.core.services.grpc.codec.country.GRPCCountryDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.currency.CurrencyCodec;
import global.symbio.billing.core.services.grpc.codec.currency.GRPCCurrencyDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.decimal.BigDecimalCodec;
import global.symbio.billing.core.services.grpc.codec.payment.method.GRPCPaymentMethodDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.payment.method.PaymentMethodCodec;
import global.symbio.billing.core.services.grpc.codec.reference.GRPCReferenceDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.reference.ReferenceCodec;
import global.symbio.billing.core.services.grpc.codec.reference.ReferenceTypeCodec;
import global.symbio.billing.core.services.grpc.codec.temporal.*;
import global.symbio.billing.core.services.grpc.message.*;
import global.symbio.billing.core.services.grpc.payment.dto.InvoiceAllocationResponseMessage;
import global.symbio.billing.core.services.grpc.payment.dto.PaymentInvoiceAllocationMessage;
import global.symbio.billing.core.services.grpc.payment.entities.PaymentMessage;
import global.symbio.billing.core.services.grpc.payment.entities.PaymentStatusMessage;
import global.symbio.billing.core.services.payment.codec.allocation.dto.impl.GRPCInvoiceAllocationResponseDTO;
import global.symbio.billing.core.services.payment.codec.allocation.dto.impl.GRPCPaymentInvoiceAllocationDTO;
import global.symbio.billing.core.services.payment.codec.payment.GRPCPaymentDataAccessObject;
import global.symbio.billing.core.services.payment.codec.payment.PaymentCodec;
import global.symbio.billing.core.services.payment.codec.payment.PaymentStatusCodec;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.*;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

public class TestPaymentInvoiceAllocationDTOCodec {

    private Codec<UUID, UUIDMessage> uuid;

    private Codec<BigDecimal, BigDecimalMessage> decimal;

    private Codec<Currency, CurrencyMessage> currency;

    private Codec<Country, CountryMessage> country;

    private Codec<ZonedDateTime, ZonedDateTimeMessage> timestamp;

    private Codec<LocalDate, LocalDateMessage> date;

    private Codec<LocalTime, LocalTimeMessage> time;

    private Codec<LocalDateTime, LocalDateTimeMessage> datetime;

    private Codec<ZoneOffset, ZoneOffsetMessage> offset;

    private Codec<ZoneId, ZoneIdMessage> zone;

    private Codec<PaymentMethod, PaymentMethodMessage> method;

    private Codec<PaymentStatus, PaymentStatusMessage> status;

    private Codec<Reference, ReferenceMessage> reference;

    private Codec<ReferenceType, ReferenceTypeMessage> referenceType;

    private Codec<Payment, PaymentMessage> payment;

    private Codec<InvoiceAllocationResponseDTO, InvoiceAllocationResponseMessage> invoiceAllocation;

    private Codec<PaymentInvoiceAllocationDTO, PaymentInvoiceAllocationMessage> codec;

    @BeforeEach
    public void setup() {
        uuid = new UUIDCodec();
        decimal = new BigDecimalCodec();
        date = new LocalDateCodec();
        time = new LocalTimeCodec();
        datetime = new LocalDateTimeCodec(date, time);
        offset = new ZoneOffsetCodec();
        zone = new ZoneIdCodec();
        timestamp = new ZonedDateTimeCodec(datetime, offset, zone);
        currency = new CurrencyCodec();
        method = new PaymentMethodCodec();
        status = new PaymentStatusCodec();
        country = new CountryCodec();
        referenceType = new ReferenceTypeCodec();
        reference = new ReferenceCodec(uuid, referenceType);
        payment = new PaymentCodec(uuid, decimal, currency, country, timestamp, method, status, reference);
        invoiceAllocation = new InvoiceAllocationResponseDTOCodec(uuid, decimal, timestamp);
        codec = new PaymentInvoiceAllocationDTOCodec(payment, invoiceAllocation);
    }

    @Test
    @DisplayName("PaymentInvoiceAllocationDTOCodec::new rejects null constructor arguments")
    public void payment_invoice_allocation_dto_codec_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new PaymentInvoiceAllocationDTOCodec(null, invoiceAllocation));
        assertThrows(NullPointerException.class, () -> new PaymentInvoiceAllocationDTOCodec(payment, null));
    }

    @Test
    @DisplayName("PaymentInvoiceAllocationDTOCodec::encode bean to protobuf")
    public void codec_bean_to_protobuf() {
        final var bean = new GRPCPaymentInvoiceAllocationDTO(payment(), invoiceAllocation());

        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));
        assertEquals(bean.payment(), payment.decode(protobuf.getPayment()));
        assertEquals(bean.invoiceAllocation(), invoiceAllocation.decode(protobuf.getAllocation()));
    }

    @Test
    @DisplayName("PaymentInvoiceAllocationDTOCodec::encode protobuf to bean")
    public void codec_protobuf_to_bean() {
        final var protobuf = PaymentInvoiceAllocationMessage.newBuilder()
            .setPayment(payment.encode(payment()))
            .setAllocation(invoiceAllocation.encode(invoiceAllocation()))
            .build();

        final var bean = assertDoesNotThrow(() -> codec.decode(protobuf));
        assertEquals(protobuf, codec.encode(bean));
    }

    @Nonnull
    private static Payment payment() {
        return new GRPCPaymentDataAccessObject(
            UUID.randomUUID(),
            UUID.randomUUID(),
            new GRPCPaymentMethodDataAccessObject(1, "BANK_TRANSFER", "Bank Transfer"),
            new GRPCCurrencyDataAccessObject(458, "MYR", "Malaysian Ringgit", "MYR"),
            new BigDecimal("100.00"),
            ZonedDateTime.now(),
            PaymentStatus.ACTIVE,
            "description",
            new GRPCCountryDataAccessObject(458, "Malaysia", "MY"),
            new GRPCReferenceDataAccessObject(UUID.randomUUID(), 1L, "MY", "CO", 2, ReferenceType.INVOICE, "MYCO00000000012"),
            ZonedDateTime.now()
        ).entity();
    }

    @Nonnull
    private static InvoiceAllocationResponseDTO invoiceAllocation() {
        return new GRPCInvoiceAllocationResponseDTO(UUID.randomUUID(), "MYCO00000008191", BigDecimal.TEN, ZonedDateTime.now());
    }
}
