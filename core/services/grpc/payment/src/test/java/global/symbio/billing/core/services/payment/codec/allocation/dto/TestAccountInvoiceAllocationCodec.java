package global.symbio.billing.core.services.payment.codec.allocation.dto;

import global.symbio.billing.core.services.codec.uuid.UUIDCodec;
import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.codec.decimal.BigDecimalCodec;
import global.symbio.billing.core.services.grpc.message.BigDecimalMessage;
import global.symbio.billing.core.services.grpc.message.UUIDMessage;
import global.symbio.billing.core.services.grpc.payment.dto.AccountInvoiceAllocationMessage;
import global.symbio.billing.core.services.grpc.payment.dto.InvoiceAllocationMessage;
import global.symbio.billing.core.services.payment.codec.allocation.dto.impl.GRPCAccountInvoiceAllocationDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.Set;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

public class TestAccountInvoiceAllocationCodec {

    private Codec<UUID, UUIDMessage> uuid;

    private Codec<BigDecimal, BigDecimalMessage> decimal;

    private Codec<AccountInvoiceAllocation.InvoiceAllocation, InvoiceAllocationMessage> invoice;

    private Codec<AccountInvoiceAllocation, AccountInvoiceAllocationMessage> codec;

    @BeforeEach
    public void setup() {
        uuid = new UUIDCodec();
        decimal = new BigDecimalCodec();
        invoice = new InvoiceAllocationCodec(uuid, decimal);
        codec = new AccountInvoiceAllocationCodec(uuid, decimal, invoice);
    }

    @Test
    @DisplayName("InvoiceAllocationCodec::new rejects null constructor arguments")
    public void invoice_allocation_codec_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new AccountInvoiceAllocationCodec(null, decimal, invoice));
        assertThrows(NullPointerException.class, () -> new AccountInvoiceAllocationCodec(uuid, null, invoice));
        assertThrows(NullPointerException.class, () -> new AccountInvoiceAllocationCodec(uuid, decimal, null));
    }

    @Test
    @DisplayName("InvoiceAllocationCodec::encode bean to protobuf")
    public void codec_bean_to_protobuf() {
        final var invoiceUUID = UUID.randomUUID();
        final var invoiceAmount = BigDecimal.TEN;
        final var reference = "MYCO00000008191";
        final var invoiceSource = new GRPCAccountInvoiceAllocationDTO.GRPCInvoiceAllocationDTO(invoiceUUID, reference, invoiceAmount);
        final var invoiceSet = Set.of(invoiceSource);
        final var bean = new GRPCAccountInvoiceAllocationDTO(UUID.randomUUID(), invoiceAmount, invoiceSet);

        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));
        assertEquals(bean.account(), uuid.decode(protobuf.getAccount()));
        assertEquals(bean.amount(), decimal.decode(protobuf.getAmount()));
        assertEquals(bean.invoices().stream().toList().getFirst(), invoice.decode(protobuf.getInvoicesList().stream().toList().getFirst()));
    }

    @Test
    @DisplayName("InvoiceAllocationCodec::decode protobuf to bean")
    public void codec_protobuf_to_bean() {
        final var accountUUID = UUID.randomUUID();
        final var invoiceUUID = UUID.randomUUID();
        final var invoiceAmount = BigDecimal.TEN;
        final var reference = "MYCO00000008191";
        final var invoiceSource = new GRPCAccountInvoiceAllocationDTO.GRPCInvoiceAllocationDTO(invoiceUUID, reference, invoiceAmount);
        final var invoiceSet = Set.of(invoiceSource);
        final var invoices = invoiceSet.stream().map(invoice::encode).toList();

        final var protobuf = AccountInvoiceAllocationMessage.newBuilder()
            .setAccount(uuid.encode(accountUUID))
            .setAmount(decimal.encode(invoiceAmount))
            .addAllInvoices(invoices)
            .build();

        final var bean = assertDoesNotThrow(() -> codec.decode(protobuf));
        assertEquals(protobuf, codec.encode(bean));
    }

}