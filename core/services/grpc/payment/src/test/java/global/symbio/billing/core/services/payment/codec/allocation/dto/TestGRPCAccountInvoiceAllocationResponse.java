package global.symbio.billing.core.services.payment.codec.allocation.dto;

import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.payment.PaymentAccountAllocation;
import global.symbio.billing.core.services.payment.codec.allocation.dto.impl.GRPCAccountInvoiceAllocationResponse;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.mock;

public class TestGRPCAccountInvoiceAllocationResponse {

    @Test
    @DisplayName("GRPCAccountInvoiceAllocationResponse::new rejects null constructor arguments")
    public void grpc_account_invoice_allocation_response_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new GRPCAccountInvoiceAllocationResponse(null, Set.of()));
        assertThrows(NullPointerException.class, () -> new GRPCAccountInvoiceAllocationResponse(mock(PaymentAccountAllocation.class), null));
    }
}
