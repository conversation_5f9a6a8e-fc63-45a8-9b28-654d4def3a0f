package global.symbio.billing.core.services.payment.codec.payment.impl;

import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.payment.PaymentAccountAllocation;
import global.symbio.billing.core.services.payment.codec.allocation.dto.impl.GRPCAccountInvoiceAllocationResponse;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.mock;

public class TestPaymentResponseImpl {

    @Test
    @DisplayName("PaymentResponseImpl::new rejects null constructor arguments")
    public void payment_response_impl_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new PaymentResponseImpl(null, Set.of()));
        assertThrows(NullPointerException.class, () -> new GRPCAccountInvoiceAllocationResponse(mock(PaymentAccountAllocation.class), null));
    }
}
