package global.symbio.billing.core.services.payment.codec.allocation.dto;

import global.symbio.billing.core.exception.sdk.BillingEntityNotFoundException;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AllocationDirection;
import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.payment.entities.AllocationDirectionMessage;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.Arrays;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;

public class TestAllocationDirectionCodec {

    private Codec<AllocationDirection, AllocationDirectionMessage> codec;

    @BeforeEach
    public void setup() {
        codec = new AllocationDirectionCodec();
    }

    @ParameterizedTest
    @DisplayName("AllocationDirectionCodec::encode bean to protobuf")
    @MethodSource("types")
    public void codec_bean_to_protobuf(final AllocationDirection bean) {
        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));
        assertEquals(bean, codec.decode(protobuf));
    }

    @ParameterizedTest
    @DisplayName("AllocationDirectionCodec::decode protobuf to bean")
    @MethodSource("messages")
    public void codec_protobuf_to_bean(final AllocationDirectionMessage message) {
        if (message != AllocationDirectionMessage.ALLOCATION_DIRECTION_UNSPECIFIED) {
            final var bean = assertDoesNotThrow(() -> codec.decode(message));
            assertEquals(message, codec.encode(bean));
        } else {
            final var unknown = AllocationDirectionMessage.ALLOCATION_DIRECTION_UNSPECIFIED;
            assertThrows(BillingEntityNotFoundException.class, () -> codec.decode(unknown));
        }
    }

    @Nonnull
    private static Stream<Arguments> types() {
        return Arrays.stream(AllocationDirection.values()).map(Arguments::of);
    }

    @Nonnull
    private static Stream<Arguments> messages() {
        return Stream.of(
            Arguments.of(AllocationDirectionMessage.IN),
            Arguments.of(AllocationDirectionMessage.OUT),
            Arguments.of(AllocationDirectionMessage.ALLOCATION_DIRECTION_UNSPECIFIED)
        );
    }
}
