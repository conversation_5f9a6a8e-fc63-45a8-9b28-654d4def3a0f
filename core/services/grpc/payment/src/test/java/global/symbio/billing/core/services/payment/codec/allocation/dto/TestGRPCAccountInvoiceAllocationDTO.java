package global.symbio.billing.core.services.payment.codec.allocation.dto;

import global.symbio.billing.core.services.payment.codec.allocation.dto.impl.GRPCAccountInvoiceAllocationDTO;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertThrows;

public class TestGRPCAccountInvoiceAllocationDTO {

    @Test
    @DisplayName("GRPCAccountInvoiceAllocationDTO::new rejects null constructor arguments")
    public void grpc_account_invoice_allocation_dto_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new GRPCAccountInvoiceAllocationDTO(null, BigDecimal.ONE, new HashSet<>()));
        assertThrows(NullPointerException.class, () -> new GRPCAccountInvoiceAllocationDTO(UUID.randomUUID(), null, new HashSet<>()));
        assertThrows(NullPointerException.class, () -> new GRPCAccountInvoiceAllocationDTO(UUID.randomUUID(), BigDecimal.ONE, null));
    }
}
