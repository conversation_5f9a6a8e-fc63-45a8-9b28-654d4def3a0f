package global.symbio.billing.core.services.payment.codec.allocation;

import global.symbio.billing.core.country.persistence.api.CountryDataAccessObject;
import global.symbio.billing.core.currency.persistence.api.CurrencyDataAccessObject;
import global.symbio.billing.core.exception.sdk.BillingEntityUnmodifiableException;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AllocationDirection;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AllocationType;
import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.payment.PaymentAccountAllocation;
import global.symbio.billing.core.payment.persistence.api.allocation.state.AllocationState;
import global.symbio.billing.core.payment.persistence.api.payment.PaymentDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.payment.PaymentStatus;
import global.symbio.billing.core.payment.persistence.api.payment.method.PaymentMethodDataAccessObject;
import global.symbio.billing.core.reference.persistence.api.reference.ReferenceDataAccessObject;
import global.symbio.billing.core.reference.persistence.api.reference.ReferenceType;
import global.symbio.billing.core.services.grpc.codec.country.GRPCCountryDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.currency.GRPCCurrencyDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.payment.method.GRPCPaymentMethodDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.reference.GRPCReferenceDataAccessObject;
import global.symbio.billing.core.services.payment.codec.payment.GRPCPaymentDataAccessObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

public class TestGRPCPaymentAccountAllocationDataAccessObject {

    private static final UUID ID = UUID.randomUUID();
    private static final UUID ACCOUNT_ID = UUID.randomUUID();
    private static final AllocationType TYPE = AllocationType.PAYMENT;
    private static final BigDecimal AMOUNT = new BigDecimal("100.00");
    private static final AllocationState STATE = AllocationState.create(AMOUNT);
    private static final AllocationDirection DIRECTION = AllocationDirection.IN;
    private static final ZonedDateTime SENT_TIMESTAMP = ZonedDateTime.now();
    private static final ZonedDateTime SETTLED_TIMESTAMP = ZonedDateTime.now();
    private static final ZonedDateTime TIMESTAMP = ZonedDateTime.now();

    private static final UUID CUSTOMER = UUID.randomUUID();
    private static final PaymentMethodDataAccessObject METHOD = new GRPCPaymentMethodDataAccessObject(1, "BANK_TRANSFER", "Bank Transfer");
    private static final CurrencyDataAccessObject CURRENCY = new GRPCCurrencyDataAccessObject(458, "MYR", "Malaysian Ringgit", "MYR");
    private static final ZonedDateTime RECEIPT_DATE = ZonedDateTime.now();
    private static final PaymentStatus STATUS = PaymentStatus.ACTIVE;
    private static final CountryDataAccessObject COUNTRY = new GRPCCountryDataAccessObject(458, "Malaysia", "MYR");
    private static final ReferenceDataAccessObject REFERENCE = new GRPCReferenceDataAccessObject(ID, 1L, "MYR", "CO", 2, ReferenceType.INVOICE, "MYCO00000000012");

    private static final PaymentDataAccessObject PAYMENT = new GRPCPaymentDataAccessObject(ID, CUSTOMER, METHOD, CURRENCY, AMOUNT, RECEIPT_DATE, STATUS, "description", COUNTRY, REFERENCE, TIMESTAMP);

    private GRPCPaymentAccountAllocationDataAccessObject paymentAccountAllocation;

    @BeforeEach
    public void setup() {
        paymentAccountAllocation = new GRPCPaymentAccountAllocationDataAccessObject(ID, ACCOUNT_ID, TYPE, COUNTRY, CURRENCY, AMOUNT, STATE, DIRECTION, SENT_TIMESTAMP, SETTLED_TIMESTAMP, TIMESTAMP, PAYMENT);
    }

    @Test
    @DisplayName("GRPCPaymentAccountAllocationDataAccessObject::new rejects null constructor arguments")
    public void grpc_payment_account_allocation_data_access_object_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new GRPCPaymentAccountAllocationDataAccessObject(null, ACCOUNT_ID, TYPE, COUNTRY, CURRENCY, AMOUNT, STATE, DIRECTION, SENT_TIMESTAMP, SETTLED_TIMESTAMP, TIMESTAMP, PAYMENT));
        assertThrows(NullPointerException.class, () -> new GRPCPaymentAccountAllocationDataAccessObject(ID, null, TYPE, COUNTRY, CURRENCY, AMOUNT, STATE, DIRECTION, SENT_TIMESTAMP, SETTLED_TIMESTAMP, TIMESTAMP, PAYMENT));
        assertThrows(NullPointerException.class, () -> new GRPCPaymentAccountAllocationDataAccessObject(ID, ACCOUNT_ID, null, COUNTRY, CURRENCY, AMOUNT, STATE, DIRECTION, SENT_TIMESTAMP, SETTLED_TIMESTAMP, TIMESTAMP, PAYMENT));
        assertThrows(NullPointerException.class, () -> new GRPCPaymentAccountAllocationDataAccessObject(ID, ACCOUNT_ID, TYPE, null, CURRENCY, AMOUNT, STATE, DIRECTION, SENT_TIMESTAMP, SETTLED_TIMESTAMP, TIMESTAMP, PAYMENT));
        assertThrows(NullPointerException.class, () -> new GRPCPaymentAccountAllocationDataAccessObject(ID, ACCOUNT_ID, TYPE, COUNTRY, null, AMOUNT, STATE, DIRECTION, SENT_TIMESTAMP, SETTLED_TIMESTAMP, TIMESTAMP, PAYMENT));
        assertThrows(NullPointerException.class, () -> new GRPCPaymentAccountAllocationDataAccessObject(ID, ACCOUNT_ID, TYPE, COUNTRY, CURRENCY, null, STATE, DIRECTION, SENT_TIMESTAMP, SETTLED_TIMESTAMP, TIMESTAMP, PAYMENT));
        assertThrows(NullPointerException.class, () -> new GRPCPaymentAccountAllocationDataAccessObject(ID, ACCOUNT_ID, TYPE, COUNTRY, CURRENCY, AMOUNT, null, DIRECTION, SENT_TIMESTAMP, SETTLED_TIMESTAMP, TIMESTAMP, PAYMENT));
        assertThrows(NullPointerException.class, () -> new GRPCPaymentAccountAllocationDataAccessObject(ID, ACCOUNT_ID, TYPE, COUNTRY, CURRENCY, AMOUNT, STATE, null, SENT_TIMESTAMP, SETTLED_TIMESTAMP, TIMESTAMP, PAYMENT));
        assertDoesNotThrow(() -> new GRPCPaymentAccountAllocationDataAccessObject(ID, ACCOUNT_ID, TYPE, COUNTRY, CURRENCY, AMOUNT, STATE, DIRECTION, null, SETTLED_TIMESTAMP, TIMESTAMP, PAYMENT));
        assertDoesNotThrow(() -> new GRPCPaymentAccountAllocationDataAccessObject(ID, ACCOUNT_ID, TYPE, COUNTRY, CURRENCY, AMOUNT, STATE, DIRECTION, SENT_TIMESTAMP, null, TIMESTAMP, PAYMENT));
        assertThrows(NullPointerException.class, () -> new GRPCPaymentAccountAllocationDataAccessObject(ID, ACCOUNT_ID, TYPE, COUNTRY, CURRENCY, AMOUNT, STATE, DIRECTION, SENT_TIMESTAMP, SETTLED_TIMESTAMP, null, PAYMENT));
        assertThrows(NullPointerException.class, () -> new GRPCPaymentAccountAllocationDataAccessObject(ID, ACCOUNT_ID, TYPE, COUNTRY, CURRENCY, AMOUNT, STATE, DIRECTION, SENT_TIMESTAMP, SETTLED_TIMESTAMP, TIMESTAMP, null));
    }

    @Test
    @DisplayName("GRPCPaymentAccountAllocationDataAccessObject::identifier is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_payment_account_allocation_data_access_object_identifier_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(PaymentAccountAllocation.class, ID, "identifier");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> paymentAccountAllocation.identifier(UUID.randomUUID()));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCPaymentAccountAllocationDataAccessObject::account is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_payment_account_allocation_data_access_object_account_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(PaymentAccountAllocation.class, ID, "account");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> paymentAccountAllocation.account(UUID.randomUUID()));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCPaymentAccountAllocationDataAccessObject::type is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_payment_account_allocation_data_access_object_type_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(PaymentAccountAllocation.class, ID, "type");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> paymentAccountAllocation.type(AllocationType.PAYMENT));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCPaymentAccountAllocationDataAccessObject::country is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_payment_account_allocation_data_access_object_country_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(PaymentAccountAllocation.class, ID, "country");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> paymentAccountAllocation.country(COUNTRY));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCPaymentAccountAllocationDataAccessObject::currency is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_payment_account_allocation_data_access_object_currency_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(PaymentAccountAllocation.class, ID, "currency");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> paymentAccountAllocation.currency(CURRENCY));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCPaymentAccountAllocationDataAccessObject::amount is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_payment_account_allocation_data_access_object_amount_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(PaymentAccountAllocation.class, ID, "amount");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> paymentAccountAllocation.amount(new BigDecimal("100.00")));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCPaymentAccountAllocationDataAccessObject::allocationState is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_payment_account_allocation_data_access_object_allocation_state_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(PaymentAccountAllocation.class, ID, "allocationState");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> paymentAccountAllocation.allocationState(paymentAccountAllocation.getAllocationState().allocate(new BigDecimal("50.00"))));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCPaymentAccountAllocationDataAccessObject::direction is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_payment_account_allocation_data_access_object_allocation_direction_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(PaymentAccountAllocation.class, ID, "direction");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> paymentAccountAllocation.direction(AllocationDirection.IN));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCPaymentAccountAllocationDataAccessObject::sentTimestamp is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_payment_account_allocation_data_access_object_sent_timestamp_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(PaymentAccountAllocation.class, ID, "sentTimestamp");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> paymentAccountAllocation.sentTimestamp(ZonedDateTime.now()));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCPaymentAccountAllocationDataAccessObject::settledTimestamp is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_payment_account_allocation_data_access_object_settled_timestamp_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(PaymentAccountAllocation.class, ID, "settledTimestamp");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> paymentAccountAllocation.settledTimestamp(ZonedDateTime.now()));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCPaymentAccountAllocationDataAccessObject::timestamp is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_payment_account_allocation_data_access_object_timestamp_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(PaymentAccountAllocation.class, ID, "timestamp");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> paymentAccountAllocation.timestamp(ZonedDateTime.now()));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCPaymentAccountAllocationDataAccessObject::payment is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_payment_account_allocation_data_access_object_payment_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(PaymentAccountAllocation.class, ID, "payment");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> paymentAccountAllocation.payment(PAYMENT));
        assertEquals(expected.getMessage(), cause.getMessage());
    }
}
