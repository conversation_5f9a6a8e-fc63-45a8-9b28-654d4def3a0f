package global.symbio.billing.core.services.payment.codec.allocation.dto;

import global.symbio.billing.core.services.payment.codec.allocation.dto.impl.GRPCInvoiceAllocationResponseDTO;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertThrows;

public class TestGRPCInvoiceAllocationResponseDTO {

    @Test
    @DisplayName("InvoiceAllocationResponseDTO::new rejects null constructor arguments")
    public void invoice_allocation_dto_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new GRPCInvoiceAllocationResponseDTO(null, "MYCO00000008191", BigDecimal.ONE, ZonedDateTime.now()));
        assertThrows(NullPointerException.class, () -> new GRPCInvoiceAllocationResponseDTO(UUID.randomUUID(), null, BigDecimal.ONE, ZonedDateTime.now()));
        assertThrows(NullPointerException.class, () -> new GRPCInvoiceAllocationResponseDTO(UUID.randomUUID(), "MYCO00000008191", null, ZonedDateTime.now()));
        assertThrows(NullPointerException.class, () -> new GRPCInvoiceAllocationResponseDTO(UUID.randomUUID(), "MYCO00000008191", BigDecimal.ONE, null));
    }
}
