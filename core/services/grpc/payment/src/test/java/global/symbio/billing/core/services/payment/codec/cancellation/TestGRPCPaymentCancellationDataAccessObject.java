package global.symbio.billing.core.services.payment.codec.cancellation;

import global.symbio.billing.core.country.persistence.api.CountryDataAccessObject;
import global.symbio.billing.core.currency.persistence.api.CurrencyDataAccessObject;
import global.symbio.billing.core.exception.sdk.BillingEntityUnmodifiableException;
import global.symbio.billing.core.payment.persistence.api.cancellation.PaymentCancellation;
import global.symbio.billing.core.payment.persistence.api.payment.PaymentDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.payment.PaymentStatus;
import global.symbio.billing.core.payment.persistence.api.payment.method.PaymentMethodDataAccessObject;
import global.symbio.billing.core.reference.persistence.api.reference.ReferenceDataAccessObject;
import global.symbio.billing.core.reference.persistence.api.reference.ReferenceType;
import global.symbio.billing.core.services.grpc.codec.country.GRPCCountryDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.currency.GRPCCurrencyDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.payment.method.GRPCPaymentMethodDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.reference.GRPCReferenceDataAccessObject;
import global.symbio.billing.core.services.payment.codec.payment.GRPCPaymentDataAccessObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

public class TestGRPCPaymentCancellationDataAccessObject {

    private static final UUID ID = UUID.randomUUID();
    private static final UUID BUSINESS = UUID.randomUUID();
    private static final PaymentMethodDataAccessObject METHOD = new GRPCPaymentMethodDataAccessObject(1, "BANK_TRANSFER", "Bank Transfer");
    private static final CurrencyDataAccessObject CURRENCY = new GRPCCurrencyDataAccessObject(458, "MYR", "Malaysian Ringgit", "MYR");
    private static final BigDecimal AMOUNT = new BigDecimal("100.00");
    private static final ZonedDateTime RECEIPT_DATE = ZonedDateTime.now();
    private static final PaymentStatus STATUS = PaymentStatus.ACTIVE;
    private static final CountryDataAccessObject COUNTRY = new GRPCCountryDataAccessObject(458, "Malaysia", "MYR");
    private static final ZonedDateTime SENT_TIMESTAMP = ZonedDateTime.now();
    private static final ZonedDateTime SETTLED_TIMESTAMP = ZonedDateTime.now();
    private static final ZonedDateTime TIMESTAMP = ZonedDateTime.now();
    private static final String DESCRIPTION = "description";
    private static final String REASON = "reason";
    private static final String CANCELLATION_REFERENCE = "MYCOPYMX00000000270";
    private static final ReferenceDataAccessObject REFERENCE = new GRPCReferenceDataAccessObject(ID, 1L, "MYR", "CO", 2, ReferenceType.INVOICE, "MYCO00000000012");
    private static final PaymentDataAccessObject PAYMENT = new GRPCPaymentDataAccessObject(ID, BUSINESS, METHOD, CURRENCY, AMOUNT, RECEIPT_DATE, STATUS, DESCRIPTION, COUNTRY, REFERENCE, TIMESTAMP);

    private GRPCPaymentCancellationDataAccessObject cancellation;

    @BeforeEach
    public void setup() {
        cancellation = new GRPCPaymentCancellationDataAccessObject(ID, PAYMENT, REASON, CANCELLATION_REFERENCE, SENT_TIMESTAMP, SETTLED_TIMESTAMP, TIMESTAMP);
    }

    @Test
    @DisplayName("GRPCPaymentCancellationDataAccessObject::new rejects null constructor arguments")
    public void grpc_payment_cancellation_data_access_object_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new GRPCPaymentCancellationDataAccessObject(null, PAYMENT, REASON, CANCELLATION_REFERENCE, SENT_TIMESTAMP, SETTLED_TIMESTAMP, TIMESTAMP));
        assertThrows(NullPointerException.class, () -> new GRPCPaymentCancellationDataAccessObject(ID, null, REASON, CANCELLATION_REFERENCE, SENT_TIMESTAMP, SETTLED_TIMESTAMP, TIMESTAMP));
        assertThrows(NullPointerException.class, () -> new GRPCPaymentCancellationDataAccessObject(ID, PAYMENT, null, CANCELLATION_REFERENCE, SENT_TIMESTAMP, SETTLED_TIMESTAMP, TIMESTAMP));
        assertThrows(NullPointerException.class, () -> new GRPCPaymentCancellationDataAccessObject(ID, PAYMENT, REASON, null, SENT_TIMESTAMP, SETTLED_TIMESTAMP, TIMESTAMP));
        assertDoesNotThrow(() -> new GRPCPaymentCancellationDataAccessObject(ID, PAYMENT, REASON, CANCELLATION_REFERENCE, null, SETTLED_TIMESTAMP, TIMESTAMP));
        assertDoesNotThrow(() -> new GRPCPaymentCancellationDataAccessObject(ID, PAYMENT, REASON, CANCELLATION_REFERENCE, SENT_TIMESTAMP, null, TIMESTAMP));
        assertThrows(NullPointerException.class, () -> new GRPCPaymentCancellationDataAccessObject(ID, PAYMENT, REASON, CANCELLATION_REFERENCE, SENT_TIMESTAMP, SETTLED_TIMESTAMP, null));
    }

    @Test
    @DisplayName("GRPCPaymentCancellationDataAccessObject::identifier is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_payment_cancellation_data_access_object_identifier_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(PaymentCancellation.class, ID, "identifier");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> cancellation.identifier(ID));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCPaymentCancellationDataAccessObject::payment is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_payment_cancellation_data_access_object_payment_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(PaymentCancellation.class, ID, "payment");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> cancellation.payment(PAYMENT));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCPaymentCancellationDataAccessObject::reason is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_payment_cancellation_data_access_object_reason_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(PaymentCancellation.class, ID, "reason");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> cancellation.reason(REASON));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCPaymentCancellationDataAccessObject::reference is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_payment_cancellation_data_access_object_reference_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(PaymentCancellation.class, ID, "reference");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> cancellation.reference(CANCELLATION_REFERENCE));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCPaymentCancellationDataAccessObject::sentTimestamp is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_payment_cancellation_data_access_object_sent_timestamp_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(PaymentCancellation.class, ID, "sentTimestamp");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> cancellation.sentTimestamp(ZonedDateTime.now()));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCPaymentCancellationDataAccessObject::settledTimestamp is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_payment_cancellation_data_access_object_settled_timestamp_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(PaymentCancellation.class, ID, "settledTimestamp");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> cancellation.settledTimestamp(ZonedDateTime.now()));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCPaymentCancellationDataAccessObject::timestamp is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_payment_cancellation_data_access_object_timestamp_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(PaymentCancellation.class, ID, "timestamp");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> cancellation.timestamp(TIMESTAMP));
        assertEquals(expected.getMessage(), cause.getMessage());
    }
}
