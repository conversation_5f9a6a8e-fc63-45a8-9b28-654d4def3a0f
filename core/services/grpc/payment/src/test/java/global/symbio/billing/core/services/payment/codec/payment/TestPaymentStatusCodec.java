package global.symbio.billing.core.services.payment.codec.payment;

import global.symbio.billing.core.payment.persistence.api.payment.PaymentStatus;
import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.payment.entities.PaymentStatusMessage;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.Arrays;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class TestPaymentStatusCodec {

    private Codec<PaymentStatus, PaymentStatusMessage> codec;

    @BeforeEach
    public void setup() {
        codec = new PaymentStatusCodec();
    }

    @ParameterizedTest
    @DisplayName("PaymentStatusCodec::encode bean to protobuf")
    @MethodSource("states")
    public void codec_bean_to_protobuf(final PaymentStatus bean) {
        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));
        assertEquals(bean, codec.decode(protobuf));
    }

    @ParameterizedTest
    @DisplayName("PaymentStatusCodec::decode protobuf to bean")
    @MethodSource("messages")
    public void codec_protobuf_to_bean(final PaymentStatusMessage message) {
        final var bean = assertDoesNotThrow(() -> codec.decode(message));
        assertEquals(message, codec.encode(bean));
    }

    @Nonnull
    private static Stream<Arguments> states() {
        return Arrays.stream(PaymentStatus.values()).map(Arguments::of);
    }

    @Nonnull
    private static Stream<Arguments> messages() {
        return Stream.of(
            Arguments.of(PaymentStatusMessage.ACTIVE),
            Arguments.of(PaymentStatusMessage.CANCELLED)
        );
    }
}
