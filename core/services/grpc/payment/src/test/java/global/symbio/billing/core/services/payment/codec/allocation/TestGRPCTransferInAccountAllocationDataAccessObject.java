package global.symbio.billing.core.services.payment.codec.allocation;

import global.symbio.billing.core.country.persistence.api.CountryDataAccessObject;
import global.symbio.billing.core.currency.persistence.api.CurrencyDataAccessObject;
import global.symbio.billing.core.exception.sdk.BillingEntityUnmodifiableException;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AllocationDirection;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AllocationType;
import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.transfer.TransferInAccountAllocation;
import global.symbio.billing.core.payment.persistence.api.allocation.state.AllocationState;
import global.symbio.billing.core.services.grpc.codec.country.GRPCCountryDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.currency.GRPCCurrencyDataAccessObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

public class TestGRPCTransferInAccountAllocationDataAccessObject {

    private static final UUID ID = UUID.randomUUID();
    private static final UUID ACCOUNT_ID = UUID.randomUUID();
    private static final AllocationType TYPE = AllocationType.PAYMENT;
    private static final BigDecimal AMOUNT = new BigDecimal("100.00");
    private static final AllocationState STATE = AllocationState.create(AMOUNT);
    private static final AllocationDirection DIRECTION = AllocationDirection.IN;
    private static final ZonedDateTime SENT_TIMESTAMP = ZonedDateTime.now();
    private static final ZonedDateTime SETTLED_TIMESTAMP = ZonedDateTime.now();
    private static final ZonedDateTime TIMESTAMP = ZonedDateTime.now();
    private static final UUID PARENT = UUID.randomUUID();
    private static final CurrencyDataAccessObject CURRENCY = new GRPCCurrencyDataAccessObject(458, "MYR", "Malaysian Ringgit", "MYR");
    private static final CountryDataAccessObject COUNTRY = new GRPCCountryDataAccessObject(458, "Malaysia", "MYR");

    private GRPCTransferInAccountAllocationDataAccessObject transferInAccountAllocation;

    @BeforeEach
    public void setup() {
        transferInAccountAllocation = new GRPCTransferInAccountAllocationDataAccessObject(ID, ACCOUNT_ID, TYPE, COUNTRY, CURRENCY, AMOUNT, STATE, DIRECTION, SENT_TIMESTAMP, SETTLED_TIMESTAMP, TIMESTAMP, PARENT);
    }

    @Test
    @DisplayName("GRPCTransferInAccountAllocationDataAccessObject::new rejects null constructor arguments")
    public void grpc_transfer_in_account_allocation_data_access_object_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new GRPCTransferInAccountAllocationDataAccessObject(null, ACCOUNT_ID, TYPE, COUNTRY, CURRENCY, AMOUNT, STATE, DIRECTION, SENT_TIMESTAMP, SETTLED_TIMESTAMP, TIMESTAMP, PARENT));
        assertThrows(NullPointerException.class, () -> new GRPCTransferInAccountAllocationDataAccessObject(ID, null, TYPE, COUNTRY, CURRENCY, AMOUNT, STATE, DIRECTION, SENT_TIMESTAMP, SETTLED_TIMESTAMP, TIMESTAMP, PARENT));
        assertThrows(NullPointerException.class, () -> new GRPCTransferInAccountAllocationDataAccessObject(ID, ACCOUNT_ID, null, COUNTRY, CURRENCY, AMOUNT, STATE, DIRECTION, SENT_TIMESTAMP, SETTLED_TIMESTAMP, TIMESTAMP, PARENT));
        assertThrows(NullPointerException.class, () -> new GRPCTransferInAccountAllocationDataAccessObject(ID, ACCOUNT_ID, TYPE, null, CURRENCY, AMOUNT, STATE, DIRECTION, SENT_TIMESTAMP, SETTLED_TIMESTAMP, TIMESTAMP, PARENT));
        assertThrows(NullPointerException.class, () -> new GRPCTransferInAccountAllocationDataAccessObject(ID, ACCOUNT_ID, TYPE, COUNTRY, null, AMOUNT, STATE, DIRECTION, SENT_TIMESTAMP, SETTLED_TIMESTAMP, TIMESTAMP, PARENT));
        assertThrows(NullPointerException.class, () -> new GRPCTransferInAccountAllocationDataAccessObject(ID, ACCOUNT_ID, TYPE, COUNTRY, CURRENCY, null, STATE, DIRECTION, SENT_TIMESTAMP, SETTLED_TIMESTAMP, TIMESTAMP, PARENT));
        assertThrows(NullPointerException.class, () -> new GRPCTransferInAccountAllocationDataAccessObject(ID, ACCOUNT_ID, TYPE, COUNTRY, CURRENCY, AMOUNT, null, DIRECTION, SENT_TIMESTAMP, SETTLED_TIMESTAMP, TIMESTAMP, PARENT));
        assertThrows(NullPointerException.class, () -> new GRPCTransferInAccountAllocationDataAccessObject(ID, ACCOUNT_ID, TYPE, COUNTRY, CURRENCY, AMOUNT, STATE, null, SENT_TIMESTAMP, SETTLED_TIMESTAMP, TIMESTAMP, PARENT));
        assertDoesNotThrow(() -> new GRPCTransferInAccountAllocationDataAccessObject(ID, ACCOUNT_ID, TYPE, COUNTRY, CURRENCY, AMOUNT, STATE, DIRECTION, null, SETTLED_TIMESTAMP, TIMESTAMP, PARENT));
        assertDoesNotThrow(() -> new GRPCTransferInAccountAllocationDataAccessObject(ID, ACCOUNT_ID, TYPE, COUNTRY, CURRENCY, AMOUNT, STATE, DIRECTION, SENT_TIMESTAMP, null, TIMESTAMP, PARENT));
        assertThrows(NullPointerException.class, () -> new GRPCTransferInAccountAllocationDataAccessObject(ID, ACCOUNT_ID, TYPE, COUNTRY, CURRENCY, AMOUNT, STATE, DIRECTION, SENT_TIMESTAMP, SETTLED_TIMESTAMP, null, PARENT));
        assertThrows(NullPointerException.class, () -> new GRPCTransferInAccountAllocationDataAccessObject(ID, ACCOUNT_ID, TYPE, COUNTRY, CURRENCY, AMOUNT, STATE, DIRECTION, SENT_TIMESTAMP, SETTLED_TIMESTAMP, TIMESTAMP, null));
    }

    @Test
    @DisplayName("GRPCTransferInAccountAllocationDataAccessObject::identifier is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_transfer_in_account_allocation_data_access_object_identifier_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(TransferInAccountAllocation.class, ID, "identifier");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> transferInAccountAllocation.identifier(UUID.randomUUID()));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCTransferInAccountAllocationDataAccessObject::account is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_transfer_in_account_allocation_data_access_object_account_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(TransferInAccountAllocation.class, ID, "account");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> transferInAccountAllocation.account(UUID.randomUUID()));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCTransferInAccountAllocationDataAccessObject::type is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_transfer_in_account_allocation_data_access_object_type_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(TransferInAccountAllocation.class, ID, "type");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> transferInAccountAllocation.type(AllocationType.PAYMENT));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCTransferInAccountAllocationDataAccessObject::country is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_transfer_in_account_allocation_data_access_object_country_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(TransferInAccountAllocation.class, ID, "country");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> transferInAccountAllocation.country(COUNTRY));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCTransferInAccountAllocationDataAccessObject::currency is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_transfer_in_account_allocation_data_access_object_currency_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(TransferInAccountAllocation.class, ID, "currency");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> transferInAccountAllocation.currency(CURRENCY));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCTransferInAccountAllocationDataAccessObject::amount is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_transfer_in_account_allocation_data_access_object_amount_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(TransferInAccountAllocation.class, ID, "amount");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> transferInAccountAllocation.amount(new BigDecimal("100.00")));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCTransferInAccountAllocationDataAccessObject::allocationState is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_transfer_in_account_allocation_data_access_object_allocation_state_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(TransferInAccountAllocation.class, ID, "allocationState");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> transferInAccountAllocation.allocationState(transferInAccountAllocation.getAllocationState().allocate(new BigDecimal("50.00"))));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCTransferInAccountAllocationDataAccessObject::direction is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_transfer_in_account_allocation_data_access_object_allocation_direction_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(TransferInAccountAllocation.class, ID, "direction");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> transferInAccountAllocation.direction(AllocationDirection.IN));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCTransferInAccountAllocationDataAccessObject::timestamp is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_transfer_in_account_allocation_data_access_object_timestamp_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(TransferInAccountAllocation.class, ID, "timestamp");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> transferInAccountAllocation.timestamp(ZonedDateTime.now()));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCTransferInAccountAllocationDataAccessObject::parent is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_transfer_in_account_allocation_data_access_object_parent_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(TransferInAccountAllocation.class, ID, "parent");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> transferInAccountAllocation.parent(PARENT));
        assertEquals(expected.getMessage(), cause.getMessage());
    }
}
