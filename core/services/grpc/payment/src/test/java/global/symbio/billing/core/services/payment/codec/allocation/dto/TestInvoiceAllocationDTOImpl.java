package global.symbio.billing.core.services.payment.codec.allocation.dto;

import global.symbio.billing.core.services.payment.codec.allocation.dto.impl.InvoiceAllocationDTOImpl;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertThrows;

@ExtendWith(MockitoExtension.class)
public class TestInvoiceAllocationDTOImpl {

    @Test
    @DisplayName("InvoiceAllocationDTO::new rejects null constructor arguments")
    public void invoice_allocation_dto_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new InvoiceAllocationDTOImpl(null, "MYCO00000008191", BigDecimal.ONE));
        assertThrows(NullPointerException.class, () -> new InvoiceAllocationDTOImpl(UUID.randomUUID(), null, BigDecimal.ONE));
        assertThrows(NullPointerException.class, () -> new InvoiceAllocationDTOImpl(UUID.randomUUID(), "MYCO00000008191", null));
    }
}
