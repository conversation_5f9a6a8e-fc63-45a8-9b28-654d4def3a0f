package global.symbio.billing.core.services.payment.codec.cancellation;

import global.symbio.billing.core.country.persistence.api.Country;
import global.symbio.billing.core.currency.persistence.api.Currency;
import global.symbio.billing.core.payment.persistence.api.cancellation.PaymentCancellation;
import global.symbio.billing.core.payment.persistence.api.payment.Payment;
import global.symbio.billing.core.payment.persistence.api.payment.PaymentStatus;
import global.symbio.billing.core.payment.persistence.api.payment.method.PaymentMethod;
import global.symbio.billing.core.reference.persistence.api.reference.Reference;
import global.symbio.billing.core.reference.persistence.api.reference.ReferenceType;
import global.symbio.billing.core.services.codec.uuid.UUIDCodec;
import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.codec.country.CountryCodec;
import global.symbio.billing.core.services.grpc.codec.country.GRPCCountryDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.currency.CurrencyCodec;
import global.symbio.billing.core.services.grpc.codec.currency.GRPCCurrencyDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.decimal.BigDecimalCodec;
import global.symbio.billing.core.services.grpc.codec.payment.method.GRPCPaymentMethodDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.payment.method.PaymentMethodCodec;
import global.symbio.billing.core.services.grpc.codec.reference.GRPCReferenceDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.reference.ReferenceCodec;
import global.symbio.billing.core.services.grpc.codec.reference.ReferenceTypeCodec;
import global.symbio.billing.core.services.grpc.codec.temporal.*;
import global.symbio.billing.core.services.grpc.message.*;
import global.symbio.billing.core.services.grpc.payment.entities.PaymentCancellationMessage;
import global.symbio.billing.core.services.grpc.payment.entities.PaymentMessage;
import global.symbio.billing.core.services.grpc.payment.entities.PaymentStatusMessage;
import global.symbio.billing.core.services.payment.codec.payment.GRPCPaymentDataAccessObject;
import global.symbio.billing.core.services.payment.codec.payment.PaymentCodec;
import global.symbio.billing.core.services.payment.codec.payment.PaymentStatusCodec;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

public class TestPaymentCancellationCodec {
    private Codec<UUID, UUIDMessage> uuid;

    private Codec<Payment, PaymentMessage> payment;

    private Codec<ZonedDateTime, ZonedDateTimeMessage> timestamp;

    private Codec<BigDecimal, BigDecimalMessage> decimal;

    private Codec<Currency, CurrencyMessage> currency;

    private Codec<PaymentMethod, PaymentMethodMessage> method;

    private Codec<PaymentStatus, PaymentStatusMessage> status;

    private Codec<Country, CountryMessage> country;

    private Codec<Reference, ReferenceMessage> reference;

    private Codec<ReferenceType, ReferenceTypeMessage> type;

    private Codec<PaymentCancellation, PaymentCancellationMessage> codec;

    @BeforeEach
    public void setup() {
        final var date = new LocalDateCodec();
        final var time = new LocalTimeCodec();
        final var datetime = new LocalDateTimeCodec(date, time);
        final var offset = new ZoneOffsetCodec();
        final var zone = new ZoneIdCodec();

        uuid = new UUIDCodec();
        decimal = new BigDecimalCodec();
        currency = new CurrencyCodec();
        method = new PaymentMethodCodec();
        status = new PaymentStatusCodec();
        country = new CountryCodec();
        type = new ReferenceTypeCodec();
        reference = new ReferenceCodec(uuid, type);
        timestamp = new ZonedDateTimeCodec(datetime, offset, zone);
        payment = new PaymentCodec(uuid, decimal, currency, country, timestamp, method, status, reference);
        codec = new PaymentCancellationCodec(uuid, payment, timestamp);
    }

    @Test
    @DisplayName("PaymentCancellationCodec::new rejects null constructor arguments")
    public void payment_codec_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new PaymentCancellationCodec(null, payment, timestamp));
        assertThrows(NullPointerException.class, () -> new PaymentCancellationCodec(uuid, null, timestamp));
        assertThrows(NullPointerException.class, () -> new PaymentCancellationCodec(uuid, payment, null));
    }

    @Test
    @DisplayName("PaymentCancellationCodec::encode bean to protobuf")
    public void codec_bean_to_protobuf() {
        final var bean = new GRPCPaymentCancellationDataAccessObject(
            UUID.randomUUID(),
            payment().data(),
            "reason",
            "MYCOPYMX00000000270",
            ZonedDateTime.now(),
            ZonedDateTime.now(),
            ZonedDateTime.now()
        ).entity();
        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));

        assertEquals(bean.getIdentifier(), uuid.decode(protobuf.getIdentifier()));
        assertEquals(bean.getPayment(), payment.decode(protobuf.getPayment()));
        assertEquals(bean.getReason(), protobuf.getReason());
        assertEquals(bean.getReference(), protobuf.getReference());
        assertEquals(bean.getTimestamp(), timestamp.decode(protobuf.getTimestamp()));
    }

    @Test
    @DisplayName("PaymentCancellationCodec::decode protobuf to bean")
    public void codec_protobuf_to_bean() {
        final var cancellation = new GRPCPaymentCancellationDataAccessObject(
            UUID.randomUUID(),
            payment().data(),
            "reason",
            "MYCOPYMX00000000270",
            ZonedDateTime.now(),
            ZonedDateTime.now(),
            ZonedDateTime.now()
        ).entity();
        final var protobuf = PaymentCancellationMessage.newBuilder()
            .setIdentifier(uuid.encode(cancellation.getIdentifier()))
            .setPayment(payment.encode(cancellation.getPayment()))
            .setReason(cancellation.getReason())
            .setReference(cancellation.getReference())
            .setSentTimestamp(timestamp.encode(cancellation.getSentTimestamp()))
            .setSettledTimestamp(timestamp.encode(cancellation.getSettledTimestamp()))
            .setTimestamp(timestamp.encode(cancellation.getTimestamp()))
            .build();

        final var bean = assertDoesNotThrow(() -> codec.decode(protobuf));
        assertEquals(cancellation, bean);
    }

    @Nonnull
    private static Payment payment() {
        return new GRPCPaymentDataAccessObject(
            UUID.randomUUID(),
            UUID.randomUUID(),
            new GRPCPaymentMethodDataAccessObject(1, "BANK_TRANSFER", "Bank Transfer"),
            new GRPCCurrencyDataAccessObject(458, "MYR", "Malaysian Ringgit", "MYR"),
            new BigDecimal("100.00"),
            ZonedDateTime.now(),
            PaymentStatus.ACTIVE,
            "description",
            new GRPCCountryDataAccessObject(458, "Malaysia", "MY"),
            new GRPCReferenceDataAccessObject(UUID.randomUUID(), 1L, "MY", "CO", 2, ReferenceType.INVOICE, "MYCO00000000012"),
            ZonedDateTime.now()
        ).entity();
    }
}
