package global.symbio.billing.core.services.payment.codec.payment;

import global.symbio.billing.core.payment.persistence.api.payment.state.PaymentState;
import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.payment.entities.PaymentStateMessage;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.Arrays;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class TestPaymentStateCodec {

    private Codec<PaymentState, PaymentStateMessage> codec;

    @BeforeEach
    public void setup() {
        codec = new PaymentStateCodec();
    }

    @ParameterizedTest
    @DisplayName("PaymentStateCodec::encode bean to protobuf")
    @MethodSource("beans")
    public void codec_bean_to_protobuf(final PaymentState bean) {
        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));
        assertEquals(bean, codec.decode(protobuf));
    }

    @ParameterizedTest
    @DisplayName("PaymentStatusCodec::decode protobuf to bean")
    @MethodSource("protos")
    public void codec_protobuf_to_bean(final PaymentStateMessage message) {
        final var bean = assertDoesNotThrow(() -> codec.decode(message));
        assertEquals(message, codec.encode(bean));
    }

    @Nonnull
    private static Stream<Arguments> protos() {
        return Stream.of(
            Arguments.of(PaymentStateMessage.PAID),
            Arguments.of(PaymentStateMessage.PARTIALLY_PAID),
            Arguments.of(PaymentStateMessage.UNPAID)
        );
    }

    @Nonnull
    private static Stream<Arguments> beans() {
        return Arrays.stream(PaymentState.values()).map(Arguments::of);
    }
}
