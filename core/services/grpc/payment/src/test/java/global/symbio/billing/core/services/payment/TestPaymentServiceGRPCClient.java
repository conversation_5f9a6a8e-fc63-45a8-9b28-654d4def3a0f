package global.symbio.billing.core.services.payment;

import com.google.common.util.concurrent.Futures;
import global.symbio.billing.core.country.persistence.api.Country;
import global.symbio.billing.core.currency.persistence.api.Currency;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AccountAllocation;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AllocationDirection;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AllocationType;
import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.payment.PaymentAccountAllocation;
import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.transfer.TransferInAccountAllocation;
import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.transfer.TransferOutAccountAllocation;
import global.symbio.billing.core.payment.persistence.api.allocation.state.AllocationState;
import global.symbio.billing.core.payment.persistence.api.cancellation.PaymentCancellation;
import global.symbio.billing.core.payment.persistence.api.payment.Payment;
import global.symbio.billing.core.payment.persistence.api.payment.PaymentStatus;
import global.symbio.billing.core.payment.persistence.api.payment.method.PaymentMethod;
import global.symbio.billing.core.payment.persistence.api.payment.state.PaymentState;
import global.symbio.billing.core.payment.persistence.api.transfer.Transfer;
import global.symbio.billing.core.reference.persistence.api.reference.Reference;
import global.symbio.billing.core.reference.persistence.api.reference.ReferenceType;
import global.symbio.billing.core.services.codec.uuid.UUIDCodec;
import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.codec.country.CountryCodec;
import global.symbio.billing.core.services.grpc.codec.country.GRPCCountryDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.currency.CurrencyCodec;
import global.symbio.billing.core.services.grpc.codec.currency.GRPCCurrencyDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.decimal.BigDecimalCodec;
import global.symbio.billing.core.services.grpc.codec.payment.method.GRPCPaymentMethodDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.payment.method.PaymentMethodCodec;
import global.symbio.billing.core.services.grpc.codec.reference.GRPCReferenceDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.reference.ReferenceCodec;
import global.symbio.billing.core.services.grpc.codec.reference.ReferenceTypeCodec;
import global.symbio.billing.core.services.grpc.codec.temporal.*;
import global.symbio.billing.core.services.grpc.message.*;
import global.symbio.billing.core.services.grpc.payment.*;
import global.symbio.billing.core.services.grpc.payment.dto.AccountInvoiceAllocationMessage;
import global.symbio.billing.core.services.grpc.payment.dto.InvoiceAllocationMessage;
import global.symbio.billing.core.services.grpc.payment.dto.InvoiceAllocationResponseMessage;
import global.symbio.billing.core.services.grpc.payment.dto.PaymentInvoiceAllocationMessage;
import global.symbio.billing.core.services.grpc.payment.entities.*;
import global.symbio.billing.core.services.payment.codec.allocation.*;
import global.symbio.billing.core.services.payment.codec.allocation.dto.*;
import global.symbio.billing.core.services.payment.codec.allocation.dto.impl.GRPCAccountInvoiceAllocationDTO;
import global.symbio.billing.core.services.payment.codec.allocation.dto.impl.GRPCInvoiceAllocationDTO;
import global.symbio.billing.core.services.payment.codec.allocation.dto.impl.GRPCInvoiceAllocationResponseDTO;
import global.symbio.billing.core.services.payment.codec.allocation.dto.impl.GRPCPaymentInvoiceAllocationDTO;
import global.symbio.billing.core.services.payment.codec.allocation.state.AllocationStateCodec;
import global.symbio.billing.core.services.payment.codec.cancellation.GRPCPaymentCancellationDataAccessObject;
import global.symbio.billing.core.services.payment.codec.cancellation.PaymentCancellationCodec;
import global.symbio.billing.core.services.payment.codec.payment.*;
import global.symbio.billing.core.services.payment.codec.transfer.GRPCTransferDataAccessObject;
import global.symbio.billing.core.services.payment.codec.transfer.TransferCodec;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.*;
import java.util.List;
import java.util.Set;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestPaymentServiceGRPCClient {

    @Mock
    PaymentGrpc.PaymentFutureStub service;

    private Codec<UUID, UUIDMessage> uuid;

    private Codec<BigDecimal, BigDecimalMessage> decimal;

    private Codec<ZonedDateTime, ZonedDateTimeMessage> timestamp;

    private Codec<LocalDate, LocalDateMessage> date;

    private Codec<LocalTime, LocalTimeMessage> time;

    private Codec<LocalDateTime, LocalDateTimeMessage> datetime;

    private Codec<ZoneOffset, ZoneOffsetMessage> offset;

    private Codec<ZoneId, ZoneIdMessage> zone;

    private Codec<AccountInvoiceAllocation.InvoiceAllocation, InvoiceAllocationMessage> invoice;

    private Codec<AccountInvoiceAllocation, AccountInvoiceAllocationMessage> allocation;

    private Codec<InvoiceAllocationDTO, InvoiceAllocationMessage> invoiceAllocation;

    private Codec<InvoiceAllocationResponseDTO, InvoiceAllocationResponseMessage> invoiceAllocationResponse;

    private Codec<PaymentInvoiceAllocationDTO, PaymentInvoiceAllocationMessage> paymentInvoiceAllocation;

    private Codec<Currency, CurrencyMessage> currency;

    private Codec<Country, CountryMessage> country;

    private Codec<PaymentMethod, PaymentMethodMessage> method;

    private Codec<PaymentStatus, PaymentStatusMessage> status;

    private Codec<Payment, PaymentMessage> payment;

    private Codec<AllocationType, AllocationTypeMessage> type;

    private Codec<AllocationState, AllocationStateMessage> state;

    private Codec<AllocationDirection, AllocationDirectionMessage> direction;

    private Codec<PaymentAccountAllocation<?>, AccountAllocationMessageApi> paymentAccountAllocation;

    private Codec<PaymentState, PaymentStateMessage> paymentState;

    private Codec<Reference, ReferenceMessage> reference;

    private Codec<ReferenceType, ReferenceTypeMessage> referenceType;

    private Codec<TransferInAccountAllocation<?>, AccountAllocationMessageApi> transferInAccountAllocation;

    private Codec<TransferOutAccountAllocation<?>, AccountAllocationMessageApi> transferOutAccountAllocation;

    private Codec<AccountInvoiceAllocationResponse, AccountInvoiceAllocationResponseMessage> accountInvoiceAllocation;

    private Codec<PaymentResponse, CreatePaymentResponse> paymentResponse;

    private Codec<Transfer, TransferMessage> transfer;

    private Codec<PaymentCancellation, PaymentCancellationMessage> cancellation;

    private PaymentServiceGRPCClient client;

    @BeforeEach
    public void setup() {
        uuid = new UUIDCodec();
        decimal = new BigDecimalCodec();
        date = new LocalDateCodec();
        time = new LocalTimeCodec();
        datetime = new LocalDateTimeCodec(date, time);
        offset = new ZoneOffsetCodec();
        zone = new ZoneIdCodec();
        timestamp = new ZonedDateTimeCodec(datetime, offset, zone);
        invoice = new InvoiceAllocationCodec(uuid, decimal);
        allocation = new AccountInvoiceAllocationCodec(uuid, decimal, invoice);
        currency = new CurrencyCodec();
        country = new CountryCodec();
        method = new PaymentMethodCodec();
        status = new PaymentStatusCodec();
        referenceType = new ReferenceTypeCodec();
        reference = new ReferenceCodec(uuid, referenceType);
        payment = new PaymentCodec(uuid, decimal, currency, country, timestamp, method, status, reference);
        type = new AllocationTypeCodec();
        state = new AllocationStateCodec(decimal);
        direction = new AllocationDirectionCodec();
        invoiceAllocation = new InvoiceAllocationDTOCodec(uuid, decimal);
        invoiceAllocationResponse = new InvoiceAllocationResponseDTOCodec(uuid, decimal, timestamp);
        paymentInvoiceAllocation = new PaymentInvoiceAllocationDTOCodec(payment, invoiceAllocationResponse);
        paymentState = new PaymentStateCodec();
        paymentAccountAllocation = new PaymentAccountAllocationCodec(uuid, decimal, type, state, direction, currency, country, timestamp, payment);
        transferInAccountAllocation = new TransferInAccountAllocationCodec(uuid, decimal, type, state, direction, currency, country, timestamp);
        transferOutAccountAllocation = new TransferOutAccountAllocationCodec(uuid, decimal, type, state, direction, currency, country, timestamp);
        transfer = new TransferCodec(uuid, decimal, currency, country, timestamp, transferInAccountAllocation, transferOutAccountAllocation, paymentAccountAllocation);
        accountInvoiceAllocation = new AccountInvoiceAllocationResponseCodec(paymentAccountAllocation, invoiceAllocation);
        paymentResponse = new PaymentResponseCodec(payment, accountInvoiceAllocation);
        cancellation = new PaymentCancellationCodec(uuid, payment, timestamp);
        client = new PaymentServiceGRPCClient(service, uuid, decimal, timestamp, allocation, status, paymentAccountAllocation, paymentInvoiceAllocation, paymentState, transferInAccountAllocation, invoiceAllocation, transfer, paymentResponse, type, cancellation);
    }

    @Test
    @DisplayName("PaymentServiceGRPCClient::new rejects null constructor arguments")
    public void payment_service_GRPC_client_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new PaymentServiceGRPCClient(null, uuid, decimal, timestamp, allocation, status, paymentAccountAllocation, paymentInvoiceAllocation, paymentState, transferInAccountAllocation, invoiceAllocation, transfer, paymentResponse, type, cancellation));
        assertThrows(NullPointerException.class, () -> new PaymentServiceGRPCClient(service, null, decimal, timestamp, allocation, status, paymentAccountAllocation, paymentInvoiceAllocation, paymentState, transferInAccountAllocation, invoiceAllocation, transfer, paymentResponse, type, cancellation));
        assertThrows(NullPointerException.class, () -> new PaymentServiceGRPCClient(service, uuid, null, timestamp, allocation, status, paymentAccountAllocation, paymentInvoiceAllocation, paymentState, transferInAccountAllocation, invoiceAllocation, transfer, paymentResponse, type, cancellation));
        assertThrows(NullPointerException.class, () -> new PaymentServiceGRPCClient(service, uuid, decimal, null, allocation, status, paymentAccountAllocation, paymentInvoiceAllocation, paymentState, transferInAccountAllocation, invoiceAllocation, transfer, paymentResponse, type, cancellation));
        assertThrows(NullPointerException.class, () -> new PaymentServiceGRPCClient(service, uuid, decimal, timestamp, null, status, paymentAccountAllocation, paymentInvoiceAllocation, paymentState, transferInAccountAllocation, invoiceAllocation, transfer, paymentResponse, type, cancellation));
        assertThrows(NullPointerException.class, () -> new PaymentServiceGRPCClient(service, uuid, decimal, timestamp, allocation, null, paymentAccountAllocation, paymentInvoiceAllocation, paymentState, transferInAccountAllocation, invoiceAllocation, transfer, paymentResponse, type, cancellation));
        assertThrows(NullPointerException.class, () -> new PaymentServiceGRPCClient(service, uuid, decimal, timestamp, allocation, status, null, paymentInvoiceAllocation, paymentState, transferInAccountAllocation, invoiceAllocation, transfer, paymentResponse, type, cancellation));
        assertThrows(NullPointerException.class, () -> new PaymentServiceGRPCClient(service, uuid, decimal, timestamp, allocation, status, paymentAccountAllocation, null, paymentState, transferInAccountAllocation, invoiceAllocation, transfer, paymentResponse, type, cancellation));
        assertThrows(NullPointerException.class, () -> new PaymentServiceGRPCClient(service, uuid, decimal, timestamp, allocation, status, paymentAccountAllocation, paymentInvoiceAllocation, null, transferInAccountAllocation, invoiceAllocation, transfer, paymentResponse, type, cancellation));
        assertThrows(NullPointerException.class, () -> new PaymentServiceGRPCClient(service, uuid, decimal, timestamp, allocation, status, paymentAccountAllocation, paymentInvoiceAllocation, paymentState, null, invoiceAllocation, transfer, paymentResponse, type, cancellation));
        assertThrows(NullPointerException.class, () -> new PaymentServiceGRPCClient(service, uuid, decimal, timestamp, allocation, status, paymentAccountAllocation, paymentInvoiceAllocation, paymentState, transferInAccountAllocation, null, transfer, paymentResponse, type, cancellation));
        assertThrows(NullPointerException.class, () -> new PaymentServiceGRPCClient(service, uuid, decimal, timestamp, allocation, status, paymentAccountAllocation, paymentInvoiceAllocation, paymentState, transferInAccountAllocation, invoiceAllocation, null, paymentResponse, type, cancellation));
        assertThrows(NullPointerException.class, () -> new PaymentServiceGRPCClient(service, uuid, decimal, timestamp, allocation, status, paymentAccountAllocation, paymentInvoiceAllocation, paymentState, transferInAccountAllocation, invoiceAllocation, transfer, null, type, cancellation));
        assertThrows(NullPointerException.class, () -> new PaymentServiceGRPCClient(service, uuid, decimal, timestamp, allocation, status, paymentAccountAllocation, paymentInvoiceAllocation, paymentState, transferInAccountAllocation, invoiceAllocation, transfer, paymentResponse, null, cancellation));
        assertThrows(NullPointerException.class, () -> new PaymentServiceGRPCClient(service, uuid, decimal, timestamp, allocation, status, paymentAccountAllocation, paymentInvoiceAllocation, paymentState, transferInAccountAllocation, invoiceAllocation, transfer, paymentResponse, type, null));
    }

    @Test
    @DisplayName("PaymentServiceGRPCClient::create invokes payment gRPC service and returns Payment")
    public void payment_service_create_invokes_payment_gRPC_service_and_returns_payments() {
        final var identifier = UUID.randomUUID();
        final var customer = UUID.randomUUID();
        final var account = UUID.randomUUID();
        final var methodId = 1;
        final var method = new GRPCPaymentMethodDataAccessObject(methodId, "BANK_TRANSFER", "Bank Transfer");
        final var currencyCode = "MYR";
        final var currency = new GRPCCurrencyDataAccessObject(458, currencyCode, "Malaysian Ringgit", "MYR");
        final var status = PaymentStatus.ACTIVE;
        final var amount = new BigDecimal(100);
        final var receipt = ZonedDateTime.now();
        final var description = "description";
        final var countryCode = "MY";
        final var country = new GRPCCountryDataAccessObject(458, "Malaysia", countryCode);
        final var allocation = new GRPCAccountInvoiceAllocationDTO(account, amount, Set.of());
        final var allocations = Set.of(allocation);
        final var reference = new GRPCReferenceDataAccessObject(UUID.randomUUID(), 1L, "MY", "CO", 2, ReferenceType.PAYMENT, "MYCOPYMT00000000012");
        final var payment = new GRPCPaymentDataAccessObject(identifier, customer, method, currency, amount, receipt, status, description, country, reference, ZonedDateTime.now()).entity();
        final var response = new GRPCPaymentResponse(payment, Set.of());

        when(service.createPayment(any(CreatePaymentRequest.class))).thenReturn(Futures.immediateFuture(paymentResponse.encode(response)));

        final var result = assertDoesNotThrow(() -> client.create(identifier, customer, methodId, currencyCode, amount, receipt, description, countryCode, allocations));

        verify(service, times(1)).createPayment(any(CreatePaymentRequest.class));
        assertEquals(response, result);
    }

    @Test
    @DisplayName("PaymentServiceGRPCClient::create throws exception given service throws exception")
    public void payment_service_create_throws_exception_given_service_throws_exception() {
        final var identifier = UUID.randomUUID();
        final var customer = UUID.randomUUID();
        final var account = UUID.randomUUID();
        final var methodId = 1;
        final var currencyCode = "MYR";
        final var amount = new BigDecimal(100);
        final var receipt = ZonedDateTime.now();
        final var description = "description";
        final var countryCode = "MY";
        final var allocation = new GRPCAccountInvoiceAllocationDTO(account, amount, Set.of());
        final var allocations = Set.of(allocation);

        when(service.createPayment(any(CreatePaymentRequest.class))).thenThrow(new RuntimeException("Service Exception"));

        assertThrows(RuntimeException.class, () -> client.create(identifier, customer, methodId, currencyCode, amount, receipt, description, countryCode, allocations));
        verify(service, times(1)).createPayment(any(CreatePaymentRequest.class));
    }

    @Test
    @DisplayName("PaymentServiceGRPCClient::getPaymentAccountAllocations invokes payment gRPC service and returns payment account allocations")
    public void payment_service_get_payment_account_allocations_invokes_grpc_service_and_returns_payment_account_allocations() {
        final var identifier = UUID.randomUUID();
        final var account = UUID.randomUUID();
        final var customer = UUID.randomUUID();
        final var amount = new BigDecimal("100.00");
        final var receipt = ZonedDateTime.now();
        final var description = "description";
        final var paymentDateStart = ZonedDateTime.now();
        final var paymentDateEnd = ZonedDateTime.now();
        final var receiptDateStart = ZonedDateTime.now();
        final var receiptDateEnd = ZonedDateTime.now();
        final var status = PaymentStatus.ACTIVE;
        final var method = new GRPCPaymentMethodDataAccessObject(1, "BANK_TRANSFER", "Bank Transfer");
        final var currency = new GRPCCurrencyDataAccessObject(458, "MYR", "Malaysian Ringgit", "MYR");
        final var country = new GRPCCountryDataAccessObject(458, "Malaysia", "MYR");
        final var states = Set.of("UNALLOCATED");
        final var methods = Set.of(1);
        final var statuses = Set.of(status);
        final var reference = new GRPCReferenceDataAccessObject(UUID.randomUUID(), 1L, "MY", "CO", 2, ReferenceType.PAYMENT, "MYCOPYMT00000000012");
        final var payment = new GRPCPaymentDataAccessObject(identifier, customer, method, currency, amount, receipt, status, description, country, reference, ZonedDateTime.now());
        final var paymentAccountAllocation = new GRPCPaymentAccountAllocationDataAccessObject(
            identifier,
            account,
            AllocationType.PAYMENT,
            country,
            currency,
            new BigDecimal("100.00"),
            AllocationState.create(new BigDecimal("100.00")),
            AllocationDirection.IN,
            ZonedDateTime.now(),
            ZonedDateTime.now(),
            ZonedDateTime.now(),
            payment
        ).entity();
        final var expected = List.of(paymentAccountAllocation);

        final var response = mock(GetPaymentAccountAllocationsResponse.class);
        when(service.getPaymentAccountAllocations(any(GetPaymentAccountAllocationsRequest.class))).thenReturn(Futures.immediateFuture(response));
        when(response.getAllocationsList()).thenReturn(expected.stream().map(this.paymentAccountAllocation::encode).toList());

        final var result = assertDoesNotThrow(() -> client.getPaymentAccountAllocations(account, paymentDateStart, paymentDateEnd, receiptDateStart, receiptDateEnd, states, methods, statuses));
        verify(service, times(1)).getPaymentAccountAllocations(any(GetPaymentAccountAllocationsRequest.class));
        verify(response, times(1)).getAllocationsList();
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("PaymentServiceGRPCClient::getPaymentAccountAllocations throws exception given service throws exception")
    public void payment_service_get_payment_account_allocations_throws_exception_given_service_throws_exception() {
        final var account = UUID.randomUUID();
        final var paymentDateStart = ZonedDateTime.now();
        final var paymentDateEnd = ZonedDateTime.now();
        final var receiptDateStart = ZonedDateTime.now();
        final var receiptDateEnd = ZonedDateTime.now();
        final var states = Set.of("UNALLOCATED");
        final var methods = Set.of(1);
        final var statuses = Set.of(PaymentStatus.ACTIVE);
        when(service.getPaymentAccountAllocations(any(GetPaymentAccountAllocationsRequest.class))).thenThrow(new RuntimeException("Service Exception"));
        assertThrows(RuntimeException.class, () -> client.getPaymentAccountAllocations(account, paymentDateStart, paymentDateEnd, receiptDateStart, receiptDateEnd, states, methods, statuses));
        verify(service, times(1)).getPaymentAccountAllocations(any(GetPaymentAccountAllocationsRequest.class));
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @DisplayName("PaymentServiceGRPCClient::assignAllocationToInvoices invokes payment gRPC service and returns AccountAllocation")
    public void payment_service_assign_allocation_to_invoices_invokes_payment_gRPC_service_and_returns_account_allocation(final boolean isPaymentType) {
        final var identifier = UUID.randomUUID();
        final var account = UUID.randomUUID();
        final var reference = "MYCO00000008191";
        final var amount = new BigDecimal(100);
        final var allocated = new GRPCInvoiceAllocationDTO(account, reference, amount);
        final var deallocated = new GRPCInvoiceAllocationDTO(account, reference, BigDecimal.ZERO);
        final var currency = new GRPCCurrencyDataAccessObject(458, "MYR", "Malaysian Ringgit", "MYR");
        final var country = new GRPCCountryDataAccessObject(458, "Malaysia", "MY");

        final var response = mock(AssignAllocationToInvoicesResponse.class);
        when(service.assignAllocationToInvoices(any(AssignAllocationToInvoicesRequest.class))).thenReturn(Futures.immediateFuture(response));

        AccountAllocation<?> expected;
        if (isPaymentType) {
            final var payment = new GRPCPaymentDataAccessObject(
                UUID.randomUUID(),
                UUID.randomUUID(),
                new GRPCPaymentMethodDataAccessObject(1, "BANK_TRANSFER", "Bank Transfer"),
                currency,
                new BigDecimal("100.00"),
                ZonedDateTime.now(),
                PaymentStatus.ACTIVE,
                "description",
                country,
                new GRPCReferenceDataAccessObject(UUID.randomUUID(), 1L, "MY", "CO", 2, ReferenceType.PAYMENT, "MYCOPYMT00000000012"),
                ZonedDateTime.now()
            );

            expected = new GRPCPaymentAccountAllocationDataAccessObject(
                UUID.randomUUID(),
                UUID.randomUUID(),
                AllocationType.PAYMENT,
                country,
                currency,
                BigDecimal.TEN,
                AllocationState.create(BigDecimal.TEN),
                AllocationDirection.IN,
                ZonedDateTime.now(),
                ZonedDateTime.now(),
                ZonedDateTime.now(),
                payment
            ).entity();

            when(response.getAllocation()).thenReturn(this.paymentAccountAllocation.encode((PaymentAccountAllocation<?>) expected));

        } else {
            expected = new GRPCTransferInAccountAllocationDataAccessObject(
                UUID.randomUUID(),
                UUID.randomUUID(),
                AllocationType.TRANSFER_IN,
                country,
                currency,
                BigDecimal.ONE,
                AllocationState.create(BigDecimal.ONE),
                AllocationDirection.IN,
                ZonedDateTime.now(),
                ZonedDateTime.now(),
                ZonedDateTime.now(),
                UUID.randomUUID()
            ).entity();

            when(response.getAllocation()).thenReturn(this.transferInAccountAllocation.encode((TransferInAccountAllocation<?>) expected));
        }

        final var result = assertDoesNotThrow(() -> client.assignAllocationToInvoices(identifier, Set.of(allocated, deallocated)));

        verify(service, times(1)).assignAllocationToInvoices(any(AssignAllocationToInvoicesRequest.class));
        verify(response, times(1)).getAllocation();
        assertEquals(expected.getIdentifier(), result.getIdentifier());
        assertEquals(expected.getAccount(), result.getAccount());
        assertEquals(expected.getAmount(), result.getAmount());
        assertEquals(expected.getAllocationState(), result.getAllocationState());
        assertEquals(expected.getDirection(), result.getDirection());
        assertEquals(expected.getTimestamp(), result.getTimestamp());
        assertEquals(expected.getType(), result.getType());
    }

    @Test
    @DisplayName("PaymentServiceGRPCClient::assignAllocationToInvoices throws exception given service throws exception")
    public void payment_service_assign_allocation_to_invoices_throws_exception_given_service_throws_exception() {
        final var identifier = UUID.randomUUID();
        final var account = UUID.randomUUID();
        final var reference = "MYCO00000008191";
        final var amount = new BigDecimal(100);
        final var allocated = new GRPCInvoiceAllocationDTO(account, reference, amount);
        final var deallocated = new GRPCInvoiceAllocationDTO(account, reference, BigDecimal.ZERO);

        when(service.assignAllocationToInvoices(any(AssignAllocationToInvoicesRequest.class))).thenThrow(new RuntimeException("Service Exception"));

        assertThrows(RuntimeException.class, () -> client.assignAllocationToInvoices(identifier, Set.of(allocated, deallocated)));
        verify(service, times(1)).assignAllocationToInvoices(any(AssignAllocationToInvoicesRequest.class));
    }

    @Test
    @DisplayName("PaymentServiceGRPCClient::getPaymentStates returns payment states")
    public void payment_service_get_payment_states_returns_payment_states() {
        final var expected = List.of(PaymentState.values());

        final var response = mock(GetPaymentStatesResponse.class);
        when(service.getPaymentStates(any(GetPaymentStatesRequest.class))).thenReturn(Futures.immediateFuture(response));
        when(response.getPaymentStateList()).thenReturn(expected.stream().map(this.paymentState::encode).toList());

        final var result = assertDoesNotThrow(() -> client.getPaymentStates());
        verify(service, times(1)).getPaymentStates(any(GetPaymentStatesRequest.class));
        verify(response, times(1)).getPaymentStateList();
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("PaymentServiceGRPCClient::getPaymentStates throws exception given that service throws exception")
    public void payment_service_get_payment_states_throws_exception_given_service_throws_exception() {
        final var expected = List.of(PaymentState.values());

        final var response = mock(GetPaymentStatesResponse.class);
        when(service.getPaymentStates(any(GetPaymentStatesRequest.class))).thenThrow(new RuntimeException("Service Exception"));

        assertThrows(RuntimeException.class, () -> client.getPaymentStates());
        verify(service, times(1)).getPaymentStates(any(GetPaymentStatesRequest.class));
    }

    @Test
    @DisplayName("PaymentServiceGRPCClient::createTransfer invokes payment gRPC service and returns Transfer")
    public void payment_service_create_transfer_invokes_payment_gRPC_service_and_returns_transfer() {
        final var identifier = UUID.randomUUID();
        final var fromAccountAllocation = UUID.randomUUID();
        final var toAccount = UUID.randomUUID();
        final var debitIdentifier = UUID.randomUUID();
        final var creditIdentifier = UUID.randomUUID();
        final var currency = new GRPCCurrencyDataAccessObject(458, "MYR", "Malaysian Ringgit", "MYR");
        final var account = UUID.randomUUID();
        final var amount = new BigDecimal(100);
        final var state = AllocationState.create(amount);
        final var description = "description";
        final var countryCode = "MY";
        final var country = new GRPCCountryDataAccessObject(458, "Malaysia", countryCode);

        final var debit = new GRPCTransferOutAccountAllocationDataAccessObject(
            debitIdentifier,
            account,
            AllocationType.TRANSFER_OUT,
            country,
            currency,
            amount,
            state.allocate(amount),
            AllocationDirection.OUT,
            ZonedDateTime.now(),
            ZonedDateTime.now(),
            ZonedDateTime.now(),
            identifier
        );

        final var credit = new GRPCTransferInAccountAllocationDataAccessObject(
            creditIdentifier,
            toAccount,
            AllocationType.TRANSFER_IN,
            country,
            currency,
            amount,
            state,
            AllocationDirection.IN,
            ZonedDateTime.now(),
            ZonedDateTime.now(),
            ZonedDateTime.now(),
            debitIdentifier
        );

        final var method = new GRPCPaymentMethodDataAccessObject(1, "BANK_TRANSFER", "Bank Transfer");
        final var reference = new GRPCReferenceDataAccessObject(UUID.randomUUID(), 1L, "MY", "CO", 2, ReferenceType.PAYMENT, "MYCOPYMT00000000012");
        final var payment = new GRPCPaymentDataAccessObject(UUID.randomUUID(), UUID.randomUUID(), method, currency, new BigDecimal("100.00"), ZonedDateTime.now(), PaymentStatus.ACTIVE, "description", country, reference,  ZonedDateTime.now());
        final var paymentAccountAllocation = new GRPCPaymentAccountAllocationDataAccessObject(
            identifier,
            account,
            AllocationType.PAYMENT,
            country,
            currency,
            new BigDecimal("100.00"),
            AllocationState.create(new BigDecimal("100.00")),
            AllocationDirection.IN,
            ZonedDateTime.now(),
            ZonedDateTime.now(),
            ZonedDateTime.now(),
            payment
        );

        final var expected = new GRPCTransferDataAccessObject(identifier, credit, debit, currency, amount, description, country, paymentAccountAllocation, ZonedDateTime.now()).entity();

        final var response = mock(CreateTransferResponse.class);
        when(service.createTransfer(any(CreateTransferRequest.class))).thenReturn(Futures.immediateFuture(response));
        when(response.getTransfer()).thenReturn(this.transfer.encode(expected));

        final var result = assertDoesNotThrow(() -> client.createTransfer(identifier, fromAccountAllocation, toAccount, amount, description));

        verify(service, times(1)).createTransfer(any(CreateTransferRequest.class));
        verify(response, times(1)).getTransfer();
        assertEquals(expected.getIdentifier(), result.getIdentifier());
        assertEquals(expected.getTransferOut(), result.getTransferOut());
        assertEquals(expected.getTransferIn(), result.getTransferIn());
        assertEquals(expected.getAmount(), result.getAmount());
        assertEquals(expected.getCountry(), result.getCountry());
        assertEquals(expected.getDescription(), result.getDescription());
        assertEquals(expected.getTimestamp(), result.getTimestamp());
    }

    @Test
    @DisplayName("PaymentServiceGRPCClient::createTransfer throws exception given service throws exception")
    public void payment_service_create_transfer_throws_exception_given_service_throws_exception() {
        final var identifier = UUID.randomUUID();
        final var fromAccountAllocation = UUID.randomUUID();
        final var toAccount = UUID.randomUUID();
        final var amount = new BigDecimal(100);
        final var description = "description";

        final var response = mock(CreateTransferResponse.class);
        when(service.createTransfer(any(CreateTransferRequest.class))).thenReturn(Futures.immediateFuture(response));

        assertThrows(RuntimeException.class, () -> client.createTransfer(identifier, fromAccountAllocation, toAccount, amount, description));
        verify(service, times(1)).createTransfer(any(CreateTransferRequest.class));
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @DisplayName("PaymentServiceGRPCClient::getPaymentAccountAllocation invokes payment gRPC service and returns payment account allocation")
    public void payment_service_get_payment_account_allocation_invokes_grpc_service_and_returns_payment_account_allocation(final boolean present) {
        final var identifier = UUID.randomUUID();
        final var paymentId = UUID.randomUUID();
        final var account = UUID.randomUUID();
        final var customer = UUID.randomUUID();
        final var amount = new BigDecimal("100.00");
        final var receipt = ZonedDateTime.now();
        final var description = "description";
        final var status = PaymentStatus.ACTIVE;
        final var method = new GRPCPaymentMethodDataAccessObject(1, "BANK_TRANSFER", "Bank Transfer");
        final var currency = new GRPCCurrencyDataAccessObject(458, "MYR", "Malaysian Ringgit", "MYR");
        final var country = new GRPCCountryDataAccessObject(458, "Malaysia", "MYR");
        final var reference = new GRPCReferenceDataAccessObject(UUID.randomUUID(), 1L, "MY", "CO", 2, ReferenceType.PAYMENT, "MYCOPYMT00000000012");
        final var payment = new GRPCPaymentDataAccessObject(paymentId, customer, method, currency, amount, receipt, status, description, country, reference, ZonedDateTime.now());
        final var paymentAccountAllocation = new GRPCPaymentAccountAllocationDataAccessObject(identifier, account, AllocationType.PAYMENT, country, currency, new BigDecimal("100.00"), AllocationState.create(new BigDecimal("100.00")), AllocationDirection.IN, ZonedDateTime.now(), ZonedDateTime.now(), ZonedDateTime.now(), payment).entity();

        final var response = mock(GetPaymentAccountAllocationResponse.class);

        final var protobuf = this.paymentAccountAllocation.encode(paymentAccountAllocation);

        when(service.getPaymentAccountAllocation(any(GetPaymentAccountAllocationRequest.class))).thenReturn(Futures.immediateFuture(response));
        when(response.hasAllocation()).thenReturn(present);
        if (present) {
            when(response.getAllocation()).thenReturn(protobuf);
        }

        final var result = assertDoesNotThrow(() -> client.getPaymentAccountAllocation(identifier));
        verify(service, times(1)).getPaymentAccountAllocation(any(GetPaymentAccountAllocationRequest.class));
        verify(response, times(1)).hasAllocation();
        verify(response, present ? times(1) : never()).getAllocation();
        assertEquals(present, result.isPresent());
        result.ifPresent(bean -> assertEquals(this.paymentAccountAllocation.decode(protobuf), bean));
    }

    @Test
    @DisplayName("PaymentServiceGRPCClient::getPaymentAccountAllocation throws exception given service throws exception")
    public void payment_service_get_payment_account_allocation_throws_exception_given_service_throws_exception() {
        final var identifier = UUID.randomUUID();
        when(service.getPaymentAccountAllocation(any(GetPaymentAccountAllocationRequest.class))).thenThrow(new RuntimeException("Service Exception"));
        assertThrows(RuntimeException.class, () -> client.getPaymentAccountAllocation(identifier));
        verify(service, times(1)).getPaymentAccountAllocation(any(GetPaymentAccountAllocationRequest.class));
    }

    @Test
    @DisplayName("PaymentServiceGRPCClient::getInvoiceAllocations invokes payment gRPC service and returns invoice allocations")
    public void payment_service_get_invoice_allocations_invokes_grpc_service_and_returns_invoice_allocations() {
        final var identifier = UUID.randomUUID();
        final var invoiceAllocations = List.of(new GRPCInvoiceAllocationDTO(UUID.randomUUID(), "MYCO00000008191", BigDecimal.TEN));
        final var response = mock(GetInvoiceAllocationsResponse.class);
        when(response.getInvoiceAllocationsList()).thenReturn(invoiceAllocations.stream().map(invoiceAllocation::encode).toList());

        when(service.getInvoiceAllocations(any(GetInvoiceAllocationsRequest.class))).thenReturn(Futures.immediateFuture(response));

        final var result = assertDoesNotThrow(() -> client.getInvoiceAllocations(identifier));
        verify(service, times(1)).getInvoiceAllocations(any(GetInvoiceAllocationsRequest.class));
        assertEquals(invoiceAllocations, result);
    }

    @Test
    @DisplayName("PaymentServiceGRPCClient::getInvoiceAllocations throws exception given service throws exception")
    public void payment_service_get_invoice_allocations_throws_exception_given_service_throws_exception() {
        final var identifier = UUID.randomUUID();
        when(service.getInvoiceAllocations(any(GetInvoiceAllocationsRequest.class))).thenThrow(new RuntimeException("Service Exception"));
        assertThrows(RuntimeException.class, () -> client.getInvoiceAllocations(identifier));
        verify(service, times(1)).getInvoiceAllocations(any(GetInvoiceAllocationsRequest.class));
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @DisplayName("PaymentServiceGRPCClient::getTransferByTransferIn invokes payment gRPC service and returns transfer")
    public void payment_service_get_transfer_by_transfer_in_invokes_grpc_service_and_returns_transfer(final boolean present) {
        final var identifier = UUID.randomUUID();
        final var account = UUID.randomUUID();
        final var amount = new BigDecimal("100.00");
        final var description = "description";
        final var toAccount = UUID.randomUUID();
        final var debitIdentifier = UUID.randomUUID();
        final var creditIdentifier = UUID.randomUUID();
        final var state = AllocationState.create(amount);
        final var currency = new GRPCCurrencyDataAccessObject(458, "MYR", "Malaysian Ringgit", "MYR");
        final var country = new GRPCCountryDataAccessObject(458, "Malaysia", "MYR");

        final var transferOut = new GRPCTransferOutAccountAllocationDataAccessObject(
            debitIdentifier,
            account,
            AllocationType.TRANSFER_OUT,
            country,
            currency,
            amount,
            state.allocate(amount),
            AllocationDirection.OUT,
            ZonedDateTime.now(),
            ZonedDateTime.now(),
            ZonedDateTime.now(),
            identifier
        );

        final var transferIn = new GRPCTransferInAccountAllocationDataAccessObject(
            creditIdentifier,
            toAccount,
            AllocationType.TRANSFER_IN,
            country,
            currency,
            amount,
            state,
            AllocationDirection.IN,
            ZonedDateTime.now(),
            ZonedDateTime.now(),
            ZonedDateTime.now(),
            debitIdentifier
        );

        final var method = new GRPCPaymentMethodDataAccessObject(1, "BANK_TRANSFER", "Bank Transfer");
        final var reference = new GRPCReferenceDataAccessObject(UUID.randomUUID(), 1L, "MY", "CO", 2, ReferenceType.PAYMENT, "MYCOPYMT00000000012");
        final var payment = new GRPCPaymentDataAccessObject(UUID.randomUUID(), UUID.randomUUID(), method, currency, new BigDecimal("100.00"), ZonedDateTime.now(), PaymentStatus.ACTIVE, "description", country, reference,  ZonedDateTime.now());
        final var paymentAccountAllocation = new GRPCPaymentAccountAllocationDataAccessObject(
            identifier,
            account,
            AllocationType.PAYMENT,
            country,
            currency,
            new BigDecimal("100.00"),
            AllocationState.create(new BigDecimal("100.00")),
            AllocationDirection.IN,
            ZonedDateTime.now(),
            ZonedDateTime.now(),
            ZonedDateTime.now(),
            payment
        );

        final var transfer = new GRPCTransferDataAccessObject(identifier, transferIn, transferOut, currency, amount, description, country, paymentAccountAllocation, ZonedDateTime.now()).entity();
        final var response = mock(GetTransferByTransferInResponse.class);
        final var protobuf = this.transfer.encode(transfer);
        when(service.getTransferByTransferIn(any(GetTransferByTransferInRequest.class))).thenReturn(Futures.immediateFuture(response));
        when(response.hasTransfer()).thenReturn(present);
        if (present) {
            when(response.getTransfer()).thenReturn(protobuf);
        }

        final var result = assertDoesNotThrow(() -> client.getTransferByTransferIn(identifier));
        verify(service, times(1)).getTransferByTransferIn(any(GetTransferByTransferInRequest.class));
        verify(response, times(1)).hasTransfer();
        verify(response, present ? times(1) : never()).getTransfer();
        assertEquals(present, result.isPresent());
        result.ifPresent(bean -> assertEquals(this.transfer.decode(protobuf), bean));
    }

    @Test
    @DisplayName("PaymentServiceGRPCClient::getTransferByTransferIn throws exception given service throws exception")
    public void payment_service_get_transfer_by_transfer_in_throws_exception_given_service_throws_exception() {
        final var identifier = UUID.randomUUID();
        when(service.getTransferByTransferIn(any(GetTransferByTransferInRequest.class))).thenThrow(new RuntimeException("Service Exception"));
        assertThrows(RuntimeException.class, () -> client.getTransferByTransferIn(identifier));
        verify(service, times(1)).getTransferByTransferIn(any(GetTransferByTransferInRequest.class));
    }

    @Test
    @DisplayName("PaymentServiceGRPCClient::getTransfers invokes payment gRPC service and returns transfers")
    public void payment_service_get_transfers_invokes_grpc_service_and_returns_transfers() {
        final var identifier = UUID.randomUUID();
        final var account = UUID.randomUUID();
        final var transferDateStart = ZonedDateTime.now();
        final var transferDateEnd = ZonedDateTime.now();
        final var amount = new BigDecimal("100.00");
        final var description = "description";
        final var toAccount = UUID.randomUUID();
        final var debitIdentifier = UUID.randomUUID();
        final var creditIdentifier = UUID.randomUUID();
        final var state = AllocationState.create(amount);
        final var currency = new GRPCCurrencyDataAccessObject(458, "MYR", "Malaysian Ringgit", "MYR");
        final var country = new GRPCCountryDataAccessObject(458, "Malaysia", "MYR");

        final var transferOut = new GRPCTransferOutAccountAllocationDataAccessObject(
            debitIdentifier,
            account,
            AllocationType.TRANSFER_OUT,
            country,
            currency,
            amount,
            state.allocate(amount),
            AllocationDirection.OUT,
            ZonedDateTime.now(),
            ZonedDateTime.now(),
            ZonedDateTime.now(),
            identifier
        );

        final var transferIn = new GRPCTransferInAccountAllocationDataAccessObject(
            creditIdentifier,
            toAccount,
            AllocationType.TRANSFER_IN,
            country,
            currency,
            amount,
            state,
            AllocationDirection.IN,
            ZonedDateTime.now(),
            ZonedDateTime.now(),
            ZonedDateTime.now(),
            debitIdentifier
        );

        final var method = new GRPCPaymentMethodDataAccessObject(1, "BANK_TRANSFER", "Bank Transfer");
        final var reference = new GRPCReferenceDataAccessObject(UUID.randomUUID(), 1L, "MY", "CO", 2, ReferenceType.PAYMENT, "MYCOPYMT00000000012");
        final var payment = new GRPCPaymentDataAccessObject(UUID.randomUUID(), UUID.randomUUID(), method, currency, new BigDecimal("100.00"), ZonedDateTime.now(), PaymentStatus.ACTIVE, "description", country, reference,  ZonedDateTime.now());
        final var paymentAccountAllocation = new GRPCPaymentAccountAllocationDataAccessObject(
            identifier,
            account,
            AllocationType.PAYMENT,
            country,
            currency,
            new BigDecimal("100.00"),
            AllocationState.create(new BigDecimal("100.00")),
            AllocationDirection.IN,
            ZonedDateTime.now(),
            ZonedDateTime.now(),
            ZonedDateTime.now(),
            payment
        );

        final var transfer = new GRPCTransferDataAccessObject(identifier, transferIn, transferOut, currency, amount, description, country, paymentAccountAllocation, ZonedDateTime.now()).entity();
        final var expected = List.of(transfer);
        final var response = mock(GetTransfersResponse.class);
        when(service.getTransfers(any(GetTransfersRequest.class))).thenReturn(Futures.immediateFuture(response));
        when(response.getTransfersList()).thenReturn(expected.stream().map(this.transfer::encode).toList());

        final var result = assertDoesNotThrow(() -> client.getTransfers(account, transferDateStart, transferDateEnd, AllocationType.TRANSFER_IN));
        verify(service, times(1)).getTransfers(any(GetTransfersRequest.class));
        verify(response, times(1)).getTransfersList();
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("PaymentServiceGRPCClient::getTransfers throws exception given service throws exception")
    public void payment_service_get_transfers_throws_exception_given_service_throws_exception() {
        final var account = UUID.randomUUID();
        final var transferDateStart = ZonedDateTime.now();
        final var transferDateEnd = ZonedDateTime.now();
        when(service.getTransfers(any(GetTransfersRequest.class))).thenThrow(new RuntimeException("Service Exception"));
        assertThrows(RuntimeException.class, () -> client.getTransfers(account, transferDateStart, transferDateEnd, AllocationType.TRANSFER_IN));
        verify(service, times(1)).getTransfers(any(GetTransfersRequest.class));
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @DisplayName("PaymentServiceGRPCClient::getAccountAllocation invokes payment gRPC service and returns payment account allocation")
    public void payment_service_get_account_allocation_invokes_grpc_service_and_returns_payment_account_allocation(final boolean present) {
        final var identifier = UUID.randomUUID();
        final var paymentId = UUID.randomUUID();
        final var account = UUID.randomUUID();
        final var customer = UUID.randomUUID();
        final var amount = new BigDecimal("100.00");
        final var receipt = ZonedDateTime.now();
        final var description = "description";
        final var status = PaymentStatus.ACTIVE;
        final var method = new GRPCPaymentMethodDataAccessObject(1, "BANK_TRANSFER", "Bank Transfer");
        final var currency = new GRPCCurrencyDataAccessObject(458, "MYR", "Malaysian Ringgit", "MYR");
        final var country = new GRPCCountryDataAccessObject(458, "Malaysia", "MYR");
        final var reference = new GRPCReferenceDataAccessObject(UUID.randomUUID(), 1L, "MY", "CO", 2, ReferenceType.PAYMENT, "MYCOPYMT00000000012");
        final var payment = new GRPCPaymentDataAccessObject(paymentId, customer, method, currency, amount, receipt, status, description, country, reference, ZonedDateTime.now());
        final var paymentAccountAllocation = new GRPCPaymentAccountAllocationDataAccessObject(identifier, account, AllocationType.PAYMENT, country, currency, new BigDecimal("100.00"), AllocationState.create(new BigDecimal("100.00")), AllocationDirection.IN, ZonedDateTime.now(), ZonedDateTime.now(), ZonedDateTime.now(), payment).entity();

        final var response = mock(GetAccountAllocationResponse.class);

        final var protobuf = this.paymentAccountAllocation.encode(paymentAccountAllocation);

        when(service.getAccountAllocation(any(GetAccountAllocationRequest.class))).thenReturn(Futures.immediateFuture(response));
        when(response.hasAllocation()).thenReturn(present);
        if (present) {
            when(response.getAllocation()).thenReturn(protobuf);
        }

        final var result = assertDoesNotThrow(() -> client.getAccountAllocation(identifier));
        verify(service, times(1)).getAccountAllocation(any(GetAccountAllocationRequest.class));
        verify(response, times(1)).hasAllocation();
        verify(response, present ? times(1) : never()).getAllocation();
        assertEquals(present, result.isPresent());
        result.ifPresent(bean -> assertEquals(this.paymentAccountAllocation.decode(protobuf), bean));
    }

    @Test
    @DisplayName("PaymentServiceGRPCClient::getAccountAllocation throws exception given service throws exception")
    public void payment_service_get_account_allocation_throws_exception_given_service_throws_exception() {
        final var identifier = UUID.randomUUID();
        when(service.getAccountAllocation(any(GetAccountAllocationRequest.class))).thenThrow(new RuntimeException("Service Exception"));
        assertThrows(RuntimeException.class, () -> client.getAccountAllocation(identifier));
        verify(service, times(1)).getAccountAllocation(any(GetAccountAllocationRequest.class));
    }

    @Test
    @DisplayName("PaymentServiceGRPCClient::cancel invokes payment gRPC service and returns PaymentCancellation")
    public void payment_service_cancel_invokes_payment_gRPC_service_and_returns_payment_cancellation() {
        final var identifier = UUID.randomUUID();
        final var customer = UUID.randomUUID();
        final var methodId = 1;
        final var method = new GRPCPaymentMethodDataAccessObject(methodId, "BANK_TRANSFER", "Bank Transfer");
        final var currencyCode = "MYR";
        final var currency = new GRPCCurrencyDataAccessObject(458, currencyCode, "Malaysian Ringgit", "MYR");
        final var status = PaymentStatus.ACTIVE;
        final var amount = new BigDecimal(100);
        final var receipt = ZonedDateTime.now();
        final var description = "description";
        final var reason = "reason";
        final var cancellationReference = "MYCOPYMX00000000270";
        final var countryCode = "MY";
        final var country = new GRPCCountryDataAccessObject(458, "Malaysia", countryCode);
        final var reference = new GRPCReferenceDataAccessObject(UUID.randomUUID(), 1L, "MY", "CO", 2, ReferenceType.PAYMENT, "MYCOPYMT00000000012");
        final var payment = new GRPCPaymentDataAccessObject(identifier, customer, method, currency, amount, receipt, status, description, country, reference, ZonedDateTime.now()).entity();
        final var expected = new GRPCPaymentCancellationDataAccessObject(identifier, payment.data(), reason, cancellationReference, ZonedDateTime.now(), ZonedDateTime.now(), ZonedDateTime.now()).entity();
        final var response = mock(CancelPaymentResponse.class);

        when(service.cancelPayment(any(CancelPaymentRequest.class))).thenReturn(Futures.immediateFuture(response));
        when(response.getCancellation()).thenReturn(this.cancellation.encode(expected));

        final var result = assertDoesNotThrow(() -> client.cancel(identifier, reason));

        verify(service, times(1)).cancelPayment(any(CancelPaymentRequest.class));
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("PaymentServiceGRPCClient::cancel throws exception given service throws exception")
    public void payment_service_cancel_throws_exception_given_service_throws_exception() {
        final var identifier = UUID.randomUUID();
        final var reason = "reason";

        when(service.cancelPayment(any(CancelPaymentRequest.class))).thenThrow(new RuntimeException("Service Exception"));

        assertThrows(RuntimeException.class, () -> client.cancel(identifier, reason));
        verify(service, times(1)).cancelPayment(any(CancelPaymentRequest.class));
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @DisplayName("PaymentServiceGRPCClient::getPaymentCancellation invokes payment gRPC service and returns PaymentCancellation")
    public void payment_service_get_payment_cancellation_invokes_payment_gRPC_service_and_returns_payment_cancellation(final boolean present) {
        final var identifier = UUID.randomUUID();
        final var customer = UUID.randomUUID();
        final var methodId = 1;
        final var method = new GRPCPaymentMethodDataAccessObject(methodId, "BANK_TRANSFER", "Bank Transfer");
        final var currencyCode = "MYR";
        final var currency = new GRPCCurrencyDataAccessObject(458, currencyCode, "Malaysian Ringgit", "MYR");
        final var status = PaymentStatus.ACTIVE;
        final var amount = new BigDecimal(100);
        final var receipt = ZonedDateTime.now();
        final var description = "description";
        final var reason = "reason";
        final var cancellationReference = "MYCOPYMX00000000270";
        final var countryCode = "MY";
        final var country = new GRPCCountryDataAccessObject(458, "Malaysia", countryCode);
        final var reference = new GRPCReferenceDataAccessObject(UUID.randomUUID(), 1L, "MY", "CO", 2, ReferenceType.PAYMENT, "MYCOPYMT00000000012");
        final var payment = new GRPCPaymentDataAccessObject(identifier, customer, method, currency, amount, receipt, status, description, country, reference, ZonedDateTime.now()).entity();
        final var expected = new GRPCPaymentCancellationDataAccessObject(identifier, payment.data(), reason, cancellationReference, ZonedDateTime.now(), ZonedDateTime.now(), ZonedDateTime.now()).entity();
        final var response = mock(GetPaymentCancellationResponse.class);
        final var protobuf = this.cancellation.encode(expected);

        when(service.getPaymentCancellation(any(GetPaymentCancellationRequest.class))).thenReturn(Futures.immediateFuture(response));
        when(response.hasCancellation()).thenReturn(present);
        if (present) {
            when(response.getCancellation()).thenReturn(protobuf);
        }

        final var result = assertDoesNotThrow(() -> client.getPaymentCancellation(identifier));
        verify(service, times(1)).getPaymentCancellation(any(GetPaymentCancellationRequest.class));
        verify(response, times(1)).hasCancellation();
        verify(response, present ? times(1) : never()).getCancellation();
        assertEquals(present, result.isPresent());
        result.ifPresent(bean -> assertEquals(this.cancellation.decode(protobuf), bean));
    }

    @Test
    @DisplayName("PaymentServiceGRPCClient::getPaymentCancellation throws exception given service throws exception")
    public void payment_service_get_payment_cancellation_throws_exception_given_service_throws_exception() {
        final var identifier = UUID.randomUUID();

        when(service.getPaymentCancellation(any(GetPaymentCancellationRequest.class))).thenThrow(new RuntimeException("Service Exception"));

        assertThrows(RuntimeException.class, () -> client.getPaymentCancellation(identifier));
        verify(service, times(1)).getPaymentCancellation(any(GetPaymentCancellationRequest.class));
    }

    @Test
    @DisplayName("PaymentServiceGRPCClient::getInvoiceAllocationsByInvoice invokes payment gRPC service and returns payment invoice allocations")
    public void payment_service_get_invoice_allocations_by_invoice_invoices_payment_gRPC_service_and_returns_payment_invoice_allocations() {
        final var identifier = UUID.randomUUID();
        final var response = mock(GetInvoiceAllocationsByInvoiceResponse.class);

        // Payment setup
        final var customer = UUID.randomUUID();
        final var methodId = 1;
        final var method = new GRPCPaymentMethodDataAccessObject(methodId, "BANK_TRANSFER", "Bank Transfer");
        final var currencyCode = "MYR";
        final var currency = new GRPCCurrencyDataAccessObject(458, currencyCode, "Malaysian Ringgit", "MYR");
        final var status = PaymentStatus.ACTIVE;
        final var amount = new BigDecimal(100);
        final var receipt = ZonedDateTime.now();
        final var description = "description";
        final var countryCode = "MY";
        final var country = new GRPCCountryDataAccessObject(458, "Malaysia", countryCode);
        final var reference = new GRPCReferenceDataAccessObject(UUID.randomUUID(), 1L, "MY", "CO", 2, ReferenceType.PAYMENT, "MYCOPYMT00000000012");
        final var payment = new GRPCPaymentDataAccessObject(identifier, customer, method, currency, amount, receipt, status, description, country, reference, ZonedDateTime.now()).entity();

        // Invoice Allocation DTO Setup
        final var allocation = new GRPCInvoiceAllocationResponseDTO(identifier, "MYCO00000000013", amount, ZonedDateTime.now());

        // Payment Invoice Allocation DTO setup
        final var invoiceAllocation = new GRPCPaymentInvoiceAllocationDTO(payment, allocation);

        final var expected = List.of(invoiceAllocation);
        when(response.getPaymentInvoiceAllocationsList()).thenReturn(expected.stream().map(this.paymentInvoiceAllocation::encode).toList());
        when(service.getInvoiceAllocationsByInvoice(any(GetInvoiceAllocationsByInvoiceRequest.class))).thenReturn(Futures.immediateFuture(response));

        final var result = assertDoesNotThrow(() -> client.getInvoiceAllocationsByInvoice(identifier));
        verify(service, times(1)).getInvoiceAllocationsByInvoice(any(GetInvoiceAllocationsByInvoiceRequest.class));
        verify(response, times(1)).getPaymentInvoiceAllocationsList();
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("PaymentServiceGRPCClient::getInvoiceAllocationsByInvoice throws exception given service throws exception")
    public void payment_service_get_invoice_allocations_by_invoice_throws_exception() {
        final var identifier = UUID.randomUUID();

        when(service.getInvoiceAllocationsByInvoice(any(GetInvoiceAllocationsByInvoiceRequest.class))).thenThrow(new RuntimeException("Service Exception"));

        assertThrows(RuntimeException.class, () -> client.getInvoiceAllocationsByInvoice(identifier));
        verify(service, times(1)).getInvoiceAllocationsByInvoice(any(GetInvoiceAllocationsByInvoiceRequest.class));
    }
}