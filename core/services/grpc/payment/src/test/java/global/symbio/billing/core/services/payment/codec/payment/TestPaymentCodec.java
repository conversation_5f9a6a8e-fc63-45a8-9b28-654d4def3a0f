package global.symbio.billing.core.services.payment.codec.payment;

import global.symbio.billing.core.country.persistence.api.Country;
import global.symbio.billing.core.currency.persistence.api.Currency;
import global.symbio.billing.core.payment.persistence.api.payment.Payment;
import global.symbio.billing.core.payment.persistence.api.payment.PaymentStatus;
import global.symbio.billing.core.payment.persistence.api.payment.method.PaymentMethod;
import global.symbio.billing.core.reference.persistence.api.reference.Reference;
import global.symbio.billing.core.reference.persistence.api.reference.ReferenceType;
import global.symbio.billing.core.services.codec.uuid.UUIDCodec;
import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.codec.country.CountryCodec;
import global.symbio.billing.core.services.grpc.codec.country.GRPCCountryDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.currency.CurrencyCodec;
import global.symbio.billing.core.services.grpc.codec.currency.GRPCCurrencyDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.decimal.BigDecimalCodec;
import global.symbio.billing.core.services.grpc.codec.payment.method.GRPCPaymentMethodDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.payment.method.PaymentMethodCodec;
import global.symbio.billing.core.services.grpc.codec.reference.GRPCReferenceDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.reference.ReferenceCodec;
import global.symbio.billing.core.services.grpc.codec.reference.ReferenceTypeCodec;
import global.symbio.billing.core.services.grpc.codec.temporal.*;
import global.symbio.billing.core.services.grpc.message.*;
import global.symbio.billing.core.services.grpc.payment.entities.PaymentMessage;
import global.symbio.billing.core.services.grpc.payment.entities.PaymentStatusMessage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

public class TestPaymentCodec {
    private Codec<UUID, UUIDMessage> uuid;

    private Codec<BigDecimal, BigDecimalMessage> decimal;

    private Codec<Currency, CurrencyMessage> currency;

    private Codec<PaymentMethod, PaymentMethodMessage> method;

    private Codec<PaymentStatus, PaymentStatusMessage> status;

    private Codec<ZonedDateTime, ZonedDateTimeMessage> timestamp;

    private Codec<Country, CountryMessage> country;

    private Codec<Reference, ReferenceMessage> reference;

    private Codec<ReferenceType, ReferenceTypeMessage> type;

    private Codec<Payment, PaymentMessage> codec;

    @BeforeEach
    public void setup() {
        final var date = new LocalDateCodec();
        final var time = new LocalTimeCodec();
        final var datetime = new LocalDateTimeCodec(date, time);
        final var offset = new ZoneOffsetCodec();
        final var zone = new ZoneIdCodec();

        uuid = new UUIDCodec();
        decimal = new BigDecimalCodec();
        currency = new CurrencyCodec();
        method = new PaymentMethodCodec();
        status = new PaymentStatusCodec();
        country = new CountryCodec();
        type = new ReferenceTypeCodec();
        reference = new ReferenceCodec(uuid, type);
        timestamp = new ZonedDateTimeCodec(datetime, offset, zone);
        codec = new PaymentCodec(uuid, decimal, currency, country, timestamp, method, status, reference);
    }

    @Test
    @DisplayName("PaymentCodec::new rejects null constructor arguments")
    public void payment_codec_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new PaymentCodec(null, decimal, currency, country, timestamp, method, status, reference));
        assertThrows(NullPointerException.class, () -> new PaymentCodec(uuid, null, currency, country, timestamp, method, status, reference));
        assertThrows(NullPointerException.class, () -> new PaymentCodec(uuid, decimal, null, country, timestamp, method, status, reference));
        assertThrows(NullPointerException.class, () -> new PaymentCodec(uuid, decimal, currency, null, timestamp, method, status, reference));
        assertThrows(NullPointerException.class, () -> new PaymentCodec(uuid, decimal, currency, country, null, method, status, reference));
        assertThrows(NullPointerException.class, () -> new PaymentCodec(uuid, decimal, currency, country, timestamp, null, status, reference));
        assertThrows(NullPointerException.class, () -> new PaymentCodec(uuid, decimal, currency, country, timestamp, method, null, reference));
        assertThrows(NullPointerException.class, () -> new PaymentCodec(uuid, decimal, currency, country, timestamp, method, status, null));
    }

    @Test
    @DisplayName("PaymentCodec::encode bean to protobuf")
    public void codec_bean_to_protobuf() {
        final var bean = new GRPCPaymentDataAccessObject(
            UUID.randomUUID(),
            UUID.randomUUID(),
            new GRPCPaymentMethodDataAccessObject(1, "BANK_TRANSFER", "Bank Transfer"),
            new GRPCCurrencyDataAccessObject(458, "MYR", "Malaysian Ringgit", "MYR"),
            new BigDecimal("100.00"),
            ZonedDateTime.now(),
            PaymentStatus.ACTIVE,
            "description",
            new GRPCCountryDataAccessObject(458, "Malaysia", "MY"),
            new GRPCReferenceDataAccessObject(UUID.randomUUID(), 1L, "MY", "CO", 2, ReferenceType.INVOICE, "MYCO00000000012"),
            ZonedDateTime.now()
        ).entity();
        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));

        assertEquals(bean.getIdentifier(), uuid.decode(protobuf.getIdentifier()));
        assertEquals(bean.getBusiness(), uuid.decode(protobuf.getBusiness()));
        assertEquals(bean.getMethod(), method.decode(protobuf.getMethod()));
        assertEquals(bean.getCurrency(), currency.decode(protobuf.getCurrency()));
        assertEquals(bean.getAmount(), decimal.decode(protobuf.getAmount()));
        assertEquals(bean.getReceiptDate(), timestamp.decode(protobuf.getReceiptDate()));
        assertEquals(bean.getStatus(), status.decode(protobuf.getStatus()));
        assertEquals(bean.getDescription(), protobuf.getDescription());
        assertEquals(bean.getCountry(), country.decode(protobuf.getCountry()));
        assertEquals(bean.getTimestamp(), timestamp.decode(protobuf.getTimestamp()));}

    @Test
    @DisplayName("PaymentCodec::decode protobuf to bean")
    public void codec_protobuf_to_bean() {
        final var payment = new GRPCPaymentDataAccessObject(
            UUID.randomUUID(),
            UUID.randomUUID(),
            new GRPCPaymentMethodDataAccessObject(1, "BANK_TRANSFER", "Bank Transfer"),
            new GRPCCurrencyDataAccessObject(458, "MYR", "Malaysian Ringgit", "MYR"),
            new BigDecimal("100.00"),
            ZonedDateTime.now(),
            PaymentStatus.ACTIVE,
            "description",
            new GRPCCountryDataAccessObject(458, "Malaysia", "MY"),
            new GRPCReferenceDataAccessObject(UUID.randomUUID(), 1L, "MY", "CO", 2, ReferenceType.INVOICE, "MYCO00000000012"),
            ZonedDateTime.now()
        ).entity();
        final var protobuf = PaymentMessage.newBuilder()
            .setIdentifier(uuid.encode(payment.getIdentifier()))
            .setBusiness(uuid.encode(payment.getBusiness()))
            .setMethod(method.encode(payment.getMethod()))
            .setCurrency(currency.encode(payment.getCurrency()))
            .setAmount(decimal.encode(payment.getAmount()))
            .setReceiptDate(timestamp.encode(payment.getReceiptDate()))
            .setStatus(status.encode(payment.getStatus()))
            .setDescription(payment.getDescription())
            .setCountry(country.encode(payment.getCountry()))
            .setReference(reference.encode(payment.getReference()))
            .setTimestamp(timestamp.encode(payment.getTimestamp()))
            .build();

        final var bean = assertDoesNotThrow(() -> codec.decode(protobuf));
        assertEquals(payment, bean);
    }
}
