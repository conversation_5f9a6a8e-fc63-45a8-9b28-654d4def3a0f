package global.symbio.billing.core.services.payment.codec.payment;

import global.symbio.billing.core.country.persistence.api.CountryDataAccessObject;
import global.symbio.billing.core.currency.persistence.api.CurrencyDataAccessObject;
import global.symbio.billing.core.exception.sdk.BillingEntityUnmodifiableException;
import global.symbio.billing.core.payment.persistence.api.payment.Payment;
import global.symbio.billing.core.payment.persistence.api.payment.PaymentStatus;
import global.symbio.billing.core.payment.persistence.api.payment.method.PaymentMethodDataAccessObject;
import global.symbio.billing.core.reference.persistence.api.reference.ReferenceDataAccessObject;
import global.symbio.billing.core.reference.persistence.api.reference.ReferenceType;
import global.symbio.billing.core.services.grpc.codec.country.GRPCCountryDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.currency.GRPCCurrencyDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.payment.method.GRPCPaymentMethodDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.reference.GRPCReferenceDataAccessObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

public class TestGRPCPaymentDataAccessObject {

    private static final UUID ID = UUID.randomUUID();
    private static final UUID BUSINESS = UUID.randomUUID();
    private static final PaymentMethodDataAccessObject METHOD = new GRPCPaymentMethodDataAccessObject(1, "BANK_TRANSFER", "Bank Transfer");
    private static final CurrencyDataAccessObject CURRENCY = new GRPCCurrencyDataAccessObject(458, "MYR", "Malaysian Ringgit", "MYR");
    private static final BigDecimal AMOUNT = new BigDecimal("100.00");
    private static final ZonedDateTime RECEIPT_DATE = ZonedDateTime.now();
    private static final PaymentStatus STATUS = PaymentStatus.ACTIVE;
    private static final CountryDataAccessObject COUNTRY = new GRPCCountryDataAccessObject(458, "Malaysia", "MYR");
    private static final ZonedDateTime TIMESTAMP = ZonedDateTime.now();
    private static final String DESCRIPTION = "description";
    private static final ReferenceDataAccessObject REFERENCE = new GRPCReferenceDataAccessObject(UUID.randomUUID(), 1L, "MY", "CO", 2, ReferenceType.INVOICE, "MYCO00000000012");

    private GRPCPaymentDataAccessObject payment;

    @BeforeEach
    public void setup() {
        payment = new GRPCPaymentDataAccessObject(ID, BUSINESS, METHOD, CURRENCY, AMOUNT, RECEIPT_DATE, STATUS, DESCRIPTION, COUNTRY, REFERENCE, TIMESTAMP);
    }

    @Test
    @DisplayName("GRPCPaymentDataAccessObject::new rejects null constructor arguments")
    public void grpc_payment_data_access_object_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new GRPCPaymentDataAccessObject(null, BUSINESS, METHOD, CURRENCY, AMOUNT, RECEIPT_DATE, STATUS, DESCRIPTION, COUNTRY, REFERENCE, TIMESTAMP));
        assertThrows(NullPointerException.class, () -> new GRPCPaymentDataAccessObject(ID, null, METHOD, CURRENCY, AMOUNT, RECEIPT_DATE, STATUS, DESCRIPTION, COUNTRY, REFERENCE, TIMESTAMP));
        assertThrows(NullPointerException.class, () -> new GRPCPaymentDataAccessObject(ID, BUSINESS, null, CURRENCY, AMOUNT, RECEIPT_DATE, STATUS, DESCRIPTION, COUNTRY, REFERENCE, TIMESTAMP));
        assertThrows(NullPointerException.class, () -> new GRPCPaymentDataAccessObject(ID, BUSINESS, METHOD, null, AMOUNT, RECEIPT_DATE, STATUS, DESCRIPTION, COUNTRY, REFERENCE, TIMESTAMP));
        assertThrows(NullPointerException.class, () -> new GRPCPaymentDataAccessObject(ID, BUSINESS, METHOD, CURRENCY, null, RECEIPT_DATE, STATUS, DESCRIPTION, COUNTRY, REFERENCE, TIMESTAMP));
        assertThrows(NullPointerException.class, () -> new GRPCPaymentDataAccessObject(ID, BUSINESS, METHOD, CURRENCY, AMOUNT, null, STATUS, DESCRIPTION, COUNTRY, REFERENCE, TIMESTAMP));
        assertThrows(NullPointerException.class, () -> new GRPCPaymentDataAccessObject(ID, BUSINESS, METHOD, CURRENCY, AMOUNT, RECEIPT_DATE, null, DESCRIPTION, COUNTRY, REFERENCE, TIMESTAMP));
        assertDoesNotThrow(() -> new GRPCPaymentDataAccessObject(ID, BUSINESS, METHOD, CURRENCY, AMOUNT, RECEIPT_DATE, STATUS, null, COUNTRY, REFERENCE, TIMESTAMP));
        assertThrows(NullPointerException.class, () -> new GRPCPaymentDataAccessObject(ID, BUSINESS, METHOD, CURRENCY, AMOUNT, RECEIPT_DATE, STATUS, DESCRIPTION, null, REFERENCE, TIMESTAMP));
        assertDoesNotThrow(() -> new GRPCPaymentDataAccessObject(ID, BUSINESS, METHOD, CURRENCY, AMOUNT, RECEIPT_DATE, STATUS, DESCRIPTION, COUNTRY, null, TIMESTAMP));
        assertThrows(NullPointerException.class, () -> new GRPCPaymentDataAccessObject(ID, BUSINESS, METHOD, CURRENCY, AMOUNT, RECEIPT_DATE, STATUS, DESCRIPTION, COUNTRY, REFERENCE, null));
    }

    @Test
    @DisplayName("GRPCPaymentDataAccessObject::identifier is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_payment_data_access_object_identifier_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(Payment.class, ID, "identifier");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> payment.identifier(ID));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCPaymentDataAccessObject::business is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_payment_data_access_object_business_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(Payment.class, ID, "business");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> payment.business(BUSINESS));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCPaymentDataAccessObject::method is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_payment_data_access_object_method_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(Payment.class, ID, "method");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> payment.method(METHOD));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCPaymentDataAccessObject::currency is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_payment_data_access_object_currency_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(Payment.class, ID, "currency");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> payment.currency(CURRENCY));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCPaymentDataAccessObject::amount is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_payment_data_access_object_amount_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(Payment.class, ID, "amount");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> payment.amount(AMOUNT));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCPaymentDataAccessObject::receiptDate is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_payment_data_access_object_receipt_date_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(Payment.class, ID, "receiptDate");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> payment.receiptDate(RECEIPT_DATE));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCPaymentDataAccessObject::status is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_payment_data_access_object_status_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(Payment.class, ID, "status");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> payment.status(STATUS));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCPaymentDataAccessObject::description is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_payment_data_access_object_description_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(Payment.class, ID, "description");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> payment.description(DESCRIPTION));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCPaymentDataAccessObject::country is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_payment_data_access_object_country_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(Payment.class, ID, "country");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> payment.country(COUNTRY));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCPaymentDataAccessObject::reference is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_payment_data_access_object_reference_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(Payment.class, ID, "reference");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> payment.reference(REFERENCE));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCPaymentDataAccessObject::timestamp is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_payment_data_access_object_timestamp_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(Payment.class, ID, "timestamp");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> payment.timestamp(TIMESTAMP));
        assertEquals(expected.getMessage(), cause.getMessage());
    }
}
