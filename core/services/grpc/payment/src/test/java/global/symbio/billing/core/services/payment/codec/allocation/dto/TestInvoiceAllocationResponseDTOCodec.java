package global.symbio.billing.core.services.payment.codec.allocation.dto;

import global.symbio.billing.core.services.codec.uuid.UUIDCodec;
import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.codec.decimal.BigDecimalCodec;
import global.symbio.billing.core.services.grpc.codec.temporal.*;
import global.symbio.billing.core.services.grpc.message.*;
import global.symbio.billing.core.services.grpc.payment.dto.InvoiceAllocationResponseMessage;
import global.symbio.billing.core.services.payment.codec.allocation.dto.impl.GRPCInvoiceAllocationResponseDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.*;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

public class TestInvoiceAllocationResponseDTOCodec {

    private Codec<UUID, UUIDMessage> uuid;

    private Codec<BigDecimal, BigDecimalMessage> decimal;

    private Codec<LocalDate, LocalDateMessage> date;

    private Codec<LocalTime, LocalTimeMessage> time;

    private Codec<LocalDateTime, LocalDateTimeMessage> datetime;

    private Codec<ZoneOffset, ZoneOffsetMessage> offset;

    private Codec<ZoneId, ZoneIdMessage> zone;

    private Codec<ZonedDateTime, ZonedDateTimeMessage> timestamp;

    private Codec<InvoiceAllocationResponseDTO, InvoiceAllocationResponseMessage> codec;

    @BeforeEach
    public void setup() {
        uuid = new UUIDCodec();
        decimal = new BigDecimalCodec();
        date = new LocalDateCodec();
        time = new LocalTimeCodec();
        datetime = new LocalDateTimeCodec(date, time);
        offset = new ZoneOffsetCodec();
        zone = new ZoneIdCodec();
        timestamp = new ZonedDateTimeCodec(datetime, offset, zone);
        codec = new InvoiceAllocationResponseDTOCodec(uuid, decimal, timestamp);
    }

    @Test
    @DisplayName("InvoiceAllocationResponseDTOCodec::new rejects null constructor arguments")
    public void invoice_allocation_dto_codec_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new InvoiceAllocationResponseDTOCodec(null, decimal, timestamp));
        assertThrows(NullPointerException.class, () -> new InvoiceAllocationResponseDTOCodec(uuid, null, timestamp));
        assertThrows(NullPointerException.class, () -> new InvoiceAllocationResponseDTOCodec(uuid, decimal, null));
    }

    @Test
    @DisplayName("InvoiceAllocationResponseDTOCodec::encode bean to protobuf")
    public void codec_bean_to_protobuf() {
        final var invoiceUUID = UUID.randomUUID();
        final var reference = "MYCO00000008191";
        final var amount = BigDecimal.TEN;
        final var lastUpdated = ZonedDateTime.now();
        final var bean = new GRPCInvoiceAllocationResponseDTO(invoiceUUID, reference, amount, lastUpdated);

        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));
        assertEquals(bean.invoice(), uuid.decode(protobuf.getInvoice()));
        assertEquals(bean.amount(), decimal.decode(protobuf.getAmount()));
        assertEquals(bean.lastUpdated(), timestamp.decode(protobuf.getLastUpdated()));
    }

    @Test
    @DisplayName("InvoiceAllocationDTOCodec::encode protobuf to bean")
    public void codec_protobuf_to_bean() {
        final var invoiceUUID = UUID.randomUUID();
        final var amount = BigDecimal.TEN;
        final var lastUpdated = ZonedDateTime.now();

        final var protobuf = InvoiceAllocationResponseMessage.newBuilder()
            .setInvoice(uuid.encode(invoiceUUID))
            .setAmount(decimal.encode(amount))
            .setLastUpdated(timestamp.encode(lastUpdated))
            .build();

        final var bean = assertDoesNotThrow(() -> codec.decode(protobuf));
        assertEquals(protobuf, codec.encode(bean));
    }

}
