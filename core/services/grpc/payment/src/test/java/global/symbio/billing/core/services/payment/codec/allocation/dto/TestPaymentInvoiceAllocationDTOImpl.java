package global.symbio.billing.core.services.payment.codec.allocation.dto;

import global.symbio.billing.core.payment.persistence.api.payment.Payment;
import global.symbio.billing.core.services.payment.codec.allocation.dto.impl.PaymentInvoiceAllocationDTOImpl;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.mock;

public class TestPaymentInvoiceAllocationDTOImpl {

    @Test
    @DisplayName("TestPaymentInvoiceAllocationDTOImpl::new rejects null constructor arguments")
    public void payment_invoice_allocation_dto_impl_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new PaymentInvoiceAllocationDTOImpl(null, mock(InvoiceAllocationResponseDTO.class)));
        assertThrows(NullPointerException.class, () -> new PaymentInvoiceAllocationDTOImpl(mock(Payment.class), null));
    }
}
