package global.symbio.billing.core.services.payment.codec.allocation;

import global.symbio.billing.core.country.persistence.api.Country;
import global.symbio.billing.core.currency.persistence.api.Currency;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AllocationDirection;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AllocationType;
import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.payment.PaymentAccountAllocation;
import global.symbio.billing.core.payment.persistence.api.allocation.state.AllocationState;
import global.symbio.billing.core.payment.persistence.api.payment.Payment;
import global.symbio.billing.core.payment.persistence.api.payment.PaymentStatus;
import global.symbio.billing.core.payment.persistence.api.payment.method.PaymentMethod;
import global.symbio.billing.core.reference.persistence.api.reference.Reference;
import global.symbio.billing.core.reference.persistence.api.reference.ReferenceType;
import global.symbio.billing.core.services.codec.uuid.UUIDCodec;
import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.codec.country.CountryCodec;
import global.symbio.billing.core.services.grpc.codec.country.GRPCCountryDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.currency.CurrencyCodec;
import global.symbio.billing.core.services.grpc.codec.currency.GRPCCurrencyDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.decimal.BigDecimalCodec;
import global.symbio.billing.core.services.grpc.codec.payment.method.GRPCPaymentMethodDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.payment.method.PaymentMethodCodec;
import global.symbio.billing.core.services.grpc.codec.reference.GRPCReferenceDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.reference.ReferenceCodec;
import global.symbio.billing.core.services.grpc.codec.reference.ReferenceTypeCodec;
import global.symbio.billing.core.services.grpc.codec.temporal.*;
import global.symbio.billing.core.services.grpc.message.*;
import global.symbio.billing.core.services.grpc.payment.AccountInvoiceAllocationResponseMessage;
import global.symbio.billing.core.services.grpc.payment.dto.InvoiceAllocationMessage;
import global.symbio.billing.core.services.grpc.payment.entities.*;
import global.symbio.billing.core.services.payment.codec.allocation.dto.*;
import global.symbio.billing.core.services.payment.codec.allocation.dto.impl.GRPCAccountInvoiceAllocationResponse;
import global.symbio.billing.core.services.payment.codec.allocation.dto.impl.GRPCInvoiceAllocationDTO;
import global.symbio.billing.core.services.payment.codec.allocation.state.AllocationStateCodec;
import global.symbio.billing.core.services.payment.codec.payment.GRPCPaymentDataAccessObject;
import global.symbio.billing.core.services.payment.codec.payment.PaymentCodec;
import global.symbio.billing.core.services.payment.codec.payment.PaymentStatusCodec;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.*;
import java.util.Set;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

public class TestAccountInvoiceAllocationResponseCodec {

    private Codec<UUID, UUIDMessage> uuid;

    private Codec<BigDecimal, BigDecimalMessage> decimal;

    private Codec<AllocationType, AllocationTypeMessage> type;

    private Codec<AllocationState, AllocationStateMessage> state;

    private Codec<AllocationDirection, AllocationDirectionMessage> direction;

    private Codec<LocalDate, LocalDateMessage> date;

    private Codec<LocalTime, LocalTimeMessage> time;

    private Codec<LocalDateTime, LocalDateTimeMessage> datetime;

    private Codec<ZoneOffset, ZoneOffsetMessage> offset;

    private Codec<ZoneId, ZoneIdMessage> zone;

    private Codec<ZonedDateTime, ZonedDateTimeMessage> timestamp;

    private Codec<Payment, PaymentMessage> payment;

    private Codec<Currency, CurrencyMessage> currency;

    private Codec<Country, CountryMessage> country;

    private Codec<PaymentMethod, PaymentMethodMessage> method;

    private Codec<PaymentStatus, PaymentStatusMessage> status;

    private Codec<ReferenceType, ReferenceTypeMessage> referenceType;

    private Codec<Reference, ReferenceMessage> reference;

    private Codec<PaymentAccountAllocation<?>, AccountAllocationMessageApi> paymentAccountAllocation;

    private Codec<InvoiceAllocationDTO, InvoiceAllocationMessage> invoice;

    private Codec<AccountInvoiceAllocationResponse, AccountInvoiceAllocationResponseMessage> codec;

    @BeforeEach
    public void setup() {
        uuid = new UUIDCodec();
        decimal = new BigDecimalCodec();
        type = new AllocationTypeCodec();
        state = new AllocationStateCodec(decimal);
        direction = new AllocationDirectionCodec();
        date = new LocalDateCodec();
        time = new LocalTimeCodec();
        datetime = new LocalDateTimeCodec(date, time);
        offset = new ZoneOffsetCodec();
        zone = new ZoneIdCodec();
        timestamp = new ZonedDateTimeCodec(datetime, offset, zone);
        currency = new CurrencyCodec();
        country = new CountryCodec();
        method = new PaymentMethodCodec();
        status = new PaymentStatusCodec();
        referenceType = new ReferenceTypeCodec();
        reference = new ReferenceCodec(uuid, referenceType);
        payment = new PaymentCodec(uuid, decimal, currency, country, timestamp, method, status, reference);
        paymentAccountAllocation = new PaymentAccountAllocationCodec(uuid, decimal, type, state, direction, currency, country, timestamp, payment);
        invoice = new InvoiceAllocationDTOCodec(uuid, decimal);
        codec = new AccountInvoiceAllocationResponseCodec(paymentAccountAllocation, invoice);
    }

    @Test
    @DisplayName("AccountInvoiceAllocationResponseCodec::new rejects null constructor arguments")
    public void account_invoice_allocation_response_codec_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new AccountInvoiceAllocationResponseCodec(null, invoice));
        assertThrows(NullPointerException.class, () -> new AccountInvoiceAllocationResponseCodec(paymentAccountAllocation, null));
    }

    @Test
    @DisplayName("AccountInvoiceAllocationResponseCodec::encode bean to protobuf")
    public void codec_bean_to_protobuf(){
        final var bean = generateAccountInvoiceAllocationResponse();

        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));
        assertEquals(bean.accountAllocation(), paymentAccountAllocation.decode(protobuf.getAccountAllocation()));
        assertEquals(bean.invoiceAllocations().stream().toList(), protobuf.getInvoiceAllocationsList().stream().map(invoice::decode).toList());
    }

    @Test
    @DisplayName("AccountInvoiceAllocationResponseCodec::decode protobuf to bean")
    public void codec_protobuf_to_bean(
    ) {
        final var response = generateAccountInvoiceAllocationResponse();

        final var protobuf = AccountInvoiceAllocationResponseMessage.newBuilder()
            .setAccountAllocation(paymentAccountAllocation.encode(response.accountAllocation()))
            .addAllInvoiceAllocations(response.invoiceAllocations().stream().map(invoice::encode).toList())
            .build();

        final var bean = assertDoesNotThrow(() -> codec.decode(protobuf));
        assertEquals(response, bean);
    }

    public AccountInvoiceAllocationResponse generateAccountInvoiceAllocationResponse() {
        final var currency = new GRPCCurrencyDataAccessObject(458, "MYR", "Malaysian Ringgit", "MYR");
        final var country = new GRPCCountryDataAccessObject(458, "Malaysia", "MY");
        return new GRPCAccountInvoiceAllocationResponse(
            new GRPCPaymentAccountAllocationDataAccessObject(
                UUID.randomUUID(),
                UUID.randomUUID(),
                AllocationType.PAYMENT,
                country,
                currency,
                BigDecimal.TEN,
                AllocationState.create(BigDecimal.TEN),
                AllocationDirection.IN,
                null,
                null,
                ZonedDateTime.now(),
                new GRPCPaymentDataAccessObject(
                    UUID.randomUUID(),
                    UUID.randomUUID(),
                    new GRPCPaymentMethodDataAccessObject(1, "BANK_TRANSFER", "Bank Transfer"),
                    new GRPCCurrencyDataAccessObject(458, "MYR", "Malaysian Ringgit", "MYR"),
                    new BigDecimal("100.00"),
                    ZonedDateTime.now(),
                    PaymentStatus.ACTIVE,
                    "description",
                    new GRPCCountryDataAccessObject(458, "Malaysia", "MY"),
                    new GRPCReferenceDataAccessObject(UUID.randomUUID(), 1L, "MY", "CO", 2, ReferenceType.INVOICE, "MYCO00000000012"),
                    ZonedDateTime.now()
                )
            ).entity(), Set.of(new GRPCInvoiceAllocationDTO(UUID.randomUUID(), "MYCO00000008191", BigDecimal.TEN)));
    }
}
