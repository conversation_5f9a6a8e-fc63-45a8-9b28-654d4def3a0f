
package global.symbio.billing.core.services.payment.codec.allocation;

import global.symbio.billing.core.country.persistence.api.Country;
import global.symbio.billing.core.currency.persistence.api.Currency;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AllocationDirection;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AllocationType;
import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.payment.PaymentAccountAllocation;
import global.symbio.billing.core.payment.persistence.api.allocation.state.AllocationState;
import global.symbio.billing.core.payment.persistence.api.payment.Payment;
import global.symbio.billing.core.payment.persistence.api.payment.PaymentStatus;
import global.symbio.billing.core.payment.persistence.api.payment.method.PaymentMethod;
import global.symbio.billing.core.reference.persistence.api.reference.Reference;
import global.symbio.billing.core.reference.persistence.api.reference.ReferenceType;
import global.symbio.billing.core.services.codec.uuid.UUIDCodec;
import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.codec.country.CountryCodec;
import global.symbio.billing.core.services.grpc.codec.country.GRPCCountryDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.currency.CurrencyCodec;
import global.symbio.billing.core.services.grpc.codec.currency.GRPCCurrencyDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.decimal.BigDecimalCodec;
import global.symbio.billing.core.services.grpc.codec.payment.method.GRPCPaymentMethodDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.payment.method.PaymentMethodCodec;
import global.symbio.billing.core.services.grpc.codec.reference.GRPCReferenceDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.reference.ReferenceCodec;
import global.symbio.billing.core.services.grpc.codec.reference.ReferenceTypeCodec;
import global.symbio.billing.core.services.grpc.codec.temporal.*;
import global.symbio.billing.core.services.grpc.message.*;
import global.symbio.billing.core.services.grpc.payment.entities.*;
import global.symbio.billing.core.services.payment.codec.allocation.dto.AllocationDirectionCodec;
import global.symbio.billing.core.services.payment.codec.allocation.dto.AllocationTypeCodec;
import global.symbio.billing.core.services.payment.codec.allocation.state.AllocationStateCodec;
import global.symbio.billing.core.services.payment.codec.payment.GRPCPaymentDataAccessObject;
import global.symbio.billing.core.services.payment.codec.payment.PaymentCodec;
import global.symbio.billing.core.services.payment.codec.payment.PaymentStatusCodec;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.math.BigDecimal;
import java.time.*;
import java.util.UUID;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;

public class TestPaymentAccountAllocationCodec {

    private Codec<UUID, UUIDMessage> uuid;

    private Codec<BigDecimal, BigDecimalMessage> decimal;

    private Codec<AllocationType, AllocationTypeMessage> type;

    private Codec<AllocationState, AllocationStateMessage> state;

    private Codec<AllocationDirection, AllocationDirectionMessage> direction;

    private Codec<ZonedDateTime, ZonedDateTimeMessage> timestamp;

    private Codec<LocalDate, LocalDateMessage> date;

    private Codec<LocalTime, LocalTimeMessage> time;

    private Codec<LocalDateTime, LocalDateTimeMessage> datetime;

    private Codec<ZoneOffset, ZoneOffsetMessage> offset;

    private Codec<ZoneId, ZoneIdMessage> zone;

    private Codec<Payment, PaymentMessage> payment;

    private Codec<Currency, CurrencyMessage> currency;

    private Codec<PaymentMethod, PaymentMethodMessage> method;

    private Codec<PaymentStatus, PaymentStatusMessage> status;

    private Codec<Country, CountryMessage> country;

    private Codec<Reference, ReferenceMessage> reference;

    private Codec<ReferenceType, ReferenceTypeMessage> referenceType;

    private Codec<PaymentAccountAllocation<?>, AccountAllocationMessageApi> codec;

    @BeforeEach
    public void setup() {
        uuid = new UUIDCodec();
        decimal = new BigDecimalCodec();
        type = new AllocationTypeCodec();
        state = new AllocationStateCodec(decimal);
        direction = new AllocationDirectionCodec();
        date = new LocalDateCodec();
        time = new LocalTimeCodec();
        datetime = new LocalDateTimeCodec(date, time);
        offset = new ZoneOffsetCodec();
        zone = new ZoneIdCodec();
        timestamp = new ZonedDateTimeCodec(datetime, offset, zone);
        currency = new CurrencyCodec();
        method = new PaymentMethodCodec();
        status = new PaymentStatusCodec();
        country = new CountryCodec();
        referenceType = new ReferenceTypeCodec();
        reference = new ReferenceCodec(uuid, referenceType);
        payment = new PaymentCodec(uuid, decimal, currency, country, timestamp, method, status, reference);
        codec = new PaymentAccountAllocationCodec(uuid, decimal, type, state, direction, currency, country, timestamp, payment);
    }

    @Test
    @DisplayName("PaymentAccountAllocationCodec::new rejects null constructor arguments")
    public void account_allocation_api_codec_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new PaymentAccountAllocationCodec(null, decimal, type, state, direction, currency, country, timestamp, payment));
        assertThrows(NullPointerException.class, () -> new PaymentAccountAllocationCodec(uuid, null, type, state, direction, currency, country, timestamp, payment));
        assertThrows(NullPointerException.class, () -> new PaymentAccountAllocationCodec(uuid, decimal, null, state, direction, currency, country, timestamp, payment));
        assertThrows(NullPointerException.class, () -> new PaymentAccountAllocationCodec(uuid, decimal, type, null, direction, currency, country, timestamp, payment));
        assertThrows(NullPointerException.class, () -> new PaymentAccountAllocationCodec(uuid, decimal, type, state, null, currency, country, timestamp, payment));
        assertThrows(NullPointerException.class, () -> new PaymentAccountAllocationCodec(uuid, decimal, type, state, direction, null, country, timestamp, payment));
        assertThrows(NullPointerException.class, () -> new PaymentAccountAllocationCodec(uuid, decimal, type, state, direction, currency, null, timestamp, payment));
        assertThrows(NullPointerException.class, () -> new PaymentAccountAllocationCodec(uuid, decimal, type, state, direction, currency, country, null, payment));
        assertThrows(NullPointerException.class, () -> new PaymentAccountAllocationCodec(uuid, decimal, type, state, direction, currency, country, timestamp, null));
    }

    @ParameterizedTest
    @MethodSource("paymentAccountAllocationArgs")
    @DisplayName("PaymentAccountAllocationCodec::encode bean to protobuf")
    public void codec_bean_to_protobuf(
        @Nonnull UUID identifier,
        @Nonnull UUID account,
        @Nonnull AllocationType type,
        @Nonnull BigDecimal amount,
        @Nonnull AllocationState allocationState,
        @Nullable ZonedDateTime sentTimestamp,
        @Nullable ZonedDateTime settledTimestamp,
        @Nonnull ZonedDateTime timestamp,
        @Nonnull Payment payment
    ) {
        final var bean = paymentAccountAllocation(identifier, account, type, amount, allocationState,sentTimestamp, settledTimestamp, timestamp, payment);

        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));
        assertEquals(bean.getIdentifier(), uuid.decode(protobuf.getIdentifier()));
        assertEquals(bean.getAccount(), uuid.decode(protobuf.getAccount()));
        assertEquals(bean.getAmount(), decimal.decode(protobuf.getAmount()));
        assertEquals(bean.getType(), this.type.decode(protobuf.getType()));
        assertEquals(bean.getAllocationState(), state.decode(protobuf.getAllocationState()));
        assertEquals(bean.getDirection(), direction.decode(protobuf.getAllocationDirection()));
        assertEquals(bean.getTimestamp(), this.timestamp.decode(protobuf.getTimestamp()));
        if (bean.getSentTimestamp() == null) {
            assertFalse(protobuf.hasSentTimestamp());
        } else {
            assertEquals(bean.getSentTimestamp(), this.timestamp.decode(protobuf.getSentTimestamp()));
        }

        if (bean.getSettledTimestamp() == null) {
            assertFalse(protobuf.hasSettledTimestamp());
        } else {
            assertEquals(bean.getSettledTimestamp(), this.timestamp.decode(protobuf.getSettledTimestamp()));
        }
        assertEquals(bean.getTimestamp(), this.timestamp.decode(protobuf.getTimestamp()));
        assertEquals(bean.getPayment(), this.payment.decode(protobuf.getPayment()));

    }

    @ParameterizedTest
    @MethodSource("paymentAccountAllocationArgs")
    @DisplayName("PaymentAccountAllocationCodec::decode protobuf to bean")
    public void codec_protobuf_to_bean(
        @Nonnull UUID identifier,
        @Nonnull UUID account,
        @Nonnull AllocationType type,
        @Nonnull BigDecimal amount,
        @Nonnull AllocationState allocationState,
        @Nullable ZonedDateTime sentTimestamp,
        @Nullable ZonedDateTime settledTimestamp,
        @Nonnull ZonedDateTime timestamp,
        @Nonnull Payment payment
    ) {
        final var accountAllocation = paymentAccountAllocation(identifier, account, type, amount, allocationState,sentTimestamp, settledTimestamp, timestamp, payment);

        final var builder = AccountAllocationMessageApi.newBuilder()
            .setIdentifier(uuid.encode(accountAllocation.getIdentifier()))
            .setAccount(uuid.encode(accountAllocation.getAccount()))
            .setAmount(decimal.encode(accountAllocation.getAmount()))
            .setAllocationState(state.encode(accountAllocation.getAllocationState()))
            .setAllocationDirection(direction.encode(accountAllocation.getDirection()))
            .setTimestamp(this.timestamp.encode(accountAllocation.getTimestamp()))
            .setType(this.type.encode(type))
            .setPayment(this.payment.encode(accountAllocation.getPayment()));

        if (sentTimestamp != null) {
            builder.setSentTimestamp(this.timestamp.encode(sentTimestamp));
        }

        if (settledTimestamp != null) {
            builder.setSettledTimestamp(this.timestamp.encode(settledTimestamp));
        }

        final var protobuf = builder.build();

        final var bean = assertDoesNotThrow(() -> codec.decode(protobuf));
        assertEquals(protobuf.getIdentifier(), uuid.encode(bean.getIdentifier()));
        assertEquals(protobuf.getAccount(), uuid.encode(bean.getAccount()));
        assertEquals(protobuf.getAmount(), decimal.encode(bean.getAmount()));
        assertEquals(protobuf.getAllocationState(), state.encode(bean.getAllocationState()));
        assertEquals(protobuf.getAllocationDirection(), direction.encode(bean.getDirection()));
        if (bean.getSentTimestamp() != null) {
            assertEquals(protobuf.getSentTimestamp(), this.timestamp.encode(bean.getSentTimestamp()));
        } else {
            assertNull(bean.getSentTimestamp());
        }
        if (bean.getSettledTimestamp() != null) {
            assertEquals(protobuf.getSettledTimestamp(), this.timestamp.encode(bean.getSettledTimestamp()));
        } else {
            assertNull(bean.getSettledTimestamp());
        }
        assertEquals(protobuf.getTimestamp(), this.timestamp.encode(bean.getTimestamp()));
        assertEquals(protobuf.getType(), this.type.encode(bean.getType()));
    }

    @Nonnull
    private static Payment payment() {
        return new GRPCPaymentDataAccessObject(
            UUID.randomUUID(),
            UUID.randomUUID(),
            new GRPCPaymentMethodDataAccessObject(1, "BANK_TRANSFER", "Bank Transfer"),
            new GRPCCurrencyDataAccessObject(458, "MYR", "Malaysian Ringgit", "MYR"),
            new BigDecimal("100.00"),
            ZonedDateTime.now(),
            PaymentStatus.ACTIVE,
            "description",
            new GRPCCountryDataAccessObject(458, "Malaysia", "MY"),
            new GRPCReferenceDataAccessObject(UUID.randomUUID(), 1L, "MY", "CO", 2, ReferenceType.INVOICE, "MYCO00000000012"),
            ZonedDateTime.now()
        ).entity();
    }

    @Nonnull
    private static PaymentAccountAllocation<GRPCPaymentAccountAllocationDataAccessObject> paymentAccountAllocation(
        @Nonnull UUID identifier,
        @Nonnull UUID account,
        @Nonnull AllocationType type,
        @Nonnull BigDecimal amount,
        @Nonnull AllocationState allocationState,
        @Nullable ZonedDateTime sentTimestamp,
        @Nullable ZonedDateTime settledTimestamp,
        @Nonnull ZonedDateTime timestamp,
        @Nonnull Payment payment
    ) {
        final var currency = new GRPCCurrencyDataAccessObject(458, "MYR", "Malaysian Ringgit", "MYR");
        final var country = new GRPCCountryDataAccessObject(458, "Malaysia", "MYR");
        return new GRPCPaymentAccountAllocationDataAccessObject(
            identifier,
            account,
            type,
            country,
            currency,
            amount,
            allocationState,
            AllocationDirection.IN,
            sentTimestamp,
            settledTimestamp,
            timestamp,
            payment.data()
        ).entity();
    }

    @Nonnull
    private static Stream<Arguments> paymentAccountAllocationArgs() {
        return Stream.of(
            Arguments.of(UUID.randomUUID(),
                UUID.randomUUID(),
                AllocationType.PAYMENT,
                BigDecimal.TEN,
                AllocationState.create(BigDecimal.TEN),
                ZonedDateTime.now(),
                ZonedDateTime.now(),
                ZonedDateTime.now(),
                payment()),
            Arguments.of(UUID.randomUUID(),
                UUID.randomUUID(),
                AllocationType.PAYMENT,
                BigDecimal.TEN,
                AllocationState.create(BigDecimal.TEN),
                null,
                null,
                ZonedDateTime.now(),
                payment())
        );
    }
}