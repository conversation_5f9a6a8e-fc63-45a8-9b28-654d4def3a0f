package global.symbio.billing.core.services.payment.codec.allocation.dto;

import global.symbio.billing.core.services.codec.uuid.UUIDCodec;
import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.codec.decimal.BigDecimalCodec;
import global.symbio.billing.core.services.grpc.message.BigDecimalMessage;
import global.symbio.billing.core.services.grpc.message.UUIDMessage;
import global.symbio.billing.core.services.grpc.payment.dto.InvoiceAllocationMessage;
import global.symbio.billing.core.services.payment.codec.allocation.dto.impl.GRPCInvoiceAllocationDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

public class TestInvoiceAllocationDTOCodec {

    private Codec<UUID, UUIDMessage> uuid;

    private Codec<BigDecimal, BigDecimalMessage> decimal;

    private Codec<InvoiceAllocationDTO, InvoiceAllocationMessage> codec;

    @BeforeEach
    public void setup() {
        uuid = new UUIDCodec();
        decimal = new BigDecimalCodec();
        codec = new InvoiceAllocationDTOCodec(uuid, decimal);
    }

    @Test
    @DisplayName("InvoiceAllocationDTOCodec::new rejects null constructor arguments")
    public void invoice_allocation_dto_codec_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new InvoiceAllocationDTOCodec(null, decimal));
        assertThrows(NullPointerException.class, () -> new InvoiceAllocationDTOCodec(uuid, null));
    }

    @Test
    @DisplayName("InvoiceAllocationDTOCodec::encode bean to protobuf")
    public void codec_bean_to_protobuf() {
        final var invoiceUUID = UUID.randomUUID();
        final var reference = "MYCO00000008191";
        final var amount = BigDecimal.TEN;
        final var bean = new GRPCInvoiceAllocationDTO(invoiceUUID, reference, amount);

        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));
        assertEquals(bean.invoice(), uuid.decode(protobuf.getInvoice()));
        assertEquals(bean.amount(), decimal.decode(protobuf.getAmount()));
    }

    @Test
    @DisplayName("InvoiceAllocationDTOCodec::encode protobuf to bean")
    public void codec_protobuf_to_bean() {
        final var invoiceUUID = UUID.randomUUID();
        final var amount = BigDecimal.TEN;

        final var protobuf = InvoiceAllocationMessage.newBuilder()
            .setInvoice(uuid.encode(invoiceUUID))
            .setAmount(decimal.encode(amount))
            .build();

        final var bean = assertDoesNotThrow(() -> codec.decode(protobuf));
        assertEquals(protobuf, codec.encode(bean));
    }

}
