package global.symbio.billing.core.services.payment.codec.allocation.dto;

import global.symbio.billing.core.exception.sdk.BillingEntityNotFoundException;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AllocationType;
import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.payment.entities.AllocationTypeMessage;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.util.Arrays;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;

public class TestAllocationTypeCodec {

    private Codec<AllocationType, AllocationTypeMessage> codec;

    @BeforeEach
    public void setup() {
        codec = new AllocationTypeCodec();
    }

    @ParameterizedTest
    @DisplayName("AllocationTypeCodec::encode bean to protobuf")
    @MethodSource("types")
    public void codec_bean_to_protobuf(final AllocationType bean) {
        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));
        assertEquals(bean, codec.decode(protobuf));
    }

    @ParameterizedTest
    @DisplayName("AllocationTypeCodec::decode protobuf to bean")
    @MethodSource("messages")
    public void codec_protobuf_to_bean(final AllocationTypeMessage message) {
        if (message != AllocationTypeMessage.ALLOCATION_TYPE_UNSPECIFIED) {
            final var bean = assertDoesNotThrow(() -> codec.decode(message));
            assertEquals(message, codec.encode(bean));
        } else {
            final var unknown = AllocationTypeMessage.ALLOCATION_TYPE_UNSPECIFIED;
            assertThrows(BillingEntityNotFoundException.class, () -> codec.decode(unknown));
        }
    }

    @Nonnull
    private static Stream<Arguments> types() {
        return Arrays.stream(AllocationType.values()).map(Arguments::of);
    }

    @Nonnull
    private static Stream<Arguments> messages() {
        return Stream.of(
            Arguments.of(AllocationTypeMessage.PAYMENT),
            Arguments.of(AllocationTypeMessage.TRANSFER_OUT),
            Arguments.of(AllocationTypeMessage.TRANSFER_IN),
            Arguments.of(AllocationTypeMessage.ALLOCATION_TYPE_UNSPECIFIED)
        );
    }
}
