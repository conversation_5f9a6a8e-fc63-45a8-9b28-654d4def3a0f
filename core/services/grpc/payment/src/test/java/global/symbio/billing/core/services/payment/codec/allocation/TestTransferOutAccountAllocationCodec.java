
package global.symbio.billing.core.services.payment.codec.allocation;

import global.symbio.billing.core.country.persistence.api.Country;
import global.symbio.billing.core.currency.persistence.api.Currency;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AllocationDirection;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AllocationType;
import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.transfer.TransferOutAccountAllocation;
import global.symbio.billing.core.payment.persistence.api.allocation.state.AllocationState;
import global.symbio.billing.core.services.codec.uuid.UUIDCodec;
import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.codec.country.CountryCodec;
import global.symbio.billing.core.services.grpc.codec.country.GRPCCountryDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.currency.CurrencyCodec;
import global.symbio.billing.core.services.grpc.codec.currency.GRPCCurrencyDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.decimal.BigDecimalCodec;
import global.symbio.billing.core.services.grpc.codec.temporal.*;
import global.symbio.billing.core.services.grpc.message.*;
import global.symbio.billing.core.services.grpc.payment.entities.*;
import global.symbio.billing.core.services.payment.codec.allocation.dto.AllocationDirectionCodec;
import global.symbio.billing.core.services.payment.codec.allocation.dto.AllocationTypeCodec;
import global.symbio.billing.core.services.payment.codec.allocation.state.AllocationStateCodec;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.*;
import java.util.Objects;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

public class TestTransferOutAccountAllocationCodec {

    private Codec<UUID, UUIDMessage> uuid;

    private Codec<BigDecimal, BigDecimalMessage> decimal;

    private Codec<AllocationType, AllocationTypeMessage> type;

    private Codec<AllocationState, AllocationStateMessage> state;

    private Codec<AllocationDirection, AllocationDirectionMessage> direction;

    private Codec<Currency, CurrencyMessage> currency;

    private Codec<Country, CountryMessage> country;

    private Codec<ZonedDateTime, ZonedDateTimeMessage> timestamp;

    private Codec<LocalDate, LocalDateMessage> date;

    private Codec<LocalTime, LocalTimeMessage> time;

    private Codec<LocalDateTime, LocalDateTimeMessage> datetime;

    private Codec<ZoneOffset, ZoneOffsetMessage> offset;

    private Codec<ZoneId, ZoneIdMessage> zone;

    private Codec<TransferOutAccountAllocation<?>, AccountAllocationMessageApi> codec;

    @BeforeEach
    public void setup() {
        uuid = new UUIDCodec();
        decimal = new BigDecimalCodec();
        type = new AllocationTypeCodec();
        state = new AllocationStateCodec(decimal);
        direction = new AllocationDirectionCodec();
        currency = new CurrencyCodec();
        country = new CountryCodec();
        date = new LocalDateCodec();
        time = new LocalTimeCodec();
        datetime = new LocalDateTimeCodec(date, time);
        offset = new ZoneOffsetCodec();
        zone = new ZoneIdCodec();
        timestamp = new ZonedDateTimeCodec(datetime, offset, zone);
        codec = new TransferOutAccountAllocationCodec(uuid, decimal, type, state, direction, currency, country, timestamp);
    }

    @Test
    @DisplayName("TransferOutAccountAllocationCodec::new rejects null constructor arguments")
    public void transfer_out_account_allocation_api_codec_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new TransferOutAccountAllocationCodec(null, decimal, type, state, direction, currency, country, timestamp));
        assertThrows(NullPointerException.class, () -> new TransferOutAccountAllocationCodec(uuid, null, type, state, direction, currency, country, timestamp));
        assertThrows(NullPointerException.class, () -> new TransferOutAccountAllocationCodec(uuid, decimal, null, state, direction, currency, country, timestamp));
        assertThrows(NullPointerException.class, () -> new TransferOutAccountAllocationCodec(uuid, decimal, type, null, direction, currency, country, timestamp));
        assertThrows(NullPointerException.class, () -> new TransferOutAccountAllocationCodec(uuid, decimal, type, state, null, currency, country, timestamp));
        assertThrows(NullPointerException.class, () -> new TransferOutAccountAllocationCodec(uuid, decimal, type, state, direction, null, country, timestamp));
        assertThrows(NullPointerException.class, () -> new TransferOutAccountAllocationCodec(uuid, decimal, type, state, direction, currency, null, timestamp));
        assertThrows(NullPointerException.class, () -> new TransferOutAccountAllocationCodec(uuid, decimal, type, state, direction, currency, country, null));
    }

    @Test
    @DisplayName("TransferOutAccountAllocationCodec::encode bean to protobuf")
    public void codec_bean_to_protobuf() {
        final var currency = new GRPCCurrencyDataAccessObject(458, "MYR", "Malaysian Ringgit", "MYR");
        final var country = new GRPCCountryDataAccessObject(458, "Malaysia", "MYR");
        final var bean = new GRPCTransferOutAccountAllocationDataAccessObject(
            UUID.randomUUID(),
            UUID.randomUUID(),
            AllocationType.PAYMENT,
            country,
            currency,
            BigDecimal.TEN,
            AllocationState.create(BigDecimal.TEN),
            AllocationDirection.IN,
            ZonedDateTime.now(),
            ZonedDateTime.now(),
            ZonedDateTime.now(),
            UUID.randomUUID()
        ).entity();

        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));
        assertEquals(bean.getIdentifier(), uuid.decode(protobuf.getIdentifier()));
        assertEquals(bean.getAccount(), uuid.decode(protobuf.getAccount()));
        assertEquals(bean.getAmount(), decimal.decode(protobuf.getAmount()));
        assertEquals(bean.getType(), type.decode(protobuf.getType()));
        assertEquals(bean.getAllocationState(), state.decode(protobuf.getAllocationState()));
        assertEquals(bean.getDirection(), direction.decode(protobuf.getAllocationDirection()));
        assertEquals(bean.getSentTimestamp(), timestamp.decode(protobuf.getSentTimestamp()));
        assertEquals(bean.getSettledTimestamp(), timestamp.decode(protobuf.getSettledTimestamp()));
        assertEquals(bean.getTimestamp(), timestamp.decode(protobuf.getTimestamp()));
        assertEquals(bean.getParent(), uuid.decode(protobuf.getTransferType().getParent()));
    }

    @Test
    @DisplayName("TransferOutAccountAllocationCodec::decode protobuf to bean")
    public void codec_protobuf_to_bean() {
        final var currency = new GRPCCurrencyDataAccessObject(458, "MYR", "Malaysian Ringgit", "MYR");
        final var country = new GRPCCountryDataAccessObject(458, "Malaysia", "MYR");
        final var accountAllocation = new GRPCTransferOutAccountAllocationDataAccessObject(
            UUID.randomUUID(),
            UUID.randomUUID(),
            AllocationType.PAYMENT,
            country,
            currency,
            BigDecimal.TEN,
            AllocationState.create(BigDecimal.TEN),
            AllocationDirection.IN,
            ZonedDateTime.now(),
            ZonedDateTime.now(),
            ZonedDateTime.now(),
            UUID.randomUUID()
        ).entity();

        final var typeBuilder = TransferTypeMessage.newBuilder()
            .setAllocation(uuid.encode(accountAllocation.getIdentifier()))
            .setParent(uuid.encode(accountAllocation.getParent()));

        final var protobuf = AccountAllocationMessageApi.newBuilder()
            .setIdentifier(uuid.encode(accountAllocation.getIdentifier()))
            .setAccount(uuid.encode(accountAllocation.getAccount()))
            .setCountry(this.country.encode(accountAllocation.getCountry()))
            .setCurrency(this.currency.encode(accountAllocation.getCurrency()))
            .setAmount(decimal.encode(accountAllocation.getAmount()))
            .setAllocationState(state.encode(accountAllocation.getAllocationState()))
            .setAllocationDirection(direction.encode(accountAllocation.getDirection()))
            .setSentTimestamp(this.timestamp.encode(Objects.requireNonNull(accountAllocation.getSentTimestamp())))
            .setSettledTimestamp(this.timestamp.encode(Objects.requireNonNull(accountAllocation.getSettledTimestamp())))
            .setTimestamp(this.timestamp.encode(accountAllocation.getTimestamp()))
            .setType(type.encode(accountAllocation.getType()))
            .setTransferType(typeBuilder)
            .build();

        final var bean = assertDoesNotThrow(() -> codec.decode(protobuf));
        assertEquals(protobuf.getIdentifier(), uuid.encode(bean.getIdentifier()));
        assertEquals(protobuf.getAccount(), uuid.encode(bean.getAccount()));
        assertEquals(protobuf.getCountry(), this.country.encode(bean.getCountry()));
        assertEquals(protobuf.getCurrency(), this.currency.encode(bean.getCurrency()));
        assertEquals(protobuf.getAmount(), decimal.encode(bean.getAmount()));
        assertEquals(protobuf.getAllocationState(), state.encode(bean.getAllocationState()));
        assertEquals(protobuf.getAllocationDirection(), direction.encode(bean.getDirection()));
        assertEquals(protobuf.getSentTimestamp(), this.timestamp.encode(Objects.requireNonNull(bean.getSentTimestamp())));
        assertEquals(protobuf.getSettledTimestamp(), this.timestamp.encode(Objects.requireNonNull(bean.getSettledTimestamp())));
        assertEquals(protobuf.getTimestamp(), this.timestamp.encode(bean.getTimestamp()));
        assertEquals(protobuf.getType(), type.encode(bean.getType()));
        assertEquals(protobuf.getTransferType().getParent(), uuid.encode(bean.getParent()));
    }
}