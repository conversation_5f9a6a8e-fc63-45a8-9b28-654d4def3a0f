package global.symbio.billing.core.services.payment.codec.transfer;

import global.symbio.billing.core.country.persistence.api.Country;
import global.symbio.billing.core.currency.persistence.api.Currency;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AllocationDirection;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AllocationType;
import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.payment.PaymentAccountAllocation;
import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.transfer.TransferInAccountAllocation;
import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.transfer.TransferOutAccountAllocation;
import global.symbio.billing.core.payment.persistence.api.allocation.state.AllocationState;
import global.symbio.billing.core.payment.persistence.api.payment.Payment;
import global.symbio.billing.core.payment.persistence.api.payment.PaymentStatus;
import global.symbio.billing.core.payment.persistence.api.payment.method.PaymentMethod;
import global.symbio.billing.core.payment.persistence.api.transfer.Transfer;
import global.symbio.billing.core.reference.persistence.api.reference.Reference;
import global.symbio.billing.core.reference.persistence.api.reference.ReferenceType;
import global.symbio.billing.core.services.codec.uuid.UUIDCodec;
import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.codec.country.CountryCodec;
import global.symbio.billing.core.services.grpc.codec.country.GRPCCountryDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.currency.CurrencyCodec;
import global.symbio.billing.core.services.grpc.codec.currency.GRPCCurrencyDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.decimal.BigDecimalCodec;
import global.symbio.billing.core.services.grpc.codec.payment.method.GRPCPaymentMethodDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.payment.method.PaymentMethodCodec;
import global.symbio.billing.core.services.grpc.codec.reference.GRPCReferenceDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.reference.ReferenceCodec;
import global.symbio.billing.core.services.grpc.codec.reference.ReferenceTypeCodec;
import global.symbio.billing.core.services.grpc.codec.temporal.*;
import global.symbio.billing.core.services.grpc.message.*;
import global.symbio.billing.core.services.grpc.payment.entities.*;
import global.symbio.billing.core.services.payment.codec.allocation.*;
import global.symbio.billing.core.services.payment.codec.allocation.dto.AllocationDirectionCodec;
import global.symbio.billing.core.services.payment.codec.allocation.dto.AllocationTypeCodec;
import global.symbio.billing.core.services.payment.codec.allocation.state.AllocationStateCodec;
import global.symbio.billing.core.services.payment.codec.payment.GRPCPaymentDataAccessObject;
import global.symbio.billing.core.services.payment.codec.payment.PaymentCodec;
import global.symbio.billing.core.services.payment.codec.payment.PaymentStatusCodec;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

public class TestTransferCodec {

    private Codec<UUID, UUIDMessage> uuid;

    private Codec<BigDecimal, BigDecimalMessage> decimal;

    private Codec<Country, CountryMessage> country;

    private Codec<ZonedDateTime, ZonedDateTimeMessage> timestamp;

    private Codec<Currency, CurrencyMessage> currency;

    private Codec<AllocationType, AllocationTypeMessage> type;

    private Codec<AllocationState, AllocationStateMessage> state;

    private Codec<AllocationDirection, AllocationDirectionMessage> direction;

    private Codec<TransferInAccountAllocation<?>, AccountAllocationMessageApi> transferIn;

    private Codec<TransferOutAccountAllocation<?>, AccountAllocationMessageApi> transferOut;

    private Codec<Payment, PaymentMessage> payment;

    private Codec<PaymentMethod, PaymentMethodMessage> method;

    private Codec<PaymentStatus, PaymentStatusMessage> status;

    private Codec<Reference, ReferenceMessage> reference;

    private Codec<ReferenceType, ReferenceTypeMessage> referenceType;

    private Codec<PaymentAccountAllocation<?>, AccountAllocationMessageApi> paymentAccountAllocation;

    private Codec<Transfer, TransferMessage> codec;

    @BeforeEach
    public void setup() {
        final var date = new LocalDateCodec();
        final var time = new LocalTimeCodec();
        final var datetime = new LocalDateTimeCodec(date, time);
        final var offset = new ZoneOffsetCodec();
        final var zone = new ZoneIdCodec();
        uuid = new UUIDCodec();
        decimal = new BigDecimalCodec();
        country = new CountryCodec();
        timestamp = new ZonedDateTimeCodec(datetime, offset, zone);
        currency = new CurrencyCodec();
        country = new CountryCodec();
        type = new AllocationTypeCodec();
        state = new AllocationStateCodec(decimal);
        direction = new AllocationDirectionCodec();
        transferIn = new TransferInAccountAllocationCodec(uuid, decimal, type, state, direction, currency, country, timestamp);
        transferOut = new TransferOutAccountAllocationCodec(uuid, decimal, type, state, direction, currency, country, timestamp);
        method = new PaymentMethodCodec();
        status = new PaymentStatusCodec();
        referenceType = new ReferenceTypeCodec();
        reference = new ReferenceCodec(uuid, referenceType);
        payment = new PaymentCodec(uuid, decimal, currency, country, timestamp, method, status, reference);
        paymentAccountAllocation = new PaymentAccountAllocationCodec(uuid, decimal, type, state, direction, currency, country, timestamp, payment);
        codec = new TransferCodec(uuid, decimal, currency, country, timestamp, transferIn, transferOut, paymentAccountAllocation);
    }

    @Test
    @DisplayName("TransferCodec::new rejects null constructor arguments")
    public void transfer_codec_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new TransferCodec(null, decimal, currency, country, timestamp, transferIn, transferOut, paymentAccountAllocation));
        assertThrows(NullPointerException.class, () -> new TransferCodec(uuid, null, currency, country, timestamp, transferIn, transferOut, paymentAccountAllocation));
        assertThrows(NullPointerException.class, () -> new TransferCodec(uuid, decimal, null, country, timestamp, transferIn, transferOut, paymentAccountAllocation));
        assertThrows(NullPointerException.class, () -> new TransferCodec(uuid, decimal, currency, null, timestamp, transferIn, transferOut, paymentAccountAllocation));
        assertThrows(NullPointerException.class, () -> new TransferCodec(uuid, decimal, currency, country, null, transferIn, transferOut, paymentAccountAllocation));
        assertThrows(NullPointerException.class, () -> new TransferCodec(uuid, decimal, currency, country, timestamp, null, transferOut, paymentAccountAllocation));
        assertThrows(NullPointerException.class, () -> new TransferCodec(uuid, decimal, currency, country, timestamp, transferIn, null, paymentAccountAllocation));
        assertThrows(NullPointerException.class, () -> new TransferCodec(uuid, decimal, currency, country, timestamp, transferIn, transferOut, null));
    }

    @Test
    @DisplayName("TransferCodec::encode bean to protobuf")
    public void codec_bean_to_protobuf() {
        final var credit = transferInAccountAllocation();
        final var debit = transferOutAccountAllocation();
        final var origin = paymentAccountAllocation();
        final var bean = new GRPCTransferDataAccessObject(
            UUID.randomUUID(),
            credit, debit,
            new GRPCCurrencyDataAccessObject(458, "MYR", "Malaysian Ringgit", "MYR"),
            BigDecimal.ONE,
            "description",
            new GRPCCountryDataAccessObject(458, "Malaysia", "MY"),
            origin,
            ZonedDateTime.now()
        ).entity();
        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));

        assertEquals(bean.getIdentifier(), uuid.decode(protobuf.getIdentifier()));
        assertEquals(bean.getTransferOut(), transferOut.decode(protobuf.getTransferOut()));
        assertEquals(bean.getTransferIn(), transferIn.decode(protobuf.getTransferIn()));
        assertEquals(bean.getCurrency(), currency.decode(protobuf.getCurrency()));
        assertEquals(bean.getAmount(), decimal.decode(protobuf.getAmount()));
        assertEquals(bean.getDescription(), protobuf.getDescription());
        assertEquals(bean.getCountry(), country.decode(protobuf.getCountry()));
        assertEquals(bean.getTimestamp(), timestamp.decode(protobuf.getTimestamp()));}

    @Test
    @DisplayName("TransferCodec::decode protobuf to bean")
    public void codec_protobuf_to_bean() {
        final var credit = transferInAccountAllocation();
        final var debit = transferOutAccountAllocation();
        final var origin = paymentAccountAllocation();
        final var transfer = new GRPCTransferDataAccessObject(
            UUID.randomUUID(),
            credit, debit,
            new GRPCCurrencyDataAccessObject(458, "MYR", "Malaysian Ringgit", "MYR"),
            BigDecimal.ONE,
            "description",
            new GRPCCountryDataAccessObject(458, "Malaysia", "MY"),
            origin,
            ZonedDateTime.now()
        ).entity();

        final var protobuf = TransferMessage.newBuilder()
            .setIdentifier(uuid.encode(transfer.getIdentifier()))
            .setTransferOut(transferOut.encode((TransferOutAccountAllocation<?>) transfer.getTransferOut()))
            .setTransferIn(transferIn.encode((TransferInAccountAllocation<?>) transfer.getTransferIn()))
            .setCurrency(currency.encode(transfer.getCurrency()))
            .setAmount(decimal.encode(transfer.getAmount()))
            .setDescription(transfer.getDescription())
            .setCountry(country.encode(transfer.getCountry()))
            .setOrigin(paymentAccountAllocation.encode((PaymentAccountAllocation<?>) transfer.getOrigin()))
            .setTimestamp(timestamp.encode(transfer.getTimestamp()))
            .build();

        final var bean = assertDoesNotThrow(() -> codec.decode(protobuf));
        assertEquals(transfer, bean);
    }

    @Nonnull
    private static GRPCTransferInAccountAllocationDataAccessObject transferInAccountAllocation() {
        final var currency = new GRPCCurrencyDataAccessObject(458, "MYR", "Malaysian Ringgit", "MYR");
        final var country = new GRPCCountryDataAccessObject(458, "Malaysia", "MY");
        return new GRPCTransferInAccountAllocationDataAccessObject(
            UUID.randomUUID(),
            UUID.randomUUID(),
            AllocationType.TRANSFER_IN,
            country,
            currency,
            BigDecimal.ONE,
            AllocationState.create(BigDecimal.ONE),
            AllocationDirection.IN,
            ZonedDateTime.now(),
            ZonedDateTime.now(),
            ZonedDateTime.now(),
            UUID.randomUUID()
        );
    }

    @Nonnull
    private static GRPCTransferOutAccountAllocationDataAccessObject transferOutAccountAllocation() {
        final var currency = new GRPCCurrencyDataAccessObject(458, "MYR", "Malaysian Ringgit", "MYR");
        final var country = new GRPCCountryDataAccessObject(458, "Malaysia", "MY");
        return new GRPCTransferOutAccountAllocationDataAccessObject(
            UUID.randomUUID(),
            UUID.randomUUID(),
            AllocationType.TRANSFER_OUT,
            country,
            currency,
            BigDecimal.ONE,
            AllocationState.create(BigDecimal.ONE),
            AllocationDirection.OUT,
            ZonedDateTime.now(),
            ZonedDateTime.now(),
            ZonedDateTime.now(),
            UUID.randomUUID()
        );
    }

    @Nonnull
    private static GRPCPaymentAccountAllocationDataAccessObject paymentAccountAllocation() {
        final var identifier = UUID.randomUUID();
        final var account = UUID.randomUUID();
        final var currency = new GRPCCurrencyDataAccessObject(458, "MYR", "Malaysian Ringgit", "MYR");
        final var country = new GRPCCountryDataAccessObject(458, "Malaysia", "MY");
        final var method = new GRPCPaymentMethodDataAccessObject(1, "BANK_TRANSFER", "Bank Transfer");
        final var reference = new GRPCReferenceDataAccessObject(UUID.randomUUID(), 1L, "MY", "CO", 2, ReferenceType.PAYMENT, "MYCOPYMT00000000012");
        final var payment = new GRPCPaymentDataAccessObject(UUID.randomUUID(), UUID.randomUUID(), method, currency, new BigDecimal("100.00"), ZonedDateTime.now(), PaymentStatus.ACTIVE, "description", country, reference,  ZonedDateTime.now());
        return new GRPCPaymentAccountAllocationDataAccessObject(
            identifier,
            account,
            AllocationType.PAYMENT,
            country,
            currency,
            new BigDecimal("100.00"),
            AllocationState.create(new BigDecimal("100.00")),
            AllocationDirection.IN,
            ZonedDateTime.now(),
            ZonedDateTime.now(),
            ZonedDateTime.now(),
            payment
        );
    }
}
