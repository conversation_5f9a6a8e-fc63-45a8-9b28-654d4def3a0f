package global.symbio.billing.core.services.payment.mixin;

import com.fasterxml.uuid.impl.UUIDUtil;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AccountAllocation;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AllocationType;
import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.payment.PaymentAccountAllocation;
import global.symbio.billing.core.payment.persistence.api.cancellation.PaymentCancellation;
import global.symbio.billing.core.payment.persistence.api.payment.PaymentStatus;
import global.symbio.billing.core.payment.persistence.api.transfer.Transfer;
import global.symbio.billing.core.services.payment.PaymentService;
import global.symbio.billing.core.services.payment.codec.allocation.dto.AccountInvoiceAllocation;
import global.symbio.billing.core.services.payment.codec.allocation.dto.InvoiceAllocationDTO;
import global.symbio.billing.core.services.payment.codec.allocation.dto.PaymentInvoiceAllocationDTO;
import global.symbio.billing.core.services.payment.codec.payment.PaymentResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestLoggingPaymentService {

    @Mock
    private Logger log;

    @Mock
    private PaymentService delegate;

    private LoggingPaymentService service;

    @BeforeEach
    public void setup() {
        service = new LoggingPaymentService(log, delegate);
    }

    @Test
    @DisplayName("LoggingPaymentService::new rejects null constructor arguments")
    public void logging_payment_service_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new LoggingPaymentService(null));
        assertThrows(NullPointerException.class, () -> new LoggingPaymentService(log, null));
        assertThrows(NullPointerException.class, () -> new LoggingPaymentService(null, delegate));
    }

    @Test
    @DisplayName("LoggingPaymentService::create logs and invokes method on delegate")
    public void logging_payment_service_create_logs_and_invokes_delegate_method() {
        final var identifier = UUIDUtil.nilUUID();
        final var customer = UUIDUtil.maxUUID();
        final var method = 1;
        final var currency = "AUD";
        final var amount = BigDecimal.TEN;
        final var receipt = ZonedDateTime.now();
        final var description = "description";
        final var country = "AU";
        final var allocations = Set.<AccountInvoiceAllocation>of();
        final var payment = mock(PaymentResponse.class);
        when(delegate.create(any(UUID.class), any(UUID.class), anyInt(), anyString(), any(BigDecimal.class), any(ZonedDateTime.class), anyString(), anyString(), anySet())).thenReturn(payment);
        final var result = assertDoesNotThrow(() -> service.create(identifier, customer, method, currency, amount, receipt, description, country, allocations));
        assertSame(payment, result);
        verify(log, times(1)).info(anyString(), eq(identifier), eq(customer), eq(method), eq(currency), eq(amount), eq(receipt), eq(description), eq(country), eq(0));
        verify(delegate, times(1)).create(eq(identifier), eq(customer), eq(method), eq(currency), eq(amount), eq(receipt), eq(description), eq(country), eq(allocations));
    }

    @Test
    @DisplayName("LoggingPaymentService::getPaymentAccountAllocations logs and invokes method on delegate")
    public void logging_payment_service_getPaymentAccountAllocations_logs_and_invokes_method() {
        final var identifier = UUIDUtil.nilUUID();
        final var paymentDateStart = ZonedDateTime.now();
        final var paymentDateEnd = ZonedDateTime.now();
        final var receiptDateStart = ZonedDateTime.now();
        final var receiptDateEnd = ZonedDateTime.now();
        final var allocation = Set.of("UNALLOCATED");
        final var methods = Set.of(1);
        final var status = Set.of(PaymentStatus.ACTIVE);
        final List<PaymentAccountAllocation<?>> paymentAccountAllocations = List.of(mock(PaymentAccountAllocation.class));
        when(delegate.getPaymentAccountAllocations(any(UUID.class), any(ZonedDateTime.class), any(ZonedDateTime.class), any(ZonedDateTime.class), any(ZonedDateTime.class), anySet(), anySet(), anySet())).thenReturn(paymentAccountAllocations);

        final var result = assertDoesNotThrow(() -> service.getPaymentAccountAllocations(identifier, paymentDateStart, paymentDateEnd, receiptDateStart, receiptDateEnd, allocation, methods, status));
        assertSame(paymentAccountAllocations, result);
        verify(log, times(1)).info(anyString(), eq(identifier), eq(paymentDateStart), eq(paymentDateEnd), eq(receiptDateStart), eq(receiptDateEnd), eq(allocation), eq(methods), eq(status));
        verify(delegate, times(1)).getPaymentAccountAllocations(eq(identifier), eq(paymentDateStart), eq(paymentDateEnd), eq(receiptDateStart), eq(receiptDateEnd), eq(allocation), eq(methods), eq(status));
    }

    @Test
    @DisplayName("LoggingPaymentService::assignAllocationToInvoices logs and invokes method on delegate")
    public void logging_payment_service_assign_allocation_to_invoices_logs_and_invokes_delegate_method() {
        final var identifier = UUIDUtil.nilUUID();
        final var accountAllocation = mock(PaymentAccountAllocation.class);
        when(delegate.assignAllocationToInvoices(any(UUID.class), anySet())).thenReturn(accountAllocation);
        final var result = assertDoesNotThrow(() -> service.assignAllocationToInvoices(identifier, Set.of()));
        assertSame(accountAllocation, result);
        verify(log, times(1)).info(anyString(), eq(identifier), eq(Set.of()), eq(0));
        verify(delegate, times(1)).assignAllocationToInvoices(eq(identifier), eq(Set.of()));
    }

    @Test
    @DisplayName("LoggingPaymentService::createTransfer logs and invokes method on delegate")
    public void logging_payment_service_create_transfer_logs_and_invokes_delegate_method() {
        final var identifier = UUIDUtil.nilUUID();
        final var debit = UUIDUtil.nilUUID();
        final var credit = UUIDUtil.nilUUID();
        final var amount = BigDecimal.TEN;
        final var description = "description";
        final var transfer = mock(Transfer.class, RETURNS_DEEP_STUBS);
        when(transfer.getCountry().getCode()).thenReturn("MY");
        when(delegate.createTransfer(any(UUID.class), any(UUID.class), any(UUID.class), any(BigDecimal.class), anyString())).thenReturn(transfer);
        final var result = assertDoesNotThrow(() -> service.createTransfer(identifier, debit, credit, amount, description));
        assertSame(transfer, result);
        verify(log, times(1)).info(anyString(), eq(identifier), eq(debit), eq(credit), eq(amount), eq(description));
        verify(delegate, times(1)).createTransfer(eq(identifier), eq(debit), eq(credit), eq(amount), eq(description));
    }

    @Test
    @DisplayName("LoggingPaymentService::getPaymentAccountAllocation logs and invokes method on delegate")
    public void logging_payment_service_get_payment_account_allocation_logs_and_invokes_delegate_method() {
        final var identifier = UUIDUtil.nilUUID();
        final var accountAllocation = mock(PaymentAccountAllocation.class);
        when(delegate.getPaymentAccountAllocation(any(UUID.class))).thenReturn(Optional.of(accountAllocation));
        final var result = assertDoesNotThrow(() -> service.getPaymentAccountAllocation(identifier));
        assertSame(accountAllocation, result.get());
        verify(log, times(1)).info(anyString(), eq(identifier));
        verify(delegate, times(1)).getPaymentAccountAllocation(eq(identifier));
    }

    @Test
    @DisplayName("LoggingPaymentService::getInvoiceAllocations logs and invokes method on delegate")
    public void logging_payment_service_get_invoice_allocations_logs_and_invokes_delegate_method() {
        final var identifier = UUIDUtil.nilUUID();
        final var invoiceAllocations = Set.of(mock(InvoiceAllocationDTO.class));
        when(delegate.getInvoiceAllocations(any(UUID.class))).thenReturn(invoiceAllocations);
        final var result = assertDoesNotThrow(() -> service.getInvoiceAllocations(identifier));
        assertSame(invoiceAllocations, result);
        verify(log, times(1)).info(anyString(), eq(identifier));
        verify(delegate, times(1)).getInvoiceAllocations(eq(identifier));
    }

    @Test
    @DisplayName("LoggingPaymentService::getTransferByTransferIn logs and invokes method on delegate")
    public void logging_payment_service_get_transfer_by_transfer_in_logs_and_invokes_delegate_method() {
        final var identifier = UUIDUtil.nilUUID();
        final var transfer = mock(Transfer.class);
        when(delegate.getTransferByTransferIn(any(UUID.class))).thenReturn(Optional.of(transfer));
        final var result = assertDoesNotThrow(() -> service.getTransferByTransferIn(identifier));
        assertSame(transfer, result.get());
        verify(log, times(1)).info(anyString(), eq(identifier));
        verify(delegate, times(1)).getTransferByTransferIn(eq(identifier));
    }

    @Test
    @DisplayName("LoggingPaymentService::getTransfers logs and invokes method on delegate")
    public void logging_payment_service_get_transfers_in_logs_and_invokes_delegate_method() {
        final var account = UUID.randomUUID();
        final var transferDateStart = ZonedDateTime.now();
        final var transferDateEnd = ZonedDateTime.now();
        final var transfer = mock(Transfer.class);
        final var expected = List.of(transfer);
        when(delegate.getTransfers(any(UUID.class), any(ZonedDateTime.class), any(ZonedDateTime.class), any(AllocationType.class))).thenReturn(expected);
        final var result = assertDoesNotThrow(() -> service.getTransfers(account, transferDateStart, transferDateEnd, AllocationType.TRANSFER_IN));
        assertSame(expected, result);
        verify(log, times(1)).info(anyString(), eq(account), eq(transferDateStart), eq(transferDateEnd), eq(AllocationType.TRANSFER_IN));
        verify(delegate, times(1)).getTransfers(eq(account), eq(transferDateStart), eq(transferDateEnd), eq(AllocationType.TRANSFER_IN));
    }

    @Test
    @DisplayName("LoggingPaymentService::getAccountAllocation logs and invokes method on delegate")
    public void logging_payment_service_get_account_allocation_logs_and_invokes_delegate_method() {
        final var identifier = UUIDUtil.nilUUID();
        final var allocation = mock(AccountAllocation.class);
        when(delegate.getAccountAllocation(any(UUID.class))).thenReturn(Optional.of(allocation));
        final var result = assertDoesNotThrow(() -> service.getAccountAllocation(identifier));
        assertSame(allocation, result.get());
        verify(log, times(1)).info(anyString(), eq(identifier));
        verify(delegate, times(1)).getAccountAllocation(eq(identifier));
    }

    @Test
    @DisplayName("LoggingPaymentService::cancel logs and invokes method on delegate")
    public void logging_payment_service_cancel_logs_and_invokes_delegate_method() {
        final var identifier = UUIDUtil.nilUUID();
        final var reason = "reason";
        final var cancellation = mock(PaymentCancellation.class, RETURNS_DEEP_STUBS);
        when(cancellation.getPayment().getCountry().getCode()).thenReturn("MY");
        when(delegate.cancel(any(UUID.class), anyString())).thenReturn(cancellation);
        final var result = assertDoesNotThrow(() -> service.cancel(identifier, reason));
        assertSame(cancellation, result);
        verify(log, times(1)).info(anyString(), eq(identifier), eq(reason));
        verify(delegate, times(1)).cancel(eq(identifier), eq(reason));
    }

    @Test
    @DisplayName("LoggingPaymentService::getPaymentCancellation logs and invokes method on delegate")
    public void logging_payment_service_get_payment_cancellation_logs_and_invokes_delegate_method() {
        final var identifier = UUIDUtil.nilUUID();
        final var cancellation = mock(PaymentCancellation.class);
        when(delegate.getPaymentCancellation(any(UUID.class))).thenReturn(Optional.of(cancellation));
        final var result = assertDoesNotThrow(() -> service.getPaymentCancellation(identifier));
        assertSame(cancellation, result.get());
        verify(log, times(1)).info(anyString(), eq(identifier));
        verify(delegate, times(1)).getPaymentCancellation(eq(identifier));
    }

    @Test
    @DisplayName("LoggingPaymentService::getInvoiceAllocationsByInvoice logs and invokes method on delegate")
    public void logging_payment_service_get_invoice_allocations_by_invoice_logs_and_invokes_delegate_method() {
        final var identifier = UUIDUtil.nilUUID();
        final var paymentInvoiceAllocations = List.of(mock(PaymentInvoiceAllocationDTO.class));
        when(delegate.getInvoiceAllocationsByInvoice(any(UUID.class))).thenReturn(paymentInvoiceAllocations);
        final var result = assertDoesNotThrow(() -> service.getInvoiceAllocationsByInvoice(identifier));
        assertSame(paymentInvoiceAllocations, result);
        verify(log, times(1)).info(anyString(), eq(identifier));
        verify(delegate, times(1)).getInvoiceAllocationsByInvoice(eq(identifier));
    }
}