package global.symbio.billing.core.services.payment.codec.allocation.state;

import global.symbio.billing.core.payment.persistence.api.allocation.state.Allocated;
import global.symbio.billing.core.payment.persistence.api.allocation.state.AllocationState;
import global.symbio.billing.core.payment.persistence.api.allocation.state.PartiallyAllocated;
import global.symbio.billing.core.payment.persistence.api.allocation.state.Unallocated;
import global.symbio.billing.core.services.grpc.codec.Codec;
import global.symbio.billing.core.services.grpc.codec.decimal.BigDecimalCodec;
import global.symbio.billing.core.services.grpc.message.BigDecimalMessage;
import global.symbio.billing.core.services.grpc.payment.entities.AllocationStateMessage;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.math.BigDecimal;
import java.util.stream.Stream;

import static java.math.BigDecimal.*;
import static org.junit.jupiter.api.Assertions.*;

public class TestAllocationStateCodec {

    private Codec<BigDecimal, BigDecimalMessage> decimal;

    private Codec<AllocationState, AllocationStateMessage> codec;

    @BeforeEach
    public void setup() {
        decimal = new BigDecimalCodec();
        codec = new AllocationStateCodec(decimal);
    }

    @Test
    @DisplayName("AllocationStateCodec::new rejects null constructor arguments")
    public void allocation_state_codec_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new AllocationStateCodec(null));
    }

    @ParameterizedTest
    @MethodSource("states")
    @DisplayName("AllocationStateCodec::encode bean to protobuf")
    public void codec_bean_to_protobuf(@Nonnull final AllocationState bean) {
        final var protobuf = assertDoesNotThrow(() -> codec.encode(bean));
        assertEquals(bean.getTotal(), decimal.decode(protobuf.getTotal()));
        assertEquals(bean.getAllocated(), decimal.decode(protobuf.getAllocated()));
        assertEquals(bean.getUnallocated(), decimal.decode(protobuf.getUnallocated()));
        final var type = switch (bean.getType()) {
            case Unallocated.TYPE -> AllocationStateMessage.Type.UNALLOCATED;
            case PartiallyAllocated.TYPE -> AllocationStateMessage.Type.PARTIALLY_ALLOCATED;
            case Allocated.TYPE -> AllocationStateMessage.Type.ALLOCATED;
            default -> throw new IllegalStateException("Unexpected value: " + bean.getType());
        };
        assertSame(type, protobuf.getType());
    }

    @ParameterizedTest
    @MethodSource("types")
    @DisplayName("AllocationStateCodec::decode protobuf to bean")
    public void codec_protobuf_to_bean(@Nonnull final AllocationStateMessage.Type type) {
        final var protobuf = AllocationStateMessage.newBuilder()
            .setType(type)
            .setTotal(decimal.encode(TEN))
            .setAllocated(decimal.encode(ONE))
            .setUnallocated(decimal.encode(TWO))
            .build();

        final var bean = assertDoesNotThrow(() -> codec.decode(protobuf));
        final var expected = switch (type) {
            case UNALLOCATED -> new Unallocated(TEN);
            case PARTIALLY_ALLOCATED -> new PartiallyAllocated(TEN, ONE, TWO);
            case ALLOCATED -> new Allocated(TEN);
            default -> throw new IllegalStateException("Unexpected value: " + type);
        };
        assertEquals(expected, bean);
    }

    @Nonnull
    private static Stream<Arguments> states() {
        return Stream.of(
            Arguments.of(new Unallocated(TEN)),
            Arguments.of(new PartiallyAllocated(TEN, ONE, TWO)),
            Arguments.of(new Allocated(TEN))
        );
    }

    @Nonnull
    private static Stream<Arguments> types() {
        return Stream.of(
            Arguments.of(AllocationStateMessage.Type.UNALLOCATED),
            Arguments.of(AllocationStateMessage.Type.PARTIALLY_ALLOCATED),
            Arguments.of(AllocationStateMessage.Type.ALLOCATED)
        );
    }
}