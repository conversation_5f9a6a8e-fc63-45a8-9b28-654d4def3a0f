package global.symbio.billing.core.services.payment.codec.transfer;

import global.symbio.billing.core.country.persistence.api.CountryDataAccessObject;
import global.symbio.billing.core.currency.persistence.api.CurrencyDataAccessObject;
import global.symbio.billing.core.exception.sdk.BillingEntityUnmodifiableException;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AllocationDirection;
import global.symbio.billing.core.payment.persistence.api.allocation.account.AllocationType;
import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.payment.PaymentAccountAllocationDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.transfer.TransferInAccountAllocationDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.transfer.TransferOutAccountAllocationDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.allocation.state.AllocationState;
import global.symbio.billing.core.payment.persistence.api.payment.PaymentStatus;
import global.symbio.billing.core.payment.persistence.api.transfer.Transfer;
import global.symbio.billing.core.reference.persistence.api.reference.ReferenceType;
import global.symbio.billing.core.services.grpc.codec.country.GRPCCountryDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.currency.GRPCCurrencyDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.payment.method.GRPCPaymentMethodDataAccessObject;
import global.symbio.billing.core.services.grpc.codec.reference.GRPCReferenceDataAccessObject;
import global.symbio.billing.core.services.payment.codec.allocation.GRPCPaymentAccountAllocationDataAccessObject;
import global.symbio.billing.core.services.payment.codec.allocation.GRPCTransferInAccountAllocationDataAccessObject;
import global.symbio.billing.core.services.payment.codec.allocation.GRPCTransferOutAccountAllocationDataAccessObject;
import global.symbio.billing.core.services.payment.codec.payment.GRPCPaymentDataAccessObject;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

public class TestGRPCTransferDataAccessObject {

    private static final UUID ID = UUID.randomUUID();
    private static final TransferOutAccountAllocationDataAccessObject<?> TRANSFER_OUT = transferOutAccountAllocation();
    private static final TransferInAccountAllocationDataAccessObject<?> TRANSFER_IN = transferInAccountAllocation();
    private static final CurrencyDataAccessObject CURRENCY = new GRPCCurrencyDataAccessObject(458, "MYR", "Malaysian Ringgit", "MYR");
    private static final BigDecimal AMOUNT = new BigDecimal("100.00");
    private static final CountryDataAccessObject COUNTRY = new GRPCCountryDataAccessObject(458, "Malaysia", "MYR");
    private static final ZonedDateTime TIMESTAMP = ZonedDateTime.now();
    private static final String DESCRIPTION = "description";
    private static final PaymentAccountAllocationDataAccessObject<?> ORIGIN = paymentAccountAllocation();

    private GRPCTransferDataAccessObject transfer;

    @BeforeEach
    public void setup() {
        transfer = new GRPCTransferDataAccessObject(ID, TRANSFER_IN, TRANSFER_OUT, CURRENCY, AMOUNT, DESCRIPTION, COUNTRY, ORIGIN, TIMESTAMP);
    }

    @Test
    @DisplayName("GRPCTransferDataAccessObject::new rejects null constructor arguments")
    public void grpc_transfer_data_access_object_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new GRPCTransferDataAccessObject(null, TRANSFER_IN, TRANSFER_OUT, CURRENCY, AMOUNT, DESCRIPTION, COUNTRY, ORIGIN, TIMESTAMP));
        assertThrows(NullPointerException.class, () -> new GRPCTransferDataAccessObject(ID, TRANSFER_IN, null, CURRENCY, AMOUNT, DESCRIPTION, COUNTRY, ORIGIN, TIMESTAMP));
        assertThrows(NullPointerException.class, () -> new GRPCTransferDataAccessObject(ID, null, TRANSFER_OUT, CURRENCY, AMOUNT, DESCRIPTION, COUNTRY, ORIGIN, TIMESTAMP));
        assertThrows(NullPointerException.class, () -> new GRPCTransferDataAccessObject(ID, TRANSFER_IN, TRANSFER_OUT, null, AMOUNT, DESCRIPTION, COUNTRY, ORIGIN, TIMESTAMP));
        assertThrows(NullPointerException.class, () -> new GRPCTransferDataAccessObject(ID, TRANSFER_IN, TRANSFER_OUT, CURRENCY, null, DESCRIPTION, COUNTRY, ORIGIN, TIMESTAMP));
        assertDoesNotThrow(() -> new GRPCTransferDataAccessObject(ID, TRANSFER_IN, TRANSFER_OUT, CURRENCY, AMOUNT, null, COUNTRY, ORIGIN, TIMESTAMP));
        assertThrows(NullPointerException.class, () -> new GRPCTransferDataAccessObject(ID, TRANSFER_IN, TRANSFER_OUT, CURRENCY, AMOUNT, DESCRIPTION, null, ORIGIN, TIMESTAMP));
        assertThrows(NullPointerException.class, () -> new GRPCTransferDataAccessObject(ID, TRANSFER_IN, TRANSFER_OUT, CURRENCY, AMOUNT, DESCRIPTION, COUNTRY, null, TIMESTAMP));
        assertThrows(NullPointerException.class, () -> new GRPCTransferDataAccessObject(ID, TRANSFER_IN, TRANSFER_OUT, CURRENCY, AMOUNT, DESCRIPTION, COUNTRY, ORIGIN, null));
    }

    @Test
    @DisplayName("GRPCTransferDataAccessObject::identifier is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_transfer_data_access_object_identifier_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(Transfer.class, ID, "identifier");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> transfer.identifier(ID));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCTransferDataAccessObject::transferOut is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_transfer_data_access_object_transfer_out_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(Transfer.class, ID, "transferOut");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> transfer.transferOut(TRANSFER_OUT));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCTransferDataAccessObject::transferIn is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_transfer_data_access_object_transfer_in_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(Transfer.class, ID, "transferIn");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> transfer.transferIn(TRANSFER_IN));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCTransferDataAccessObject::currency is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_transfer_data_access_object_currency_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(Transfer.class, ID, "currency");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> transfer.currency(CURRENCY));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCTransferDataAccessObject::amount is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_transfer_data_access_object_amount_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(Transfer.class, ID, "amount");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> transfer.amount(AMOUNT));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCTransferDataAccessObject::description is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_transfer_data_access_object_description_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(Transfer.class, ID, "description");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> transfer.description(DESCRIPTION));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCTransferDataAccessObject::country is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_transfer_data_access_object_country_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(Transfer.class, ID, "country");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> transfer.country(COUNTRY));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCTransferDataAccessObject::origin is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_transfer_data_access_object_origin_in_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(Transfer.class, ID, "origin");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> transfer.origin(ORIGIN));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Test
    @DisplayName("GRPCTransferDataAccessObject::timestamp is immutable and throws BillingEntityUnmodifiableException")
    public void grpc_transfer_data_access_object_timestamp_is_immutable() {
        final var expected = new BillingEntityUnmodifiableException(Transfer.class, ID, "timestamp");
        final var cause = assertThrows(BillingEntityUnmodifiableException.class, () -> transfer.timestamp(TIMESTAMP));
        assertEquals(expected.getMessage(), cause.getMessage());
    }

    @Nonnull
    private static GRPCTransferInAccountAllocationDataAccessObject transferInAccountAllocation() {
        final var currency = new GRPCCurrencyDataAccessObject(458, "MYR", "Malaysian Ringgit", "MYR");
        final var country = new GRPCCountryDataAccessObject(458, "Malaysia", "MYR");
        return new GRPCTransferInAccountAllocationDataAccessObject(
            UUID.randomUUID(),
            UUID.randomUUID(),
            AllocationType.TRANSFER_IN,
            country,
            currency,
            BigDecimal.ONE,
            AllocationState.create(BigDecimal.ONE),
            AllocationDirection.IN,
            ZonedDateTime.now(),
            ZonedDateTime.now(),
            ZonedDateTime.now(),
            UUID.randomUUID()
        );
    }

    @Nonnull
    private static GRPCTransferOutAccountAllocationDataAccessObject transferOutAccountAllocation() {
        final var currency = new GRPCCurrencyDataAccessObject(458, "MYR", "Malaysian Ringgit", "MYR");
        final var country = new GRPCCountryDataAccessObject(458, "Malaysia", "MYR");
        return new GRPCTransferOutAccountAllocationDataAccessObject(
            UUID.randomUUID(),
            UUID.randomUUID(),
            AllocationType.TRANSFER_OUT,
            country,
            currency,
            BigDecimal.ONE,
            AllocationState.create(BigDecimal.ONE),
            AllocationDirection.OUT,
            ZonedDateTime.now(),
            ZonedDateTime.now(),
            ZonedDateTime.now(),
            UUID.randomUUID()
        );
    }

    @Nonnull
    private static GRPCPaymentAccountAllocationDataAccessObject paymentAccountAllocation() {
        final var identifier = UUID.randomUUID();
        final var account = UUID.randomUUID();
        final var currency = new GRPCCurrencyDataAccessObject(458, "MYR", "Malaysian Ringgit", "MYR");
        final var country = new GRPCCountryDataAccessObject(458, "Malaysia", "MY");
        final var method = new GRPCPaymentMethodDataAccessObject(1, "BANK_TRANSFER", "Bank Transfer");
        final var reference = new GRPCReferenceDataAccessObject(UUID.randomUUID(), 1L, "MY", "CO", 2, ReferenceType.PAYMENT, "MYCOPYMT00000000012");
        final var payment = new GRPCPaymentDataAccessObject(UUID.randomUUID(), UUID.randomUUID(), method, currency, new BigDecimal("100.00"), ZonedDateTime.now(), PaymentStatus.ACTIVE, "description", country, reference,  ZonedDateTime.now());
        return new GRPCPaymentAccountAllocationDataAccessObject(
            identifier,
            account,
            AllocationType.PAYMENT,
            country,
            currency,
            new BigDecimal("100.00"),
            AllocationState.create(new BigDecimal("100.00")),
            AllocationDirection.IN,
            ZonedDateTime.now(),
            ZonedDateTime.now(),
            ZonedDateTime.now(),
            payment
        );
    }
}
