package global.symbio.billing.core.services.rest;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class ServiceDefinitions {

    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static final class Header {
        public static final String API_KEY = "X-API-Key";
        public static final String ORIGINATING_SERVICE_ID = "X-Originating-Service-Id";
        public static final String ENVIRONMENT_NAME = "X-Environment";
        public static final String COUNTRY_NAME = "X-Country";

        @NoArgsConstructor(access = AccessLevel.PRIVATE)
        public static final class Value {
            public static final String ORIGINATING_SERVICE_ID = "billing-${micronaut.application.name}-service";
        }
    }

    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static final class Retry {
        public static final String ATTEMPTS = "5";
        public static final String DELAY = "2s";
    }

    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static final class Plexus {

        @NoArgsConstructor(access = AccessLevel.PRIVATE)
        public static final class Gateway {

            @NoArgsConstructor(access = AccessLevel.PRIVATE)
            public static final class MY {
                public static final String TOKEN = "${services.my.plexus.gateway.token}";
                public static final String HEALTH_URL = "${services.my.plexus.gateway.url}/healthCheck";
            }

            @NoArgsConstructor(access = AccessLevel.PRIVATE)
            public static final class SG {
                public static final String TOKEN = "${services.sg.plexus.gateway.token}";
                public static final String HEALTH_URL = "${services.sg.plexus.gateway.url}/healthCheck";
            }

            @NoArgsConstructor(access = AccessLevel.PRIVATE)
            public static final class AU {
                public static final String TOKEN = "${services.au.plexus.gateway.token}";
                public static final String HEALTH_URL = "${services.au.plexus.gateway.url}/healthCheck";
            }
        }

        @NoArgsConstructor(access = AccessLevel.PRIVATE)
        public static final class Notification {

            @NoArgsConstructor(access = AccessLevel.PRIVATE)
            public static final class Global {
                public static final String URL = "${services.go.plexus.notification.url}";
                public static final String HEALTH_URL = URL + "/v2/healthCheck";
                public static final String ENV = "${services.go.plexus.notification.environment}";
            }
        }

        @NoArgsConstructor(access = AccessLevel.PRIVATE)
        public static final class Audit {

            @NoArgsConstructor(access = AccessLevel.PRIVATE)
            public static final class Global {
                public static final String URL = "${services.go.plexus.audit.url}";
            }
        }

        @NoArgsConstructor(access = AccessLevel.PRIVATE)
        public static final class Account {

            @NoArgsConstructor(access = AccessLevel.PRIVATE)
            public static final class MY {
                public static final String URL = "${services.my.plexus.gateway.url}/account";
                public static final String HEALTH_URL = "${services.my.plexus.account.health.url}";
            }

            @NoArgsConstructor(access = AccessLevel.PRIVATE)
            public static final class SG {
                public static final String URL = "${services.sg.plexus.gateway.url}/account";
                public static final String HEALTH_URL = "${services.sg.plexus.account.health.url}";
            }

            @NoArgsConstructor(access = AccessLevel.PRIVATE)
            public static final class AU {
                public static final String URL = "${services.au.plexus.gateway.url}/account";
                public static final String QUERY_PARAM_COUNTRY_CODE = "?countryCode=AU";
                public static final String HEALTH_URL = "${services.au.plexus.account.health.url}";
            }

            @NoArgsConstructor(access = AccessLevel.PRIVATE)
            public static final class NZ {
                public static final String QUERY_PARAM_COUNTRY_CODE = "?countryCode=NZ";
            }
        }

        @NoArgsConstructor(access = AccessLevel.PRIVATE)
        public static final class Customer {

            @NoArgsConstructor(access = AccessLevel.PRIVATE)
            public static final class Global {
                public static final String HEALTH_URL = "${services.go.plexus.customer.health.url}";
            }

            @NoArgsConstructor(access = AccessLevel.PRIVATE)
            public static final class MY {
                public static final String URL = "${services.my.plexus.gateway.url}/customer";
            }

            @NoArgsConstructor(access = AccessLevel.PRIVATE)
            public static final class SG {
                public static final String URL = "${services.sg.plexus.gateway.url}/customer";
            }

            @NoArgsConstructor(access = AccessLevel.PRIVATE)
            public static final class AU {
                public static final String URL = "${services.au.plexus.gateway.url}/customer";
            }
        }
    }

    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static final class Sonar {

        @NoArgsConstructor(access = AccessLevel.PRIVATE)
        public static final class CustomerBridge {

            @NoArgsConstructor(access = AccessLevel.PRIVATE)
            public static final class MY {
                public static final String URL = "${services.my.sonar.customer.bridge.url}";
                public static final String HEALTH_URL = URL +"/health";
                public static final String TOKEN = "${services.my.sonar.customer.bridge.token}";
            }

            @NoArgsConstructor(access = AccessLevel.PRIVATE)
            public static final class SG {
                public static final String URL = "${services.sg.sonar.customer.bridge.url}";
                public static final String HEALTH_URL = URL + "/health";
                public static final String TOKEN = "${services.sg.sonar.customer.bridge.token}";
            }

            @NoArgsConstructor(access = AccessLevel.PRIVATE)
            public static final class AU {
                public static final String URL = "${services.au.sonar.customer.bridge.url}";
                public static final String HEALTH_URL = URL + "/health";
                public static final String TOKEN = "${services.au.sonar.customer.bridge.token}";
            }
        }
    }

    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static final class Billing {

        @NoArgsConstructor(access = AccessLevel.PRIVATE)
        public static final class Invoice {

            @NoArgsConstructor(access = AccessLevel.PRIVATE)
            public static final class Global {
                public static final String HEALTH_URL = "${services.go.billing.invoice.health.url}";
            }
        }

        @NoArgsConstructor(access = AccessLevel.PRIVATE)
        public static final class Ledger {

            @NoArgsConstructor(access = AccessLevel.PRIVATE)
            public static final class Global {
                public static final String HEALTH_URL = "${services.go.billing.ledger.health.url}";
            }
        }

        @NoArgsConstructor(access = AccessLevel.PRIVATE)
        public static final class ProductBridge {

            @NoArgsConstructor(access = AccessLevel.PRIVATE)
            public static final class Global {
                public static final String HEALTH_URL = "${services.go.billing.product.bridge.health.url}";
            }
        }
    }
}
