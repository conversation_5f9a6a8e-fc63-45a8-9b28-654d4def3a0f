package global.symbio.billing.core.services.rest.plexus.service.i18n.customer.impl;

import com.fasterxml.uuid.impl.UUIDUtil;
import com.github.benmanes.caffeine.cache.CacheLoader;
import global.symbio.billing.core.i18n.localisation.LocationAware;
import global.symbio.billing.core.i18n.provider.LocalisedServiceProvider;
import global.symbio.billing.core.services.rest.plexus.model.customer.GetCustomerResponse;
import global.symbio.billing.core.services.rest.plexus.model.customer.PlexusCustomer;
import global.symbio.billing.core.services.rest.plexus.service.PlexusService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestLocationAwarePlexusCustomerCacheLoader {

    @Mock
    private LocalisedServiceProvider provider;

    private CacheLoader<LocationAwarePlexusCustomerLookupRequest, PlexusCustomer> loader;

    @BeforeEach
    public void setup() {
        loader = new LocationAwarePlexusCustomerCacheLoader(provider);
    }

    @Test
    @DisplayName("LocationAwarePlexusCustomerCacheLoader::new rejects null constructor arguments")
    public void location_aware_plexus_customer_cache_loader_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new LocationAwarePlexusCustomerCacheLoader(null));
    }

    @ParameterizedTest
    @EnumSource(value = LookupTestcase.class, names = {"NULL", "FAILURE", "SUCCESS"})
    @DisplayName("LocationAwarePlexusCustomerCacheLoader::load invokes localised PlexusService customer lookup")
    public void location_aware_plexus_customer_cache_loader_load_invokes_plexus_service_customer_lookup(final LookupTestcase scenario) {
        final var plexus = mock(PlexusService.class);
        final var customer = scenario == LookupTestcase.SUCCESS ? mock(PlexusCustomer.class) : null;
        final var request = new LocationAwarePlexusCustomerLookupRequest(UUIDUtil.nilUUID(), "AU");
        final var response = scenario == null ? null : new GetCustomerResponse(customer, scenario == LookupTestcase.SUCCESS);
        when(provider.lookup(eq(PlexusService.class), any(LocationAware.class))).thenReturn(plexus);
        when(plexus.getCustomer(any(UUID.class))).thenReturn(response);
        final var result = assertDoesNotThrow(() -> loader.load(request));
        assertSame(customer, result);
        verify(plexus, times(1)).getCustomer(eq(UUIDUtil.nilUUID()));
    }

    public enum LookupTestcase {
        NULL,
        FAILURE,
        SUCCESS
    }
}
