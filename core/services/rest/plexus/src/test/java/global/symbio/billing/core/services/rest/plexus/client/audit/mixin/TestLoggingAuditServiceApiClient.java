package global.symbio.billing.core.services.rest.plexus.client.audit.mixin;

import global.symbio.billing.core.services.rest.plexus.client.audit.AuditServiceApiClient;
import global.symbio.billing.core.services.rest.plexus.model.audit.GetAuditRecordSetsResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestLoggingAuditServiceApiClient {

    @Mock
    private Logger log;

    @Mock
    private AuditServiceApiClient delegate;

    private LoggingAuditServiceApiClient client;

    @BeforeEach
    public void setup() {
        client = new LoggingAuditServiceApiClient(log, delegate);
    }

    @Test
    @DisplayName("LoggingAuditServiceApiClient::new rejects null constructor arguments")
    public void logging_audit_service_api_client_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new LoggingAuditServiceApiClient(null));
        assertThrows(NullPointerException.class, () -> new LoggingAuditServiceApiClient(log, null));
        assertThrows(NullPointerException.class, () -> new LoggingAuditServiceApiClient(null, delegate));
    }

    @Test
    @DisplayName("LoggingAuditServiceApiClient::getRecordSets logs and invokes method on delegate")
    public void logging_audit_service_api_client_get_recordsets_logs_and_invokes_delegate_method() {
        final var reference = UUID.fromString("0191ba6f-b17b-799d-9216-f9bd5c595a67");
        final var response = mock(GetAuditRecordSetsResponse.class);
        when(delegate.getRecordSets(any(UUID.class))).thenReturn(response);
        final var result = assertDoesNotThrow(() -> client.getRecordSets(reference));
        assertSame(response, result);
        verify(log, times(1)).info(anyString(), eq(reference));
        verify(delegate, times(1)).getRecordSets(eq(reference));
    }
}
