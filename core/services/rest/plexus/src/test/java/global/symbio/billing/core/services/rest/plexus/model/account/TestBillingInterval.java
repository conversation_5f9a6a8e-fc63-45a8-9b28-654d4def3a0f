package global.symbio.billing.core.services.rest.plexus.model.account;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.ZonedDateTime;

import static org.junit.jupiter.api.Assertions.assertEquals;

public class TestBillingInterval {

    @Test
    @DisplayName("Test next billing date when BillingInterval is DAILY")
    public void testDailyNext() {
        final var date = ZonedDateTime.now();
        final var nextDate = date.plusDays(1L);
        assertEquals(nextDate, BillingInterval.daily.next(date));
    }

    @Test
    @DisplayName("Test previous billing date when BillingInterval is DAILY")
    public void testDailyPrevious() {
        final var date = ZonedDateTime.now();
        final var previousDate = date.minusDays(1L);
        assertEquals(previousDate, BillingInterval.daily.previous(date));
    }

    @Test
    @DisplayName("Test next billing date when BillingInterval is WEEKLY")
    public void testWeeklyNext() {
        final var date = ZonedDateTime.now();
        final var nextDate = date.plusWeeks(1L);
        assertEquals(nextDate, BillingInterval.weekly.next(date));
    }

    @Test
    @DisplayName("Test previous billing date when BillingInterval is WEEKLY")
    public void testWeeklyPrevious() {
        final var date = ZonedDateTime.now();
        final var previousDate = date.minusWeeks(1L);
        assertEquals(previousDate, BillingInterval.weekly.previous(date));
    }

    @Test
    @DisplayName("Test next billing date when BillingInterval is FORTNIGHTLY")
    public void testFortnightlyNext() {
        final var date = ZonedDateTime.now();
        final var nextDate = date.plusWeeks(2L);
        assertEquals(nextDate, BillingInterval.fortnightly.next(date));
    }

    @Test
    @DisplayName("Test previous billing date when BillingInterval is FORTNIGHTLY")
    public void testFortnightlyPrevious() {
        final var date = ZonedDateTime.now();
        final var previousDate = date.minusWeeks(2L);
        assertEquals(previousDate, BillingInterval.fortnightly.previous(date));
    }

    @Test
    @DisplayName("Test next billing date when BillingInterval is MONTHLY")
    public void testMonthlyNext() {
        final var date = ZonedDateTime.now();
        final var nextDate = date.plusMonths(1L);
        assertEquals(nextDate, BillingInterval.monthly.next(date));
    }

    @Test
    @DisplayName("Test previous billing date when BillingInterval is MONTHLY")
    public void testMonthlyPrevious() {
        final var date = ZonedDateTime.now();
        final var previousDate = date.minusMonths(1L);
        assertEquals(previousDate, BillingInterval.monthly.previous(date));
    }

    @Test
    @DisplayName("Test next billing date when BillingInterval is FIRST_AND_SIXTEENTH_OF_MONTH and date is before the 16th")
    public void testFirstAndSixteenthOfMonthNext_before16th() {
        final var date = ZonedDateTime.parse("2023-03-15T10:00:00Z");
        final var nextDate = ZonedDateTime.parse("2023-03-16T10:00:00Z");
        assertEquals(nextDate, BillingInterval.first_and_sixteenth_of_month.next(date));
    }

    @Test
    @DisplayName("Test previous billing date when BillingInterval is FIRST_AND_SIXTEENTH_OF_MONTH and date is before the 16th")
    public void testFirstAndSixteenthOfMonthPrevious_before16th() {
        final var date = ZonedDateTime.parse("2023-03-15T10:00:00Z");
        final var previousDate = ZonedDateTime.parse("2023-03-01T10:00:00Z");
        assertEquals(previousDate, BillingInterval.first_and_sixteenth_of_month.previous(date));
    }

    @Test
    @DisplayName("Test previous billing date when BillingInterval is FIRST_AND_SIXTEENTH_OF_MONTH and date is equal to the 1st")
    public void testFirstAndSixteenthOfMonthPrevious_equal1st() {
        final var date = ZonedDateTime.parse("2023-03-01T10:00:00Z");
        final var previousDate = ZonedDateTime.parse("2023-02-16T10:00:00Z");
        assertEquals(previousDate, BillingInterval.first_and_sixteenth_of_month.previous(date));
    }

    @Test
    @DisplayName("Test previous billing date when BillingInterval is FIRST_AND_SIXTEENTH_OF_MONTH and date is equal to the 16th")
    public void testFirstAndSixteenthOfMonthPrevious_equal16th() {
        final var date = ZonedDateTime.parse("2023-03-16T10:00:00Z");
        final var previousDate = ZonedDateTime.parse("2023-03-01T10:00:00Z");
        assertEquals(previousDate, BillingInterval.first_and_sixteenth_of_month.previous(date));
    }

    @Test
    @DisplayName("Test next billing date when BillingInterval is FIRST_AND_SIXTEENTH_OF_MONTH and date is after 16th")
    public void testFirstAndSixteenthOfMonthNext_after16th() {
        final var date = ZonedDateTime.parse("2023-03-17T10:00:00Z");
        final var nextDate = ZonedDateTime.parse("2023-04-01T10:00:00Z");
        assertEquals(nextDate, BillingInterval.first_and_sixteenth_of_month.next(date));
    }

    @Test
    @DisplayName("Test previous billing date when BillingInterval is FIRST_AND_SIXTEENTH_OF_MONTH and date is after 16th")
    public void testFirstAndSixteenthOfMonthPrevious_after16th() {
        final var date = ZonedDateTime.parse("2023-03-17T10:00:00Z");
        final var previousDate = ZonedDateTime.parse("2023-03-16T10:00:00Z");
        assertEquals(previousDate, BillingInterval.first_and_sixteenth_of_month.previous(date));
    }

    @Test
    @DisplayName("Test next billing date when BillingInterval is quarterly")
    public void testQuarterlyNext() {
        final var date = ZonedDateTime.now();
        final var nextDate = date.plusMonths(3L);
        assertEquals(nextDate, BillingInterval.quarterly.next(date));
    }

    @Test
    @DisplayName("Test previous billing date when BillingInterval is quarterly")
    public void testQuarterlyPrevious() {
        final var date = ZonedDateTime.now();
        final var previousDate = date.minusMonths(3L);
        assertEquals(previousDate, BillingInterval.quarterly.previous(date));
    }

    @Test
    @DisplayName("Test next billing date when BillingInterval is yearly")
    public void testYearlyNext() {
        final var date = ZonedDateTime.now();
        final var nextDate = date.plusYears(1L);
        assertEquals(nextDate, BillingInterval.yearly.next(date));
    }

    @Test
    @DisplayName("Test previous billing date when BillingInterval is yearly")
    public void testYearlyPrevious() {
        final var date = ZonedDateTime.now();
        final var previousDate = date.minusYears(1L);
        assertEquals(previousDate, BillingInterval.yearly.previous(date));
    }
}