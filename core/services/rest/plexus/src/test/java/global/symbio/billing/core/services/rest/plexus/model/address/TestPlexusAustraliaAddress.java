package global.symbio.billing.core.services.rest.plexus.model.address;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.micronaut.test.extensions.junit5.annotation.MicronautTest;
import jakarta.inject.Inject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@MicronautTest
public class TestPlexusAustraliaAddress {

    private PlexusAustraliaAddress address;

    @Inject
    private ObjectMapper mapper;

    private static final String JSON = """
            {
              "id" : "11111111-d7c0-40a3-bfe4-37d2f9698ada",
              "plexid" : "plexid:customer:address:11111111-d7c0-40a3-bfe4-37d2f9698ada",
              "country" : "AU",
              "postcode" : "2170",
              "buildingType" : "building type",
              "buildingName" : "building name",
              "unitNumber" : "1",
              "levelNumber" : "2",
              "levelType" : "level type",
              "lotNumber" : "3",
              "roadNumber1" : "4",
              "roadNumber2" : "5",
              "roadName" : "road name",
              "roadSuffix" : "road suffix",
              "roadType" : "road type",
              "locality" : "Sydney",
              "state" : "NSW",
              "postalDeliveryType" : "postal delivery type",
              "type" : "Australia"
            }""".stripIndent();

    @BeforeEach
    public void setup() {
        address = new PlexusAustraliaAddress(
                UUID.fromString("11111111-d7c0-40a3-bfe4-37d2f9698ada"),
                "plexid:customer:address:11111111-d7c0-40a3-bfe4-37d2f9698ada",
                "AU",
                "2170",
                "building type",
                "building name",
                "1",
                "2",
                "level type",
                "3",
                "4",
                "5",
                "road name",
                "road suffix",
                "road type",
                "Sydney",
                "NSW",
                "postal delivery type"
        );
    }


    @Test
    @DisplayName("PlexusAustraliaAddress is subclass of PlexusAddress")
    public void plexus_australia_address_is_subtype_of_plexus_address() {
        assertInstanceOf(PlexusAustraliaAddress.class, address);
        assertInstanceOf(PlexusAddress.class, address);
    }

    @Test
    @DisplayName("PlexusAustraliaAddress#TYPE equals Australia and PlexusAustraliaAddress#type equals PlexusAustraliaAddress#TYPE")
    public void plexus_australia_address_type_equals_australia() {
        assertEquals("Australia", PlexusAustraliaAddress.TYPE);
        assertEquals(PlexusAustraliaAddress.TYPE, address.getType());
        assertSame(PlexusAustraliaAddress.TYPE, address.getType());
    }

    @Test
    @DisplayName("PlexusAustraliaAddress#getCountryName returns Australia")
    public void plexus_australia_address_get_country_name_returns_australia() {
        assertEquals("Australia", address.getCountryName());
    }

    @Test
    @DisplayName("PlexusAustraliaAddress#getCompleteAddress returns complete address")
    public void plexus_australia_address_get_complete_address_returns_complete_address() {
        assertEquals("building type, building name, 1, 2, 3, 4, 5, road name, road suffix, road type, Sydney, NSW", address.getCompleteAddress());
    }

    @Test
    @DisplayName("PlexusAustraliaAddress deserialization/serialization is correct")
    public void plexus_australia_address_deserialization_serialization() throws JsonProcessingException {
        //String -> PlexusAustraliaAddress
        final var base = assertInstanceOf(PlexusAustraliaAddress.class, mapper.readValue(JSON, PlexusAddress.class));
        final var impl = assertInstanceOf(PlexusAustraliaAddress.class, mapper.readValue(JSON, PlexusAustraliaAddress.class));
        assertEquals(base, impl);

        //PlexusAustraliaAddress -> String
        final var json = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(impl);

        //validate original JSON is equal to generated string after deserialization/serialization. ignore line separators
        assertEquals(JSON.replaceAll("\\s+",""), json.replaceAll("\\s+",""));
    }
}
