package global.symbio.billing.core.services.rest.plexus.model.address;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.micronaut.test.extensions.junit5.annotation.MicronautTest;
import jakarta.inject.Inject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@MicronautTest
public class TestPlexusInternationalAddress {

    private PlexusInternationalAddress address;

    @Inject
    private ObjectMapper mapper;

    private static final String JSON = """
            {   
              "id" : "11111111-d7c0-40a3-bfe4-37d2f9698ada",
              "plexid" : "plexid:customer:address:11111111-d7c0-40a3-bfe4-37d2f9698ada",
              "country" : "MY",
              "postcode" : "28000",
              "floor" : "1",
              "subpremise" : "2",
              "premise" : "3",
              "streetNumber" : "4",
              "route" : "route",
              "locality" : "locality",
              "district" : "district",
              "state" : "Pahang",
              "postalTown" : "postal town",
              "postalLocation" : "postal location",
              "type" : "International"
            }""".stripIndent();

    @BeforeEach
    public void setup() {
        address = new PlexusInternationalAddress(
                UUID.fromString("11111111-d7c0-40a3-bfe4-37d2f9698ada"),
                "plexid:customer:address:11111111-d7c0-40a3-bfe4-37d2f9698ada",
                "MY",
                "28000",
                "1",
                "2",
                "3",
                "4",
                "route",
                "locality",
                "district",
                "Pahang",
                "postal town",
                "postal location"
        );
    }


    @Test
    @DisplayName("PlexusInternationalAddress is subclass of PlexusAddress")
    public void plexus_international_address_is_subtype_of_plexus_address() {
        assertInstanceOf(PlexusInternationalAddress.class, address);
        assertInstanceOf(PlexusAddress.class, address);
    }

    @Test
    @DisplayName("PlexusInternationalAddress#TYPE equals International and PlexusInternationalAddress#type equals PlexusInternationalAddress#TYPE")
    public void plexus_international_address_type_equals_australia() {
        assertEquals("International", PlexusInternationalAddress.TYPE);
        assertEquals(PlexusInternationalAddress.TYPE, address.getType());
        assertSame(PlexusInternationalAddress.TYPE, address.getType());
    }

    @Test
    @DisplayName("PlexusInternationalAddress#getCountryName returns Malaysia")
    public void plexus_international_address_get_country_name_returns_australia() {
        assertEquals("Malaysia", address.getCountryName());
    }

    @Test
    @DisplayName("PlexusInternationalAddress#getCompleteAddress returns complete address")
    public void plexus_international_address_get_complete_address_returns_complete_address() {
        assertEquals("1, 2, 3, 4, route, locality, district, Pahang", address.getCompleteAddress());
    }

    @Test
    @DisplayName("PlexusInternationalAddress deserialization/serialization is correct")
    public void plexus_international_address_deserialization_serialization() throws JsonProcessingException {
        //String -> PlexusInternationalAddress
        final var base = assertInstanceOf(PlexusInternationalAddress.class, mapper.readValue(JSON, PlexusAddress.class));
        final var impl = assertInstanceOf(PlexusInternationalAddress.class, mapper.readValue(JSON, PlexusInternationalAddress.class));
        assertEquals(base, impl);

        //PlexusInternationalAddress -> String
        final var json = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(impl);

        //validate original JSON is equal to generated string after deserialization/serialization. ignore line separators
        assertEquals(JSON.replaceAll("\\s+",""), json.replaceAll("\\s+",""));
    }
}
