package global.symbio.billing.core.services.rest.plexus.service.i18n.account.impl;

import com.fasterxml.uuid.impl.UUIDUtil;
import com.github.benmanes.caffeine.cache.AsyncLoadingCache;
import com.github.benmanes.caffeine.cache.LoadingCache;
import global.symbio.billing.core.services.rest.plexus.model.account.PlexusAccount;
import global.symbio.billing.core.services.rest.plexus.service.i18n.account.PlexusAccountService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestCachingLocationAwarePlexusAccountService {

    @Mock
    private LoadingCache<LocationAwarePlexusAccountLookupRequest, PlexusAccount> accounts;

    private PlexusAccountService service;

    @BeforeEach
    public void setup() {
        service = new CachingLocationAwarePlexusAccountService(accounts);
    }

    @Test
    @DisplayName("CachingLocationAwarePlexusAccountService::new rejects null constructor arguments")
    public void caching_location_aware_plexus_account_service_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new CachingLocationAwarePlexusAccountService((LoadingCache<LocationAwarePlexusAccountLookupRequest, PlexusAccount>) null));
        assertThrows(NullPointerException.class, () -> new CachingLocationAwarePlexusAccountService((AsyncLoadingCache<LocationAwarePlexusAccountLookupRequest, PlexusAccount>) null));
    }

    @Test
    @DisplayName("CachingLocationAwarePlexusAccountService::lookup returns account from cache when account in the cache")
    public void caching_location_aware_plexus_account_service_lookup_returns_account_from_cache_when_account_in_the_cache() {
        final var account = mock(PlexusAccount.class);
        when(accounts.get(any(LocationAwarePlexusAccountLookupRequest.class))).thenReturn(account);
        final var result = assertDoesNotThrow(() -> service.lookup(UUIDUtil.nilUUID(), "AU"));
        assertSame(account, result);
        verify(accounts, times(1)).get(eq(new LocationAwarePlexusAccountLookupRequest(UUIDUtil.nilUUID(), "AU")));
    }

    @Test
    @DisplayName("CachingLocationAwarePlexusAccountService::lookup returns null when account not in the cache")
    public void caching_location_aware_plexus_account_service_lookup_returns_null_when_account_not_in_cache() {
        when(accounts.get(any(LocationAwarePlexusAccountLookupRequest.class))).thenReturn(null);
        final var result = assertDoesNotThrow(() -> service.lookup(UUIDUtil.nilUUID(), "AU"));
        assertNull(result);
        verify(accounts, times(1)).get(eq(new LocationAwarePlexusAccountLookupRequest(UUIDUtil.nilUUID(), "AU")));
    }

    @Test
    @DisplayName("CachingLocationAwarePlexusAccountService::lookup throws exception when cache hit throws exception")
    public void caching_location_aware_plexus_account_service_lookup_throws_exception_when_cache_hit_throws_exception() {
        final var failure = new RuntimeException("Underlying exception");
        when(accounts.get(any(LocationAwarePlexusAccountLookupRequest.class))).thenThrow(failure);
        final var cause = assertThrows(RuntimeException.class, () -> service.lookup(UUIDUtil.nilUUID(), "AU"));
        assertSame(failure, cause);
        verify(accounts, times(1)).get(eq(new LocationAwarePlexusAccountLookupRequest(UUIDUtil.nilUUID(), "AU")));
    }
}