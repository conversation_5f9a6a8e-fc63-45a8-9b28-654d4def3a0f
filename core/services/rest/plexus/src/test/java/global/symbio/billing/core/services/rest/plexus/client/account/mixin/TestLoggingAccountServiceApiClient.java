package global.symbio.billing.core.services.rest.plexus.client.account.mixin;

import com.fasterxml.uuid.impl.UUIDUtil;
import global.symbio.billing.core.services.rest.plexus.client.account.AccountServiceApiClient;
import global.symbio.billing.core.services.rest.plexus.model.account.GetAccountResponse;
import global.symbio.billing.core.services.rest.plexus.model.account.GetAccountsResponse;
import global.symbio.billing.core.services.rest.plexus.model.account.PatchAccountRequest;
import global.symbio.billing.core.services.rest.plexus.model.response.BasePlexusApiResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestLoggingAccountServiceApiClient {

    @Mock
    private Logger log;

    @Mock
    private AccountServiceApiClient delegate;

    private LoggingAccountServiceApiClient client;

    @BeforeEach
    public void setup() {
        client = new LoggingAccountServiceApiClient(log, delegate);
    }

    @Test
    @DisplayName("LoggingAccountServiceApiClient::new rejects null constructor arguments")
    public void logging_account_service_api_client_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new LoggingAccountServiceApiClient(null));
        assertThrows(NullPointerException.class, () -> new LoggingAccountServiceApiClient(log, null));
        assertThrows(NullPointerException.class, () -> new LoggingAccountServiceApiClient(null, delegate));
    }

    @Test
    @DisplayName("LoggingAccountServiceApiClient::getBillableAccounts logs and invokes method on delegate")
    public void logging_account_service_api_client_get_billable_accounts_logs_and_invokes_delegate_method() {
        final var billingDate = "11/22/3333";
        final var limit = 999;
        final var response = mock(GetAccountsResponse.class);
        when(delegate.getBillableAccounts(anyString(), anyInt())).thenReturn(response);
        final var result = assertDoesNotThrow(() -> client.getBillableAccounts(billingDate, limit));
        assertSame(response, result);
        verify(log, times(1)).info(anyString(), eq(billingDate), eq(limit));
        verify(delegate, times(1)).getBillableAccounts(eq(billingDate), eq(limit));
    }

    @Test
    @DisplayName("LoggingAccountServiceApiClient::getAccount logs and invokes method on delegate")
    public void logging_account_service_api_client_get_account_logs_and_invokes_delegate_method() {
        final var account = UUIDUtil.nilUUID();
        final var response = mock(GetAccountResponse.class);
        when(delegate.getAccount(any(UUID.class))).thenReturn(response);
        final var result = assertDoesNotThrow(() -> client.getAccount(account));
        assertSame(response, result);
        verify(log, times(1)).info(anyString(), eq(account));
        verify(delegate, times(1)).getAccount(eq(account));
    }

    @Test
    @DisplayName("LoggingAccountServiceApiClient::patchAccount logs and invokes method on delegate")
    public void logging_account_service_api_client_patch_account_logs_and_invokes_delegate_method() {
        final var account = UUIDUtil.nilUUID();
        final var request = mock(PatchAccountRequest.class);
        final var response = mock(BasePlexusApiResponse.class);
        when(delegate.patchAccount(any(UUID.class), any(PatchAccountRequest.class))).thenReturn(response);
        final var result = assertDoesNotThrow(() -> client.patchAccount(account, request));
        assertSame(response, result);
        verify(log, times(1)).info(anyString(), eq(account), eq(request));
        verify(delegate, times(1)).patchAccount(eq(account), eq(request));
    }

    @Test
    @DisplayName("LoggingAccountServiceApiClient::getAccounts logs and invokes method on delegate")
    public void logging_account_service_api_client_get_accounts_logs_and_invokes_delegate_method() {
        final var customer = UUID.randomUUID();
        final var response = mock(GetAccountsResponse.class);
        when(delegate.getAccounts(any(UUID.class))).thenReturn(response);
        final var result = assertDoesNotThrow(() -> client.getAccounts(customer));
        assertSame(response, result);
        verify(log, times(1)).info(anyString(), eq(customer));
        verify(delegate, times(1)).getAccounts(eq(customer));
    }
}