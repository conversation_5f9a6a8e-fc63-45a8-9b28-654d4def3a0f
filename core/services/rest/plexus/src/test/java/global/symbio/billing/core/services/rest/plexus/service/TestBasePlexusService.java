package global.symbio.billing.core.services.rest.plexus.service;

import global.symbio.billing.core.i18n.LocalisationSettings;
import global.symbio.billing.core.services.rest.plexus.client.account.AccountServiceApiClient;
import global.symbio.billing.core.services.rest.plexus.client.customer.CustomerServiceApiClient;
import global.symbio.billing.core.services.rest.plexus.client.notification.NotificationServiceApiClient;
import global.symbio.billing.core.services.rest.plexus.model.account.GetAccountResponse;
import global.symbio.billing.core.services.rest.plexus.model.account.GetAccountsResponse;
import global.symbio.billing.core.services.rest.plexus.model.address.GetAddressResponse;
import global.symbio.billing.core.services.rest.plexus.model.customer.GetCustomerResponse;
import global.symbio.billing.core.services.rest.plexus.model.customer.GetPersonResponse;
import global.symbio.billing.core.services.rest.plexus.model.notification.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.File;
import java.time.ZonedDateTime;
import java.util.Set;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestBasePlexusService {

    @Mock
    private LocalisationSettings localisation;

    @Mock
    private AccountServiceApiClient accounts;

    @Mock
    private CustomerServiceApiClient customers;

    @Mock
    private NotificationServiceApiClient notifications;

    private BasePlexusService plexus;

    @BeforeEach
    public void setup() {
        plexus = new BasePlexusService(localisation, accounts, customers, notifications);
    }

    @Test
    @DisplayName("BasePlexusService::new rejects null constructor arguments")
    public void base_plexus_service_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new BasePlexusService(null, accounts, customers, notifications));
        assertThrows(NullPointerException.class, () -> new BasePlexusService(localisation, null, customers, notifications));
        assertThrows(NullPointerException.class, () -> new BasePlexusService(localisation, accounts, null, notifications));
        assertThrows(NullPointerException.class, () -> new BasePlexusService(localisation, accounts, customers, null));
    }

    @Test
    @DisplayName("BasePlexusService::localisation returns LocalisationSettings")
    public void base_plexus_service_localisation_returns_localisation_settings() {
        assertSame(localisation, plexus.localisation());
    }

    @Test
    @DisplayName("BasePlexusService::getBillableAccounts invokes AccountServiceClient to retrieve billable accounts")
    public void base_plexus_service_get_billable_accounts_invokes_account_service_api_client() {
        final var timestamp = ZonedDateTime.now();
        final var expected = mock(GetAccountsResponse.class);

        when(accounts.getBillableAccounts(anyString(), anyInt())).thenReturn(expected);

        final var response = assertDoesNotThrow(() -> plexus.getBillableAccounts(timestamp));

        verify(accounts, times(1)).getBillableAccounts(anyString(), anyInt());
        assertEquals(expected, response);
    }

    @Test
    @DisplayName("BasePlexusService::getAccount invokes AccountServiceClient to retrieve account")
    public void base_plexus_service_get_account_invokes_account_service_api_client() {
        final var uuid = UUID.randomUUID();
        final var expected = mock(GetAccountResponse.class);

        when(accounts.getAccount(uuid)).thenReturn(expected);

        final var response = assertDoesNotThrow(() -> plexus.getAccount(uuid));

        verify(accounts, times(1)).getAccount(uuid);
        assertEquals(expected, response);
    }

    @Test
    @DisplayName("BasePlexusService::getAddress invokes CustomerServiceClient to retrieve address")
    public void base_plexus_service_get_address_invokes_customer_service_api_client() {
        final var expected = mock(GetAddressResponse.class);
        when(customers.getAddress(any(), any())).thenReturn(expected);

        final var response = assertDoesNotThrow(() -> plexus.getAddress(any(), any()));

        verify(customers, times(1)).getAddress(any(), any());
        assertEquals(expected, response);
    }

    @Test
    @DisplayName("BasePlexusService::getCustomer invokes CustomerServiceClient to retrieve customer")
    public void base_plexus_service_get_customer_invokes_customer_service_api_client() {
        final var expected = mock(GetCustomerResponse.class);
        when(customers.getCustomer(any())).thenReturn(expected);

        final var response = assertDoesNotThrow(() -> plexus.getCustomer(any()));

        verify(customers, times(1)).getCustomer(any());
        assertEquals(expected, response);
    }

    @Test
    @DisplayName("BasePlexusService::getPerson invokes CustomerServiceApiClient to retrieve person information")
    public void base_plexus_service_get_person_invokes_customer_service_api_client() {
        final var expected = mock(GetPersonResponse.class);
        when(customers.getPerson(any(), any())).thenReturn(expected);

        final var response = assertDoesNotThrow(() -> plexus.getPerson(UUID.randomUUID(), UUID.randomUUID()));

        verify(customers, times(1)).getPerson(any(), any());
        assertEquals(expected, response);
    }

    @Test
    @DisplayName("BasePlexusService::uploadAttachment invokes NotificationServiceClient to retrieve notificationId after upload")
    public void base_plexus_service_upload_attachment_invokes_notification_service_api_client() {
        final var expected = mock(NotificationResponse.class);
        when(notifications.uploadAttachment(any())).thenReturn(expected);

        final var response = assertDoesNotThrow(() -> plexus.uploadAttachment("invoice.pdf", new byte[1]));

        verify(notifications, times(1)).uploadAttachment(any());
        assertEquals(expected, response);
    }

    @Test
    @DisplayName("BasePlexusService::uploadAttachment invokes NotificationServiceClient when passing in a File argument")
    public void base_plexus_service_upload_attachment_invokes_notification_service_api_client_when_passing_in_a_file_argument() {
        final var file = mock(File.class);
        final var expected = mock(NotificationResponse.class);
        when(notifications.uploadAttachment(any())).thenReturn(expected);

        final var response = assertDoesNotThrow(() -> plexus.uploadAttachment("invoice.pdf", file));

        verify(notifications, times(1)).uploadAttachment(any());
        assertEquals(expected, response);
    }

    @Test
    @DisplayName("BasePlexusService::uploadAttachment invokes NotificationServiceClient when passing in a File argument with attachmentRequestId")
    public void base_plexus_service_upload_attachment_invokes_notification_service_api_client_when_passing_in_a_file_argument_with_attachment_request() {
        final var file = mock(File.class);
        final var expected = mock(NotificationResponse.class);
        when(notifications.uploadAttachment(any())).thenReturn(expected);

        final var response = assertDoesNotThrow(() -> plexus.uploadAttachment("invoice.pdf", file, "attachmentRequestId"));

        verify(notifications, times(1)).uploadAttachment(any());
        assertEquals(expected, response);
    }

    @Test
    @DisplayName("BasePlexusService::sendEmail invokes NotificationServiceClient to upload details to plexus")
    public void base_plexus_service_send_email_invokes_notification_service_api_client() {
        final var expected = mock(NotificationResponse.class);
        final var template = mock(Template.class);
        when(notifications.sendEmail(any())).thenReturn(expected);

        final var response = assertDoesNotThrow(() -> plexus.sendEmail(
            new RecipientsListEmailDestination(new RecipientEmail(Set.of("<EMAIL>"), null, null)),
            "********-1234-1234-1234_********9abc",
            "<EMAIL>",
            "email",
            "subject",
            "",
            template
        ));

        verify(notifications, times(1)).sendEmail(any());
        assertEquals(expected, response);
    }

    @Test
    @DisplayName("BasePlexusService::getNotificationStatus invokes NotificationServiceClient to retrieve notification status")
    public void base_plexus_service_notification_status_invokes_notification_service_api_client() {
        final var expected = mock(NotificationStatusResponse.class);
        when(notifications.getNotificationStatus(any())).thenReturn(expected);

        final var response = assertDoesNotThrow(() -> plexus.getNotificationStatus("********-1234-1234-1234_********9abc"));

        verify(notifications, times(1)).getNotificationStatus(any());
        assertEquals(expected, response);
    }

    @Test
    @DisplayName("BasePlexusService::getAccounts invokes AccountServiceClient to retrieve accounts of a customer")
    public void base_plexus_service_get_accounts_invokes_account_service_api_client() {
        final var expected = mock(GetAccountsResponse.class);

        when(accounts.getAccounts(any(UUID.class))).thenReturn(expected);

        final var response = assertDoesNotThrow(() -> plexus.getAccounts(UUID.randomUUID()));

        verify(accounts, times(1)).getAccounts(any(UUID.class));
        assertEquals(expected, response);
    }

}
