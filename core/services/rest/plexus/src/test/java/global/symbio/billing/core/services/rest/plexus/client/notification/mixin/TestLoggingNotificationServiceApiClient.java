package global.symbio.billing.core.services.rest.plexus.client.notification.mixin;

import com.fasterxml.uuid.impl.UUIDUtil;
import global.symbio.billing.core.services.rest.plexus.client.notification.NotificationServiceApiClient;
import global.symbio.billing.core.services.rest.plexus.model.notification.*;
import io.micronaut.http.client.multipart.MultipartBody;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;

import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestLoggingNotificationServiceApiClient {

    private static final String ACCOUNTS_CONTACT_TYPE = "Accounts";

    @Mock
    private Logger log;

    @Mock
    private NotificationServiceApiClient delegate;

    private LoggingNotificationServiceApiClient client;

    @BeforeEach
    public void setup() {
        client = new LoggingNotificationServiceApiClient(log, delegate);
    }

    @Test
    @DisplayName("LoggingNotificationServiceApiClient::new rejects null constructor arguments")
    public void logging_notification_service_api_client_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new LoggingNotificationServiceApiClient(null));
        assertThrows(NullPointerException.class, () -> new LoggingNotificationServiceApiClient(log, null));
        assertThrows(NullPointerException.class, () -> new LoggingNotificationServiceApiClient(null, delegate));
    }

    @Test
    @DisplayName("LoggingNotificationServiceApiClient::uploadAttachment logs and invokes method on delegate")
    public void logging_notification_service_api_client_upload_attachment_logs_and_invokes_delegate_method() {
        final var request = mock(MultipartBody.class);
        final var response = mock(NotificationResponse.class);
        when(delegate.uploadAttachment(any(MultipartBody.class))).thenReturn(response);
        final var result = assertDoesNotThrow(() -> client.uploadAttachment(request));
        assertSame(response, result);
        verify(log, times(1)).info(anyString());
        verify(delegate, times(1)).uploadAttachment(eq(request));
    }

    @Test
    @DisplayName("LoggingNotificationServiceApiClient::sendEmail logs and invokes method on delegate")
    public void logging_notification_service_api_client_send_email_logs_and_invokes_delegate_method() {
        final var recipientContactType = new RecipientContactType(Set.of(ACCOUNTS_CONTACT_TYPE), Set.of(), Set.of());
        final var destination = new AccountAndContactTypeEmailDestination(UUIDUtil.nilUUID(), recipientContactType, null);
        final var requestID = "requestID";
        final var request = mock(SendEmailRequest.class);
        when(request.destination()).thenReturn(destination);
        when(request.attachmentRequestId()).thenReturn(requestID);
        final var response = mock(NotificationResponse.class);
        when(delegate.sendEmail(any(SendEmailRequest.class))).thenReturn(response);
        final var result = assertDoesNotThrow(() -> client.sendEmail(request));
        assertSame(response, result);
        verify(log, times(1)).info(anyString(), eq(destination), eq(requestID));
        verify(delegate, times(1)).sendEmail(eq(request));
    }

    @Test
    @DisplayName("LoggingNotificationServiceApiClient::getNotificationStatus logs and invokes method on delegate")
    public void logging_notification_service_api_client_get_notification_status_logs_and_invokes_delegate_method() {
        final var response = mock(NotificationStatusResponse.class);
        when(delegate.getNotificationStatus(anyString())).thenReturn(response);
        final var result = assertDoesNotThrow(() -> client.getNotificationStatus("abc"));
        assertSame(response, result);
        verify(log, times(1)).info(anyString(), eq("abc"));
        verify(delegate, times(1)).getNotificationStatus(eq("abc"));
    }
}