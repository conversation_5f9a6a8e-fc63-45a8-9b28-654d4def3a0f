package global.symbio.billing.core.services.rest.plexus.service.i18n.customer.impl;

import com.fasterxml.uuid.impl.UUIDUtil;
import com.github.benmanes.caffeine.cache.AsyncLoadingCache;
import global.symbio.billing.core.services.rest.plexus.model.customer.PlexusCustomer;
import global.symbio.billing.core.services.rest.plexus.service.i18n.customer.AsyncPlexusCustomerService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestCachingLocationAwareAsyncPlexusCustomerService {

    @Mock
    private AsyncLoadingCache<LocationAwarePlexusCustomerLookupRequest, PlexusCustomer> customers;

    private AsyncPlexusCustomerService service;

    @BeforeEach
    public void setup() {
        service = new CachingLocationAwareAsyncPlexusCustomerService(customers);
    }

    @Test
    @DisplayName("CachingLocationAwareAsyncPlexusCustomerService::new rejects null constructor arguments")
    public void caching_location_aware_async_plexus_customer_service_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new CachingLocationAwareAsyncPlexusCustomerService(null));
    }

    @Test
    @DisplayName("CachingLocationAwareAsyncPlexusCustomerService::lookup returns customer from cache when customer in the cache")
    public void caching_location_aware_async_plexus_customer_service_lookup_returns_customer_from_cache_when_customer_in_the_cache() {
        final var customer = mock(PlexusCustomer.class);
        when(customers.get(any(LocationAwarePlexusCustomerLookupRequest.class))).thenReturn(CompletableFuture.completedFuture(customer));
        final var future = assertDoesNotThrow(() -> service.lookup(UUIDUtil.nilUUID(), "AU"));
        final var result = assertDoesNotThrow(future::join);
        assertSame(customer, result);
        verify(customers, times(1)).get(eq(new LocationAwarePlexusCustomerLookupRequest(UUIDUtil.nilUUID(), "AU")));
    }

    @Test
    @DisplayName("CachingLocationAwareAsyncPlexusCustomerService::lookup returns null when customer not in the cache")
    public void caching_location_aware_async_plexus_customer_service_lookup_returns_null_when_customer_not_in_cache() {
        when(customers.get(any(LocationAwarePlexusCustomerLookupRequest.class))).thenReturn(CompletableFuture.completedFuture(null));
        final var future = assertDoesNotThrow(() -> service.lookup(UUIDUtil.nilUUID(), "AU"));
        final var result = assertDoesNotThrow(future::join);
        assertNull(result);
        verify(customers, times(1)).get(eq(new LocationAwarePlexusCustomerLookupRequest(UUIDUtil.nilUUID(), "AU")));
    }

    @Test
    @DisplayName("CachingLocationAwareAsyncPlexusCustomerService::lookup throws exception when cache hit throws exception")
    public void caching_location_aware_async_plexus_customer_service_lookup_throws_exception_when_cache_hit_throws_exception() {
        final var failure = new RuntimeException("Underlying exception");
        when(customers.get(any(LocationAwarePlexusCustomerLookupRequest.class))).thenReturn(CompletableFuture.failedFuture(failure));
        final var future = assertDoesNotThrow(() -> service.lookup(UUIDUtil.nilUUID(), "AU"));
        final var cause = assertThrows(CompletionException.class, future::join);
        assertSame(failure, cause.getCause());
        verify(customers, times(1)).get(eq(new LocationAwarePlexusCustomerLookupRequest(UUIDUtil.nilUUID(), "AU")));
    }
}