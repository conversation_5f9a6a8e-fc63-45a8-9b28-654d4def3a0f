package global.symbio.billing.core.services.rest.plexus.model.notification;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertThrows;

public class TestTemplate {

    @Test
    @DisplayName("Template::new rejects null constructor arguments")
    public void template_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new Template(null, "version", Map.of()));
        assertThrows(NullPointerException.class, () -> new Template("templateId", null, Map.of()));
        assertThrows(NullPointerException.class, () -> new Template("templateId", "version", null));
    }
}
