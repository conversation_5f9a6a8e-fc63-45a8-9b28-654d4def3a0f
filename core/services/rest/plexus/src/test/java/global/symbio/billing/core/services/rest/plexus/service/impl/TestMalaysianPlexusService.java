package global.symbio.billing.core.services.rest.plexus.service.impl;

import global.symbio.billing.core.i18n.LocalisationSettings;
import global.symbio.billing.core.services.rest.plexus.client.account.AccountServiceApiClient;
import global.symbio.billing.core.services.rest.plexus.client.customer.CustomerServiceApiClient;
import global.symbio.billing.core.services.rest.plexus.client.notification.NotificationServiceApiClient;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertThrows;

@ExtendWith(MockitoExtension.class)
public class TestMalaysianPlexusService {

    @Mock
    private LocalisationSettings localisation;

    @Mock
    private AccountServiceApiClient accounts;

    @Mock
    private CustomerServiceApiClient customers;

    @Mock
    private NotificationServiceApiClient notifications;

    @Test
    @DisplayName("MalaysianPlexusService::new rejects null constructor arguments")
    public void malaysian_plexus_service_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new MalaysianPlexusService(null, accounts, customers, notifications));
        assertThrows(NullPointerException.class, () -> new MalaysianPlexusService(localisation, null, customers, notifications));
        assertThrows(NullPointerException.class, () -> new MalaysianPlexusService(localisation, accounts, null, notifications));
        assertThrows(NullPointerException.class, () -> new MalaysianPlexusService(localisation, accounts, customers, null));
    }
}
