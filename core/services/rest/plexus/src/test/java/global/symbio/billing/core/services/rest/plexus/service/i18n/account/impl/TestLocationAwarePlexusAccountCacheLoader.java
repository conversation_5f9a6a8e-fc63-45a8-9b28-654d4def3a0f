package global.symbio.billing.core.services.rest.plexus.service.i18n.account.impl;

import com.fasterxml.uuid.impl.UUIDUtil;
import com.github.benmanes.caffeine.cache.CacheLoader;
import global.symbio.billing.core.i18n.localisation.LocationAware;
import global.symbio.billing.core.i18n.provider.LocalisedServiceProvider;
import global.symbio.billing.core.services.rest.plexus.model.account.GetAccountResponse;
import global.symbio.billing.core.services.rest.plexus.model.account.PlexusAccount;
import global.symbio.billing.core.services.rest.plexus.service.PlexusService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestLocationAwarePlexusAccountCacheLoader {

    @Mock
    private LocalisedServiceProvider provider;

    private CacheLoader<LocationAwarePlexusAccountLookupRequest, PlexusAccount> loader;

    @BeforeEach
    public void setup() {
        loader = new LocationAwarePlexusAccountCacheLoader(provider);
    }

    @Test
    @DisplayName("LocationAwarePlexusAccountCacheLoader::new rejects null constructor arguments")
    public void location_aware_plexus_account_cache_loader_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new LocationAwarePlexusAccountCacheLoader(null));
    }

    @ParameterizedTest
    @EnumSource(value = LookupTestcase.class, names = {"NULL", "FAILURE", "SUCCESS"})
    @DisplayName("LocationAwarePlexusAccountCacheLoader::load invokes localised PlexusService account lookup")
    public void location_aware_plexus_account_cache_loader_load_invokes_plexus_service_account_lookup(final LookupTestcase scenario) {
        final var plexus = mock(PlexusService.class);
        final var account = scenario == LookupTestcase.SUCCESS ? mock(PlexusAccount.class) : null;
        final var request = new LocationAwarePlexusAccountLookupRequest(UUIDUtil.nilUUID(), "AU");
        final var response = scenario == null ? null : new GetAccountResponse(account, scenario == LookupTestcase.SUCCESS);
        when(provider.lookup(eq(PlexusService.class), any(LocationAware.class))).thenReturn(plexus);
        when(plexus.getAccount(any(UUID.class))).thenReturn(response);
        final var result = assertDoesNotThrow(() -> loader.load(request));
        assertSame(account, result);
        verify(plexus, times(1)).getAccount(eq(UUIDUtil.nilUUID()));
    }

    public enum LookupTestcase {
        NULL,
        FAILURE,
        SUCCESS
    }
}