package global.symbio.billing.core.services.rest.plexus.model.address;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.micronaut.test.extensions.junit5.annotation.MicronautTest;
import jakarta.inject.Inject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@MicronautTest
public class TestPlexusNewZealandAddress {

    private PlexusNewZealandAddress address;

    @Inject
    private ObjectMapper mapper;
    
    private static final String JSON = """
            {   
              "id" : "11111111-d7c0-40a3-bfe4-37d2f9698ada",
              "plexid" : "plexid:customer:address:11111111-d7c0-40a3-bfe4-37d2f9698ada",
              "country" : "NZ",
              "postcode" : "1234",
              "unitType" : "unit type",
              "unitNumber" : "1",
              "floorType" : "floor type",
              "floorNumber" : "2",
              "buildingName" : "building name",
              "streetNumber" : "3",
              "streetNumberSuffix" : "st",
              "streetName" : "street name",
              "streetType" : "street type",
              "streetDirection" : "street direction",
              "suburb" : "suburb",
              "townOrCity" : "town",
              "postalDeliveryType" : "postal delivery type",
              "postalDeliveryNumber" : "4",
              "type" : "NewZealand"
            }""".stripIndent();

    @BeforeEach
    public void setup() {
        address = new PlexusNewZealandAddress(
                UUID.fromString("11111111-d7c0-40a3-bfe4-37d2f9698ada"),
                "plexid:customer:address:11111111-d7c0-40a3-bfe4-37d2f9698ada",
                "NZ",
                "1234",
                "unit type",
                "1",
                "floor type",
                "2",
                "building name",
                "3",
                "st",
                "street name",
                "street type",
                "street direction",
                "suburb",
                "town",
                "postal delivery type",
                "4"
        );
    }


    @Test
    @DisplayName("PlexusNewZealandAddress is subclass of PlexusAddress")
    public void plexus_new_zealand_address_is_subtype_of_plexus_address() {
        assertInstanceOf(PlexusNewZealandAddress.class, address);
        assertInstanceOf(PlexusAddress.class, address);
    }

    @Test
    @DisplayName("PlexusNewZealandAddress#TYPE equals NewZealand and PlexusNewZealandAddress#type equals PlexusNewZealandAddress#TYPE")
    public void plexus_new_zealand_address_type_equals_australia() {
        assertEquals("NewZealand", PlexusNewZealandAddress.TYPE);
        assertEquals(PlexusNewZealandAddress.TYPE, address.getType());
        assertSame(PlexusNewZealandAddress.TYPE, address.getType());
    }

    @Test
    @DisplayName("PlexusNewZealandAddress#getCountryName returns New Zealand")
    public void plexus_new_zealand_address_get_country_name_returns_australia() {
        assertEquals("New Zealand", address.getCountryName());
    }

    @Test
    @DisplayName("PlexusNewZealandAddress#getCompleteAddress returns complete address")
    public void plexus_new_zealand_address_get_complete_address_returns_complete_address() {
        assertEquals("unit type, 1, floor type, 2, building name, 3, st, street name, street type, street direction, suburb, town", address.getCompleteAddress());
    }

    @Test
    @DisplayName("PlexusNewZealandAddress deserialization/serialization is correct")
    public void plexus_new_zealand_address_deserialization_serialization() throws JsonProcessingException {
        //String -> PlexusNewZealandAddress
        final var base = assertInstanceOf(PlexusNewZealandAddress.class, mapper.readValue(JSON, PlexusAddress.class));
        final var impl = assertInstanceOf(PlexusNewZealandAddress.class, mapper.readValue(JSON, PlexusNewZealandAddress.class));
        assertEquals(base, impl);

        //PlexusNewZealandAddress -> String
        final var json = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(impl);

        //validate original JSON is equal to generated string after deserialization/serialization. ignore line separators
        assertEquals(JSON.replaceAll("\\s+",""), json.replaceAll("\\s+",""));
    }
}
