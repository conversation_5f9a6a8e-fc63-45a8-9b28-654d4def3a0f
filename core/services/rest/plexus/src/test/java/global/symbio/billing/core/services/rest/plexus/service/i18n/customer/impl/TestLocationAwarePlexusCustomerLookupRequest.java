package global.symbio.billing.core.services.rest.plexus.service.i18n.customer.impl;

import com.fasterxml.uuid.impl.UUIDUtil;
import global.symbio.billing.core.i18n.localisation.LocationAware;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class TestLocationAwarePlexusCustomerLookupRequest {

    @Test
    @DisplayName("LocationAwarePlexusCustomerLookupRequest::new rejects null constructor arguments")
    public void location_aware_plexus_customer_lookup_request_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new LocationAwarePlexusCustomerLookupRequest(null, "AU"));
        assertThrows(NullPointerException.class, () -> new LocationAwarePlexusCustomerLookupRequest(UUIDUtil.nilUUID(), null));
    }

    @Test
    @DisplayName("LocationAwarePlexusCustomerLookupRequest implements LocationAware interface")
    public void location_aware_plexus_customer_lookup_request_is_location_aware() {
        assertTrue(LocationAware.class.isAssignableFrom(LocationAwarePlexusCustomerLookupRequest.class));
    }
}