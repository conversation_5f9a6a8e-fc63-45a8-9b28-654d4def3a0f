package global.symbio.billing.core.services.rest.plexus.model.customer;

import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class TestPlexusCustomerSubtype {

    @ParameterizedTest
    @ValueSource(strings = {"business", "carrier"})
    @DisplayName("PlexusCustomerSubtype::get returns a customer's subtype")
    public void plexus_customer_subtype_returns_customer_subtype(@Nonnull String subtype) {
        final var customer = mock(PlexusCustomer.class);
        when(customer.subtype()).thenReturn(subtype);
        final var result = PlexusCustomerSubtype.get(customer);
        assertEquals(subtype, result.name());
    }

    //TODO: PRGBIL-235 temp behavior
    @Test
    @DisplayName("PlexusCustomerSubtype::get returns business if customer subtype is missing")
    public void plexus_customer_subtype_returns_business_if_subtype_missing() {
        final var customer = mock(PlexusCustomer.class);
        when(customer.subtype()).thenReturn(null);
        final var result = PlexusCustomerSubtype.get(customer);
        assertEquals("business", result.name());
    }

}
