package global.symbio.billing.core.services.rest.plexus.service.mixin;

import com.fasterxml.uuid.impl.UUIDUtil;
import global.symbio.billing.core.i18n.LocalisationSettings;
import global.symbio.billing.core.services.rest.plexus.model.account.GetAccountResponse;
import global.symbio.billing.core.services.rest.plexus.model.account.GetAccountsResponse;
import global.symbio.billing.core.services.rest.plexus.model.address.GetAddressResponse;
import global.symbio.billing.core.services.rest.plexus.model.customer.GetCustomerResponse;
import global.symbio.billing.core.services.rest.plexus.model.customer.GetPersonResponse;
import global.symbio.billing.core.services.rest.plexus.model.notification.*;
import global.symbio.billing.core.services.rest.plexus.model.response.PlexusAPIResponse;
import global.symbio.billing.core.services.rest.plexus.service.PlexusService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.time.ZonedDateTime;
import java.util.Set;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestLoggingPlexusService {

    private static final String ACCOUNTS_CONTACT_TYPE = "Accounts";

    @Mock
    private Logger log;

    @Mock
    private PlexusService delegate;

    private LoggingPlexusService service;

    @BeforeEach
    public void setup() {
        service = new LoggingPlexusService(log, delegate);
    }

    @Test
    @DisplayName("LoggingPlexusService::new rejects null constructor arguments")
    public void logging_plexus_service_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new LoggingPlexusService(null));
        assertThrows(NullPointerException.class, () -> new LoggingPlexusService(log, null));
        assertThrows(NullPointerException.class, () -> new LoggingPlexusService(null, delegate));
    }

    @Test
    @DisplayName("LoggingPlexusService::localisation retrieves localisation settings from delegate")
    public void logging_plexus_service_localisation_invokes_delegate_method() {
        final var localisation = mock(LocalisationSettings.class);
        when(delegate.localisation()).thenReturn(localisation);
        final var result = assertDoesNotThrow(service::localisation);
        assertSame(localisation, result);
        verify(delegate, times(1)).localisation();
    }

    @Test
    @DisplayName("LoggingPlexusService::getBillableAccounts logs and invokes method on delegate")
    public void logging_plexus_service_get_billable_accounts_logs_and_invokes_delegate_method() {
        final var timestamp = ZonedDateTime.now();
        final var response = mock(GetAccountsResponse.class);
        when(delegate.getBillableAccounts(any(ZonedDateTime.class))).thenReturn(response);
        final var result = assertDoesNotThrow(() -> service.getBillableAccounts(timestamp));
        assertSame(response, result);
        verify(log, times(1)).info(anyString(), eq(timestamp));
        verify(delegate, times(1)).getBillableAccounts(eq(timestamp));
    }

    @Test
    @DisplayName("LoggingPlexusService::getAccount logs and invokes method on delegate")
    public void logging_plexus_service_get_account_logs_and_invokes_delegate_method() {
        final var account = UUIDUtil.nilUUID();
        final var response = mock(GetAccountResponse.class);
        when(delegate.getAccount(any(UUID.class))).thenReturn(response);
        final var result = assertDoesNotThrow(() -> service.getAccount(account));
        assertSame(response, result);
        verify(log, times(1)).info(anyString(), eq(account));
        verify(delegate, times(1)).getAccount(eq(account));
    }

    @Test
    @DisplayName("LoggingPlexusService::getAddress logs and invokes method on delegate")
    public void logging_plexus_service_get_address_logs_and_invokes_delegate_method() {
        final var customer = UUIDUtil.nilUUID();
        final var address = UUIDUtil.maxUUID();
        final var response = mock(GetAddressResponse.class);
        when(delegate.getAddress(any(UUID.class), any(UUID.class))).thenReturn(response);
        final var result = assertDoesNotThrow(() -> service.getAddress(customer, address));
        assertSame(response, result);
        verify(log, times(1)).info(anyString(), eq(customer), eq(address));
        verify(delegate, times(1)).getAddress(eq(customer), eq(address));
    }

    @Test
    @DisplayName("LoggingPlexusService::getCustomer logs and invokes method on delegate")
    public void logging_plexus_service_get_customer_logs_and_invokes_delegate_method() {
        final var customer = UUIDUtil.nilUUID();
        final var response = mock(GetCustomerResponse.class);
        when(delegate.getCustomer(any(UUID.class))).thenReturn(response);
        final var result = assertDoesNotThrow(() -> service.getCustomer(customer));
        assertSame(response, result);
        verify(log, times(1)).info(anyString(), eq(customer));
        verify(delegate, times(1)).getCustomer(eq(customer));
    }

    @Test
    @DisplayName("LoggingPlexusService::getPerson logs and invokes method on delegate")
    public void logging_plexus_service_get_person_logs_and_invokes_delegate_method() {
        final var customer = UUIDUtil.nilUUID();
        final var person = UUIDUtil.maxUUID();
        final var response = mock(GetPersonResponse.class);
        when(delegate.getPerson(any(UUID.class), any(UUID.class))).thenReturn(response);
        final var result = assertDoesNotThrow(() -> service.getPerson(customer, person));
        assertSame(response, result);
        verify(log, times(1)).info(anyString(), eq(customer), eq(person));
        verify(delegate, times(1)).getPerson(eq(customer), eq(person));
    }

    @Test
    @DisplayName("LoggingPlexusService::updateAccountBillingDate logs and invokes method on delegate")
    public void logging_plexus_service_update_account_billing_date_logs_and_invokes_delegate_method() {
        final var account = UUIDUtil.nilUUID();
        final var date = ZonedDateTime.now();
        final var response = mock(PlexusAPIResponse.class);
        when(delegate.updateAccountBillingDate(any(UUID.class), any(ZonedDateTime.class))).thenReturn(response);
        final var result = assertDoesNotThrow(() -> service.updateAccountBillingDate(account, date));
        assertSame(response, result);
        verify(log, times(1)).info(anyString(), eq(account), eq(date));
        verify(delegate, times(1)).updateAccountBillingDate(eq(account), eq(date));
    }

    @Test
    @DisplayName("LoggingPlexusService::uploadAttachment logs and invokes method on delegate")
    public void logging_plexus_service_upload_attachment_as_bytes_logs_and_invokes_delegate_method() {
        final var filename = "filename";
        final var data = "data".getBytes(StandardCharsets.UTF_8);
        final var response = mock(NotificationResponse.class);
        when(delegate.uploadAttachment(anyString(), any(byte[].class))).thenReturn(response);
        final var result = assertDoesNotThrow(() -> service.uploadAttachment(filename, data));
        assertSame(response, result);
        verify(log, times(1)).info(anyString(), eq(filename), eq(4));
        verify(delegate, times(1)).uploadAttachment(eq(filename), eq(data));
    }

    @Test
    @DisplayName("LoggingPlexusService::uploadAttachment logs and invokes method on delegate")
    public void logging_plexus_service_upload_attachment_as_file_logs_and_invokes_delegate_method() {
        final var filename = "filename";
        final var file = mock(File.class);
        when(file.length()).thenReturn(4L);
        final var response = mock(NotificationResponse.class);
        when(delegate.uploadAttachment(anyString(), any(File.class))).thenReturn(response);
        final var result = assertDoesNotThrow(() -> service.uploadAttachment(filename, file));
        assertSame(response, result);
        verify(log, times(1)).info(anyString(), eq(filename), eq(4L));
        verify(delegate, times(1)).uploadAttachment(eq(filename), eq(file));
    }

    @Test
    @DisplayName("LoggingPlexusService::uploadAttachment with attachmentRequestId logs and invokes method on delegate")
    public void logging_plexus_service_upload_attachment_as_file_with_attachment_request_logs_and_invokes_delegate_method() {
        final var filename = "filename";
        final var attachmentRequestId = "attachmentRequestId";
        final var file = mock(File.class);
        when(file.length()).thenReturn(4L);
        final var response = mock(NotificationResponse.class);
        when(delegate.uploadAttachment(anyString(), any(File.class), anyString())).thenReturn(response);
        final var result = assertDoesNotThrow(() -> service.uploadAttachment(filename, file, attachmentRequestId));
        assertSame(response, result);
        verify(log, times(1)).info(anyString(), eq(filename), eq(4L), eq(attachmentRequestId));
        verify(delegate, times(1)).uploadAttachment(eq(filename), eq(file), eq(attachmentRequestId));
    }

    @Test
    @DisplayName("LoggingPlexusService::sendEmail logs and invokes method on delegate")
    public void logging_plexus_service_send_email_logs_and_invokes_delegate_method() {
        final var recipientContactType = new RecipientContactType(Set.of(ACCOUNTS_CONTACT_TYPE), Set.of(), Set.of());
        final var destination = new AccountAndContactTypeEmailDestination(UUIDUtil.nilUUID(), recipientContactType, null);
        final var requestID = "requestID";
        final var fromEmail = "a@b.c";
        final var fromDisplayName = "From";
        final var subject = "subject";
        final var response = mock(NotificationResponse.class);
        when(delegate.sendEmail(any(EmailDestination.class), anyString(), anyString(), anyString(), anyString(), nullable(String.class), nullable(Template.class))).thenReturn(response);
        final var result = assertDoesNotThrow(() -> service.sendEmail(destination, requestID, fromEmail, fromDisplayName, subject, null, null));
        assertSame(response, result);
        verify(log, times(1)).info(anyString(), eq(destination), eq(requestID));
        verify(delegate, times(1)).sendEmail(eq(destination), eq(requestID), eq(fromEmail), eq(fromDisplayName), eq(subject), isNull(), isNull());
    }

    @Test
    @DisplayName("LoggingPlexusService::getNotificationStatus logs and invokes method on delegate")
    public void logging_plexus_service_get_notification_status_logs_and_invokes_delegate_method() {
        final var notification = "notification";
        final var response = mock(NotificationStatusResponse.class);
        when(delegate.getNotificationStatus(anyString())).thenReturn(response);
        final var result = assertDoesNotThrow(() -> service.getNotificationStatus(notification));
        assertSame(response, result);
        verify(log, times(1)).info(anyString(), eq(notification));
        verify(delegate, times(1)).getNotificationStatus(eq(notification));
    }

    @Test
    @DisplayName("LoggingPlexusService::getAccounts logs and invokes method on delegate")
    public void logging_plexus_service_get_accounts_logs_and_invokes_delegate_method() {
        final var customer = UUID.randomUUID();
        final var response = mock(GetAccountsResponse.class);
        when(delegate.getAccounts(any(UUID.class))).thenReturn(response);
        final var result = assertDoesNotThrow(() -> service.getAccounts(customer));
        assertSame(response, result);
        verify(log, times(1)).info(anyString(), eq(customer));
        verify(delegate, times(1)).getAccounts(eq(customer));
    }
}