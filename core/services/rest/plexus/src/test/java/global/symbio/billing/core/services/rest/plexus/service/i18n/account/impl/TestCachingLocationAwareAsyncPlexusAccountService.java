package global.symbio.billing.core.services.rest.plexus.service.i18n.account.impl;

import com.fasterxml.uuid.impl.UUIDUtil;
import com.github.benmanes.caffeine.cache.AsyncLoadingCache;
import global.symbio.billing.core.services.rest.plexus.model.account.PlexusAccount;
import global.symbio.billing.core.services.rest.plexus.service.i18n.account.AsyncPlexusAccountService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestCachingLocationAwareAsyncPlexusAccountService {

    @Mock
    private AsyncLoadingCache<LocationAwarePlexusAccountLookupRequest, PlexusAccount> accounts;

    private AsyncPlexusAccountService service;

    @BeforeEach
    public void setup() {
        service = new CachingLocationAwareAsyncPlexusAccountService(accounts);
    }

    @Test
    @DisplayName("CachingLocationAwareAsyncPlexusAccountService::new rejects null constructor arguments")
    public void caching_location_aware_async_plexus_account_service_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new CachingLocationAwareAsyncPlexusAccountService(null));
    }

    @Test
    @DisplayName("CachingLocationAwareAsyncPlexusAccountService::lookup returns account from cache when account in the cache")
    public void caching_location_aware_async_plexus_account_service_lookup_returns_account_from_cache_when_account_in_the_cache() {
        final var account = mock(PlexusAccount.class);
        when(accounts.get(any(LocationAwarePlexusAccountLookupRequest.class))).thenReturn(CompletableFuture.completedFuture(account));
        final var future = assertDoesNotThrow(() -> service.lookup(UUIDUtil.nilUUID(), "AU"));
        final var result = assertDoesNotThrow(future::join);
        assertSame(account, result);
        verify(accounts, times(1)).get(eq(new LocationAwarePlexusAccountLookupRequest(UUIDUtil.nilUUID(), "AU")));
    }

    @Test
    @DisplayName("CachingLocationAwareAsyncPlexusAccountService::lookup returns null when account not in the cache")
    public void caching_location_aware_async_plexus_account_service_lookup_returns_null_when_account_not_in_cache() {
        when(accounts.get(any(LocationAwarePlexusAccountLookupRequest.class))).thenReturn(CompletableFuture.completedFuture(null));
        final var future = assertDoesNotThrow(() -> service.lookup(UUIDUtil.nilUUID(), "AU"));
        final var result = assertDoesNotThrow(future::join);
        assertNull(result);
        verify(accounts, times(1)).get(eq(new LocationAwarePlexusAccountLookupRequest(UUIDUtil.nilUUID(), "AU")));
    }

    @Test
    @DisplayName("CachingLocationAwareAsyncPlexusAccountService::lookup throws exception when cache hit throws exception")
    public void caching_location_aware_async_plexus_account_service_lookup_throws_exception_when_cache_hit_throws_exception() {
        final var failure = new RuntimeException("Underlying exception");
        when(accounts.get(any(LocationAwarePlexusAccountLookupRequest.class))).thenReturn(CompletableFuture.failedFuture(failure));
        final var future = assertDoesNotThrow(() -> service.lookup(UUIDUtil.nilUUID(), "AU"));
        final var cause = assertThrows(CompletionException.class, future::join);
        assertSame(failure, cause.getCause());
        verify(accounts, times(1)).get(eq(new LocationAwarePlexusAccountLookupRequest(UUIDUtil.nilUUID(), "AU")));
    }
}