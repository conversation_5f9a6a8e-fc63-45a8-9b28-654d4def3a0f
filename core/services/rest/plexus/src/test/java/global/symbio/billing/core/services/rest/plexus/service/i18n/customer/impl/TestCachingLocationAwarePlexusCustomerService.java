package global.symbio.billing.core.services.rest.plexus.service.i18n.customer.impl;

import com.fasterxml.uuid.impl.UUIDUtil;
import com.github.benmanes.caffeine.cache.AsyncLoadingCache;
import com.github.benmanes.caffeine.cache.LoadingCache;
import global.symbio.billing.core.services.rest.plexus.model.customer.PlexusCustomer;
import global.symbio.billing.core.services.rest.plexus.service.i18n.customer.PlexusCustomerService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestCachingLocationAwarePlexusCustomerService {

    @Mock
    private LoadingCache<LocationAwarePlexusCustomerLookupRequest, PlexusCustomer> customers;

    private PlexusCustomerService service;

    @BeforeEach
    public void setup() {
        service = new CachingLocationAwarePlexusCustomerService(customers);
    }

    @Test
    @DisplayName("CachingLocationAwarePlexusCustomerService::new rejects null constructor arguments")
    public void caching_location_aware_plexus_customer_service_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new CachingLocationAwarePlexusCustomerService((LoadingCache<LocationAwarePlexusCustomerLookupRequest, PlexusCustomer>) null));
        assertThrows(NullPointerException.class, () -> new CachingLocationAwarePlexusCustomerService((AsyncLoadingCache<LocationAwarePlexusCustomerLookupRequest, PlexusCustomer>) null));
    }

    @Test
    @DisplayName("CachingLocationAwarePlexusCustomerService::lookup returns customer from cache when customer in the cache")
    public void caching_location_aware_plexus_customer_service_lookup_returns_customer_from_cache_when_customer_in_the_cache() {
        final var customer = mock(PlexusCustomer.class);
        when(customers.get(any(LocationAwarePlexusCustomerLookupRequest.class))).thenReturn(customer);
        final var result = assertDoesNotThrow(() -> service.lookup(UUIDUtil.nilUUID(), "AU"));
        assertSame(customer, result);
        verify(customers, times(1)).get(eq(new LocationAwarePlexusCustomerLookupRequest(UUIDUtil.nilUUID(), "AU")));
    }

    @Test
    @DisplayName("CachingLocationAwarePlexusCustomerService::lookup returns null when customer not in the cache")
    public void caching_location_aware_plexus_customer_service_lookup_returns_null_when_customer_not_in_cache() {
        when(customers.get(any(LocationAwarePlexusCustomerLookupRequest.class))).thenReturn(null);
        final var result = assertDoesNotThrow(() -> service.lookup(UUIDUtil.nilUUID(), "AU"));
        assertNull(result);
        verify(customers, times(1)).get(eq(new LocationAwarePlexusCustomerLookupRequest(UUIDUtil.nilUUID(), "AU")));
    }

    @Test
    @DisplayName("CachingLocationAwarePlexusCustomerService::lookup throws exception when cache hit throws exception")
    public void caching_location_aware_plexus_customer_service_lookup_throws_exception_when_cache_hit_throws_exception() {
        final var failure = new RuntimeException("Underlying exception");
        when(customers.get(any(LocationAwarePlexusCustomerLookupRequest.class))).thenThrow(failure);
        final var cause = assertThrows(RuntimeException.class, () -> service.lookup(UUIDUtil.nilUUID(), "AU"));
        assertSame(failure, cause);
        verify(customers, times(1)).get(eq(new LocationAwarePlexusCustomerLookupRequest(UUIDUtil.nilUUID(), "AU")));
    }
}