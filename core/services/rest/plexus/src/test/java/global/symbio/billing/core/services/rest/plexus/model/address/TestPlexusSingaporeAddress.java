package global.symbio.billing.core.services.rest.plexus.model.address;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.micronaut.test.extensions.junit5.annotation.MicronautTest;
import jakarta.inject.Inject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

@MicronautTest
public class TestPlexusSingaporeAddress {

    private PlexusSingaporeAddress address;

    @Inject
    private ObjectMapper mapper;

    private static final String JSON = """
            {   
              "id" : "11111111-d7c0-40a3-bfe4-37d2f9698ada",
              "plexid" : "plexid:customer:address:11111111-d7c0-40a3-bfe4-37d2f9698ada",
              "country" : "SG",
              "postcode" : "321",
              "buildingNameOrNumber" : "building name",
              "apartmentNumber" : "1",
              "floor" : "floor",
              "streetNumber" : "2",
              "streetName" : "street name",
              "locality" : "locality",
              "postalDeliveryNumber" : "3",
              "type" : "Singapore"
            }""".stripIndent();

    @BeforeEach
    public void setup() {
        address = new PlexusSingaporeAddress(
                UUID.fromString("11111111-d7c0-40a3-bfe4-37d2f9698ada"),
                "plexid:customer:address:11111111-d7c0-40a3-bfe4-37d2f9698ada",
                "SG",
                "321",
                "building name",
                "1",
                "floor",
                "2",
                "street name",
                "locality",
                "3"
        );
    }


    @Test
    @DisplayName("PlexusSingaporeAddress is subclass of PlexusAddress")
    public void plexus_singapore_address_is_subtype_of_plexus_address() {
        assertInstanceOf(PlexusSingaporeAddress.class, address);
        assertInstanceOf(PlexusAddress.class, address);
    }

    @Test
    @DisplayName("PlexusSingaporeAddress#TYPE equals Singapore and PlexusSingaporeAddress#type equals PlexusSingaporeAddress#TYPE")
    public void plexus_singapore_address_type_equals_australia() {
        assertEquals("Singapore", PlexusSingaporeAddress.TYPE);
        assertEquals(PlexusSingaporeAddress.TYPE, address.getType());
        assertSame(PlexusSingaporeAddress.TYPE, address.getType());
    }

    @Test
    @DisplayName("PlexusSingaporeAddress#getCountryName returns Singapore")
    public void plexus_singapore_address_get_country_name_returns_australia() {
        assertEquals("Singapore", address.getCountryName());
    }

    @Test
    @DisplayName("PlexusSingaporeAddress#getCompleteAddress returns complete address")
    public void plexus_singapore_address_get_complete_address_returns_complete_address() {
        assertEquals("building name, 1, floor, 2, street name, locality", address.getCompleteAddress());
    }

    @Test
    @DisplayName("PlexusSingaporeAddress deserialization/serialization is correct")
    public void plexus_singapore_address_deserialization_serialization() throws JsonProcessingException {
        //String -> PlexusSingaporeAddress
        final var base = assertInstanceOf(PlexusSingaporeAddress.class, mapper.readValue(JSON, PlexusAddress.class));
        final var impl = assertInstanceOf(PlexusSingaporeAddress.class, mapper.readValue(JSON, PlexusSingaporeAddress.class));
        assertEquals(base, impl);

        //PlexusSingaporeAddress -> String
        final var json = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(impl);

        //validate original JSON is equal to generated string after deserialization/serialization. ignore line separators
        assertEquals(JSON.replaceAll("\\s+",""), json.replaceAll("\\s+",""));
    }
}
