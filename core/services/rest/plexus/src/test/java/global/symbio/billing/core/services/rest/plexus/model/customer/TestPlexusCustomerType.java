package global.symbio.billing.core.services.rest.plexus.model.customer;

import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class TestPlexusCustomerType {

    @ParameterizedTest
    @ValueSource(strings = {"business", "individual"})
    @DisplayName("PlexusCustomerType::get returns a customer's subtype")
    public void plexus_customer_type_returns_customer_type(@Nonnull String type) {
        final var customer = mock(PlexusCustomer.class);
        when(customer.type()).thenReturn(type);
        final var result = PlexusCustomerType.get(customer);
        assertEquals(type, result.name());
    }
}
