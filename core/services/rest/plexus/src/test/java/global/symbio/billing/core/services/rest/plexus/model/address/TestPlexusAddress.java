package global.symbio.billing.core.services.rest.plexus.model.address;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.lang.reflect.Modifier;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class TestPlexusAddress {

    @Test
    @DisplayName("PlexusAddress is an abstract class.")
    public void plexus_address_is_abstract_sealed_class() {
        assertTrue(Modifier.isAbstract(PlexusAddress.class.getModifiers()));
    }

    @Test
    @DisplayName("PlexusAddress is a sealed class.")
    public void plexus_address_is_sealed_class() {
        assertTrue(PlexusAddress.class.isSealed());
    }

    @Test
    @DisplayName("PlexusAddress only permits Australia, International, New Zealand, and Singapore implementations.")
    public void plexus_address_only_permits_au_nz_sg_int_subtypes() {
        final var permitted = PlexusAddress.class.getPermittedSubclasses();
        final var expected = new Class[]{ PlexusAustraliaAddress.class, PlexusInternationalAddress.class, PlexusNewZealandAddress.class, PlexusSingaporeAddress.class };
        assertArrayEquals(expected, permitted);
    }
}
