package global.symbio.billing.core.services.rest.plexus.client.customer.mixin;

import com.fasterxml.uuid.impl.UUIDUtil;
import global.symbio.billing.core.services.rest.plexus.client.customer.CustomerServiceApiClient;
import global.symbio.billing.core.services.rest.plexus.model.address.GetAddressResponse;
import global.symbio.billing.core.services.rest.plexus.model.customer.GetCustomerResponse;
import global.symbio.billing.core.services.rest.plexus.model.customer.GetPersonResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestLoggingCustomerServiceApiClient {

    @Mock
    private Logger log;

    @Mock
    private CustomerServiceApiClient delegate;

    private LoggingCustomerServiceApiClient client;

    @BeforeEach
    public void setup() {
        client = new LoggingCustomerServiceApiClient(log, delegate);
    }

    @Test
    @DisplayName("LoggingCustomerServiceApiClient::new rejects null constructor arguments")
    public void logging_customer_service_api_client_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new LoggingCustomerServiceApiClient(null));
        assertThrows(NullPointerException.class, () -> new LoggingCustomerServiceApiClient(log, null));
        assertThrows(NullPointerException.class, () -> new LoggingCustomerServiceApiClient(null, delegate));
    }

    @Test
    @DisplayName("LoggingCustomerServiceApiClient::getCustomer logs and invokes method on delegate")
    public void logging_customer_service_api_client_get_customer_logs_and_invokes_delegate_method() {
        final var customer = UUIDUtil.nilUUID();
        final var response = mock(GetCustomerResponse.class);
        when(delegate.getCustomer(any(UUID.class))).thenReturn(response);
        final var result = assertDoesNotThrow(() -> client.getCustomer(customer));
        assertSame(response, result);
        verify(log, times(1)).info(anyString(), eq(customer));
        verify(delegate, times(1)).getCustomer(eq(customer));
    }

    @Test
    @DisplayName("LoggingCustomerServiceApiClient::getAddress logs and invokes method on delegate")
    public void logging_customer_service_api_client_get_address_logs_and_invokes_delegate_method() {
        final var customer = UUIDUtil.nilUUID();
        final var address = UUIDUtil.maxUUID();
        final var response = mock(GetAddressResponse.class);
        when(delegate.getAddress(any(UUID.class), any(UUID.class))).thenReturn(response);
        final var result = assertDoesNotThrow(() -> client.getAddress(customer, address));
        assertSame(response, result);
        verify(log, times(1)).info(anyString(), eq(customer), eq(address));
        verify(delegate, times(1)).getAddress(eq(customer), eq(address));
    }

    @Test
    @DisplayName("LoggingCustomerServiceApiClient::getPerson logs and invokes method on delegate")
    public void logging_customer_service_api_client_get_person_logs_and_invokes_delegate_method() {
        final var customer = UUIDUtil.nilUUID();
        final var person = UUIDUtil.maxUUID();
        final var response = mock(GetPersonResponse.class);
        when(delegate.getPerson(any(UUID.class), any(UUID.class))).thenReturn(response);
        final var result = assertDoesNotThrow(() -> client.getPerson(customer, person));
        assertSame(response, result);
        verify(log, times(1)).info(anyString(), eq(customer), eq(person));
        verify(delegate, times(1)).getPerson(eq(customer), eq(person));
    }
}