package global.symbio.billing.core.services.rest.plexus.service.i18n.account;

import global.symbio.billing.core.i18n.localisation.LocationAware;
import global.symbio.billing.core.services.rest.plexus.model.account.PlexusAccount;
import jakarta.annotation.Nonnull;

import java.util.UUID;
import java.util.concurrent.CompletableFuture;

@FunctionalInterface
public interface AsyncPlexusAccountService {

    @Nonnull
    CompletableFuture<PlexusAccount> lookup(@Nonnull UUID identifier, @Nonnull LocationAware location);

    @Nonnull
    default CompletableFuture<PlexusAccount> lookup(@Nonnull UUID identifier, @Nonnull String country) {
        return lookup(identifier, LocationAware.of(country));
    }
}