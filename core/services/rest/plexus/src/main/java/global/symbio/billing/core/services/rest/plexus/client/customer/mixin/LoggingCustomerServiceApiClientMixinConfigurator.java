package global.symbio.billing.core.services.rest.plexus.client.customer.mixin;

import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.mixin.MixinConfigurator;
import global.symbio.billing.core.services.rest.plexus.client.customer.CustomerServiceApiClient;
import io.micronaut.context.annotation.Factory;
import io.micronaut.core.order.Ordered;
import io.micronaut.inject.BeanDefinition;
import io.micronaut.inject.BeanIdentifier;
import jakarta.annotation.Nonnull;

@Factory
@VisibleForTesting
class LoggingCustomerServiceApiClientMixinConfigurator extends MixinConfigurator<CustomerServiceApiClient> {

    private static final String COMPONENT = "logging";

    @Nonnull
    @Override
    public CustomerServiceApiClient mixin(@Nonnull CustomerServiceApiClient bean, @Nonnull String name) {
        return new LoggingCustomerServiceApiClient(bean);
    }

    @Nonnull
    @Override
    protected String component() {
        return COMPONENT;
    }

    @Nonnull
    @Override
    protected String getName(@Nonnull CustomerServiceApiClient bean, @Nonnull BeanDefinition<CustomerServiceApiClient> definition, @Nonnull BeanIdentifier identifier) {
        return identifier.getName();
    }

    @Override
    protected boolean isEnabled() {
        return true;
    }

    @Override
    public int getOrder() {
        return Ordered.LOWEST_PRECEDENCE;
    }
}