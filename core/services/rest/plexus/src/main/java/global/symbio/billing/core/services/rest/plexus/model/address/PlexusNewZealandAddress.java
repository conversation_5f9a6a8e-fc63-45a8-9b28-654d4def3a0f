package global.symbio.billing.core.services.rest.plexus.model.address;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.MoreObjects;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.Getter;

import java.util.Objects;
import java.util.UUID;

@Getter
public final class PlexusNewZealandAddress extends PlexusAddress {

    @Nonnull
    public static final String TYPE = "NewZealand";

    @Nonnull
    public static final String COUNTRY_NAME = "New Zealand";

    private final String unitType;
    private final String unitNumber;
    private final String floorType;
    private final String floorNumber;
    private final String buildingName;
    private final String streetNumber;
    private final String streetNumberSuffix;
    private final String streetName;
    private final String streetType;
    private final String streetDirection;
    private final String suburb;
    private final String townOrCity;
    private final String postalDeliveryType;
    private final String postalDeliveryNumber;


    @JsonCreator(mode = JsonCreator.Mode.PROPERTIES)
    public PlexusNewZealandAddress(
            @Nonnull @JsonProperty("id") UUID id,
            @Nullable @JsonProperty("plexid") String plexid,
            @Nullable @JsonProperty("country") String country,
            @Nullable @JsonProperty("postcode") String postcode,
            @Nullable @JsonProperty("unitType") String unitType,
            @Nullable @JsonProperty("unitNumber") String unitNumber,
            @Nullable @JsonProperty("floorType") String floorType,
            @Nullable @JsonProperty("floorNumber") String floorNumber,
            @Nullable @JsonProperty("buildingName") String buildingName,
            @Nullable @JsonProperty("streetNumber") String streetNumber,
            @Nullable @JsonProperty("streetNumberSuffix") String streetNumberSuffix,
            @Nullable @JsonProperty("streetName") String streetName,
            @Nullable @JsonProperty("streetType") String streetType,
            @Nullable @JsonProperty("streetDirection") String streetDirection,
            @Nullable @JsonProperty("suburb") String suburb,
            @Nullable @JsonProperty("townOrCity") String townOrCity,
            @Nullable @JsonProperty("postalDeliveryType") String postalDeliveryType,
            @Nullable @JsonProperty("postalDeliveryNumber") String postalDeliveryNumber) {
        super(TYPE, id, plexid, country, postcode);
        this.unitType = unitType;
        this.unitNumber = unitNumber;
        this.floorType = floorType;
        this.floorNumber = floorNumber;
        this.buildingName = buildingName;
        this.streetNumber = streetNumber;
        this.streetNumberSuffix = streetNumberSuffix;
        this.streetName = streetName;
        this.streetType = streetType;
        this.streetDirection = streetDirection;
        this.suburb = suburb;
        this.townOrCity = townOrCity;
        this.postalDeliveryType = postalDeliveryType;
        this.postalDeliveryNumber = postalDeliveryNumber;
    }

    @Nonnull
    @Override
    @JsonIgnore
    public String getCountryName() {
        return COUNTRY_NAME;
    }

    @Nonnull
    @Override
    @JsonIgnore
    public String getCompleteAddress() {
        return compose(
                unitType,
                unitNumber,
                floorType,
                floorNumber,
                buildingName,
                streetNumber,
                streetNumberSuffix,
                streetName,
                streetType,
                streetDirection,
                suburb,
                townOrCity
        );
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof PlexusNewZealandAddress entity)) return false;
        return Objects.equals(getId(), entity.getId())
                && Objects.equals(getPlexid(), entity.getPlexid())
                && Objects.equals(getType(), entity.getType())
                && Objects.equals(getCountry(), entity.getCountry())
                && Objects.equals(getPostcode(), entity.getPostcode())
                && Objects.equals(getUnitType(), entity.getUnitType())
                && Objects.equals(getUnitNumber(), entity.getUnitNumber())
                && Objects.equals(getFloorType(), entity.getFloorType())
                && Objects.equals(getFloorNumber(), entity.getFloorNumber())
                && Objects.equals(getBuildingName(), entity.getBuildingName())
                && Objects.equals(getStreetNumber(), entity.getStreetNumber())
                && Objects.equals(getStreetNumberSuffix(), entity.getStreetNumberSuffix())
                && Objects.equals(getStreetName(), entity.getStreetName())
                && Objects.equals(getStreetType(), entity.getStreetType())
                && Objects.equals(getStreetDirection(), entity.getStreetDirection())
                && Objects.equals(getSuburb(), entity.getSuburb())
                && Objects.equals(getTownOrCity(), entity.getTownOrCity())
                && Objects.equals(getPostalDeliveryType(), entity.getPostalDeliveryType())
                && Objects.equals(getPostalDeliveryNumber(), entity.getPostalDeliveryNumber());
    }

    @Override
    public int hashCode() {
        return Objects.hash(
                getId(),
                getPlexid(),
                getType(),
                getCountry(),
                getPostcode(),
                getUnitType(),
                getUnitNumber(),
                getFloorType(),
                getFloorNumber(),
                getBuildingName(),
                getStreetNumber(),
                getStreetNumberSuffix(),
                getStreetName(),
                getStreetType(),
                getStreetDirection(),
                getSuburb(),
                getTownOrCity(),
                getPostalDeliveryType(),
                getPostalDeliveryNumber()
        );
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(getClass())
                .add("identifier", getId())
                .add("plexid", getPlexid())
                .add("type", getType())
                .add("country", getCountry())
                .add("postcode", getPostcode())
                .add("unitType", getUnitType())
                .add("unitNumber", getUnitNumber())
                .add("floorType", getFloorType())
                .add("floorNumber", getFloorNumber())
                .add("buildingName", getBuildingName())
                .add("streetNumber", getStreetNumber())
                .add("streetNumberSuffix", getStreetNumberSuffix())
                .add("streetName", getStreetName())
                .add("streetType", getStreetType())
                .add("streetDirection", getStreetDirection())
                .add("suburb", getSuburb())
                .add("townOrCity", getTownOrCity())
                .add("postalDeliveryType", getPostalDeliveryType())
                .add("postalDeliveryNumber", getPostalDeliveryNumber())
                .toString();
    }
}
