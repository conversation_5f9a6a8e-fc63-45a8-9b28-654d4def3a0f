package global.symbio.billing.core.services.rest.plexus.model.address;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.MoreObjects;
import global.symbio.billing.core.i18n.ISO3166CountryCodes;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.Getter;

import java.util.Objects;
import java.util.UUID;

@Getter
public final class PlexusInternationalAddress extends PlexusAddress {

    @Nonnull
    public static final String TYPE = "International";

    @Nonnull
    public static final String COUNTRY_MY_NAME = "Malaysia";

    private final String floor;
    private final String subpremise;
    private final String premise;
    private final String streetNumber;
    private final String route;
    private final String locality;
    private final String district;
    private final String state;
    private final String postalTown;
    private final String postalLocation;

    @JsonCreator(mode = JsonCreator.Mode.PROPERTIES)
    public PlexusInternationalAddress(
            @Nonnull @JsonProperty("id") UUID id,
            @Nullable @JsonProperty("plexid") String plexid,
            @Nullable @JsonProperty("country") String country,
            @Nullable @JsonProperty("postcode") String postcode,
            @Nullable @JsonProperty("floor") String floor,
            @Nullable @JsonProperty("subpremise") String subpremise,
            @Nullable @JsonProperty("premise") String premise,
            @Nullable @JsonProperty("streetNumber") String streetNumber,
            @Nullable @JsonProperty("route") String route,
            @Nullable @JsonProperty("locality") String locality,
            @Nullable @JsonProperty("district") String district,
            @Nullable @JsonProperty("state") String state,
            @Nullable @JsonProperty("postalTown") String postalTown,
            @Nullable @JsonProperty("postalLocation") String postalLocation
    ) {
        super(TYPE, id, plexid, country, postcode);
        this.floor = floor;
        this.subpremise = subpremise;
        this.premise = premise;
        this.streetNumber = streetNumber;
        this.route = route;
        this.locality = locality;
        this.district = district;
        this.state = state;
        this.postalTown = postalTown;
        this.postalLocation = postalLocation;
    }

    @Nonnull
    @Override
    @JsonIgnore
    public String getCountryName() {
        if (ISO3166CountryCodes.MALAYSIA.equals(country)) {
            return COUNTRY_MY_NAME;
        }
        return country;
    }

    @Nonnull
    @Override
    @JsonIgnore
    public String getCompleteAddress() {
        return compose(
                floor,
                subpremise,
                premise,
                streetNumber,
                route,
                locality,
                district,
                state
        );
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof PlexusInternationalAddress entity)) return false;
        return Objects.equals(getId(), entity.getId())
                && Objects.equals(getPlexid(), entity.getPlexid())
                && Objects.equals(getType(), entity.getType())
                && Objects.equals(getCountry(), entity.getCountry())
                && Objects.equals(getPostcode(), entity.getPostcode())
                && Objects.equals(getFloor(), entity.getFloor())
                && Objects.equals(getSubpremise(), entity.getSubpremise())
                && Objects.equals(getPremise(), entity.getPremise())
                && Objects.equals(getStreetNumber(), entity.getStreetNumber())
                && Objects.equals(getRoute(), entity.getRoute())
                && Objects.equals(getLocality(), entity.getLocality())
                && Objects.equals(getDistrict(), entity.getDistrict())
                && Objects.equals(getState(), entity.getState())
                && Objects.equals(getPostalTown(), entity.getPostalTown())
                && Objects.equals(getPostalLocation(), entity.getPostalLocation());
    }

    @Override
    public int hashCode() {
        return Objects.hash(
                getId(),
                getPlexid(),
                getType(),
                getCountry(),
                getPostcode(),
                getFloor(),
                getSubpremise(),
                getPremise(),
                getStreetNumber(),
                getRoute(),
                getLocality(),
                getDistrict(),
                getState(),
                getPostalTown(),
                getPostalLocation()
        );
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(getClass())
                .add("identifier", getId())
                .add("plexid", getPlexid())
                .add("type", getType())
                .add("country", getCountry())
                .add("postcode", getPostcode())
                .add("floor", getFloor())
                .add("subpremise", getSubpremise())
                .add("premise", getPremise())
                .add("streetNumber", getStreetNumber())
                .add("route", getRoute())
                .add("locality", getLocality())
                .add("district", getDistrict())
                .add("state", getState())
                .add("postalTown", getPostalTown())
                .add("postalLocation", getPostalLocation())
                .toString();
    }
}
