package global.symbio.billing.core.services.rest.plexus.model.account;

import jakarta.annotation.Nonnull;

import java.time.ZonedDateTime;

public enum BillingInterval {
    daily {
        @Nonnull
        @Override
        public ZonedDateTime next(@Nonnull final ZonedDateTime date) {
            return date.plusDays(1L);
        }

        @Nonnull
        @Override
        public ZonedDateTime previous(@Nonnull ZonedDateTime date) {
            return date.minusDays(1L);
        }
    },
    weekly {
        @Nonnull
        @Override
        public ZonedDateTime next(@Nonnull final ZonedDateTime date) {
            return date.plusWeeks(1L);
        }

        @Nonnull
        @Override
        public ZonedDateTime previous(@Nonnull ZonedDateTime date) {
            return date.minusWeeks(1L);
        }
    },
    fortnightly {
        @Nonnull
        @Override
        public ZonedDateTime next(@Nonnull final ZonedDateTime date) {
            return date.plusWeeks(2L);
        }

        @Nonnull
        @Override
        public ZonedDateTime previous(@Nonnull ZonedDateTime date) {
            return date.minusWeeks(2L);
        }
    },
    monthly {
        @Nonnull
        @Override
        public ZonedDateTime next(@Nonnull final ZonedDateTime date) {
            return date.plusMonths(1L);
        }

        @Nonnull
        @Override
        public ZonedDateTime previous(@Nonnull final ZonedDateTime date) {
            return date.minusMonths(1L);
        }
    },
    first_and_sixteenth_of_month {
        @Nonnull
        @Override
        public ZonedDateTime next(@Nonnull final ZonedDateTime date) {
            if (date.getDayOfMonth() < 16) {
                return date.withDayOfMonth(16);
            }
            return date.plusMonths(1).withDayOfMonth(1);
        }

        @Nonnull
        @Override
        public ZonedDateTime previous(@Nonnull ZonedDateTime date) {
            if (date.getDayOfMonth() == 1) {
                return date.minusMonths(1).withDayOfMonth(16);
            }
            else if (date.getDayOfMonth() <= 16) {
                return date.withDayOfMonth(1);
            }
            return date.withDayOfMonth(16);
        }
    },
    quarterly {
        @Nonnull
        @Override
        public ZonedDateTime next(@Nonnull final ZonedDateTime date) {
            return date.plusMonths(3L);
        }

        @Nonnull
        @Override
        public ZonedDateTime previous(@Nonnull final ZonedDateTime date) {
            return date.minusMonths(3L);
        }
    },
    yearly {
        @Nonnull
        @Override
        public ZonedDateTime next(@Nonnull final ZonedDateTime date) {
            return date.plusYears(1L);
        }

        @Nonnull
        @Override
        public ZonedDateTime previous(@Nonnull final ZonedDateTime date) {
            return date.minusYears(1L);
        }
    };

    @Nonnull
    public abstract ZonedDateTime next(@Nonnull final ZonedDateTime date);

    @Nonnull
    public abstract ZonedDateTime previous(@Nonnull final ZonedDateTime date);
}
