package global.symbio.billing.core.services.rest.plexus.client.account.mixin;

import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.mixin.MixinConfigurator;
import global.symbio.billing.core.services.rest.plexus.client.account.AccountServiceApiClient;
import io.micronaut.context.annotation.Factory;
import io.micronaut.core.order.Ordered;
import io.micronaut.inject.BeanDefinition;
import io.micronaut.inject.BeanIdentifier;
import jakarta.annotation.Nonnull;

@Factory
@VisibleForTesting
class LoggingAccountServiceApiClientMixinConfigurator extends MixinConfigurator<AccountServiceApiClient> {

    private static final String COMPONENT = "logging";

    @Nonnull
    @Override
    public AccountServiceApiClient mixin(@Nonnull AccountServiceApiClient bean, @Nonnull String name) {
        return new LoggingAccountServiceApiClient(bean);
    }

    @Nonnull
    @Override
    protected String component() {
        return COMPONENT;
    }

    @Nonnull
    @Override
    protected String getName(@Nonnull AccountServiceApiClient bean, @Nonnull BeanDefinition<AccountServiceApiClient> definition, @Nonnull BeanIdentifier identifier) {
        return identifier.getName();
    }

    @Override
    protected boolean isEnabled() {
        return true;
    }

    @Override
    public int getOrder() {
        return Ordered.LOWEST_PRECEDENCE;
    }
}