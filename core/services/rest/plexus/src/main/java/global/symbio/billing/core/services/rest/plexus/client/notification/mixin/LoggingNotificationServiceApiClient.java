package global.symbio.billing.core.services.rest.plexus.client.notification.mixin;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.MoreObjects;
import global.symbio.billing.core.services.rest.plexus.client.notification.NotificationServiceApiClient;
import global.symbio.billing.core.services.rest.plexus.model.notification.NotificationResponse;
import global.symbio.billing.core.services.rest.plexus.model.notification.NotificationStatusResponse;
import global.symbio.billing.core.services.rest.plexus.model.notification.SendEmailRequest;
import io.micronaut.http.client.multipart.MultipartBody;
import jakarta.annotation.Nonnull;
import lombok.AccessLevel;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;

@VisibleForTesting
class LoggingNotificationServiceApiClient implements NotificationServiceApiClient {

    @Nonnull
    private final Logger log;

    @Nonnull
    @Getter(AccessLevel.PACKAGE)
    private final NotificationServiceApiClient api;

    public LoggingNotificationServiceApiClient(@Nonnull NotificationServiceApiClient api) {
        this(LoggerFactory.getLogger(api.getClass()), api);
    }

    public LoggingNotificationServiceApiClient(@Nonnull Logger log, @Nonnull NotificationServiceApiClient api) {
        this.log = Objects.requireNonNull(log, "log");
        this.api = Objects.requireNonNull(api, "api");
    }

    @Nonnull
    @Override
    public NotificationResponse uploadAttachment(@Nonnull MultipartBody body) {
        log.info("NotificationService::uploadAttachment");
        return api.uploadAttachment(body);
    }

    @Nonnull
    @Override
    public NotificationResponse sendEmail(@Nonnull SendEmailRequest request) {
        log.info("NotificationService::sendEmail: destination - {}, attachment - {}", request.destination(), request.attachmentRequestId());
        return api.sendEmail(request);
    }

    @Nonnull
    @Override
    public NotificationStatusResponse getNotificationStatus(@Nonnull String notificationRequestId) {
        log.info("NotificationService::getNotificationStatus: notification - {}", notificationRequestId);
        return api.getNotificationStatus(notificationRequestId);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof LoggingNotificationServiceApiClient notifications)) return false;
        return Objects.equals(getApi(), notifications.getApi());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getApi());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("api", getApi())
            .toString();
    }
}