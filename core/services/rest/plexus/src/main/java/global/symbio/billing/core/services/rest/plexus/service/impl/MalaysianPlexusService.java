package global.symbio.billing.core.services.rest.plexus.service.impl;

import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.i18n.LocalisationSettings;
import global.symbio.billing.core.i18n.annotation.country.Malaysia;
import global.symbio.billing.core.services.rest.ServiceDefinitions;
import global.symbio.billing.core.services.rest.plexus.client.account.AccountServiceApiClient;
import global.symbio.billing.core.services.rest.plexus.client.customer.CustomerServiceApiClient;
import global.symbio.billing.core.services.rest.plexus.client.notification.NotificationServiceApiClient;
import global.symbio.billing.core.services.rest.plexus.service.BasePlexusService;
import io.micronaut.context.annotation.Context;
import io.micronaut.retry.annotation.Retryable;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;

@Context
@Malaysia
@Retryable(attempts = ServiceDefinitions.Retry.ATTEMPTS, delay = ServiceDefinitions.Retry.DELAY)
@VisibleForTesting
class MalaysianPlexusService extends BasePlexusService {

    @Inject
    public MalaysianPlexusService(
        @Nonnull @Malaysia LocalisationSettings localisation,
        @Nonnull @Malaysia AccountServiceApiClient accounts,
        @Nonnull @Malaysia CustomerServiceApiClient customers,
        @Nonnull @Malaysia NotificationServiceApiClient notifications
    ) {
        super(localisation, accounts, customers, notifications);
    }
}