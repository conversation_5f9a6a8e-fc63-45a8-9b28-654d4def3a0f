package global.symbio.billing.core.services.rest.plexus.client.account.mixin;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.MoreObjects;
import global.symbio.billing.core.services.rest.plexus.client.account.AccountServiceApiClient;
import global.symbio.billing.core.services.rest.plexus.model.account.GetAccountResponse;
import global.symbio.billing.core.services.rest.plexus.model.account.GetAccountsResponse;
import global.symbio.billing.core.services.rest.plexus.model.account.PatchAccountRequest;
import global.symbio.billing.core.services.rest.plexus.model.response.BasePlexusApiResponse;
import jakarta.annotation.Nonnull;
import lombok.AccessLevel;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;
import java.util.UUID;

@VisibleForTesting
class LoggingAccountServiceApiClient implements AccountServiceApiClient {

    @Nonnull
    private final Logger log;

    @Nonnull
    @Getter(AccessLevel.PACKAGE)
    private final AccountServiceApiClient api;

    public LoggingAccountServiceApiClient(@Nonnull AccountServiceApiClient api) {
        this(LoggerFactory.getLogger(api.getClass()), api);
    }

    public LoggingAccountServiceApiClient(@Nonnull Logger log, @Nonnull AccountServiceApiClient api) {
        this.log = Objects.requireNonNull(log, "log");
        this.api = Objects.requireNonNull(api, "api");
    }

    @Nonnull
    @Override
    public GetAccountsResponse getBillableAccounts(@Nonnull String billingDate, int limit) {
        log.info("AccountService::getBillableAccounts: date - {}, limit - {}", billingDate, limit);
        return api.getBillableAccounts(billingDate, limit);
    }

    @Nonnull
    @Override
    public GetAccountResponse getAccount(@Nonnull UUID account) {
        log.info("AccountService::getAccount: account - {}", account);
        return api.getAccount(account);
    }

    @Nonnull
    @Override
    public BasePlexusApiResponse patchAccount(@Nonnull UUID account, @Nonnull PatchAccountRequest body) {
        log.info("AccountService::patchAccount: account - {}, body - {}", account, body);
        return api.patchAccount(account, body);
    }

    @Nonnull
    @Override
    public GetAccountsResponse getAccounts(@Nonnull UUID customer) {
        log.info("AccountService::getAccounts: customer - {}", customer);
        return api.getAccounts(customer);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof LoggingAccountServiceApiClient accounts)) return false;
        return Objects.equals(getApi(), accounts.getApi());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getApi());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("api", getApi())
            .toString();
    }
}