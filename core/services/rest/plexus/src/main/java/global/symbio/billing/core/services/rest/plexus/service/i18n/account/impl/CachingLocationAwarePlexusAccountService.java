package global.symbio.billing.core.services.rest.plexus.service.i18n.account.impl;

import com.github.benmanes.caffeine.cache.AsyncLoadingCache;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.i18n.localisation.LocationAware;
import global.symbio.billing.core.services.rest.plexus.model.account.PlexusAccount;
import global.symbio.billing.core.services.rest.plexus.service.i18n.account.PlexusAccountService;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.util.Objects;
import java.util.UUID;

@Singleton
@VisibleForTesting
record CachingLocationAwarePlexusAccountService(
    @Inject @Nonnull LoadingCache<LocationAwarePlexusAccountLookupRequest, PlexusAccount> accounts
) implements PlexusAccountService {

    public CachingLocationAwarePlexusAccountService {
        Objects.requireNonNull(accounts, "accounts");
    }

    @Inject
    public CachingLocationAwarePlexusAccountService(@Nonnull AsyncLoadingCache<LocationAwarePlexusAccountLookupRequest, PlexusAccount> accounts) {
        this(accounts.synchronous());
    }

    @Nullable
    @Override
    public PlexusAccount lookup(@Nonnull UUID identifier, @Nonnull LocationAware location) {
        final var request = new LocationAwarePlexusAccountLookupRequest(identifier, location.country());
        return accounts.get(request);
    }
}