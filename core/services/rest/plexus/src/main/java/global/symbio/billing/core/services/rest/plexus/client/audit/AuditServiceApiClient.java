package global.symbio.billing.core.services.rest.plexus.client.audit;

import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.services.rest.ServiceDefinitions;
import global.symbio.billing.core.services.rest.plexus.model.audit.GetAuditRecordSetsResponse;
import io.micronaut.http.annotation.Get;
import io.micronaut.http.annotation.Header;
import io.micronaut.http.annotation.QueryValue;
import io.micronaut.http.client.annotation.Client;
import jakarta.annotation.Nonnull;

import java.util.UUID;

@Client(ServiceDefinitions.Plexus.Audit.Global.URL)
@Header(name = ServiceDefinitions.Header.ORIGINATING_SERVICE_ID, value = ServiceDefinitions.Header.Value.ORIGINATING_SERVICE_ID)
@VisibleForTesting
public interface AuditServiceApiClient {

    @Nonnull
    @Get("/v2/recordsets")
    GetAuditRecordSetsResponse getRecordSets(@QueryValue @Nonnull UUID referenceID);

}