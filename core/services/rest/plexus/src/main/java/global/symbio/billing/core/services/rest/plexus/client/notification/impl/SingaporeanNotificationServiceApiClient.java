package global.symbio.billing.core.services.rest.plexus.client.notification.impl;

import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.i18n.annotation.country.Singapore;
import global.symbio.billing.core.services.rest.ServiceDefinitions;
import global.symbio.billing.core.services.rest.plexus.client.notification.NotificationServiceApiClient;
import io.micronaut.http.annotation.Header;
import io.micronaut.http.client.annotation.Client;

@Singapore
@Client(ServiceDefinitions.Plexus.Notification.Global.URL)
@Header(name = ServiceDefinitions.Header.COUNTRY_NAME, value = "sg") //TODO: Use ISO-3166 alpha-codes
@VisibleForTesting
interface SingaporeanNotificationServiceApiClient extends NotificationServiceApiClient {}