package global.symbio.billing.core.services.rest.plexus.model.account;

import global.symbio.billing.core.services.rest.plexus.model.common.PlexusContact;
import global.symbio.billing.core.services.rest.plexus.model.common.PlexusLink;
import global.symbio.billing.core.services.rest.plexus.model.common.PlexusReference;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.UUID;

public record PlexusAccount(
        UUID id,
        String plexid,
        String currency,
        String status,
        PlexusReference owner,
        String name,
        String accountNumber,
        PlexusReference billingAddress,
        List<PlexusContact> contacts,
        List<PlexusLink> links,
        String locale,
        ZonedDateTime createdAt,
        ZonedDateTime updatedAt,
        ZonedDateTime billingDate,
        BillingInterval billingPeriod,
        String purchaseOrderNumber,
        Boolean taxExempt,
        Boolean isTestAccount,
        String taxRegistrationNumber
) {}
