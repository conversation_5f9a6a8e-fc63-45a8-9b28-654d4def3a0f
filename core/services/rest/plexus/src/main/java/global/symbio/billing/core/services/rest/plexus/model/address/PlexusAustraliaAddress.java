package global.symbio.billing.core.services.rest.plexus.model.address;


import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.MoreObjects;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.Getter;

import java.util.Objects;
import java.util.UUID;

@Getter
public final class PlexusAustraliaAddress extends PlexusAddress {

    @Nonnull
    public static final String TYPE = "Australia";

    private final String buildingType;
    private final String buildingName;
    private final String unitNumber;
    private final String levelNumber;
    private final String levelType;
    private final String lotNumber;
    private final String roadNumber1;
    private final String roadNumber2;
    private final String roadName;
    private final String roadSuffix;
    private final String roadType;
    private final String locality;
    private final String state;
    private final String postalDeliveryType;

    @JsonCreator(mode = JsonCreator.Mode.PROPERTIES)
    public PlexusAustraliaAddress(
            @Nonnull @JsonProperty("id") UUID id,
            @Nullable @JsonProperty("plexid") String plexid,
            @Nullable @JsonProperty("country") String country,
            @Nullable @JsonProperty("postcode") String postcode,
            @Nullable @JsonProperty("buildingType") String buildingType,
            @Nullable @JsonProperty("buildingName") String buildingName,
            @Nullable @JsonProperty("unitNumber") String unitNumber,
            @Nullable @JsonProperty("levelNumber") String levelNumber,
            @Nullable @JsonProperty("levelType") String levelType,
            @Nullable @JsonProperty("lotNumber") String lotNumber,
            @Nullable @JsonProperty("roadNumber1") String roadNumber1,
            @Nullable @JsonProperty("roadNumber2") String roadNumber2,
            @Nullable @JsonProperty("roadName") String roadName,
            @Nullable @JsonProperty("roadSuffix") String roadSuffix,
            @Nullable @JsonProperty("roadType") String roadType,
            @Nullable @JsonProperty("locality") String locality,
            @Nullable @JsonProperty("state") String state,
            @Nullable @JsonProperty("postalDeliveryType") String postalDeliveryType)
    {
        super(TYPE, id, plexid, country, postcode);
        this.buildingType = buildingType;
        this.buildingName = buildingName;
        this.unitNumber = unitNumber;
        this.levelNumber = levelNumber;
        this.levelType = levelType;
        this.lotNumber = lotNumber;
        this.roadNumber1 = roadNumber1;
        this.roadNumber2 = roadNumber2;
        this.roadName = roadName;
        this.roadSuffix = roadSuffix;
        this.roadType = roadType;
        this.locality = locality;
        this.state = state;
        this.postalDeliveryType = postalDeliveryType;
    }

    @Nonnull
    @Override
    @JsonIgnore
    public String getCountryName() {
        return TYPE;
    }

    @Nonnull
    @Override
    @JsonIgnore
    public String getCompleteAddress() {
        return compose(
                buildingType,
                buildingName,
                unitNumber,
                levelNumber,
                lotNumber,
                roadNumber1,
                roadNumber2,
                roadName,
                roadSuffix,
                roadType,
                locality,
                state
        );
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof PlexusAustraliaAddress entity)) return false;
        return Objects.equals(getId(), entity.getId())
                && Objects.equals(getPlexid(), entity.getPlexid())
                && Objects.equals(getType(), entity.getType())
                && Objects.equals(getCountry(), entity.getCountry())
                && Objects.equals(getPostcode(), entity.getPostcode())
                && Objects.equals(getBuildingType(), entity.getBuildingType())
                && Objects.equals(getBuildingName(), entity.getBuildingName())
                && Objects.equals(getUnitNumber(), entity.getUnitNumber())
                && Objects.equals(getLevelNumber(), entity.getLevelNumber())
                && Objects.equals(getLevelType(), entity.getLevelType())
                && Objects.equals(getLotNumber(), entity.getLotNumber())
                && Objects.equals(getRoadNumber1(), entity.getRoadNumber1())
                && Objects.equals(getRoadNumber2(), entity.getRoadNumber2())
                && Objects.equals(getRoadName(), entity.getRoadName())
                && Objects.equals(getRoadSuffix(), entity.getRoadSuffix())
                && Objects.equals(getRoadType(), entity.getRoadType())
                && Objects.equals(getLocality(), entity.getLocality())
                && Objects.equals(getState(), entity.getState())
                && Objects.equals(getPostalDeliveryType(), entity.getPostalDeliveryType());
    }

    @Override
    public int hashCode() {
        return Objects.hash(
                getId(),
                getPlexid(),
                getType(),
                getCountry(),
                getPostcode(),
                getBuildingType(),
                getBuildingName(),
                getUnitNumber(),
                getLevelNumber(),
                getLevelType(),
                getLotNumber(),
                getRoadNumber1(),
                getRoadNumber2(),
                getRoadName(),
                getRoadSuffix(),
                getRoadType(),
                getLocality(),
                getState(),
                getPostalDeliveryType()
        );
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(getClass())
                .add("identifier", getId())
                .add("plexid", getPlexid())
                .add("type", getType())
                .add("country", getCountry())
                .add("postcode", getPostcode())
                .add("buildingType", getBuildingType())
                .add("buildingName", getBuildingName())
                .add("unitNumber", getUnitNumber())
                .add("levelNumber", getLevelNumber())
                .add("levelType", getLevelType())
                .add("lotNumber", getLotNumber())
                .add("roadNumber1", getRoadNumber1())
                .add("roadNumber2", getRoadNumber2())
                .add("roadName", getRoadName())
                .add("roadSuffix", getRoadSuffix())
                .add("roadType", getRoadType())
                .add("locality", getLocality())
                .add("state", getState())
                .add("postalDeliveryType", getPostalDeliveryType())
                .toString();
    }
}
