package global.symbio.billing.core.services.rest.plexus.model.notification;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonUnwrapped;
import jakarta.annotation.Nullable;

@JsonInclude(JsonInclude.Include.NON_NULL)
public record SendEmailRequest(
    @JsonUnwrapped
    EmailDestination destination,
    String attachmentRequestId,
    String from,
    String fromDisplayName,
    String subjectLine,
    @Nullable Template template,
    @Nullable String emailBody
) {}