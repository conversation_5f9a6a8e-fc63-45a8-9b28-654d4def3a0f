package global.symbio.billing.core.services.rest.plexus.client.notification.mixin;

import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.mixin.MixinConfigurator;
import global.symbio.billing.core.services.rest.plexus.client.notification.NotificationServiceApiClient;
import io.micronaut.context.annotation.Factory;
import io.micronaut.core.order.Ordered;
import io.micronaut.inject.BeanDefinition;
import io.micronaut.inject.BeanIdentifier;
import jakarta.annotation.Nonnull;

@Factory
@VisibleForTesting
class LoggingNotificationServiceApiClientMixinConfigurator extends MixinConfigurator<NotificationServiceApiClient> {

    private static final String COMPONENT = "logging";

    @Nonnull
    @Override
    public NotificationServiceApiClient mixin(@Nonnull NotificationServiceApiClient bean, @Nonnull String name) {
        return new LoggingNotificationServiceApiClient(bean);
    }

    @Nonnull
    @Override
    protected String component() {
        return COMPONENT;
    }

    @Nonnull
    @Override
    protected String getName(@Nonnull NotificationServiceApiClient bean, @Nonnull BeanDefinition<NotificationServiceApiClient> definition, @Nonnull BeanIdentifier identifier) {
        return identifier.getName();
    }

    @Override
    protected boolean isEnabled() {
        return true;
    }

    @Override
    public int getOrder() {
        return Ordered.LOWEST_PRECEDENCE;
    }
}