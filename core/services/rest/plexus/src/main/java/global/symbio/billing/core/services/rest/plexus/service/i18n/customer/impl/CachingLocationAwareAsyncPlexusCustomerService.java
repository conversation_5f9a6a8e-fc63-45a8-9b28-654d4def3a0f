package global.symbio.billing.core.services.rest.plexus.service.i18n.customer.impl;

import com.github.benmanes.caffeine.cache.AsyncLoadingCache;
import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.i18n.localisation.LocationAware;
import global.symbio.billing.core.services.rest.plexus.model.customer.PlexusCustomer;
import global.symbio.billing.core.services.rest.plexus.service.i18n.customer.AsyncPlexusCustomerService;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

@Singleton
@VisibleForTesting
record CachingLocationAwareAsyncPlexusCustomerService(
    @Inject @Nonnull AsyncLoadingCache<LocationAwarePlexusCustomerLookupRequest, PlexusCustomer> customers
) implements AsyncPlexusCustomerService {

    public CachingLocationAwareAsyncPlexusCustomerService {
        Objects.requireNonNull(customers, "customers");
    }

    @Nonnull
    @Override
    public CompletableFuture<PlexusCustomer> lookup(@Nonnull UUID identifier, @Nonnull LocationAware location) {
        final var request = new LocationAwarePlexusCustomerLookupRequest(identifier, location.country());
        return customers.get(request);
    }
}