package global.symbio.billing.core.services.rest.plexus.service;

import global.symbio.billing.core.i18n.localisation.Localised;
import global.symbio.billing.core.services.rest.plexus.model.account.GetAccountResponse;
import global.symbio.billing.core.services.rest.plexus.model.account.GetAccountsResponse;
import global.symbio.billing.core.services.rest.plexus.model.address.GetAddressResponse;
import global.symbio.billing.core.services.rest.plexus.model.customer.GetCustomerResponse;
import global.symbio.billing.core.services.rest.plexus.model.customer.GetPersonResponse;
import global.symbio.billing.core.services.rest.plexus.model.notification.EmailDestination;
import global.symbio.billing.core.services.rest.plexus.model.notification.NotificationResponse;
import global.symbio.billing.core.services.rest.plexus.model.notification.NotificationStatusResponse;
import global.symbio.billing.core.services.rest.plexus.model.notification.Template;
import global.symbio.billing.core.services.rest.plexus.model.response.PlexusAPIResponse;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.io.File;
import java.time.ZonedDateTime;
import java.util.UUID;

public interface PlexusService extends Localised {

    @Nonnull
    GetAccountsResponse getBillableAccounts(@Nonnull ZonedDateTime timestamp);

    @Nonnull
    GetAccountResponse getAccount(@Nonnull UUID account);

    @Nonnull
    GetAddressResponse getAddress(@Nonnull UUID customer, @Nonnull UUID address);

    @Nonnull
    GetCustomerResponse getCustomer(@Nonnull UUID customer);

    @Nonnull
    GetPersonResponse getPerson(@Nonnull UUID customer, @Nonnull UUID person);

    @Nonnull
    PlexusAPIResponse updateAccountBillingDate(@Nonnull UUID account, @Nonnull ZonedDateTime date);

    @Nonnull
    NotificationResponse uploadAttachment(@Nonnull String filename, @Nonnull byte[] data);

    @Nonnull
    NotificationResponse uploadAttachment(@Nonnull String filename, @Nonnull File file);

    NotificationResponse uploadAttachment(@Nonnull String filename, @Nonnull File file, @Nullable String attachmentRequestId);

    @Nonnull
    NotificationResponse sendEmail(
        @Nonnull EmailDestination destination,
        @Nonnull String attachmentRequestId,
        @Nonnull String fromEmail,
        @Nonnull String fromDisplayName,
        @Nonnull String subjectLine,
        @Nullable String emailBody,
        @Nullable Template template
    );

    @Nonnull
    NotificationStatusResponse getNotificationStatus(@Nonnull String notification);

    @Nonnull
    GetAccountsResponse getAccounts(@Nonnull UUID customer);
}
