package global.symbio.billing.core.services.rest.plexus.client.customer.impl;

import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.i18n.annotation.country.Singapore;
import global.symbio.billing.core.services.rest.ServiceDefinitions;
import global.symbio.billing.core.services.rest.plexus.client.customer.CustomerServiceApiClient;
import io.micronaut.http.annotation.Header;
import io.micronaut.http.client.annotation.Client;

@Singapore
@Client(ServiceDefinitions.Plexus.Customer.SG.URL)
@Header(name = ServiceDefinitions.Header.API_KEY, value = ServiceDefinitions.Plexus.Gateway.SG.TOKEN)
@VisibleForTesting
interface SingaporeanCustomerServiceApiClient extends CustomerServiceApiClient {}