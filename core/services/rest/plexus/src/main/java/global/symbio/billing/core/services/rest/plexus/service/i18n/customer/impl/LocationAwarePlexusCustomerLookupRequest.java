package global.symbio.billing.core.services.rest.plexus.service.i18n.customer.impl;

import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.i18n.localisation.LocationAware;
import jakarta.annotation.Nonnull;

import java.util.Objects;
import java.util.UUID;

@VisibleForTesting
record LocationAwarePlexusCustomerLookupRequest(
    @Nonnull UUID customer,
    @Nonnull String country
) implements LocationAware {

    public LocationAwarePlexusCustomerLookupRequest {
        Objects.requireNonNull(customer, "customer");
        Objects.requireNonNull(country, "country");
    }
}