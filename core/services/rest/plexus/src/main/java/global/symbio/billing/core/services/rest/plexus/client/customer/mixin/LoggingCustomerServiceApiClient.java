package global.symbio.billing.core.services.rest.plexus.client.customer.mixin;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.MoreObjects;
import global.symbio.billing.core.services.rest.plexus.client.customer.CustomerServiceApiClient;
import global.symbio.billing.core.services.rest.plexus.model.address.GetAddressResponse;
import global.symbio.billing.core.services.rest.plexus.model.customer.GetCustomerResponse;
import global.symbio.billing.core.services.rest.plexus.model.customer.GetPersonResponse;
import jakarta.annotation.Nonnull;
import lombok.AccessLevel;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;
import java.util.UUID;

@VisibleForTesting
class LoggingCustomerServiceApiClient implements CustomerServiceApiClient {

    @Nonnull
    private final Logger log;

    @Nonnull
    @Getter(AccessLevel.PACKAGE)
    private final CustomerServiceApiClient api;

    public LoggingCustomerServiceApiClient(@Nonnull CustomerServiceApiClient api) {
        this(LoggerFactory.getLogger(api.getClass()), api);
    }

    public LoggingCustomerServiceApiClient(@Nonnull Logger log, @Nonnull CustomerServiceApiClient api) {
        this.log = Objects.requireNonNull(log, "log");
        this.api = Objects.requireNonNull(api, "api");
    }

    @Override
    public GetCustomerResponse getCustomer(@Nonnull UUID customer) {
        log.info("CustomerService::getCustomer: customer - {}", customer);
        return api.getCustomer(customer);
    }

    @Nonnull
    @Override
    public GetAddressResponse getAddress(@Nonnull UUID customer, @Nonnull UUID address) {
        log.info("CustomerService::getAddress: customer - {}, address - {}", customer, address);
        return api.getAddress(customer, address);
    }

    @Nonnull
    @Override
    public GetPersonResponse getPerson(@Nonnull UUID customer, @Nonnull UUID person) {
        log.info("CustomerService::getPerson: customer - {}, person - {}", customer, person);
        return api.getPerson(customer, person);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof LoggingCustomerServiceApiClient customers)) return false;
        return Objects.equals(getApi(), customers.getApi());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getApi());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("api", getApi())
            .toString();
    }
}