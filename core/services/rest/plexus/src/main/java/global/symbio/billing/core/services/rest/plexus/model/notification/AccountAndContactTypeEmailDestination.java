package global.symbio.billing.core.services.rest.plexus.model.notification;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.util.Objects;
import java.util.UUID;

@JsonInclude(JsonInclude.Include.NON_NULL)
public record AccountAndContactTypeEmailDestination(
    @Nonnull UUID accountId,
    @Nonnull RecipientContactType recipientContactType,
    @Nullable RecipientEmail recipientEmail
) implements EmailDestination {

    public AccountAndContactTypeEmailDestination {
        Objects.requireNonNull(accountId, "accountId");
        Objects.requireNonNull(recipientContactType, "recipientContactType");
    }
}
