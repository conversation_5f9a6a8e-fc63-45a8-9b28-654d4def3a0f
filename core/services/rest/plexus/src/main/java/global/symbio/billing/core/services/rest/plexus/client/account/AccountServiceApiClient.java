package global.symbio.billing.core.services.rest.plexus.client.account;

import global.symbio.billing.core.services.rest.ServiceDefinitions;
import global.symbio.billing.core.services.rest.plexus.model.account.GetAccountResponse;
import global.symbio.billing.core.services.rest.plexus.model.account.GetAccountsResponse;
import global.symbio.billing.core.services.rest.plexus.model.account.PatchAccountRequest;
import global.symbio.billing.core.services.rest.plexus.model.response.BasePlexusApiResponse;
import io.micronaut.http.annotation.*;
import jakarta.annotation.Nonnull;

import java.util.UUID;

@Header(name = ServiceDefinitions.Header.ORIGINATING_SERVICE_ID, value = ServiceDefinitions.Header.Value.ORIGINATING_SERVICE_ID)
public interface AccountServiceApiClient {

    @Nonnull
    @Get
    GetAccountsResponse getBillableAccounts(@QueryValue @Nonnull String billingDate, @QueryValue int limit);

    @Nonnull
    @Get("/{account}")
    GetAccountResponse getAccount(@PathVariable @Nonnull UUID account);

    @Nonnull
    @Patch("/{account}")
    BasePlexusApiResponse patchAccount(@PathVariable @Nonnull UUID account, @Body @Nonnull PatchAccountRequest body);

    @Nonnull
    @Get
    GetAccountsResponse getAccounts(@QueryValue @Nonnull UUID owner);

}