package global.symbio.billing.core.services.rest.plexus.client.notification;

import global.symbio.billing.core.services.rest.ServiceDefinitions;
import global.symbio.billing.core.services.rest.plexus.model.notification.NotificationResponse;
import global.symbio.billing.core.services.rest.plexus.model.notification.NotificationStatusResponse;
import global.symbio.billing.core.services.rest.plexus.model.notification.SendEmailRequest;
import io.micronaut.http.MediaType;
import io.micronaut.http.annotation.*;
import io.micronaut.http.client.multipart.MultipartBody;
import jakarta.annotation.Nonnull;

@Header(name = ServiceDefinitions.Header.ORIGINATING_SERVICE_ID, value = ServiceDefinitions.Header.Value.ORIGINATING_SERVICE_ID)
public interface NotificationServiceApiClient {

    @Nonnull
    @Post(value = "/v2/uploadAttachment", produces = MediaType.MULTIPART_FORM_DATA)
    NotificationResponse uploadAttachment(@Body @Nonnull MultipartBody body);

    @Nonnull
    @Post("/v2/sendEmail")
    @Header(name = ServiceDefinitions.Header.ENVIRONMENT_NAME, value = ServiceDefinitions.Plexus.Notification.Global.ENV)
    NotificationResponse sendEmail(@Body @Nonnull SendEmailRequest request);

    @Nonnull
    @Get("/v2/notificationStatus/{notificationRequestId}")
    NotificationStatusResponse getNotificationStatus(@PathVariable @Nonnull String notificationRequestId);
}