package global.symbio.billing.core.services.rest.plexus.model.address;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

@Getter
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "type")
@JsonSubTypes(
    value = {
        @JsonSubTypes.Type(value = PlexusAustraliaAddress.class, name = PlexusAustraliaAddress.TYPE),
        @JsonSubTypes.Type(value = PlexusNewZealandAddress.class, name = PlexusNewZealandAddress.TYPE),
        @JsonSubTypes.Type(value = PlexusSingaporeAddress.class, name = PlexusSingaporeAddress.TYPE),
        @JsonSubTypes.Type(value = PlexusInternationalAddress.class, name = PlexusInternationalAddress.TYPE) //Malaysia falls under this in plexus
    }
)
public abstract sealed class PlexusAddress permits PlexusAustraliaAddress, PlexusInternationalAddress, PlexusNewZealandAddress, PlexusSingaporeAddress {

    @Nonnull
    protected final String type;

    @Nonnull
    protected final UUID id;

    @Nullable
    protected final String plexid, country, postcode;

    public PlexusAddress(@Nonnull String type, @Nonnull UUID id, @Nullable String plexid, @Nullable String country, @Nullable String postcode) {
        this.type = Objects.requireNonNull(type, "type");
        this.id = Objects.requireNonNull(id, "id");
        this.plexid = plexid;
        this.country = country;
        this.postcode = postcode;
    }

    @Nonnull
    public abstract String getCountryName();

    @Nonnull
    public abstract String getCompleteAddress();

    @Nonnull
    protected static String compose(@Nonnull final String... components) {
        return Arrays.stream(components).filter(Objects::nonNull).collect(Collectors.joining(", "));
    }
}