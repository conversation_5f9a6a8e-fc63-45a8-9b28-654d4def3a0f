package global.symbio.billing.core.services.rest.plexus.model.customer;

import global.symbio.billing.core.services.rest.plexus.model.address.PlexusAddress;
import global.symbio.billing.core.services.rest.plexus.model.common.PlexusContact;
import global.symbio.billing.core.services.rest.plexus.model.common.PlexusLink;
import global.symbio.billing.core.services.rest.plexus.model.common.PlexusReference;
import jakarta.annotation.Nonnull;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.UUID;

public record PlexusCustomer(
    List<PlexusAddress> addresses,
    PlexusReference brand,
    PlexusBusinessNumber businessNumber,
    List<PlexusContact> contacts,
    String country,
    UUID id,
    String legalName,
    List<PlexusLink> links,
    List<PlexusPeople> people,
    String plexid,
    PlexusReference registeredAddress,
    String status,
    String subtype,
    String type,
    String tradingName,
    Integer paymentTerm,
    ZonedDateTime createdAt,
    ZonedDateTime updatedAt,

    //for individuals
    PlexusName name,
    ZonedDateTime dob

) {

    @Nonnull
    public String getCustomerName() {
        final var type = PlexusCustomerType.get(this);
        return switch (type) {
            case business -> this.legalName;
            case individual -> this.name.getName();
        };
    }
}
