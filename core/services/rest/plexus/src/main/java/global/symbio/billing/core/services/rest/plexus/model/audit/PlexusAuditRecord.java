package global.symbio.billing.core.services.rest.plexus.model.audit;

import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.UUID;

public record PlexusAuditRecord(
    UUID id,
    Integer version,
    ZonedDateTime eventTime,
    String service,
    Principal principal,
    UUID interactionId,
    UUID correlationId,
    String action,
    String originPath,
    Map<String, Object> arguments,
    List<String> argumentsRefs,
    Map<String, Object> outcome,
    List<String> outcomeRefs
) {
}
