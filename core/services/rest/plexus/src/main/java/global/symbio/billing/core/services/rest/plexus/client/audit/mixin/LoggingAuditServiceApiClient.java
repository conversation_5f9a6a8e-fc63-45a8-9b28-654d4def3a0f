package global.symbio.billing.core.services.rest.plexus.client.audit.mixin;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.MoreObjects;
import global.symbio.billing.core.services.rest.plexus.client.audit.AuditServiceApiClient;
import global.symbio.billing.core.services.rest.plexus.model.audit.GetAuditRecordSetsResponse;
import jakarta.annotation.Nonnull;
import lombok.AccessLevel;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;
import java.util.UUID;

@VisibleForTesting
class LoggingAuditServiceApiClient implements AuditServiceApiClient {

    @Nonnull
    private final Logger log;

    @Nonnull
    @Getter(AccessLevel.PACKAGE)
    private final AuditServiceApiClient api;

    public LoggingAuditServiceApiClient(@Nonnull AuditServiceApiClient api) {
        this(LoggerFactory.getLogger(api.getClass()), api);
    }

    public LoggingAuditServiceApiClient(@Nonnull Logger log, @Nonnull AuditServiceApiClient api) {
        this.log = Objects.requireNonNull(log, "log");
        this.api = Objects.requireNonNull(api, "api");
    }

    @Nonnull
    @Override
    public GetAuditRecordSetsResponse getRecordSets(@Nonnull UUID referenceID) {
        log.info("AuditService::getRecordSets: reference - {}", referenceID);
        return api.getRecordSets(referenceID);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof LoggingAuditServiceApiClient accounts)) return false;
        return Objects.equals(getApi(), accounts.getApi());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getApi());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("api", getApi())
            .toString();
    }
}