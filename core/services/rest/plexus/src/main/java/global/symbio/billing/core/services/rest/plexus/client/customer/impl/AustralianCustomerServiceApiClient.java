package global.symbio.billing.core.services.rest.plexus.client.customer.impl;

import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.i18n.annotation.country.Australia;
import global.symbio.billing.core.services.rest.ServiceDefinitions;
import global.symbio.billing.core.services.rest.plexus.client.customer.CustomerServiceApiClient;
import io.micronaut.http.annotation.Header;
import io.micronaut.http.client.annotation.Client;

@Australia
@Client(ServiceDefinitions.Plexus.Customer.AU.URL)
@Header(name = ServiceDefinitions.Header.API_KEY, value = ServiceDefinitions.Plexus.Gateway.AU.TOKEN)
@VisibleForTesting
interface AustralianCustomerServiceApiClient extends CustomerServiceApiClient {}