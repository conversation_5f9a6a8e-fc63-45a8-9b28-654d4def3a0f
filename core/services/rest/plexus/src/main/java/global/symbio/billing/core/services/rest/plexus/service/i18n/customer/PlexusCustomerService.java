package global.symbio.billing.core.services.rest.plexus.service.i18n.customer;

import global.symbio.billing.core.i18n.localisation.LocationAware;
import global.symbio.billing.core.services.rest.plexus.model.customer.PlexusCustomer;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.util.UUID;

@FunctionalInterface
public interface PlexusCustomerService {

    @Nullable
    PlexusCustomer lookup(@Nonnull UUID identifier, @Nonnull LocationAware location);

    @Nullable
    default PlexusCustomer lookup(@Nonnull UUID identifier, @Nonnull String country) {
        return lookup(identifier, LocationAware.of(country));
    }
}
