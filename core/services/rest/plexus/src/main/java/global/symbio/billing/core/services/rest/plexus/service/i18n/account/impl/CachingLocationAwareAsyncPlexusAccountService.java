package global.symbio.billing.core.services.rest.plexus.service.i18n.account.impl;

import com.github.benmanes.caffeine.cache.AsyncLoadingCache;
import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.i18n.localisation.LocationAware;
import global.symbio.billing.core.services.rest.plexus.model.account.PlexusAccount;
import global.symbio.billing.core.services.rest.plexus.service.i18n.account.AsyncPlexusAccountService;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

@Singleton
@VisibleForTesting
record CachingLocationAwareAsyncPlexusAccountService(
    @Inject @Nonnull AsyncLoadingCache<LocationAwarePlexusAccountLookupRequest, PlexusAccount> accounts
) implements AsyncPlexusAccountService {

    public CachingLocationAwareAsyncPlexusAccountService {
        Objects.requireNonNull(accounts, "accounts");
    }

    @Nonnull
    @Override
    public CompletableFuture<PlexusAccount> lookup(@Nonnull UUID identifier, @Nonnull LocationAware location) {
        final var request = new LocationAwarePlexusAccountLookupRequest(identifier, location.country());
        return accounts.get(request);
    }
}