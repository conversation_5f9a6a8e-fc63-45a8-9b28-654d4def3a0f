package global.symbio.billing.core.services.rest.plexus.client.customer;

import global.symbio.billing.core.services.rest.ServiceDefinitions;
import global.symbio.billing.core.services.rest.plexus.model.address.GetAddressResponse;
import global.symbio.billing.core.services.rest.plexus.model.customer.GetCustomerResponse;
import global.symbio.billing.core.services.rest.plexus.model.customer.GetPersonResponse;
import io.micronaut.http.annotation.Get;
import io.micronaut.http.annotation.Header;
import io.micronaut.http.annotation.PathVariable;
import jakarta.annotation.Nonnull;

import java.util.UUID;

@Header(name = ServiceDefinitions.Header.ORIGINATING_SERVICE_ID, value = ServiceDefinitions.Header.Value.ORIGINATING_SERVICE_ID)
public interface CustomerServiceApiClient {

    @Get("/{customer}")
    GetCustomerResponse getCustomer(@PathVariable @Nonnull UUID customer);

    @Nonnull
    @Get("/{customer}/addresses/{address}")
    GetAddressResponse getAddress(@PathVariable @Nonnull UUID customer, @PathVariable @Nonnull UUID address);

    @Nonnull
    @Get("/{customer}/people/{person}")
    GetPersonResponse getPerson(@PathVariable @Nonnull UUID customer, @PathVariable @Nonnull UUID person);
}