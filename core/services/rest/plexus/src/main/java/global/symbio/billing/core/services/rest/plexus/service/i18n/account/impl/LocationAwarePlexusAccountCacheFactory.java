package global.symbio.billing.core.services.rest.plexus.service.i18n.account.impl;

import com.github.benmanes.caffeine.cache.AsyncLoadingCache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.i18n.provider.LocalisedServiceProvider;
import global.symbio.billing.core.services.rest.plexus.model.account.PlexusAccount;
import io.micronaut.context.annotation.Factory;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.inject.Singleton;

import java.time.Duration;

@Factory
@VisibleForTesting
class LocationAwarePlexusAccountCacheFactory {

    public static final String CACHE_NAME = "accounts";
    private static final Duration REFRESH_INTERVAL = Duration.ofSeconds(90L);

    @Inject
    @Nonnull
    @Singleton
    @Named(CACHE_NAME)
    public AsyncLoadingCache<LocationAwarePlexusAccountLookupRequest, PlexusAccount> create_cache(
        @Nonnull LocalisedServiceProvider provider
    ) {
        final var loader = new LocationAwarePlexusAccountCacheLoader(provider);
        return Caffeine.newBuilder().refreshAfterWrite(REFRESH_INTERVAL).buildAsync(loader);
    }
}