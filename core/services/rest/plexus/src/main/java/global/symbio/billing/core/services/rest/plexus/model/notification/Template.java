package global.symbio.billing.core.services.rest.plexus.model.notification;

import jakarta.annotation.Nonnull;

import java.util.Map;
import java.util.Objects;

public record Template(
    @Nonnull String templateId,
    @Nonnull String version,
    @Nonnull Map<String, ?> content
) {

    public Template {
        Objects.requireNonNull(templateId, "templateId");
        Objects.requireNonNull(version, "version");
        Objects.requireNonNull(content, "content");
    }
}