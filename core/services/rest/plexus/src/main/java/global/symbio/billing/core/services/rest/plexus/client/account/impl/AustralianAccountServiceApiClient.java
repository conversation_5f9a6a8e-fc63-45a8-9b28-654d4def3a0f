package global.symbio.billing.core.services.rest.plexus.client.account.impl;

import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.i18n.annotation.country.Australia;
import global.symbio.billing.core.services.rest.ServiceDefinitions;
import global.symbio.billing.core.services.rest.plexus.client.account.AccountServiceApiClient;
import global.symbio.billing.core.services.rest.plexus.model.account.GetAccountsResponse;
import io.micronaut.http.annotation.Get;
import io.micronaut.http.annotation.Header;
import io.micronaut.http.annotation.QueryValue;
import io.micronaut.http.client.annotation.Client;
import jakarta.annotation.Nonnull;

@Australia
@Client(ServiceDefinitions.Plexus.Account.AU.URL)
@Header(name = ServiceDefinitions.Header.API_KEY, value = ServiceDefinitions.Plexus.Gateway.AU.TOKEN)
@VisibleForTesting
interface AustralianAccountServiceApiClient extends AccountServiceApiClient {

    @Override
    @Nonnull
    @Get(ServiceDefinitions.Plexus.Account.AU.QUERY_PARAM_COUNTRY_CODE)
    GetAccountsResponse getBillableAccounts(@QueryValue @Nonnull String billingDate, @QueryValue int limit);
}