package global.symbio.billing.core.services.rest.plexus.client.audit.mixin;

import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.mixin.MixinConfigurator;
import global.symbio.billing.core.services.rest.plexus.client.audit.AuditServiceApiClient;
import io.micronaut.context.annotation.Factory;
import io.micronaut.core.order.Ordered;
import io.micronaut.inject.BeanDefinition;
import io.micronaut.inject.BeanIdentifier;
import jakarta.annotation.Nonnull;

@Factory
@VisibleForTesting
class LoggingAuditServiceApiClientMixinConfigurator extends MixinConfigurator<AuditServiceApiClient> {

    private static final String COMPONENT = "logging";

    @Nonnull
    @Override
    public AuditServiceApiClient mixin(@Nonnull AuditServiceApiClient bean, @Nonnull String name) {
        return new LoggingAuditServiceApiClient(bean);
    }

    @Nonnull
    @Override
    protected String component() {
        return COMPONENT;
    }

    @Nonnull
    @Override
    protected String getName(@Nonnull AuditServiceApiClient bean, @Nonnull BeanDefinition<AuditServiceApiClient> definition, @Nonnull BeanIdentifier identifier) {
        return identifier.getName();
    }

    @Override
    protected boolean isEnabled() {
        return true;
    }

    @Override
    public int getOrder() {
        return Ordered.LOWEST_PRECEDENCE;
    }
}