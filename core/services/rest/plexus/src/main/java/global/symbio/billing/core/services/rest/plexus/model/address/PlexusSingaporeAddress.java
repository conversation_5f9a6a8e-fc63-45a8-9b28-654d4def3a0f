package global.symbio.billing.core.services.rest.plexus.model.address;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.MoreObjects;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.Getter;

import java.util.Objects;
import java.util.UUID;

@Getter
public final class PlexusSingaporeAddress extends PlexusAddress {

    @Nonnull
    public static final String TYPE = "Singapore";

    private final String buildingNameOrNumber;
    private final String apartmentNumber;
    private final String floor;
    private final String streetNumber;
    private final String streetName;
    private final String locality;
    private final String postalDeliveryNumber;

    @JsonCreator(mode = JsonCreator.Mode.PROPERTIES)
    public PlexusSingaporeAddress(
            @Nonnull @JsonProperty("id") UUID id,
            @Nullable @JsonProperty("plexid") String plexid,
            @Nullable @JsonProperty("country") String country,
            @Nullable @JsonProperty("postcode") String postcode,
            @Nullable @JsonProperty("buildingNameOrNumber") String buildingNameOrNumber,
            @Nullable @JsonProperty("apartmentNumber") String apartmentNumber,
            @Nullable @JsonProperty("floor") String floor,
            @Nullable @JsonProperty("streetNumber") String streetNumber,
            @Nullable @JsonProperty("streetName") String streetName,
            @Nullable @JsonProperty("locality") String locality,
            @Nullable @JsonProperty("postalDeliveryNumber") String postalDeliveryNumber) {
        super(TYPE, id, plexid, country, postcode);
        this.buildingNameOrNumber = buildingNameOrNumber;
        this.apartmentNumber = apartmentNumber;
        this.floor = floor;
        this.streetNumber = streetNumber;
        this.streetName = streetName;
        this.locality = locality;
        this.postalDeliveryNumber = postalDeliveryNumber;
    }

    @Nonnull
    @Override
    @JsonIgnore
    public String getCountryName() {
        return TYPE;
    }

    @Nonnull
    @Override
    @JsonIgnore
    public String getCompleteAddress() {
        return compose(
                buildingNameOrNumber,
                apartmentNumber,
                floor,
                streetNumber,
                streetName,
                locality
        );
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof PlexusSingaporeAddress entity)) return false;
        return Objects.equals(getId(), entity.getId())
                && Objects.equals(getPlexid(), entity.getPlexid())
                && Objects.equals(getType(), entity.getType())
                && Objects.equals(getCountry(), entity.getCountry())
                && Objects.equals(getPostcode(), entity.getPostcode())
                && Objects.equals(getBuildingNameOrNumber(), entity.getBuildingNameOrNumber())
                && Objects.equals(getApartmentNumber(), entity.getApartmentNumber())
                && Objects.equals(getFloor(), entity.getFloor())
                && Objects.equals(getStreetNumber(), entity.getStreetNumber())
                && Objects.equals(getStreetName(), entity.getStreetName())
                && Objects.equals(getLocality(), entity.getLocality())
                && Objects.equals(getPostalDeliveryNumber(), entity.getPostalDeliveryNumber());
    }

    @Override
    public int hashCode() {
        return Objects.hash(
                getId(),
                getPlexid(),
                getType(),
                getCountry(),
                getPostcode(),
                getBuildingNameOrNumber(),
                getApartmentNumber(),
                getFloor(),
                getStreetNumber(),
                getStreetName(),
                getLocality(),
                getPostalDeliveryNumber()
        );
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(getClass())
                .add("identifier", getId())
                .add("plexid", getPlexid())
                .add("type", getType())
                .add("country", getCountry())
                .add("postcode", getPostcode())
                .add("buildingNameOrNumber", getBuildingNameOrNumber())
                .add("apartmentNumber", getApartmentNumber())
                .add("floor", getFloor())
                .add("streetNumber", getStreetNumber())
                .add("streetName", getStreetName())
                .add("locality", getLocality())
                .add("postalDeliveryNumber", getPostalDeliveryNumber())
                .toString();
    }
}
