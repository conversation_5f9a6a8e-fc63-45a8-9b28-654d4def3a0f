package global.symbio.billing.core.services.rest.plexus.service.i18n.account;

import global.symbio.billing.core.i18n.localisation.LocationAware;
import global.symbio.billing.core.services.rest.plexus.model.account.PlexusAccount;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.util.UUID;

@FunctionalInterface
public interface PlexusAccountService {

    @Nullable
    PlexusAccount lookup(@Nonnull UUID identifier, @Nonnull LocationAware location);

    @Nullable
    default PlexusAccount lookup(@Nonnull UUID identifier, @Nonnull String country) {
        return lookup(identifier, LocationAware.of(country));
    }
}