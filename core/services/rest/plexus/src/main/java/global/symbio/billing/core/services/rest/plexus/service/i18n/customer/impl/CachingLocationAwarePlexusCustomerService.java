package global.symbio.billing.core.services.rest.plexus.service.i18n.customer.impl;

import com.github.benmanes.caffeine.cache.AsyncLoadingCache;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.i18n.localisation.LocationAware;
import global.symbio.billing.core.services.rest.plexus.model.customer.PlexusCustomer;
import global.symbio.billing.core.services.rest.plexus.service.i18n.customer.PlexusCustomerService;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.util.Objects;
import java.util.UUID;

@Singleton
@VisibleForTesting
record CachingLocationAwarePlexusCustomerService(
    @Inject @Nonnull LoadingCache<LocationAwarePlexusCustomerLookupRequest, PlexusCustomer> customers
) implements PlexusCustomerService {

    public CachingLocationAwarePlexusCustomerService {
        Objects.requireNonNull(customers, "customers");
    }

    @Inject
    public CachingLocationAwarePlexusCustomerService(@Nonnull AsyncLoadingCache<LocationAwarePlexusCustomerLookupRequest, PlexusCustomer> customers) {
        this(customers.synchronous());
    }

    @Nullable
    @Override
    public PlexusCustomer lookup(@Nonnull UUID identifier, @Nonnull LocationAware location) {
        final var request = new LocationAwarePlexusCustomerLookupRequest(identifier, location.country());
        return customers.get(request);
    }
}