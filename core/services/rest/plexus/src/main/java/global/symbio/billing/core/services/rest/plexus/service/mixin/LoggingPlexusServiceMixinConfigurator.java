package global.symbio.billing.core.services.rest.plexus.service.mixin;

import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.mixin.MixinConfigurator;
import global.symbio.billing.core.services.rest.plexus.service.PlexusService;
import io.micronaut.context.annotation.Factory;
import io.micronaut.core.order.Ordered;
import io.micronaut.inject.BeanDefinition;
import io.micronaut.inject.BeanIdentifier;
import jakarta.annotation.Nonnull;

@Factory
@VisibleForTesting
class LoggingPlexusServiceMixinConfigurator extends MixinConfigurator<PlexusService> {

    private static final String COMPONENT = "logging";

    @Nonnull
    @Override
    public PlexusService mixin(@Nonnull PlexusService bean, @Nonnull String name) {
        return new LoggingPlexusService(bean);
    }

    @Nonnull
    @Override
    protected String component() {
        return COMPONENT;
    }

    @Nonnull
    @Override
    protected String getName(@Nonnull PlexusService bean, @Nonnull BeanDefinition<PlexusService> definition, @Nonnull BeanIdentifier identifier) {
        return identifier.getName();
    }

    @Override
    protected boolean isEnabled() {
        return true;
    }

    @Override
    public int getOrder() {
        return Ordered.LOWEST_PRECEDENCE;
    }
}