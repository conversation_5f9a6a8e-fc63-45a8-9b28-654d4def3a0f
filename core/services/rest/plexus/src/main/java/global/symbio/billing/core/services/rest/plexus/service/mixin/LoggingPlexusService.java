package global.symbio.billing.core.services.rest.plexus.service.mixin;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.MoreObjects;
import global.symbio.billing.core.i18n.LocalisationSettings;
import global.symbio.billing.core.services.rest.plexus.model.account.GetAccountResponse;
import global.symbio.billing.core.services.rest.plexus.model.account.GetAccountsResponse;
import global.symbio.billing.core.services.rest.plexus.model.address.GetAddressResponse;
import global.symbio.billing.core.services.rest.plexus.model.customer.GetCustomerResponse;
import global.symbio.billing.core.services.rest.plexus.model.customer.GetPersonResponse;
import global.symbio.billing.core.services.rest.plexus.model.notification.EmailDestination;
import global.symbio.billing.core.services.rest.plexus.model.notification.NotificationResponse;
import global.symbio.billing.core.services.rest.plexus.model.notification.NotificationStatusResponse;
import global.symbio.billing.core.services.rest.plexus.model.notification.Template;
import global.symbio.billing.core.services.rest.plexus.model.response.PlexusAPIResponse;
import global.symbio.billing.core.services.rest.plexus.service.PlexusService;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.AccessLevel;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.time.ZonedDateTime;
import java.util.Objects;
import java.util.UUID;

@VisibleForTesting
class LoggingPlexusService implements PlexusService {

    @Nonnull
    private final Logger log;

    @Nonnull
    @Getter(AccessLevel.PACKAGE)
    private final PlexusService service;

    public LoggingPlexusService(@Nonnull PlexusService service) {
        this(LoggerFactory.getLogger(service.getClass()), service);
    }

    public LoggingPlexusService(@Nonnull Logger log, @Nonnull PlexusService service) {
        this.log = Objects.requireNonNull(log, "log");
        this.service = Objects.requireNonNull(service, "service");
    }

    @Nonnull
    @Override
    public LocalisationSettings localisation() {
        return service.localisation();
    }

    @Nonnull
    @Override
    public GetAccountsResponse getBillableAccounts(@Nonnull ZonedDateTime timestamp) {
        log.info("Plexus::getBillableAccounts: timestamp - {}", timestamp);
        return service.getBillableAccounts(timestamp);
    }

    @Nonnull
    @Override
    public GetAccountResponse getAccount(@Nonnull UUID account) {
        log.info("Plexus::getAccount: account - {}", account);
        return service.getAccount(account);
    }

    @Nonnull
    @Override
    public GetAddressResponse getAddress(@Nonnull UUID customer, @Nonnull UUID address) {
        log.info("Plexus::getAddress: customer - {}, address - {}", customer, address);
        return service.getAddress(customer, address);
    }

    @Nonnull
    @Override
    public GetCustomerResponse getCustomer(@Nonnull UUID customer) {
        log.info("Plexus::getCustomer: customer - {}", customer);
        return service.getCustomer(customer);
    }

    @Nonnull
    @Override
    public GetPersonResponse getPerson(@Nonnull UUID customer, @Nonnull UUID person) {
        log.info("Plexus::getPerson: customer - {}, person - {}", customer, person);
        return service.getPerson(customer, person);
    }

    @Nonnull
    @Override
    public PlexusAPIResponse updateAccountBillingDate(@Nonnull UUID account, @Nonnull ZonedDateTime date) {
        log.info("Plexus::updateAccountBillingDate: account - {}, date - {}", account, date);
        return service.updateAccountBillingDate(account, date);
    }

    @Nonnull
    @Override
    public NotificationResponse uploadAttachment(@Nonnull String filename, @Nonnull byte[] data) {
        log.info("Plexus::uploadAttachment: filename - {}, size - {}", filename, data.length);
        return service.uploadAttachment(filename, data);
    }

    @Nonnull
    @Override
    public NotificationResponse uploadAttachment(@Nonnull String filename, @Nonnull File file) {
        log.info("Plexus::uploadAttachment: filename - {}, size - {}", filename, file.length());
        return service.uploadAttachment(filename, file);
    }

    @Override
    public NotificationResponse uploadAttachment(@Nonnull String filename, @Nonnull File file, @Nullable String attachmentRequestId) {
        log.info("Plexus::uploadAttachment: filename - {}, size - {}, id - {}", filename, file.length(), attachmentRequestId);
        return service.uploadAttachment(filename, file, attachmentRequestId);
    }

    @Nonnull
    @Override
    public NotificationResponse sendEmail(
        @Nonnull EmailDestination destination,
        @Nonnull String attachmentRequestId,
        @Nonnull String fromEmail,
        @Nonnull String fromDisplayName,
        @Nonnull String subjectLine,
        @Nullable String emailBody,
        @Nullable Template template
    ) {
        log.info("Plexus::sendEmail: destination - {}, attachment - {}", destination, attachmentRequestId);
        return service.sendEmail(destination, attachmentRequestId, fromEmail, fromDisplayName, subjectLine, emailBody, template);
    }

    @Nonnull
    @Override
    public NotificationStatusResponse getNotificationStatus(@Nonnull String notification) {
        log.info("Plexus::getNotificationStatus: notification - {}", notification);
        return service.getNotificationStatus(notification);
    }

    @Nonnull
    @Override
    public GetAccountsResponse getAccounts(@Nonnull UUID customer) {
        log.info("Plexus::getAccounts: customer - {}", customer);
        return service.getAccounts(customer);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof LoggingPlexusService plexus)) return false;
        return Objects.equals(getService(), plexus.getService());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getService());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("service", getService())
            .toString();
    }
}