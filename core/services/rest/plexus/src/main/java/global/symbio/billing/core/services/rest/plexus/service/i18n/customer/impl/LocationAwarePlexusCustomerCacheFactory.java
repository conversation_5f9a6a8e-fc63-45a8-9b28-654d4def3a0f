package global.symbio.billing.core.services.rest.plexus.service.i18n.customer.impl;

import com.github.benmanes.caffeine.cache.AsyncLoadingCache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.i18n.provider.LocalisedServiceProvider;
import global.symbio.billing.core.services.rest.plexus.model.customer.PlexusCustomer;
import io.micronaut.context.annotation.Factory;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.inject.Singleton;

import java.time.Duration;

@Factory
@VisibleForTesting
class LocationAwarePlexusCustomerCacheFactory {

    public static final String CACHE_NAME = "customers";
    private static final Duration REFRESH_INTERVAL = Duration.ofSeconds(90L);

    @Inject
    @Nonnull
    @Singleton
    @Named(CACHE_NAME)
    public AsyncLoadingCache<LocationAwarePlexusCustomerLookupRequest, PlexusCustomer> create_cache(
        @Nonnull LocalisedServiceProvider provider
    ) {
        final var loader = new LocationAwarePlexusCustomerCacheLoader(provider);
        return Caffeine.newBuilder().refreshAfterWrite(REFRESH_INTERVAL).buildAsync(loader);
    }
}