package global.symbio.billing.core.services.rest.plexus.service.i18n.account.impl;

import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.i18n.localisation.LocationAware;
import jakarta.annotation.Nonnull;

import java.util.Objects;
import java.util.UUID;

@VisibleForTesting
record LocationAwarePlexusAccountLookupRequest(
    @Nonnull UUID account,
    @Nonnull String country
) implements LocationAware {

    public LocationAwarePlexusAccountLookupRequest {
        Objects.requireNonNull(account, "account");
        Objects.requireNonNull(country, "country");
    }
}