package global.symbio.billing.core.services.rest.plexus.model.audit;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.Builder;
import jakarta.validation.constraints.NotBlank;
import java.util.Objects;

@Builder
public record Principal(
    @Nonnull @NotBlank String id,
    @Nonnull @NotBlank String name,
    @Nullable String type
) {

    public Principal {
        Objects.requireNonNull(id, "id");
        Objects.requireNonNull(name, "name");
    }
}
