package global.symbio.billing.core.services.rest.plexus.service.impl;

import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.i18n.LocalisationSettings;
import global.symbio.billing.core.i18n.annotation.country.Australia;
import global.symbio.billing.core.services.rest.ServiceDefinitions;
import global.symbio.billing.core.services.rest.plexus.client.account.AccountServiceApiClient;
import global.symbio.billing.core.services.rest.plexus.client.customer.CustomerServiceApiClient;
import global.symbio.billing.core.services.rest.plexus.client.notification.NotificationServiceApiClient;
import global.symbio.billing.core.services.rest.plexus.service.BasePlexusService;
import io.micronaut.context.annotation.Context;
import io.micronaut.retry.annotation.Retryable;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;

@Context
@Australia
@Retryable(attempts = ServiceDefinitions.Retry.ATTEMPTS, delay = ServiceDefinitions.Retry.DELAY)
@VisibleForTesting
class AustralianPlexusService extends BasePlexusService {

    @Inject
    public AustralianPlexusService(
        @Nonnull @Australia LocalisationSettings localisation,
        @Nonnull @Australia AccountServiceApiClient accounts,
        @Nonnull @Australia CustomerServiceApiClient customers,
        @Nonnull @Australia NotificationServiceApiClient notifications
    ) {
        super(localisation, accounts, customers, notifications);
    }
}