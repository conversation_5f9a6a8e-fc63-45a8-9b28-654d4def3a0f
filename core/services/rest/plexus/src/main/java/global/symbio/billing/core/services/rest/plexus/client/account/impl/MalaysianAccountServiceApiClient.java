package global.symbio.billing.core.services.rest.plexus.client.account.impl;

import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.i18n.annotation.country.Malaysia;
import global.symbio.billing.core.services.rest.ServiceDefinitions;
import global.symbio.billing.core.services.rest.plexus.client.account.AccountServiceApiClient;
import io.micronaut.http.annotation.Header;
import io.micronaut.http.client.annotation.Client;

@Malaysia
@Client(ServiceDefinitions.Plexus.Account.MY.URL)
@Header(name = ServiceDefinitions.Header.API_KEY, value = ServiceDefinitions.Plexus.Gateway.MY.TOKEN)
@VisibleForTesting
interface MalaysianAccountServiceApiClient extends AccountServiceApiClient {}