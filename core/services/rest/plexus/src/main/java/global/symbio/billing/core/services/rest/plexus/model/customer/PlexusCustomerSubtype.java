package global.symbio.billing.core.services.rest.plexus.model.customer;

import jakarta.annotation.Nonnull;

public enum PlexusCustomerSubtype {
    business,
    carrier
    ;

    @Nonnull
    public static PlexusCustomerSubtype get(@Nonnull PlexusCustomer customer) {
        //TODO: PRGBIL-235 temp behavior to default it to business.
        //not all customers have subtypes right now and this will affect our invoicing if we don't default it
        return customer.subtype() == null ? business : valueOf(customer.subtype());
    }
}
