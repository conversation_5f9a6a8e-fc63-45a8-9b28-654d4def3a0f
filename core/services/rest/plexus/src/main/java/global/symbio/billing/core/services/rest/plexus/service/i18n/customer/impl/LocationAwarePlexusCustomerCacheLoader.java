package global.symbio.billing.core.services.rest.plexus.service.i18n.customer.impl;

import com.github.benmanes.caffeine.cache.CacheLoader;
import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.i18n.provider.LocalisedServiceProvider;
import global.symbio.billing.core.services.rest.plexus.model.customer.PlexusCustomer;
import global.symbio.billing.core.services.rest.plexus.service.PlexusService;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.util.Objects;

@VisibleForTesting
record LocationAwarePlexusCustomerCacheLoader(
    @Nonnull LocalisedServiceProvider provider
) implements CacheLoader<LocationAwarePlexusCustomerLookupRequest, PlexusCustomer> {

    public LocationAwarePlexusCustomerCacheLoader {
        Objects.requireNonNull(provider, "provider");
    }

    @Nullable
    @Override
    public PlexusCustomer load(@Nonnull LocationAwarePlexusCustomerLookupRequest key) {
        final var plexus = provider.lookup(PlexusService.class, key); //TODO: memoize?
        final var response = plexus.getCustomer(key.customer());
        if (response == null || !response.success()) {
            return null;
        }
        return response.data();
    }
}