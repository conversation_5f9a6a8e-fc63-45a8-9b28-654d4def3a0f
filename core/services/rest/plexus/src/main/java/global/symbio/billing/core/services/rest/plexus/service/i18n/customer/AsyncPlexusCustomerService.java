package global.symbio.billing.core.services.rest.plexus.service.i18n.customer;

import global.symbio.billing.core.i18n.localisation.LocationAware;
import global.symbio.billing.core.services.rest.plexus.model.customer.PlexusCustomer;
import jakarta.annotation.Nonnull;

import java.util.UUID;
import java.util.concurrent.CompletableFuture;

@FunctionalInterface
public interface AsyncPlexusCustomerService {

    @Nonnull
    CompletableFuture<PlexusCustomer> lookup(@Nonnull UUID identifier, @Nonnull LocationAware location);

    @Nonnull
    default CompletableFuture<PlexusCustomer> lookup(@Nonnull UUID identifier, @Nonnull String country) {
        return lookup(identifier, LocationAware.of(country));
    }
}