package global.symbio.billing.core.services.rest.plexus.service.impl;

import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.i18n.LocalisationSettings;
import global.symbio.billing.core.i18n.annotation.country.Singapore;
import global.symbio.billing.core.services.rest.ServiceDefinitions;
import global.symbio.billing.core.services.rest.plexus.client.account.AccountServiceApiClient;
import global.symbio.billing.core.services.rest.plexus.client.customer.CustomerServiceApiClient;
import global.symbio.billing.core.services.rest.plexus.client.notification.NotificationServiceApiClient;
import global.symbio.billing.core.services.rest.plexus.service.BasePlexusService;
import io.micronaut.context.annotation.Context;
import io.micronaut.retry.annotation.Retryable;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;

@Context
@Singapore
@Retryable(attempts = ServiceDefinitions.Retry.ATTEMPTS, delay = ServiceDefinitions.Retry.DELAY)
@VisibleForTesting
class SingaporeanPlexusService extends BasePlexusService {

    @Inject
    public SingaporeanPlexusService(
        @Nonnull @Singapore LocalisationSettings localisation,
        @Nonnull @Singapore AccountServiceApiClient accounts,
        @Nonnull @Singapore CustomerServiceApiClient customers,
        @Nonnull @Singapore NotificationServiceApiClient notifications
    ) {
        super(localisation, accounts, customers, notifications);
    }
}