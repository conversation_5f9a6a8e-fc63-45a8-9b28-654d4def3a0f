package global.symbio.billing.core.services.rest.plexus.service;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.i18n.LocalisationSettings;
import global.symbio.billing.core.services.rest.plexus.client.account.AccountServiceApiClient;
import global.symbio.billing.core.services.rest.plexus.client.customer.CustomerServiceApiClient;
import global.symbio.billing.core.services.rest.plexus.client.notification.NotificationServiceApiClient;
import global.symbio.billing.core.services.rest.plexus.model.account.GetAccountResponse;
import global.symbio.billing.core.services.rest.plexus.model.account.GetAccountsResponse;
import global.symbio.billing.core.services.rest.plexus.model.account.PatchAccountRequest;
import global.symbio.billing.core.services.rest.plexus.model.address.GetAddressResponse;
import global.symbio.billing.core.services.rest.plexus.model.customer.GetCustomerResponse;
import global.symbio.billing.core.services.rest.plexus.model.customer.GetPersonResponse;
import global.symbio.billing.core.services.rest.plexus.model.notification.*;
import global.symbio.billing.core.services.rest.plexus.model.response.PlexusAPIResponse;
import io.micronaut.http.client.multipart.MultipartBody;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.io.File;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;
import java.util.UUID;

public class BasePlexusService implements PlexusService {

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ISO_INSTANT;

    //TODO remove after plexus fixes response
    private static final int MAX_ACCOUNT_LIMIT = 999;

    @Nonnull
    private final LocalisationSettings localisation;

    @Nonnull
    private final AccountServiceApiClient accounts;

    @Nonnull
    private final CustomerServiceApiClient customers;

    @Nonnull
    private final NotificationServiceApiClient notifications;

    protected BasePlexusService(
        @Nonnull LocalisationSettings localisation,
        @Nonnull AccountServiceApiClient accounts,
        @Nonnull CustomerServiceApiClient customers,
        @Nonnull NotificationServiceApiClient notifications
    ) {
        this.localisation = Objects.requireNonNull(localisation, "localisation");
        this.accounts = Objects.requireNonNull(accounts, "accounts");
        this.customers = Objects.requireNonNull(customers, "customers");
        this.notifications = Objects.requireNonNull(notifications, "notifications");
    }

    @Nonnull
    @Override
    public LocalisationSettings localisation() {
        return localisation;
    }

    @Nonnull
    @Override
    public GetAccountsResponse getBillableAccounts(@Nonnull ZonedDateTime timestamp) {
        final var date = timestamp.format(FORMATTER);
        return accounts.getBillableAccounts(date, MAX_ACCOUNT_LIMIT);
    }

    @Nonnull
    @Override
    public GetAccountResponse getAccount(@Nonnull UUID account) {
        return accounts.getAccount(account);
    }

    @Nonnull
    @Override
    public GetAddressResponse getAddress(@Nonnull UUID customer, @Nonnull UUID address) {
        return customers.getAddress(customer, address);
    }

    @Nonnull
    @Override
    public GetCustomerResponse getCustomer(@Nonnull UUID customer) {
        return customers.getCustomer(customer);
    }

    @Nonnull
    @Override
    public GetPersonResponse getPerson(@Nonnull UUID customer, @Nonnull UUID person) {
        return customers.getPerson(customer, person);
    }

    @Nonnull
    @Override
    public PlexusAPIResponse updateAccountBillingDate(@Nonnull UUID account, @Nonnull ZonedDateTime date) {
        final var timestamp = date.format(FORMATTER);
        final var request = new PatchAccountRequest(timestamp);
        return accounts.patchAccount(account, request);
    }

    @Nonnull
    @Override
    public NotificationResponse uploadAttachment(@Nonnull String filename, @Nonnull byte[] data) {
        final var request = MultipartBody.builder().addPart("attachment", filename, data);
        return upload(request.build());
    }

    @Nonnull
    @Override
    public NotificationResponse uploadAttachment(@Nonnull String filename, @Nonnull File file) {
        return uploadAttachment(filename, file, null);
    }

    @Nonnull
    @Override
    public NotificationResponse uploadAttachment(
        @Nonnull String filename,
        @Nonnull File file,
        @Nullable String attachmentRequestId
    ) {
        final var request = MultipartBody.builder().addPart("attachment", filename, file);
        //multiple attachments in an email should have the same attachmentRequestId
        if (attachmentRequestId != null) {
            request.addPart("attachmentRequestId", attachmentRequestId);
        }
        return upload(request.build());
    }

    @Nonnull
    private NotificationResponse upload(@Nonnull MultipartBody body) {
        return notifications.uploadAttachment(body);
    }

    @Nonnull
    @Override
    public NotificationResponse sendEmail(
        @Nonnull EmailDestination destination,
        @Nonnull String attachmentRequestId,
        @Nonnull String fromEmail,
        @Nonnull String fromDisplayName,
        @Nonnull String subjectLine,
        @Nullable String emailBody,
        @Nullable Template template
    ) {
        final var request = new SendEmailRequest(destination, attachmentRequestId, fromEmail, fromDisplayName, subjectLine, template, emailBody);
        return notifications.sendEmail(request);
    }

    @Nonnull
    @Override
    public NotificationStatusResponse getNotificationStatus(@Nonnull String notification) {
        return notifications.getNotificationStatus(notification);
    }

    @Nonnull
    @Override
    public GetAccountsResponse getAccounts(@Nonnull UUID customer) {
        return accounts.getAccounts(customer);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof BasePlexusService plexus)) return false;
        return Objects.equals(localisation, plexus.localisation) && Objects.equals(accounts, plexus.accounts) && Objects.equals(customers, plexus.customers) && Objects.equals(notifications, plexus.notifications);
    }

    @Override
    public int hashCode() {
        return Objects.hash(localisation, accounts, customers, notifications);
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("localisation", localisation)
            .add("accounts", accounts)
            .add("customers", customers)
            .add("notifications", notifications)
            .toString();
    }
}