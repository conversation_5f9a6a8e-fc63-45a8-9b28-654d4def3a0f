package global.symbio.billing.core.services.rest.plexus.service.i18n.account.impl;

import com.github.benmanes.caffeine.cache.CacheLoader;
import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.i18n.provider.LocalisedServiceProvider;
import global.symbio.billing.core.services.rest.plexus.model.account.PlexusAccount;
import global.symbio.billing.core.services.rest.plexus.service.PlexusService;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.util.Objects;

@VisibleForTesting
record LocationAwarePlexusAccountCacheLoader(
    @Nonnull LocalisedServiceProvider provider
) implements CacheLoader<LocationAwarePlexusAccountLookupRequest, PlexusAccount> {

    public LocationAwarePlexusAccountCacheLoader {
        Objects.requireNonNull(provider, "provider");
    }

    @Nullable
    @Override
    public PlexusAccount load(@Nonnull LocationAwarePlexusAccountLookupRequest key) {
        final var plexus = provider.lookup(PlexusService.class, key); //TODO: memoize?
        final var response = plexus.getAccount(key.account());
        if (response == null || !response.success()) {
            return null;
        }
        return response.data();
    }
}
