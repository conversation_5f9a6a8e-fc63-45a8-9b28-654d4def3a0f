package global.symbio.billing.core.services.rest.sonar.customer.bridge.service.mixin;

import global.symbio.billing.core.i18n.LocalisationSettings;
import global.symbio.billing.core.services.rest.sonar.customer.bridge.model.AccountTenancy;
import global.symbio.billing.core.services.rest.sonar.customer.bridge.service.CustomerBridgeService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestLoggingCustomerBridgeService {

    @Mock
    private Logger log;

    @Mock
    private CustomerBridgeService delegate;

    private LoggingCustomerBridgeService service;

    @BeforeEach
    public void setup() {
        service = new LoggingCustomerBridgeService(log, delegate);
    }

    @Test
    @DisplayName("LoggingCustomerBridgeService::new rejects null constructor arguments")
    public void logging_customer_bridge_service_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new LoggingCustomerBridgeService(null));
        assertThrows(NullPointerException.class, () -> new LoggingCustomerBridgeService(log, null));
        assertThrows(NullPointerException.class, () -> new LoggingCustomerBridgeService(null, delegate));
    }

    @Test
    @DisplayName("LoggingCustomerBridgeService::localisation retrieves localisation settings from delegate")
    public void logging_customer_bridge_service_localisation_invokes_delegate_method() {
        final var localisation = mock(LocalisationSettings.class);
        when(delegate.localisation()).thenReturn(localisation);
        final var result = assertDoesNotThrow(service::localisation);
        assertSame(localisation, result);
        verify(delegate, times(1)).localisation();
    }

    @Test
    @DisplayName("LoggingCustomerBridgeService::tenancy logs and invokes method on delegate")
    public void logging_customer_bridge_service_tenancy_logs_and_invokes_delegate_method() {
        final var tenancy = mock(AccountTenancy.class);
        when(delegate.tenancy(anyString(), anyLong())).thenReturn(tenancy);
        final var result = assertDoesNotThrow(() -> service.tenancy("platform", 1337L));
        assertSame(tenancy, result);
        verify(log, times(1)).info(anyString(), eq("platform"), eq(1337L));
        verify(delegate, times(1)).tenancy(eq("platform"), eq(1337L));
    }
}