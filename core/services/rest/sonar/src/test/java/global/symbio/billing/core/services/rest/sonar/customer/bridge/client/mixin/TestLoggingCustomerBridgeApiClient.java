package global.symbio.billing.core.services.rest.sonar.customer.bridge.client.mixin;

import global.symbio.billing.core.services.rest.sonar.customer.bridge.client.CustomerBridgeApiClient;
import global.symbio.billing.core.services.rest.sonar.customer.bridge.model.AccountTenancy;
import global.symbio.billing.core.services.rest.sonar.customer.bridge.model.AccountTenancyLookupRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestLoggingCustomerBridgeApiClient {

    @Mock
    private Logger log;

    @Mock
    private CustomerBridgeApiClient delegate;

    private LoggingCustomerBridgeApiClient client;

    @BeforeEach
    public void setup() {
        client = new LoggingCustomerBridgeApiClient(log, delegate);
    }

    @Test
    @DisplayName("LoggingCustomerBridgeApiClient::new rejects null constructor arguments")
    public void logging_customer_bridge_api_client_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new LoggingCustomerBridgeApiClient(null));
        assertThrows(NullPointerException.class, () -> new LoggingCustomerBridgeApiClient(log, null));
        assertThrows(NullPointerException.class, () -> new LoggingCustomerBridgeApiClient(null, delegate));
    }

    @Test
    @DisplayName("LoggingCustomerBridgeApiClient::lookup logs and invokes method on delegate")
    public void logging_customer_bridge_api_client_lookup_logs_and_invokes_delegate_method() {
        final var tenancy = Optional.of(mock(AccountTenancy.class));
        when(delegate.lookup(any(AccountTenancyLookupRequest.class))).thenReturn(tenancy);
        final var request = new AccountTenancyLookupRequest("platform", 1337L);
        final var result = assertDoesNotThrow(() -> client.lookup(request));
        assertSame(tenancy, result);
        verify(log, times(1)).info(anyString(), eq("platform"), eq(1337L));
        verify(delegate, times(1)).lookup(eq(request));
    }
}