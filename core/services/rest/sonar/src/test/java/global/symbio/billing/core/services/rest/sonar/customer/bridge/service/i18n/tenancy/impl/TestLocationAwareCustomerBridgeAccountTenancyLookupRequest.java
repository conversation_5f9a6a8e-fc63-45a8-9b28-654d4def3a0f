package global.symbio.billing.core.services.rest.sonar.customer.bridge.service.i18n.tenancy.impl;

import global.symbio.billing.core.i18n.localisation.LocationAware;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class TestLocationAwareCustomerBridgeAccountTenancyLookupRequest {

    @Test
    @DisplayName("LocationAwareCustomerBridgeAccountTenancyLookupRequest::new rejects null constructor arguments")
    public void location_aware_customer_bridge_account_tenancy_lookup_request_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new LocationAwareCustomerBridgeAccountTenancyLookupRequest(null, 0L, "country"));
        assertThrows(NullPointerException.class, () -> new LocationAwareCustomerBridgeAccountTenancyLookupRequest("platform", 0L, null));
    }

    @Test
    @DisplayName("LocationAwareCustomerBridgeAccountTenancyLookupRequest implements LocationAware interface")
    public void location_aware_customer_bridge_account_tenancy_lookup_request_is_location_aware() {
        assertTrue(LocationAware.class.isAssignableFrom(LocationAwareCustomerBridgeAccountTenancyLookupRequest.class));
    }
}