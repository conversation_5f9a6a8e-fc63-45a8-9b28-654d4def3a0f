package global.symbio.billing.core.services.rest.sonar.customer.bridge.service.i18n.tenancy.impl;

import com.github.benmanes.caffeine.cache.AsyncLoadingCache;
import com.github.benmanes.caffeine.cache.LoadingCache;
import global.symbio.billing.core.services.rest.sonar.customer.bridge.model.AccountTenancy;
import global.symbio.billing.core.services.rest.sonar.customer.bridge.service.i18n.tenancy.CustomerBridgeAccountTenancyService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestCachingLocationAwareCustomerBridgeAccountTenancyService {

    @Mock
    private LoadingCache<LocationAwareCustomerBridgeAccountTenancyLookupRequest, AccountTenancy> tenancies;

    private CustomerBridgeAccountTenancyService service;

    @BeforeEach
    public void setup() {
        service = new CachingLocationAwareCustomerBridgeAccountTenancyService(tenancies);
    }

    @Test
    @DisplayName("CachingLocationAwareCustomerBridgeAccountTenancyService::new rejects null constructor arguments")
    public void caching_location_aware_customer_bridge_account_tenancy_service_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new CachingLocationAwareCustomerBridgeAccountTenancyService((LoadingCache<LocationAwareCustomerBridgeAccountTenancyLookupRequest, AccountTenancy>) null));
        assertThrows(NullPointerException.class, () -> new CachingLocationAwareCustomerBridgeAccountTenancyService((AsyncLoadingCache<LocationAwareCustomerBridgeAccountTenancyLookupRequest, AccountTenancy>) null));
    }

    @Test
    @DisplayName("CachingLocationAwareCustomerBridgeAccountTenancyService::lookup returns tenancy from cache when tenancy in the cache")
    public void caching_location_aware_customer_bridge_account_tenancy_service_lookup_returns_tenancy_from_cache_when_tenancy_in_the_cache() {
        final var tenancy = mock(AccountTenancy.class);
        when(tenancies.get(any(LocationAwareCustomerBridgeAccountTenancyLookupRequest.class))).thenReturn(tenancy);
        final var result = assertDoesNotThrow(() -> service.lookup("platform", 1337L, "AU"));
        assertSame(tenancy, result);
        verify(tenancies, times(1)).get(eq(new LocationAwareCustomerBridgeAccountTenancyLookupRequest("platform", 1337L, "AU")));
    }

    @Test
    @DisplayName("CachingLocationAwareCustomerBridgeAccountTenancyService::lookup returns null when tenancy not in the cache")
    public void caching_location_aware_customer_bridge_account_tenancy_service_lookup_returns_null_when_tenancy_not_in_cache() {
        when(tenancies.get(any(LocationAwareCustomerBridgeAccountTenancyLookupRequest.class))).thenReturn(null);
        final var result = assertDoesNotThrow(() -> service.lookup("platform", 420L, "AU"));
        assertNull(result);
        verify(tenancies, times(1)).get(eq(new LocationAwareCustomerBridgeAccountTenancyLookupRequest("platform", 420L, "AU")));
    }

    @Test
    @DisplayName("CachingLocationAwareCustomerBridgeAccountTenancyService::lookup throws exception when cache hit throws exception")
    public void caching_location_aware_customer_bridge_account_tenancy_service_lookup_throws_exception_when_cache_hit_throws_exception() {
        final var failure = new RuntimeException("Underlying exception");
        when(tenancies.get(any(LocationAwareCustomerBridgeAccountTenancyLookupRequest.class))).thenThrow(failure);
        final var cause = assertThrows(RuntimeException.class, () -> service.lookup("platform", 69L, "AU"));
        assertSame(failure, cause);
        verify(tenancies, times(1)).get(eq(new LocationAwareCustomerBridgeAccountTenancyLookupRequest("platform", 69L, "AU")));
    }
}