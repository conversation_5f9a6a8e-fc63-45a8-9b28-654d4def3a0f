package global.symbio.billing.core.services.rest.sonar.customer.bridge.service;

import global.symbio.billing.core.i18n.LocalisationSettings;
import global.symbio.billing.core.services.rest.sonar.customer.bridge.client.CustomerBridgeApiClient;
import global.symbio.billing.core.services.rest.sonar.customer.bridge.model.AccountTenancy;
import global.symbio.billing.core.services.rest.sonar.customer.bridge.model.AccountTenancyLookupRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestBaseCustomerBridgeService {

    @Mock
    private LocalisationSettings localisation;

    @Mock
    private CustomerBridgeApiClient client;

    private BaseCustomerBridgeService bridge;

    @BeforeEach
    public void setup() {
        bridge = new BaseCustomerBridgeService(localisation, client);
    }

    @Test
    @DisplayName("BaseCustomerBridgeService::new rejects null constructor arguments")
    public void base_customer_bridge_service_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new BaseCustomerBridgeService(null, client));
        assertThrows(NullPointerException.class, () -> new BaseCustomerBridgeService(localisation, null));
    }

    @Test
    @DisplayName("BaseCustomerBridgeService::localisation returns LocalisationSettings")
    public void base_customer_bridge_service_localisation_returns_localisation_settings() {
        assertSame(localisation, bridge.localisation());
    }

    @Test
    @DisplayName("BaseCustomerBridgeService::tenancy invokes CustomerBridgeApiClient to retrieve account tenancy")
    public void base_customer_bridge_service_tenancy_invokes_customer_bridge_api_client() {
        final var tenancy = mock(AccountTenancy.class);
        final var request = new AccountTenancyLookupRequest("platform", 1337L);

        when(client.lookup(any(AccountTenancyLookupRequest.class))).thenReturn(Optional.of(tenancy));

        final var response = assertDoesNotThrow(() -> bridge.tenancy(request.platform(), request.account()));
        verify(client, times(1)).lookup(eq(request));
        assertSame(tenancy, response);
    }
}
