package global.symbio.billing.core.services.rest.sonar.customer.bridge.service.i18n.tenancy.impl;

import com.github.benmanes.caffeine.cache.CacheLoader;
import global.symbio.billing.core.i18n.localisation.LocationAware;
import global.symbio.billing.core.i18n.provider.LocalisedServiceProvider;
import global.symbio.billing.core.services.rest.sonar.customer.bridge.model.AccountTenancy;
import global.symbio.billing.core.services.rest.sonar.customer.bridge.service.CustomerBridgeService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestLocationAwareCustomerBridgeAccountTenancyCacheLoader {

    @Mock
    private LocalisedServiceProvider provider;

    private CacheLoader<LocationAwareCustomerBridgeAccountTenancyLookupRequest, AccountTenancy> loader;

    @BeforeEach
    public void setup() {
        loader = new LocationAwareCustomerBridgeAccountTenancyCacheLoader(provider);
    }

    @Test
    @DisplayName("LocationAwareCustomerBridgeAccountTenancyCacheLoader::new rejects null constructor arguments")
    public void location_aware_customer_bridge_account_tenancy_cache_loader_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new LocationAwareCustomerBridgeAccountTenancyCacheLoader(null));
    }

    @Test
    @DisplayName("LocationAwareCustomerBridgeAccountTenancyCacheLoader::load invokes localised CustomerBridgeService tenancy lookup")
    public void location_aware_customer_bridge_account_tenancy_cache_loader_load_invokes_customer_bridge_service_tenancy_lookup() {
        final var bridge = mock(CustomerBridgeService.class);
        final var tenancy = mock(AccountTenancy.class);
        final var request = new LocationAwareCustomerBridgeAccountTenancyLookupRequest("platform", 1337L, "country");
        when(provider.lookup(eq(CustomerBridgeService.class), any(LocationAware.class))).thenReturn(bridge);
        when(bridge.tenancy(anyString(), anyLong())).thenReturn(tenancy);
        final var result = assertDoesNotThrow(() -> loader.load(request));
        assertSame(tenancy, result);
        verify(bridge, times(1)).tenancy(eq("platform"), eq(1337L));
    }
}