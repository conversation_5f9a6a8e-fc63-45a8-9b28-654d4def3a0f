package global.symbio.billing.core.services.rest.sonar.customer.bridge.service.mixin;

import global.symbio.billing.core.services.rest.sonar.customer.bridge.service.CustomerBridgeService;
import io.micronaut.core.order.Ordered;
import io.micronaut.inject.BeanDefinition;
import io.micronaut.inject.BeanIdentifier;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestLoggingCustomerBridgeServiceMixinConfigurator {

    private LoggingCustomerBridgeServiceMixinConfigurator configurator;

    @BeforeEach
    public void setup() {
        configurator = new LoggingCustomerBridgeServiceMixinConfigurator();
    }

    @Test
    @DisplayName("LoggingCustomerBridgeServiceMixinConfigurator::mixin returns LoggingCustomerBridgeService")
    public void logging_customer_bridge_service_mixin_configurator_mixin_returns_logging_customer_bridge_service() {
        final var bean = mock(CustomerBridgeService.class);
        final var mixin = assertInstanceOf(LoggingCustomerBridgeService.class, assertDoesNotThrow(() -> configurator.mixin(bean, "name")));
        assertSame(bean, mixin.getService());
    }

    @Test
    @DisplayName("LoggingCustomerBridgeServiceMixinConfigurator::component is `logging`")
    public void logging_customer_bridge_service_mixin_configurator_component_is_logging() {
        assertSame("logging", configurator.component());
    }

    @Test
    @DisplayName("LoggingCustomerBridgeServiceMixinConfigurator::getName returns BeanIdentifier name")
    public void logging_customer_bridge_service_mixin_configurator_get_name_returns_bean_identifier_name() {
        final var bean = mock(CustomerBridgeService.class);
        final var definition = mock(BeanDefinition.class);
        final var identifier = mock(BeanIdentifier.class);
        when(identifier.getName()).thenReturn("name");
        final var name = assertDoesNotThrow(() -> configurator.getName(bean, definition, identifier));
        assertEquals("name", name);
        verifyNoInteractions(bean);
        verifyNoInteractions(definition);
        verify(identifier, times(1)).getName();
    }

    @Test
    @DisplayName("LoggingCustomerBridgeServiceMixinConfigurator::isEnabled is always `true`")
    public void logging_customer_bridge_service_mixin_configurator_is_enabled_is_always_true() {
        final var enabled = assertDoesNotThrow(configurator::isEnabled);
        assertTrue(enabled);
    }

    @Test
    @DisplayName("LoggingCustomerBridgeServiceMixinConfigurator::getOrder is `LOWEST_PRECEDENCE`")
    public void logging_customer_bridge_service_mixin_configurator_order_is_lowest_precedence() {
        final var order = assertDoesNotThrow(configurator::getOrder);
        assertEquals(Ordered.LOWEST_PRECEDENCE, order);
    }
}