package global.symbio.billing.core.services.rest.sonar.customer.bridge.service.i18n.tenancy.impl;

import com.github.benmanes.caffeine.cache.AsyncLoadingCache;
import global.symbio.billing.core.services.rest.sonar.customer.bridge.model.AccountTenancy;
import global.symbio.billing.core.services.rest.sonar.customer.bridge.service.i18n.tenancy.AsyncCustomerBridgeAccountTenancyService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestCachingLocationAwareAsyncCustomerBridgeAccountTenancyService {

    @Mock
    private AsyncLoadingCache<LocationAwareCustomerBridgeAccountTenancyLookupRequest, AccountTenancy> tenancies;

    private AsyncCustomerBridgeAccountTenancyService service;

    @BeforeEach
    public void setup() {
        service = new CachingLocationAwareAsyncCustomerBridgeAccountTenancyService(tenancies);
    }

    @Test
    @DisplayName("CachingLocationAwareAsyncCustomerBridgeAccountTenancyService::new rejects null constructor arguments")
    public void caching_location_aware_async_customer_bridge_account_tenancy_service_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new CachingLocationAwareAsyncCustomerBridgeAccountTenancyService(null));
    }

    @Test
    @DisplayName("CachingLocationAwareAsyncCustomerBridgeAccountTenancyService::lookup returns tenancy from cache when tenancy in the cache")
    public void caching_location_aware_async_customer_bridge_account_tenancy_service_lookup_returns_tenancy_from_cache_when_tenancy_in_the_cache() {
        final var tenancy = mock(AccountTenancy.class);
        when(tenancies.get(any(LocationAwareCustomerBridgeAccountTenancyLookupRequest.class))).thenReturn(CompletableFuture.completedFuture(tenancy));
        final var future = assertDoesNotThrow(() -> service.lookup("platform", 1337L, "AU"));
        final var result = assertDoesNotThrow(future::join);
        assertSame(tenancy, result);
        verify(tenancies, times(1)).get(eq(new LocationAwareCustomerBridgeAccountTenancyLookupRequest("platform", 1337L, "AU")));
    }

    @Test
    @DisplayName("CachingLocationAwareAsyncCustomerBridgeAccountTenancyService::lookup returns null when tenancy not in the cache")
    public void caching_location_aware_async_customer_bridge_account_tenancy_service_lookup_returns_null_when_tenancy_not_in_cache() {
        when(tenancies.get(any(LocationAwareCustomerBridgeAccountTenancyLookupRequest.class))).thenReturn(CompletableFuture.completedFuture(null));
        final var future = assertDoesNotThrow(() -> service.lookup("platform", 420L, "AU"));
        final var result = assertDoesNotThrow(future::join);
        assertNull(result);
        verify(tenancies, times(1)).get(eq(new LocationAwareCustomerBridgeAccountTenancyLookupRequest("platform", 420L, "AU")));
    }

    @Test
    @DisplayName("CachingLocationAwareAsyncCustomerBridgeAccountTenancyService::lookup throws exception when cache hit throws exception")
    public void caching_location_aware_async_customer_bridge_account_tenancy_service_lookup_throws_exception_when_cache_hit_throws_exception() {
        final var failure = new RuntimeException("Underlying exception");
        when(tenancies.get(any(LocationAwareCustomerBridgeAccountTenancyLookupRequest.class))).thenReturn(CompletableFuture.failedFuture(failure));
        final var future = assertDoesNotThrow(() -> service.lookup("platform", 69L, "AU"));
        final var cause = assertThrows(CompletionException.class, future::join);
        assertSame(failure, cause.getCause());
        verify(tenancies, times(1)).get(eq(new LocationAwareCustomerBridgeAccountTenancyLookupRequest("platform", 69L, "AU")));
    }
}