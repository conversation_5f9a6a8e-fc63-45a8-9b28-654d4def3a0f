package global.symbio.billing.core.services.rest.sonar.customer.bridge.service;

import global.symbio.billing.core.i18n.LocalisationSettings;
import global.symbio.billing.core.services.rest.sonar.customer.bridge.client.CustomerBridgeApiClient;
import global.symbio.billing.core.services.rest.sonar.customer.bridge.model.AccountTenancy;
import global.symbio.billing.core.services.rest.sonar.customer.bridge.model.AccountTenancyLookupRequest;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.util.Objects;

public class BaseCustomerBridgeService implements CustomerBridgeService {

    @Nonnull
    private final LocalisationSettings localisation;

    @Nonnull
    private final CustomerBridgeApiClient bridge;

    protected BaseCustomerBridgeService(
        @Nonnull LocalisationSettings localisation,
        @Nonnull CustomerBridgeApiClient bridge
    ) {
        this.localisation = Objects.requireNonNull(localisation, "localisation");
        this.bridge = Objects.requireNonNull(bridge, "bridge");
    }

    @Nonnull
    @Override
    public LocalisationSettings localisation() {
        return localisation;
    }

    @Nullable
    @Override
    public AccountTenancy tenancy(@Nonnull String platform, long account) {
        final var request = new AccountTenancyLookupRequest(platform, account);
        return bridge.lookup(request).orElse(null);
    }
}
