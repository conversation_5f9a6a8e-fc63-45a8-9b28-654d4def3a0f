package global.symbio.billing.core.services.rest.sonar.customer.bridge.service.i18n.tenancy;

import global.symbio.billing.core.i18n.localisation.LocationAware;
import global.symbio.billing.core.services.rest.sonar.customer.bridge.model.AccountTenancy;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

@FunctionalInterface
public interface CustomerBridgeAccountTenancyService {

    @Nullable
    AccountTenancy lookup(@Nonnull String platform, long account, @Nonnull LocationAware location);

    @Nullable
    default AccountTenancy lookup(@Nonnull String platform, long account, @Nonnull String country) {
        return lookup(platform, account, LocationAware.of(country));
    }
}