package global.symbio.billing.core.services.rest.sonar.customer.bridge.service.mixin;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.MoreObjects;
import global.symbio.billing.core.i18n.LocalisationSettings;
import global.symbio.billing.core.services.rest.sonar.customer.bridge.model.AccountTenancy;
import global.symbio.billing.core.services.rest.sonar.customer.bridge.service.CustomerBridgeService;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.AccessLevel;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;

@VisibleForTesting
class LoggingCustomerBridgeService implements CustomerBridgeService {

    @Nonnull
    private final Logger log;

    @Nonnull
    @Getter(AccessLevel.PACKAGE)
    private final CustomerBridgeService service;

    public LoggingCustomerBridgeService(@Nonnull CustomerBridgeService service) {
        this(LoggerFactory.getLogger(service.getClass()), service);
    }

    public LoggingCustomerBridgeService(@Nonnull Logger log, @Nonnull CustomerBridgeService service) {
        this.log = Objects.requireNonNull(log, "log");
        this.service = Objects.requireNonNull(service, "service");
    }

    @Nonnull
    @Override
    public LocalisationSettings localisation() {
        return service.localisation();
    }

    @Nullable
    @Override
    public AccountTenancy tenancy(@Nonnull String platform, long account) {
        log.info("CustomerBridge::tenancy: platform - {}, account - {}", platform, account);
        return service.tenancy(platform, account);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof LoggingCustomerBridgeService bridge)) return false;
        return Objects.equals(getService(), bridge.getService());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getService());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("service", getService())
            .toString();
    }
}