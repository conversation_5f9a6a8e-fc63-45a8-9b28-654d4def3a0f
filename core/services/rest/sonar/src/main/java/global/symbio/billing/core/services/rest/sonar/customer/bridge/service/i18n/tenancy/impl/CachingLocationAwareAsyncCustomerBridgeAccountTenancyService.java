package global.symbio.billing.core.services.rest.sonar.customer.bridge.service.i18n.tenancy.impl;

import com.github.benmanes.caffeine.cache.AsyncLoadingCache;
import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.i18n.localisation.LocationAware;
import global.symbio.billing.core.services.rest.sonar.customer.bridge.model.AccountTenancy;
import global.symbio.billing.core.services.rest.sonar.customer.bridge.service.i18n.tenancy.AsyncCustomerBridgeAccountTenancyService;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.util.Objects;
import java.util.concurrent.CompletableFuture;

@Singleton
@VisibleForTesting
record CachingLocationAwareAsyncCustomerBridgeAccountTenancyService(
    @Inject @Nonnull AsyncLoadingCache<LocationAwareCustomerBridgeAccountTenancyLookupRequest, AccountTenancy> tenancies
) implements AsyncCustomerBridgeAccountTenancyService {

    public CachingLocationAwareAsyncCustomerBridgeAccountTenancyService {
        Objects.requireNonNull(tenancies, "tenancies");
    }

    @Nonnull
    @Override
    public CompletableFuture<AccountTenancy> lookup(@Nonnull String platform, long account, @Nonnull LocationAware location) {
        final var request = new LocationAwareCustomerBridgeAccountTenancyLookupRequest(platform, account, location.country());
        return tenancies.get(request);
    }
}