package global.symbio.billing.core.services.rest.sonar.customer.bridge.service.mixin;

import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.mixin.MixinConfigurator;
import global.symbio.billing.core.services.rest.sonar.customer.bridge.service.CustomerBridgeService;
import io.micronaut.context.annotation.Factory;
import io.micronaut.core.order.Ordered;
import io.micronaut.inject.BeanDefinition;
import io.micronaut.inject.BeanIdentifier;
import jakarta.annotation.Nonnull;

@Factory
@VisibleForTesting
class LoggingCustomerBridgeServiceMixinConfigurator extends MixinConfigurator<CustomerBridgeService> {

    private static final String COMPONENT = "logging";

    @Nonnull
    @Override
    public CustomerBridgeService mixin(@Nonnull CustomerBridgeService bean, @Nonnull String name) {
        return new LoggingCustomerBridgeService(bean);
    }

    @Nonnull
    @Override
    protected String component() {
        return COMPONENT;
    }

    @Nonnull
    @Override
    protected String getName(@Nonnull CustomerBridgeService bean, @Nonnull BeanDefinition<CustomerBridgeService> definition, @Nonnull BeanIdentifier identifier) {
        return identifier.getName();
    }

    @Override
    protected boolean isEnabled() {
        return true;
    }

    @Override
    public int getOrder() {
        return Ordered.LOWEST_PRECEDENCE;
    }
}