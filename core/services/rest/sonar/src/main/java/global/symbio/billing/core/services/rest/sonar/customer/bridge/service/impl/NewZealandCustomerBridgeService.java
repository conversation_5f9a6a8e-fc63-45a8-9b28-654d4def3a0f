package global.symbio.billing.core.services.rest.sonar.customer.bridge.service.impl;

import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.i18n.LocalisationSettings;
import global.symbio.billing.core.i18n.annotation.country.NewZealand;
import global.symbio.billing.core.services.rest.ServiceDefinitions;
import global.symbio.billing.core.services.rest.sonar.customer.bridge.client.CustomerBridgeApiClient;
import global.symbio.billing.core.services.rest.sonar.customer.bridge.service.BaseCustomerBridgeService;
import io.micronaut.context.annotation.Context;
import io.micronaut.retry.annotation.Retryable;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;

@Context
@NewZealand
@Retryable(attempts = ServiceDefinitions.Retry.ATTEMPTS, delay = ServiceDefinitions.Retry.DELAY)
@VisibleForTesting
public class NewZealandCustomerBridgeService extends BaseCustomerBridgeService {
    @Inject
    public NewZealandCustomerBridgeService(
        @Nonnull @NewZealand LocalisationSettings localisation,
        @Nonnull @NewZealand CustomerBridgeApiClient bridge
    ) {
        super(localisation, bridge);
    }
}
