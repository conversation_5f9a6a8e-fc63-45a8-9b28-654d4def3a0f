package global.symbio.billing.core.services.rest.sonar.customer.bridge.client;

import global.symbio.billing.core.services.rest.sonar.customer.bridge.model.AccountTenancy;
import global.symbio.billing.core.services.rest.sonar.customer.bridge.model.AccountTenancyLookupRequest;
import io.micronaut.http.annotation.Body;
import io.micronaut.http.annotation.Post;

import java.util.Optional;

public interface CustomerBridgeApiClient {

    @Post("/v1/accounts/lookup")
    Optional<AccountTenancy> lookup(@Body AccountTenancyLookupRequest request);
}
