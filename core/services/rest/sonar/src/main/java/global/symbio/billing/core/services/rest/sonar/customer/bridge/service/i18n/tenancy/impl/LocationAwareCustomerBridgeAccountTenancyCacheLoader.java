package global.symbio.billing.core.services.rest.sonar.customer.bridge.service.i18n.tenancy.impl;

import com.github.benmanes.caffeine.cache.CacheLoader;
import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.i18n.provider.LocalisedServiceProvider;
import global.symbio.billing.core.services.rest.sonar.customer.bridge.model.AccountTenancy;
import global.symbio.billing.core.services.rest.sonar.customer.bridge.service.CustomerBridgeService;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.util.Objects;

@VisibleForTesting
record LocationAwareCustomerBridgeAccountTenancyCacheLoader(
    @Nonnull LocalisedServiceProvider provider
) implements CacheLoader<LocationAwareCustomerBridgeAccountTenancyLookupRequest, AccountTenancy> {

    public LocationAwareCustomerBridgeAccountTenancyCacheLoader {
        Objects.requireNonNull(provider, "provider");
    }

    @Nullable
    @Override
    public AccountTenancy load(@Nonnull LocationAwareCustomerBridgeAccountTenancyLookupRequest key) {
        final var bridge = provider.lookup(CustomerBridgeService.class, key); //TODO: memoize?
        return bridge.tenancy(key.platform(), key.account());
    }
}