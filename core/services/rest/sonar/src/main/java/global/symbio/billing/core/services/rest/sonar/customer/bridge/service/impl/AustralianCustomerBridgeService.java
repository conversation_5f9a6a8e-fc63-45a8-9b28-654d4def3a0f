package global.symbio.billing.core.services.rest.sonar.customer.bridge.service.impl;

import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.i18n.LocalisationSettings;
import global.symbio.billing.core.i18n.annotation.country.Australia;
import global.symbio.billing.core.services.rest.ServiceDefinitions;
import global.symbio.billing.core.services.rest.sonar.customer.bridge.client.CustomerBridgeApiClient;
import global.symbio.billing.core.services.rest.sonar.customer.bridge.service.BaseCustomerBridgeService;
import io.micronaut.context.annotation.Context;
import io.micronaut.retry.annotation.Retryable;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;

@Context
@Australia
@Retryable(attempts = ServiceDefinitions.Retry.ATTEMPTS, delay = ServiceDefinitions.Retry.DELAY)
@VisibleForTesting
public class AustralianCustomerBridgeService extends BaseCustomerBridgeService {
    @Inject
    public AustralianCustomerBridgeService(
        @Nonnull @Australia LocalisationSettings localisation,
        @Nonnull @Australia CustomerBridgeApiClient bridge
    ) {
        super(localisation, bridge);
    }
}
