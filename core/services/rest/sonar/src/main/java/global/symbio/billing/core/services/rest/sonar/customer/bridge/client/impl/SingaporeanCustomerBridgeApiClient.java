package global.symbio.billing.core.services.rest.sonar.customer.bridge.client.impl;

import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.i18n.annotation.country.Singapore;
import global.symbio.billing.core.services.rest.ServiceDefinitions;
import global.symbio.billing.core.services.rest.sonar.customer.bridge.client.CustomerBridgeApiClient;
import io.micronaut.http.annotation.Header;
import io.micronaut.http.client.annotation.Client;

@Singapore
@Client(ServiceDefinitions.Sonar.CustomerBridge.SG.URL)
@Header(name = ServiceDefinitions.Header.API_KEY, value = ServiceDefinitions.Sonar.CustomerBridge.SG.TOKEN)
@VisibleForTesting
interface SingaporeanCustomerBridgeApiClient extends CustomerBridgeApiClient {}