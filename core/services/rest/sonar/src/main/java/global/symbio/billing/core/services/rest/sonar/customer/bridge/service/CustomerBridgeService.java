package global.symbio.billing.core.services.rest.sonar.customer.bridge.service;

import global.symbio.billing.core.i18n.localisation.Localised;
import global.symbio.billing.core.services.rest.sonar.customer.bridge.model.AccountTenancy;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

public interface CustomerBridgeService extends Localised {

    @Nullable
    AccountTenancy tenancy(@Nonnull String platform, long account);
}
