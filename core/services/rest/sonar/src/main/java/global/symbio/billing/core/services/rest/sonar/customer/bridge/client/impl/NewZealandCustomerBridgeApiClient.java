package global.symbio.billing.core.services.rest.sonar.customer.bridge.client.impl;

import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.i18n.annotation.country.NewZealand;
import global.symbio.billing.core.services.rest.ServiceDefinitions;
import global.symbio.billing.core.services.rest.sonar.customer.bridge.client.CustomerBridgeApiClient;
import io.micronaut.http.annotation.Header;
import io.micronaut.http.client.annotation.Client;

@NewZealand
@Client(ServiceDefinitions.Sonar.CustomerBridge.AU.URL)
@Header(name = ServiceDefinitions.Header.API_KEY, value = ServiceDefinitions.Sonar.CustomerBridge.AU.TOKEN)
@VisibleForTesting
interface NewZealandCustomerBridgeApiClient extends CustomerBridgeApiClient {
}
