package global.symbio.billing.core.services.rest.sonar.customer.bridge.client.mixin;

import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.mixin.MixinConfigurator;
import global.symbio.billing.core.services.rest.sonar.customer.bridge.client.CustomerBridgeApiClient;
import io.micronaut.context.annotation.Factory;
import io.micronaut.core.order.Ordered;
import io.micronaut.inject.BeanDefinition;
import io.micronaut.inject.BeanIdentifier;
import jakarta.annotation.Nonnull;

@Factory
@VisibleForTesting
class LoggingCustomerBridgeApiClientMixinConfigurator extends MixinConfigurator<CustomerBridgeApiClient> {

    private static final String COMPONENT = "logging";

    @Nonnull
    @Override
    public CustomerBridgeApiClient mixin(@Nonnull CustomerBridgeApiClient bean, @Nonnull String name) {
        return new LoggingCustomerBridgeApiClient(bean);
    }

    @Nonnull
    @Override
    protected String component() {
        return COMPONENT;
    }

    @Nonnull
    @Override
    protected String getName(@Nonnull CustomerBridgeApiClient bean, @Nonnull BeanDefinition<CustomerBridgeApiClient> definition, @Nonnull BeanIdentifier identifier) {
        return identifier.getName();
    }

    @Override
    protected boolean isEnabled() {
        return true;
    }

    @Override
    public int getOrder() {
        return Ordered.LOWEST_PRECEDENCE;
    }
}