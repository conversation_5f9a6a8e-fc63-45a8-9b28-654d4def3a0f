package global.symbio.billing.core.services.rest.sonar.customer.bridge.service.i18n.tenancy.impl;

import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.i18n.localisation.LocationAware;
import jakarta.annotation.Nonnull;

import java.util.Objects;

@VisibleForTesting
record LocationAwareCustomerBridgeAccountTenancyLookupRequest(
    @Nonnull String platform,
    long account,
    @Nonnull String country
) implements LocationAware {

    public LocationAwareCustomerBridgeAccountTenancyLookupRequest {
        Objects.requireNonNull(platform, "platform");
        Objects.requireNonNull(country, "country");
    }
}
