package global.symbio.billing.core.services.rest.sonar.customer.bridge.service.i18n.tenancy.impl;

import com.github.benmanes.caffeine.cache.AsyncLoadingCache;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.i18n.localisation.LocationAware;
import global.symbio.billing.core.services.rest.sonar.customer.bridge.model.AccountTenancy;
import global.symbio.billing.core.services.rest.sonar.customer.bridge.service.i18n.tenancy.CustomerBridgeAccountTenancyService;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.util.Objects;

@Singleton
@VisibleForTesting
record CachingLocationAwareCustomerBridgeAccountTenancyService(
    @Inject @Nonnull LoadingCache<LocationAwareCustomerBridgeAccountTenancyLookupRequest, AccountTenancy> tenancies
) implements CustomerBridgeAccountTenancyService {

    public CachingLocationAwareCustomerBridgeAccountTenancyService {
        Objects.requireNonNull(tenancies, "tenancies");
    }

    @Inject
    public CachingLocationAwareCustomerBridgeAccountTenancyService(@Nonnull AsyncLoadingCache<LocationAwareCustomerBridgeAccountTenancyLookupRequest, AccountTenancy> tenancies) {
        this(tenancies.synchronous());
    }

    @Nullable
    @Override
    public AccountTenancy lookup(@Nonnull String platform, long account, @Nonnull LocationAware location) {
        final var request = new LocationAwareCustomerBridgeAccountTenancyLookupRequest(platform, account, location.country());
        return tenancies.get(request);
    }
}