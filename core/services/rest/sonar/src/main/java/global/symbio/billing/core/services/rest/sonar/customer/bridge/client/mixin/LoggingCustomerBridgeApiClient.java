package global.symbio.billing.core.services.rest.sonar.customer.bridge.client.mixin;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.MoreObjects;
import global.symbio.billing.core.services.rest.sonar.customer.bridge.client.CustomerBridgeApiClient;
import global.symbio.billing.core.services.rest.sonar.customer.bridge.model.AccountTenancy;
import global.symbio.billing.core.services.rest.sonar.customer.bridge.model.AccountTenancyLookupRequest;
import jakarta.annotation.Nonnull;
import lombok.AccessLevel;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;
import java.util.Optional;

@VisibleForTesting
class LoggingCustomerBridgeApiClient implements CustomerBridgeApiClient {

    @Nonnull
    private final Logger log;

    @Nonnull
    @Getter(AccessLevel.PACKAGE)
    private final CustomerBridgeApiClient api;

    public LoggingCustomerBridgeApiClient(@Nonnull CustomerBridgeApiClient api) {
        this(LoggerFactory.getLogger(api.getClass()), api);
    }

    public LoggingCustomerBridgeApiClient(@Nonnull Logger log, @Nonnull CustomerBridgeApiClient api) {
        this.log = Objects.requireNonNull(log, "log");
        this.api = Objects.requireNonNull(api, "api");
    }

    @Override
    public Optional<AccountTenancy> lookup(AccountTenancyLookupRequest request) {
        log.info("CustomerBridge::lookup: platform - {}, account - {}", request.platform(), request.account());
        return api.lookup(request);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof LoggingCustomerBridgeApiClient bridge)) return false;
        return Objects.equals(getApi(), bridge.getApi());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getApi());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("api", getApi())
            .toString();
    }
}