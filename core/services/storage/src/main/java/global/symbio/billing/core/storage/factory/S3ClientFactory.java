package global.symbio.billing.core.storage.factory;

import io.micronaut.context.annotation.Bean;
import io.micronaut.context.annotation.Factory;
import io.micronaut.context.annotation.Primary;
import io.micronaut.context.env.Environment;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.WebIdentityTokenFileCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;

import java.net.URI;

@Slf4j
@Factory
public class S3ClientFactory {

    public static final String NAME = "S3Client";

    @Inject @Primary @Bean @Named(NAME)
    public S3Client create_s3_client(@Nonnull Environment environment) {
        final var url = environment.getProperty("s3.url", String.class).get();
        final var region = environment.getProperty("s3.region", String.class).get();
        final var builder = S3Client.builder()
            .region(Region.of(region))
            .endpointOverride(URI.create(url))
            .forcePathStyle(true);

        if (environment.getActiveNames().contains(Environment.DEVELOPMENT)) {
            final var accessKey = environment.getProperty("s3.access.key", String.class).get();
            final var secretKey = environment.getProperty("s3.secret.key", String.class).get();
            builder.credentialsProvider(() -> AwsBasicCredentials.create(accessKey, secretKey));
        } else if (environment.getActiveNames().contains(Environment.CLOUD)) {
            builder.credentialsProvider(WebIdentityTokenFileCredentialsProvider.create());
        }
        final var s3Client = builder.build();
        log.info("Created {}", S3Client.class.getSimpleName());

        return s3Client;
    }
}
