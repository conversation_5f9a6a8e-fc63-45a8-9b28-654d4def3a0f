package global.symbio.billing.core.storage.mixin;

import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.storage.service.StorageService;
import jakarta.annotation.Nonnull;
import lombok.AccessLevel;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.FileNotFoundException;
import java.io.InputStream;
import java.util.Objects;

@VisibleForTesting
class LoggingStorageService implements StorageService {

    @Nonnull
    private final Logger log;

    @Nonnull
    @Getter(AccessLevel.PACKAGE)
    private final StorageService storage;

    public LoggingStorageService(@Nonnull StorageService storage) {
        this(LoggerFactory.getLogger(storage.getClass()), storage);
    }

    public LoggingStorageService(@Nonnull Logger log, @Nonnull StorageService storage) {
        this.log = Objects.requireNonNull(log, "log");
        this.storage = Objects.requireNonNull(storage, "storage");
    }

    @Nonnull
    @Override
    public InputStream downloadFile(@Nonnull String bucketName, @Nonnull String s3Key) throws FileNotFoundException {
        log.info("StorageService::downloadFile: bucketName - {}, key - {}", bucketName, s3Key);
        return storage.downloadFile(bucketName, s3Key);
    }
}