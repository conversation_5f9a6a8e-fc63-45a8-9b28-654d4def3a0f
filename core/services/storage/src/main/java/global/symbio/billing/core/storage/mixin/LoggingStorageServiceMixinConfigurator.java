package global.symbio.billing.core.storage.mixin;

import com.google.common.annotations.VisibleForTesting;
import global.symbio.billing.core.mixin.MixinConfigurator;
import global.symbio.billing.core.storage.service.StorageService;
import io.micronaut.context.annotation.Factory;
import io.micronaut.core.order.Ordered;
import io.micronaut.inject.BeanDefinition;
import io.micronaut.inject.BeanIdentifier;
import jakarta.annotation.Nonnull;

@Factory
@VisibleForTesting
class LoggingStorageServiceMixinConfigurator extends MixinConfigurator<StorageService> {

    private static final String COMPONENT = "logging";

    @Nonnull
    @Override
    public StorageService mixin(@Nonnull StorageService bean, @Nonnull String name) {
        return new LoggingStorageService(bean);
    }

    @Nonnull
    @Override
    protected String component() {
        return COMPONENT;
    }

    @Nonnull
    @Override
    protected String getName(@Nonnull StorageService bean, @Nonnull BeanDefinition<StorageService> definition, @Nonnull BeanIdentifier identifier) {
        return identifier.getName();
    }

    @Override
    protected boolean isEnabled() {
        return true;
    }

    @Override
    public int getOrder() {
        return Ordered.LOWEST_PRECEDENCE;
    }
}