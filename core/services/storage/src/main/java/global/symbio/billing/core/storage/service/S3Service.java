package global.symbio.billing.core.storage.service;

import global.symbio.billing.core.storage.factory.S3ClientFactory;
import io.micronaut.context.annotation.Primary;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.inject.Singleton;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.NoSuchKeyException;

import java.io.FileNotFoundException;
import java.io.InputStream;
import java.util.Objects;

@Primary
@Singleton
public class S3Service implements StorageService {

    @Nonnull
    private final S3Client s3Client;

    @Inject
    public S3Service(
        @Nonnull @Named(S3ClientFactory.NAME) S3Client s3Client
    ) {
        this.s3Client = Objects.requireNonNull(s3Client, "s3Client");
    }

    @Nonnull
    public InputStream downloadFile(@Nonnull String bucketName, @Nonnull String s3Key) throws FileNotFoundException {
        try {
            return s3Client.getObject(GetObjectRequest.builder()
                .bucket(bucketName)
                .key(s3Key)
                .build());
        } catch (NoSuchKeyException e) {
            throw new FileNotFoundException("File not found in S3 bucket");
        } catch (Exception e) {
            throw new RuntimeException("Error retrieving file from S3", e);
        }
    }
}
