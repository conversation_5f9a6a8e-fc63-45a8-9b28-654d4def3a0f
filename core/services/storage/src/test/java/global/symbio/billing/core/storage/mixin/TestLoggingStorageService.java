package global.symbio.billing.core.storage.mixin;

import global.symbio.billing.core.storage.service.StorageService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;

import java.io.FileNotFoundException;
import java.io.InputStream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestLoggingStorageService {

    @Mock
    private Logger log;

    @Mock
    private StorageService delegate;

    @Mock
    private InputStream inputStream;

    private LoggingStorageService service;

    @Captor
    private ArgumentCaptor<String> bucketNameCaptor;

    @Captor
    private ArgumentCaptor<String> s3KeyCaptor;

    @BeforeEach
    public void setup() {
        service = new LoggingStorageService(log, delegate);
    }

    @Test
    @DisplayName("LoggingStorageService::new rejects null constructor arguments")
    public void logging_storage_service_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new LoggingStorageService(null));
        assertThrows(NullPointerException.class, () -> new LoggingStorageService(log, null));
        assertThrows(NullPointerException.class, () -> new LoggingStorageService(null, delegate));
    }

    @Test
    @DisplayName("LoggingStorageService::downloadFile logs and invokes method on delegate")
    public void logging_storage_service_download_file_logs_and_invokes_delegate_method() throws FileNotFoundException {
        final var bucketName = "my-bucket";
        final var s3Key = "file-key";

        when(delegate.downloadFile(bucketName, s3Key)).thenReturn(inputStream);

        final var result = assertDoesNotThrow(() -> service.downloadFile(bucketName, s3Key));

        assertSame(inputStream, result);

        verify(log, times(1)).info(eq("StorageService::downloadFile: bucketName - {}, key - {}"), eq(bucketName), eq(s3Key));
        verify(delegate, times(1)).downloadFile(bucketNameCaptor.capture(), s3KeyCaptor.capture());

        assertEquals(bucketName, bucketNameCaptor.getValue());
        assertEquals(s3Key, s3KeyCaptor.getValue());
    }
}
