package global.symbio.billing.core.storage.mixin;

import global.symbio.billing.core.storage.service.StorageService;
import io.micronaut.core.order.Ordered;
import io.micronaut.inject.BeanDefinition;
import io.micronaut.inject.BeanIdentifier;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestLoggingStorageServiceMixinConfigurator {

    private LoggingStorageServiceMixinConfigurator configurator;

    @BeforeEach
    public void setup() {
        configurator = new LoggingStorageServiceMixinConfigurator();
    }

    @Test
    @DisplayName("LoggingStorageServiceMixinConfigurator::mixin returns LoggingStorageService")
    public void logging_storage_service_mixin_configurator_mixin_returns_logging_storage_service() {
        final var bean = mock(StorageService.class);
        final var mixin = assertInstanceOf(LoggingStorageService.class, assertDoesNotThrow(() -> configurator.mixin(bean, "name")));
        assertSame(bean, mixin.getStorage());
    }

    @Test
    @DisplayName("LoggingStorageServiceMixinConfigurator::component is `logging`")
    public void logging_storage_service_mixin_configurator_component_is_logging() {
        assertSame("logging", configurator.component());
    }

    @Test
    @DisplayName("LoggingStorageServiceMixinConfigurator::getName returns BeanIdentifier name")
    public void logging_storage_service_mixin_configurator_get_name_returns_bean_identifier_name() {
        final var bean = mock(StorageService.class);
        final var definition = mock(BeanDefinition.class);
        final var identifier = mock(BeanIdentifier.class);
        when(identifier.getName()).thenReturn("name");
        final var name = assertDoesNotThrow(() -> configurator.getName(bean, definition, identifier));
        assertEquals("name", name);
        verifyNoInteractions(bean);
        verifyNoInteractions(definition);
        verify(identifier, times(1)).getName();
    }

    @Test
    @DisplayName("LoggingStorageServiceMixinConfigurator::isEnabled is always `true`")
    public void logging_storage_service_mixin_configurator_is_enabled_is_always_true() {
        final var enabled = assertDoesNotThrow(configurator::isEnabled);
        assertTrue(enabled);
    }

    @Test
    @DisplayName("LoggingStorageServiceMixinConfigurator::getOrder is `LOWEST_PRECEDENCE`")
    public void logging_storage_service_mixin_configurator_order_is_lowest_precedence() {
        final var order = assertDoesNotThrow(configurator::getOrder);
        assertEquals(Ordered.LOWEST_PRECEDENCE, order);
    }
}