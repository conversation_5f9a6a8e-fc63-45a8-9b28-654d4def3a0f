package global.symbio.billing.core.storage.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import software.amazon.awssdk.core.ResponseInputStream;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.NoSuchKeyException;

import java.io.FileNotFoundException;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class TestS3Service {
    @Mock
    private S3Client s3Client;

    private S3Service s3Service;

    @BeforeEach
    public void setup() {
        s3Service = new S3Service(s3Client);
    }

    @Test
    @DisplayName("S3Service::new rejects null constructor arguments")
    public void s3_service_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new S3Service(null));
    }

    @Test
    @DisplayName("S3Service::downloadInvoice returns streamed file given valid request")
    public void s3_service_returns_streamed_file_given_valid_request() throws FileNotFoundException {
        final var bucket = "bucket";
        final var key = "key";
        final var stream = mock(ResponseInputStream.class);
        when(s3Client.getObject(any(GetObjectRequest.class))).thenReturn(stream);

        final var result = s3Service.downloadFile(bucket, key);

        assertNotNull(result);
    }

    @Test
    @DisplayName("S3Service::downloadInvoice throws not found when file does not exist in s3")
    public void s3_service_throws_not_found_when_file_does_not_exist() {
        final var bucket = "bucket";
        final var key = "key";
        when(s3Client.getObject(any(GetObjectRequest.class))).thenThrow(NoSuchKeyException.builder().build());

        assertThrows(FileNotFoundException.class, () -> s3Service.downloadFile(bucket, key));
    }

    @Test
    @DisplayName("S3Service::downloadInvoice throws internal server error on failed request")
    public void s3_service_throws_internal_server_error_on_failed_request() {
        final var bucket = "bucket";
        final var key = "key";
        when(s3Client.getObject(any(GetObjectRequest.class))).thenThrow(RuntimeException.class);

        assertThrows(RuntimeException.class, () -> s3Service.downloadFile(bucket, key));
    }
}
