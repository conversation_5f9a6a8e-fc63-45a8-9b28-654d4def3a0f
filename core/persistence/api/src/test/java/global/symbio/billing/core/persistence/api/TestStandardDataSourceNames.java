package global.symbio.billing.core.persistence.api;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertSame;

public class TestStandardDataSourceNames {

    @Test
    @DisplayName("StandardDataSourceNames::READ_WRITE is `default`")
    public void standard_datasource_names_read_write_is_default() {
        assertSame("default", StandardDataSourceNames.READ_WRITE);
    }

    @Test
    @DisplayName("StandardDataSourceNames::READ_ONLY is `replica`")
    public void standard_datasource_names_read_only_is_replica() {
        assertSame("replica", StandardDataSourceNames.READ_ONLY);
    }
}