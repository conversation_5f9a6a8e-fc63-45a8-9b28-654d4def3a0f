package global.symbio.billing.core.persistence.api;

import com.google.common.annotations.VisibleForTesting;
import io.micronaut.context.annotation.Primary;
import io.micronaut.context.annotation.Secondary;
import io.micronaut.data.annotation.Repository;
import jakarta.inject.Named;
import jakarta.inject.Singleton;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.Objects;

import static global.symbio.billing.core.persistence.api.StandardDataSourceNames.READ_ONLY;
import static global.symbio.billing.core.persistence.api.StandardDataSourceNames.READ_WRITE;

@VisibleForTesting
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class Repositories {

    @VisibleForTesting
    public static <T> boolean isReadOnlyRepository(final Class<T> type) {
        return isRepository(type, READ_ONLY, true);
    }

    @VisibleForTesting
    public static <T> boolean isReadWriteRepository(final Class<T> type) {
        return isRepository(type, READ_WRITE, false);
    }

    @VisibleForTesting
    public static <T> boolean isRepository(final Class<T> type, final String name, boolean primary) {
        if (primary) {
            if (!type.isAnnotationPresent(Primary.class)) {
                throw new IllegalStateException(type.getSimpleName() + " is not annotated with @Primary");
            }
        } else {
            if (!type.isAnnotationPresent(Secondary.class)) {
                throw new IllegalStateException(type.getSimpleName() + " is not annotated with @Secondary");
            }
        }

        final var store = type.getAnnotation(PersistenceStore.class);
        final var expected = primary ? PersistenceStore.Capabilities.READ_ONLY : PersistenceStore.Capabilities.READ_WRITE;
        if (store == null || expected != store.value()) {
            throw new IllegalStateException(type.getSimpleName() + " is not annotated with @PersistenceStore(" + expected + ")");
        }

        if (!type.isAnnotationPresent(Singleton.class)) {
            throw new IllegalStateException(type.getSimpleName() + " is not annotated with @Singleton");
        }

        final var repository = type.getAnnotation(Repository.class);
        if (repository == null || !Objects.equals(name, repository.value())) {
            throw new IllegalStateException(type.getSimpleName() + " is not annotated with @Repository(" + name + ")");
        }

        final var named = type.getAnnotation(Named.class);
        if (named == null || !Objects.equals(name, named.value())) {
            throw new IllegalStateException(type.getSimpleName() + " is not annotated with @Named(" + name + ")");
        }
        return true;
    }
}