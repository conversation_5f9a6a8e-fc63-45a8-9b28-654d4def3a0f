package global.symbio.billing.core.persistence.api;

import com.google.common.base.MoreObjects;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.util.Objects;

public abstract class Entity<I extends Comparable<I>, D extends DataAccessObject<I, D>> implements Identifiable<I>, Comparable<Entity<I, D>> {

    @Nonnull
    protected final I identifier;

    @Nonnull
    protected final D data;

    private Entity(@Nonnull I identifier, @Nonnull D data) {
        this.identifier = Objects.requireNonNull(identifier, "identifier");
        this.data = Objects.requireNonNull(data, "data");
    }

    protected Entity(@Nonnull D data) {
        this(data.getIdentifier(), data);
    }

    @Nonnull
    @Override
    public I getIdentifier() {
        // TODO: determine whether the identifier field should be removed in favour of asking the DAO for its ID.
        return identifier;
    }

    @Nonnull
    public D data() {
        return data;
    }

    @Nonnull
    public <T extends D> T data(@Nonnull Class<T> type) {
        return type.cast(data());
    }

    @Override
    public final boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Entity<?, ?> entity)) return false;
        return Objects.equals(getClass(), entity.getClass()) && Objects.equals(getIdentifier(), entity.getIdentifier()) && Objects.equals(data(), entity.data());
    }

    @Override
    public final int hashCode() {
        return Objects.hash(getIdentifier(), data());
    }

    @Override
    public int compareTo(Entity<I, D> other) {
        return getIdentifier().compareTo(other.getIdentifier());
    }

    @Override
    public final String toString() {
        return MoreObjects.toStringHelper(this).add("identifier", getIdentifier()).add("data", data()).toString();
    }

    @Nullable
    @SuppressWarnings("unchecked")
    protected static <ID extends Comparable<ID>, DAO extends DataAccessObject<ID, DAO>, ENTITY extends Entity<ID, DAO>> ENTITY unwrap(@Nullable DAO data) {
        if (data == null) {
            return null;
        }
        return (ENTITY) data.entity();
    }
}
