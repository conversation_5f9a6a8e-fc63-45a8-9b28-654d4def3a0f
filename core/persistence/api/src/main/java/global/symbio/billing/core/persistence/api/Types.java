package global.symbio.billing.core.persistence.api;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class Types {

    @Nullable
    public static <T> T require(@Nullable Object source, @Nonnull String name, @Nonnull Class<T> type) {
        if (source == null) {
            return null;
        } else if (type.isInstance(source)) {
            return type.cast(source);
        } else {
            throw new IllegalArgumentException(name + " was not of expected type: " + type.getSimpleName() + ", but rather: " + source.getClass().getSimpleName());
        }
    }
}