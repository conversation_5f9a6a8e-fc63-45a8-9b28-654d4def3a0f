package global.symbio.billing.core.persistence.api;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.hypersistence.utils.hibernate.type.util.ObjectMapperSupplier;
import io.micronaut.context.annotation.Context;
import io.micronaut.context.event.BeanCreatedEvent;
import io.micronaut.context.event.BeanCreatedEventListener;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;

/**
 * This component receives an {@link ObjectMapper} configured by Micronaut and shares it with <a href=https://github.com/vladmihalcea/hypersistence-utils>Hypersistence</a>
 * in order to ensure that configuration is consistent between frameworks.
 */
@Slf4j
@Context
public class HypersistenceObjectMapperInjector implements ObjectMapperSupplier, BeanCreatedEventListener<ObjectMapper> {

    private static final AtomicReference<ObjectMapper> reference = new AtomicReference<>();

    @Override
    public ObjectMapper get() {
        return Objects.requireNonNull(reference.get(), "The Hypersistence ObjectMapper instance has not been set.");
    }

    @Override
    public ObjectMapper onCreated(BeanCreatedEvent<ObjectMapper> event) {
        final var mapper = event.getBean();
        final var witness = reference.compareAndExchange(null, mapper);
        if (witness == null) {
            log.info("Hypersistence ObjectMapper configured: {}", mapper);
        } else {
            log.warn("Hypersistence ObjectMapper is already configured. Previous: {}, Current: {}", witness, mapper);
        }
        return mapper;
    }
}