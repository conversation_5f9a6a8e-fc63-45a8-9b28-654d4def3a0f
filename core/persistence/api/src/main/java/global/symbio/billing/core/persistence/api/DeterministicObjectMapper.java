package global.symbio.billing.core.persistence.api;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.google.common.annotations.VisibleForTesting;
import jakarta.annotation.Nonnull;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import static com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES;
import static com.fasterxml.jackson.databind.MapperFeature.SORT_CREATOR_PROPERTIES_FIRST;
import static com.fasterxml.jackson.databind.MapperFeature.SORT_PROPERTIES_ALPHABETICALLY;

@VisibleForTesting
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class DeterministicObjectMapper {

    /**
     * ONLY for use in TEST code.
     */
    @Nonnull
    @VisibleForTesting
    public static ObjectMapper get() {
        return ObjectMapperForTestCodeOnly.SINGLETON;
    }

    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    private static final class ObjectMapperForTestCodeOnly {

        @Nonnull
        private static final ObjectMapper SINGLETON = JsonMapper
            .builder()
            .disable(FAIL_ON_UNKNOWN_PROPERTIES)
            .disable(SORT_CREATOR_PROPERTIES_FIRST)
            .enable(SORT_PROPERTIES_ALPHABETICALLY)
            .addModule(new JavaTimeModule())
            .build();
    }
}