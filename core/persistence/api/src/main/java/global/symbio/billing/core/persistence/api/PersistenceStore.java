package global.symbio.billing.core.persistence.api;

import jakarta.annotation.Nonnull;
import jakarta.inject.Qualifier;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;

import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * A {@link Qualifier} for distinguishing and selecting different persistence implementations based on their capabilities.
 * @see Qualifier @Qualifier
 */
@Qualifier
@Documented
@Retention(RUNTIME)
public @interface PersistenceStore {

    @Nonnull
    Capabilities value() default Capabilities.READ_ONLY;

    enum Capabilities {
        READ_ONLY,
        READ_WRITE
    }
}
