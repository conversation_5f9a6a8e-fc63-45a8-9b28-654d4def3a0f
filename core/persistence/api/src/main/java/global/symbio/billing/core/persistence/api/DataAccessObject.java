package global.symbio.billing.core.persistence.api;

import io.micronaut.core.annotation.Introspected;
import jakarta.annotation.Nonnull;

import java.io.Serializable;

@Introspected
public interface DataAccessObject<I extends Comparable<I>, T extends DataAccessObject<I, T>> extends Serializable {

    @Nonnull
    I getIdentifier();

    @Nonnull
    T identifier(@Nonnull I identifier);

    @Nonnull
    Entity<I, T> entity();
}