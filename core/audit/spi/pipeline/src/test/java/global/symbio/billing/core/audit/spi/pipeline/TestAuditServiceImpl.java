package global.symbio.billing.core.audit.spi.pipeline;

import global.symbio.billing.core.audit.api.AuditAction;
import global.symbio.billing.core.audit.api.AuditReferenceType;
import global.symbio.billing.core.audit.api.encoder.AuditEncoderRegistry;
import global.symbio.billing.core.audit.api.encoder.PaymentAuditEncoder;
import global.symbio.billing.core.audit.api.event.AuditEvent;
import global.symbio.billing.core.audit.api.log.AuditLog;
import global.symbio.billing.core.pipeline.api.MonoPipeline;
import global.symbio.billing.core.pipeline.api.request.Request;
import global.symbio.billing.core.services.rest.plexus.client.audit.AuditServiceApiClient;
import global.symbio.billing.core.services.rest.plexus.model.audit.GetAuditRecordSetsResponse;
import global.symbio.billing.core.services.rest.plexus.model.audit.PlexusAuditData;
import global.symbio.billing.core.services.rest.plexus.model.audit.PlexusAuditRecord;
import global.symbio.billing.core.services.rest.plexus.model.audit.Principal;
import global.symbio.billing.core.util.mapper.MapperUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestAuditServiceImpl {

    @Mock
    private MonoPipeline<AuditEvent> pipeline;

    @Mock
    private AuditServiceApiClient apiClient;

    @Mock
    private AuditEncoderRegistry registry;

    private AuditServiceImpl service;

    @BeforeEach
    public void setup() {
        service = new AuditServiceImpl(pipeline, apiClient, registry);
    }

    @Test
    @DisplayName("AuditServiceImpl::new rejects null constructor arguments")
    public void audit_service_impl_new_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new AuditServiceImpl(null, apiClient, registry));
        assertThrows(NullPointerException.class, () -> new AuditServiceImpl(pipeline, null, registry));
        assertThrows(NullPointerException.class, () -> new AuditServiceImpl(pipeline, apiClient, null));
    }

    @Test
    @DisplayName("AuditServiceImpl::submit pipeline consumes events")
    public void audit_service_impl_submit_pipeline_consumes_events() throws Exception {
        final var event1 = mock(AuditEvent.class);
        final var event2 = mock(AuditEvent.class);
        final AuditEvent[] events = {event1, event2};
        assertDoesNotThrow(() -> service.submit(events));
        verify(pipeline, times(1)).consume(eq(Request.of(Arrays.asList(events))));
    }

    @Test
    @DisplayName("AuditServiceImpl::getAuditLogs invokes audit api")
    public void audit_service_impl_get_audit_logs_invokes_audit_api() {
        final var identifier = UUID.fromString("0191ba6f-b17b-799d-9216-f9bd5c595a67");
        final var logId = UUID.randomUUID();
        final var type = AuditReferenceType.ACCOUNT_ALLOCATION;
        final var eventTime = ZonedDateTime.now();
        final var user = "Test User";

        final var plexusAuditData = mock(PlexusAuditData.class);
        final var response = mock(GetAuditRecordSetsResponse.class);
        when(apiClient.getRecordSets(any(UUID.class))).thenReturn(response);
        when(response.success()).thenReturn(true);
        when(response.data()).thenReturn(plexusAuditData);

        final var principal = mock(Principal.class);
        when(principal.name()).thenReturn(user);

        final var plexusAuditRecord = mock(PlexusAuditRecord.class);
        when(plexusAuditRecord.action()).thenReturn(AuditAction.CREATE_PAYMENT.name());
        when(plexusAuditRecord.id()).thenReturn(logId);
        when(plexusAuditRecord.principal()).thenReturn(principal);
        when(plexusAuditRecord.version()).thenReturn(1);
        when(plexusAuditData.records()).thenReturn(List.of(plexusAuditRecord));

        final var arguments = """
            {
                "account": {
                    "ref": "plexid:account:0ec64160-d3c8-49de-aac0-059f8989cb8d"
                },
                "business": {
                    "ref": "plexid:customer:00af9976-6376-42c6-a7b0-1beef21d649a"
                },
                "details": {
                    "allocationDetails": [
                        {
                            "account": "0ec64160-d3c8-49de-aac0-059f8989cb8d",
                            "amount": 2000,
                            "invoiceAllocationDetails": []
                        }
                    ],
                    "amount": 2000,
                    "country": "MY",
                    "currency": "MYR",
                    "description": "Thank you for your payment",
                    "method": 1,
                    "receiptDate": "2024-08-06T00:00:00Z"
                },
                "user": {
                    "email": "<EMAIL>",
                    "name": "Test User"
                }
            }
            """;

        final var outcome = """
            {
                "details": {
                    "allocationDetails": [
                        {
                            "account": "0ec64160-d3c8-49de-aac0-059f8989cb8d",
                            "amount": 2000,
                            "identifier": "0191ba6f-b17b-799d-9216-f9bd5c595a67",
                            "invoiceAllocationDetails": [],
                            "state": {
                                "allocated": 0,
                                "total": 2000,
                                "type": "UNALLOCATED",
                                "unallocated": 2000
                            },
                            "type": "PAYMENT"
                        }
                    ],
                    "amount": 2000,
                    "country": "MY",
                    "currency": "MYR",
                    "description": "Thank you for your payment",
                    "identifier": "0191ba6f-aea1-7925-bb0c-5b05645bea4b",
                    "method": 1,
                    "receiptDate": "2024-08-06T00:00:00Z",
                    "reference": "MYCOPYMT00000013430",
                    "timestamp": "2024-09-04T00:29:27.773430519Z"
                },
                "payment": {
                    "ref": "plexid:payment:0191ba6f-aea1-7925-bb0c-5b05645bea4b"
                },
                "paymentAccountAllocations": [
                    {
                        "ref": "plexid:account:allocation:0191ba6f-b17b-799d-9216-f9bd5c595a67"
                    }
                ],
                "status": "SUCCESS"
            }
            """;


        final var recordArguments = MapperUtil.fromJson(arguments, Map.class);
        final var recordOutcome = MapperUtil.fromJson(outcome, Map.class);

        when(plexusAuditRecord.arguments()).thenReturn(recordArguments);
        when(plexusAuditRecord.outcome()).thenReturn(recordOutcome);
        when(plexusAuditRecord.eventTime()).thenReturn(eventTime);
        when(plexusAuditRecord.version()).thenReturn(1);

        final var auditLog = mock(AuditLog.class);
        when(auditLog.action()).thenReturn(AuditAction.CREATE_PAYMENT.name());
        when(auditLog.user()).thenReturn(user);

        final var auditLogBuilder = mock(AuditLog.AuditLogBuilder.class);
        when(auditLogBuilder.id(any(UUID.class))).thenReturn(auditLogBuilder);
        when(auditLogBuilder.eventTime(any(ZonedDateTime.class))).thenReturn(auditLogBuilder);
        when(auditLogBuilder.action(anyString())).thenReturn(auditLogBuilder);
        when(auditLogBuilder.build()).thenReturn(auditLog);

        final var encoder = mock(PaymentAuditEncoder.class);
        when(registry.lookup(anyString())).thenReturn(encoder);
        when(encoder.composeAuditLog(anyMap(), anyMap(), any(UUID.class), anyString(), anyInt())).thenReturn(auditLogBuilder);

        final var logs = assertDoesNotThrow(() -> service.getAuditLogs(identifier, type));
        verify(apiClient, times(1)).getRecordSets(any(UUID.class));
        verify(registry, times(1)).lookup(anyString());
        final var log = logs.stream().findFirst().get();
        assertEquals("CREATE_PAYMENT", log.action());
        assertEquals(user, log.user());
    }

    @Test
    @DisplayName("AuditServiceImpl::getAuditLogs invokes audit api for version 2 input")
    public void audit_service_impl_get_audit_logs_invokes_audit_api_v2_input() {
        final var identifier = UUID.fromString("0191ba6f-b17b-799d-9216-f9bd5c595a67");
        final var logId = UUID.randomUUID();
        final var type = AuditReferenceType.ACCOUNT_ALLOCATION;
        final var eventTime = ZonedDateTime.now();
        final var user = "Test User";

        final var plexusAuditData = mock(PlexusAuditData.class);
        final var response = mock(GetAuditRecordSetsResponse.class);
        when(apiClient.getRecordSets(any(UUID.class))).thenReturn(response);
        when(response.success()).thenReturn(true);
        when(response.data()).thenReturn(plexusAuditData);

        final var principal = mock(Principal.class);
        when(principal.name()).thenReturn(user);

        final var plexusAuditRecord = mock(PlexusAuditRecord.class);
        when(plexusAuditRecord.action()).thenReturn(AuditAction.CREATE_PAYMENT.auditName);
        when(plexusAuditRecord.id()).thenReturn(logId);
        when(plexusAuditRecord.principal()).thenReturn(principal);
        when(plexusAuditRecord.version()).thenReturn(2);
        when(plexusAuditData.records()).thenReturn(List.of(plexusAuditRecord));

        final var arguments = """
            {
                "currentPayload": {
                    "allocationDetails": [
                        {
                            "account": "0ec64160-d3c8-49de-aac0-059f8989cb8d",
                            "amount": 2000,
                            "invoiceAllocationDetails": []
                        }
                    ],
                    "amount": 2000,
                    "country": "MY",
                    "currency": "MYR",
                    "description": "Thank you for your payment",
                    "method": 1,
                    "receiptDate": "2024-08-06T00:00:00Z"
                },
                "references": [
                    {
                        "entityName": "account",
                        "referenceID": "0ec64160-d3c8-49de-aac0-059f8989cb8d"
                    },
                    {
                        "entityName": "business",
                        "referenceID": "00af9976-6376-42c6-a7b0-1beef21d649a"
                    },
                    {
                        "entityName": "payment",
                        "referenceID": "0191ba6f-aea1-7925-bb0c-5b05645bea4b"
                    },
                    {
                        "entityName": "accountAllocation",
                        "referenceID": "allocation:0191ba6f-b17b-799d-9216-f9bd5c595a67"
                    }
                ]
            }
            """;

        final var outcome = """
            {
                "details": {
                    "allocationDetails": [
                        {
                            "account": "0ec64160-d3c8-49de-aac0-059f8989cb8d",
                            "amount": 2000,
                            "identifier": "0191ba6f-b17b-799d-9216-f9bd5c595a67",
                            "invoiceAllocationDetails": [],
                            "state": {
                                "allocated": 0,
                                "total": 2000,
                                "type": "UNALLOCATED",
                                "unallocated": 2000
                            },
                            "type": "PAYMENT"
                        }
                    ],
                    "amount": 2000,
                    "country": "MY",
                    "currency": "MYR",
                    "description": "Thank you for your payment",
                    "identifier": "0191ba6f-aea1-7925-bb0c-5b05645bea4b",
                    "method": 1,
                    "receiptDate": "2024-08-06T00:00:00Z",
                    "reference": "MYCOPYMT00000013430",
                    "timestamp": "2024-09-04T00:29:27.773430519Z"
                },
                "status": "SUCCESS"
            }
            """;


        final var recordArguments = MapperUtil.fromJson(arguments, Map.class);
        final var recordOutcome = MapperUtil.fromJson(outcome, Map.class);

        when(plexusAuditRecord.arguments()).thenReturn(recordArguments);
        when(plexusAuditRecord.outcome()).thenReturn(recordOutcome);
        when(plexusAuditRecord.eventTime()).thenReturn(eventTime);
        when(plexusAuditRecord.version()).thenReturn(2);

        final var auditLog = mock(AuditLog.class);
        when(auditLog.action()).thenReturn(AuditAction.CREATE_PAYMENT.name());
        when(auditLog.user()).thenReturn(user);

        final var auditLogBuilder = mock(AuditLog.AuditLogBuilder.class);
        when(auditLogBuilder.id(any(UUID.class))).thenReturn(auditLogBuilder);
        when(auditLogBuilder.eventTime(any(ZonedDateTime.class))).thenReturn(auditLogBuilder);
        when(auditLogBuilder.action(anyString())).thenReturn(auditLogBuilder);
        when(auditLogBuilder.build()).thenReturn(auditLog);

        final var encoder = mock(PaymentAuditEncoder.class);
        when(registry.lookup(anyString())).thenReturn(encoder);
        when(encoder.composeAuditLog(anyMap(), anyMap(), any(UUID.class), anyString(), anyInt())).thenReturn(auditLogBuilder);

        final var logs = assertDoesNotThrow(() -> service.getAuditLogs(identifier, type));
        verify(apiClient, times(1)).getRecordSets(any(UUID.class));
        verify(registry, times(1)).lookup(anyString());
        final var log = logs.stream().findFirst().get();
        assertEquals("CREATE_PAYMENT", log.action());
        assertEquals(user, log.user());
    }

    @Test
    @DisplayName("AuditServiceImpl::getAuditLogs skips record when compose returns null")
    public void audit_service_impl_get_audit_logs_skips_record_when_compose_returns_null() {
        final var identifier = UUID.fromString("0191ba6f-b17b-799d-9216-f9bd5c595a67");
        final var type = AuditReferenceType.ACCOUNT_ALLOCATION;
        final var eventTime = ZonedDateTime.now();
        final var user = "Test User";

        final var plexusAuditData = mock(PlexusAuditData.class);
        final var response = mock(GetAuditRecordSetsResponse.class);
        when(apiClient.getRecordSets(any(UUID.class))).thenReturn(response);
        when(response.success()).thenReturn(true);
        when(response.data()).thenReturn(plexusAuditData);

        final var principal = mock(Principal.class);
        when(principal.name()).thenReturn(user);

        final var plexusAuditRecord = mock(PlexusAuditRecord.class);
        when(plexusAuditRecord.action()).thenReturn(AuditAction.CREATE_PAYMENT.name());
        when(plexusAuditRecord.principal()).thenReturn(principal);
        when(plexusAuditRecord.version()).thenReturn(1);
        when(plexusAuditData.records()).thenReturn(List.of(plexusAuditRecord));

        final var arguments = """
            {
                "account": {
                    "ref": "plexid:account:0ec64160-d3c8-49de-aac0-059f8989cb8d"
                },
                "business": {
                    "ref": "plexid:customer:00af9976-6376-42c6-a7b0-1beef21d649a"
                },
                "details": {
                    "allocationDetails": [
                        {
                            "account": "0ec64160-d3c8-49de-aac0-059f8989cb8d",
                            "amount": 2000,
                            "invoiceAllocationDetails": []
                        }
                    ],
                    "amount": 2000,
                    "country": "MY",
                    "currency": "MYR",
                    "description": "Thank you for your payment",
                    "method": 1,
                    "receiptDate": "2024-08-06T00:00:00Z"
                },
                "user": {
                    "email": "<EMAIL>",
                    "name": "Test User"
                }
            }
            """;

        final var outcome = """
            {
                "details": {
                    "allocationDetails": [
                        {
                            "account": "0ec64160-d3c8-49de-aac0-059f8989cb8d",
                            "amount": 2000,
                            "identifier": "0191ba6f-b17b-799d-9216-f9bd5c595a67",
                            "invoiceAllocationDetails": [],
                            "state": {
                                "allocated": 0,
                                "total": 2000,
                                "type": "UNALLOCATED",
                                "unallocated": 2000
                            },
                            "type": "PAYMENT"
                        }
                    ],
                    "amount": 2000,
                    "country": "MY",
                    "currency": "MYR",
                    "description": "Thank you for your payment",
                    "identifier": "0191ba6f-aea1-7925-bb0c-5b05645bea4b",
                    "method": 1,
                    "receiptDate": "2024-08-06T00:00:00Z",
                    "reference": "MYCOPYMT00000013430",
                    "timestamp": "2024-09-04T00:29:27.773430519Z"
                },
                "payment": {
                    "ref": "plexid:payment:0191ba6f-aea1-7925-bb0c-5b05645bea4b"
                },
                "paymentAccountAllocations": [
                    {
                        "ref": "plexid:account:allocation:0191ba6f-b17b-799d-9216-f9bd5c595a67"
                    }
                ],
                "status": "SUCCESS"
            }
            """;

        final var recordArguments = MapperUtil.fromJson(arguments, Map.class);
        final var recordOutcome = MapperUtil.fromJson(outcome, Map.class);

        when(plexusAuditRecord.arguments()).thenReturn(recordArguments);
        when(plexusAuditRecord.outcome()).thenReturn(recordOutcome);
        when(plexusAuditRecord.eventTime()).thenReturn(eventTime);

        final var encoder = mock(PaymentAuditEncoder.class);
        when(registry.lookup(anyString())).thenReturn(encoder);
        when(encoder.composeAuditLog(anyMap(), anyMap(), any(UUID.class), anyString(), anyInt())).thenReturn(null);

        final var logs = assertDoesNotThrow(() -> service.getAuditLogs(identifier, type));
        verify(apiClient, times(1)).getRecordSets(any(UUID.class));
        verify(registry, times(1)).lookup(anyString());
        assertEquals(0, logs.size());
    }

    @Test
    @DisplayName("AuditServiceImpl::getAuditLogs throws an exception if encoder is null")
    public void audit_service_impl_get_audit_logs_throws_exception_if_encoder_is_null() {
        final var identifier = UUID.randomUUID();
        final var type = AuditReferenceType.ACCOUNT_ALLOCATION;
        final var eventTime = ZonedDateTime.now();

        final var plexusAuditData = mock(PlexusAuditData.class);
        final var response = mock(GetAuditRecordSetsResponse.class);
        when(apiClient.getRecordSets(any(UUID.class))).thenReturn(response);
        when(response.success()).thenReturn(true);
        when(response.data()).thenReturn(plexusAuditData);

        final var principal = mock(Principal.class);
        when(principal.name()).thenReturn("Test User");

        final var plexusAuditRecord = mock(PlexusAuditRecord.class);
        when(plexusAuditRecord.action()).thenReturn(AuditAction.CREATE_PAYMENT.name());
        when(plexusAuditRecord.principal()).thenReturn(principal);
        when(plexusAuditData.records()).thenReturn(List.of(plexusAuditRecord));

        when(plexusAuditRecord.eventTime()).thenReturn(eventTime);

        when(registry.lookup(anyString())).thenReturn(null);

        assertThrows(IllegalStateException.class, () -> service.getAuditLogs(identifier, type));
        verify(apiClient, times(1)).getRecordSets(any(UUID.class));
        verify(registry, times(1)).lookup(anyString());
    }

    @Test
    @DisplayName("AuditServiceImpl::getAuditLogs Plexus response not successful return empty list")
    public void audit_service_impl_get_audit_logs_plexus_response_not_successful_return_empty_list() {
        final var identifier = UUID.randomUUID();
        final var type = AuditReferenceType.ACCOUNT_ALLOCATION;

        final var response = mock(GetAuditRecordSetsResponse.class);
        when(apiClient.getRecordSets(any(UUID.class))).thenReturn(response);
        when(response.success()).thenReturn(false);

        final var result = assertDoesNotThrow(() -> service.getAuditLogs(identifier, type));
        assertEquals(0, result.size());
        verify(apiClient, times(1)).getRecordSets(any(UUID.class));
        verify(registry, never()).lookup(anyString());
    }

    @Test
    @DisplayName("AuditServiceImpl::getAuditLogs Plexus response data null return empty list")
    public void audit_service_impl_get_audit_logs_plexus_response_data_null_successful_return_empty_list() {
        final var identifier = UUID.randomUUID();
        final var type = AuditReferenceType.ACCOUNT_ALLOCATION;

        final var plexusAuditData = mock(PlexusAuditData.class);
        final var response = mock(GetAuditRecordSetsResponse.class);
        when(apiClient.getRecordSets(any(UUID.class))).thenReturn(response);
        when(response.success()).thenReturn(true);
        when(response.data()).thenReturn(plexusAuditData);
        when(plexusAuditData.records()).thenReturn(null);

        final var result = assertDoesNotThrow(() -> service.getAuditLogs(identifier, type));
        assertEquals(0, result.size());
        verify(apiClient, times(1)).getRecordSets(any(UUID.class));
        verify(registry, never()).lookup(anyString());
    }

    @Test
    @DisplayName("AuditServiceImpl::start starts underlying Pipeline")
    public void audit_service_impl_start_starts_pipeline() {
        assertDoesNotThrow(service::start);
        verify(pipeline, times(1)).start();
    }

    @Test
    @DisplayName("AuditServiceImpl::stop stops underlying Pipeline")
    public void audit_service_impl_stop_stops_pipeline() {
        assertDoesNotThrow(service::stop);
        verify(pipeline, times(1)).stop();
    }
}
