package global.symbio.billing.core.audit.spi.pipeline.factory;

import com.lmax.disruptor.*;
import com.lmax.disruptor.dsl.Disruptor;
import com.lmax.disruptor.dsl.ProducerType;
import global.symbio.billing.core.audit.api.event.AuditEvent;
import global.symbio.billing.core.pipeline.api.Pipeline;
import global.symbio.billing.core.pipeline.api.PipelineEngine;
import global.symbio.billing.core.pipeline.api.request.batch.EngineRequestBatchingStrategy;
import global.symbio.billing.core.pipeline.api.request.handler.MultipleItemRequestHandler;
import global.symbio.billing.core.pipeline.api.request.handler.SingleItemRequestHandler;
import global.symbio.billing.core.pipeline.api.request.router.DefaultRequestRouter;
import global.symbio.billing.core.pipeline.api.request.router.RequestRouter;
import global.symbio.billing.core.pipeline.spi.disruptor.DisruptorPipelineEngine;
import global.symbio.billing.core.pipeline.spi.disruptor.event.batch.BatchConsumer;
import global.symbio.billing.core.pipeline.spi.disruptor.event.batch.BatchingEventHandler;
import global.symbio.billing.core.pipeline.spi.disruptor.event.value.ValueEvent;
import global.symbio.billing.core.pipeline.spi.disruptor.event.value.ValueEventRecycler;
import io.micronaut.context.annotation.Bean;
import io.micronaut.context.annotation.Factory;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Named;

import java.util.concurrent.TimeUnit;

@Factory
class AuditServicePipelineFactory {

    public static final String NAME = "AuditEventPipeline";
    public static final String PIPELINE_NAME = "audit-pipeline";

    /**
     * Representation of the disruptor architecture for the Auditing pipeline.
     * <p>
     * AuditEvent -----> Kafka -----> Recycler
     */
    @Inject
    @Bean
    @Named(NAME)
    public Pipeline<AuditEvent, PipelineEngine<AuditEvent>, RequestRouter<AuditEvent>> pipeline(
        @Nonnull @Named(AuditEmitterFactory.NAME) BatchConsumer<AuditEvent> emitter
    ) {
        final var type = ProducerType.MULTI; // very important
        final var size = 256;
        final var backoff = PhasedBackoffWaitStrategy.withLock(60, 60, TimeUnit.SECONDS);

        final var factory = Thread.ofPlatform().daemon().name(PIPELINE_NAME + "-", 0L).factory();
        final Disruptor<ValueEvent<AuditEvent>> disruptor = new Disruptor<>(ValueEvent::new, size, factory, type, backoff);

        final var chain = disruptor
            .handleEventsWith((buffer, sequences) -> create(buffer, sequences, emitter))
            .then(ValueEventRecycler.getInstance()); // recycle value events

        final var engine = new DisruptorPipelineEngine<>(disruptor, AuditEvent[]::new);
        final var router = DefaultRequestRouter.<AuditEvent>builder().withSingle(SingleItemRequestHandler.create()).withMultiple(MultipleItemRequestHandler.create(EngineRequestBatchingStrategy.partitioned(size))).build();
        return Pipeline.mono(PIPELINE_NAME, engine, router);
    }

    @Nonnull
    private static <T> BatchEventProcessor<ValueEvent<T>> create(@Nonnull RingBuffer<ValueEvent<T>> buffer, @Nonnull Sequence[] sequences, @Nonnull BatchConsumer<T> consumer) {
        final var processor = new BatchEventProcessor<>(buffer, buffer.newBarrier(sequences), new BatchingEventHandler<>(consumer));
        processor.setRewindStrategy(new EventuallyGiveUpBatchRewindStrategy(5L));
        processor.setExceptionHandler(new IgnoreExceptionHandler());
        return processor;
    }
}