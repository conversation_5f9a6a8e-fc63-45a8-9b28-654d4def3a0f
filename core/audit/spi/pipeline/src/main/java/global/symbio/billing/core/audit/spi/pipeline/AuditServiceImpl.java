package global.symbio.billing.core.audit.spi.pipeline;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.MoreObjects;
import global.symbio.billing.core.audit.api.AuditAction;
import global.symbio.billing.core.audit.api.AuditReferenceType;
import global.symbio.billing.core.audit.api.AuditService;
import global.symbio.billing.core.audit.api.encoder.AuditEncoderRegistry;
import global.symbio.billing.core.audit.api.event.AuditEvent;
import global.symbio.billing.core.audit.api.log.AuditLog;
import global.symbio.billing.core.pipeline.api.Pipeline;
import global.symbio.billing.core.pipeline.api.PipelineEngine;
import global.symbio.billing.core.pipeline.api.request.Request;
import global.symbio.billing.core.pipeline.api.request.router.RequestRouter;
import global.symbio.billing.core.services.rest.plexus.client.audit.AuditServiceApiClient;
import jakarta.annotation.Nonnull;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

@Slf4j
@Singleton
@VisibleForTesting
class AuditServiceImpl implements AuditService {

    @Nonnull
    private final Pipeline<AuditEvent, PipelineEngine<AuditEvent>, RequestRouter<AuditEvent>> pipeline;

    @Nonnull
    private final AuditServiceApiClient apiClient;

    @Nonnull
    private final AuditEncoderRegistry registry;

    @Inject
    public AuditServiceImpl(
        @Nonnull Pipeline<AuditEvent, PipelineEngine<AuditEvent>, RequestRouter<AuditEvent>> pipeline,
        @Nonnull AuditServiceApiClient apiClient,
        @Nonnull AuditEncoderRegistry registry
    ) {
        this.pipeline = Objects.requireNonNull(pipeline, "pipeline");
        this.apiClient = Objects.requireNonNull(apiClient, "apiClient");
        this.registry = Objects.requireNonNull(registry, "registry");
    }

    @Override
    public void submit(@Nonnull AuditEvent... events) {
        final var request = Request.of(Arrays.asList(events));
        try {
            pipeline.consume(request);
        } catch (Throwable cause) {
            log.warn("Exception submitting events to pipeline: {}", this, cause);
        }
    }

    @Override
    public List<AuditLog> getAuditLogs(@Nonnull UUID identifier, @Nonnull AuditReferenceType type) {
        final var response = apiClient.getRecordSets(identifier);

        if (!response.success() || response.data().records() == null) {
            return Collections.emptyList();
        }

        final var logs = new ArrayList<AuditLog>();
        for (final var record : response.data().records()) {
            final var action = record.action();
            final var eventTime = record.eventTime();
            final var user = record.principal().name();
            final var version = record.version() == null ? 1 : record.version();

            final var encoder = registry.lookup(action);
            if (encoder == null) {
                throw new IllegalStateException("No encoder found for action: " + action);
            }

            final var auditLog = encoder.composeAuditLog(record.arguments(), record.outcome(), identifier, user, version);
            if (auditLog == null) {
                continue;
            }

            final var auditAction = version == 1 ? action : AuditAction.fromAuditName(action).name();

            auditLog.id(record.id())
                .eventTime(eventTime)
                .action(auditAction);

            logs.add(auditLog.build());
        }
        return logs.stream().sorted(Comparator.comparing(AuditLog::eventTime)).toList();
    }

    @PostConstruct
    public void start() {
        log.info("Starting - {}.", this);
        pipeline.start();
        log.info("Started - {}.", this);
    }

    @PreDestroy
    public void stop() {
        log.info("Stopping - {}.", this);
        pipeline.stop();
        log.info("Stopped - {}.", this);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof AuditServiceImpl audit)) return false;
        return Objects.equals(pipeline, audit.pipeline);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(pipeline);
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("pipeline", pipeline)
            .toString();
    }
}