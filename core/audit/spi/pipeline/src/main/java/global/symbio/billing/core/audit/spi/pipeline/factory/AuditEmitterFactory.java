package global.symbio.billing.core.audit.spi.pipeline.factory;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import global.symbio.billing.core.audit.api.event.AuditEvent;
import global.symbio.billing.core.pipeline.spi.disruptor.consumer.kafka.KafkaEntityEmitterBatchConsumer;
import global.symbio.billing.core.pipeline.spi.disruptor.event.batch.BatchConsumer;
import io.micronaut.configuration.kafka.annotation.KafkaClient;
import io.micronaut.context.annotation.Bean;
import io.micronaut.context.annotation.Factory;
import io.micronaut.context.annotation.Property;
import io.micronaut.core.util.functional.ThrowingFunction;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.Producer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.header.Header;
import org.apache.kafka.common.header.internals.RecordHeader;

import java.util.Base64;
import java.util.List;
import java.util.UUID;

import static global.symbio.billing.core.audit.api.event.KafkaAuditEvent.ORIGIN;
import static global.symbio.billing.core.kafka.api.CommonKafkaConfigurationGroups.CONFIGURATION_GROUP_AUDIT;
import static java.nio.charset.StandardCharsets.UTF_8;

@Slf4j
@Factory
class AuditEmitterFactory {

    public static final String NAME = "AuditEmitter";
    private static final String JWT_TOKEN_HEADER = "{\"alg\":\"none\",\"typ\":\"json\"}";
    private static final String HEADER_MESSAGE_TYPE = "messageType";
    private static final String HEADER_AUDIT_TRAIL = "auditTrail";
    private static final String HEADER_VERSION = "version";
    private static final String HEADER_VERSION_VALUE = "2";
    private static final String HEADER_SERVICE = "service";

    @Inject
    @Nonnull
    @Property(name = "kafka.producers." + CONFIGURATION_GROUP_AUDIT + ".topic")
    private String topic;

    @Inject
    @Nonnull
    @Property(name = "kafka.producers." + CONFIGURATION_GROUP_AUDIT + "." + ProducerConfig.TRANSACTIONAL_ID_CONFIG, defaultValue = "")
    private String transactional;

    @Inject
    @Nullable
    @Property(name = "pipeline." + CONFIGURATION_GROUP_AUDIT + ".limit", defaultValue = "512")
    private Integer limit;

    @Inject
    @Bean
    @Named(NAME)
    public BatchConsumer<AuditEvent> create_audit_emitter(
        @Nonnull @KafkaClient(CONFIGURATION_GROUP_AUDIT) Producer<UUID, String> producer,
        @Nonnull ObjectMapper mapper
    ) {
        final Iterable<Header> memoizedHeader = List.of(
            new RecordHeader(HEADER_MESSAGE_TYPE, HEADER_AUDIT_TRAIL.getBytes(UTF_8)),
            new RecordHeader(HEADER_VERSION, HEADER_VERSION_VALUE.getBytes(UTF_8)),
            new RecordHeader(HEADER_SERVICE, ORIGIN.getBytes(UTF_8))
        );
        final var header = Base64.getEncoder().withoutPadding().encodeToString(JWT_TOKEN_HEADER.getBytes());
        final ThrowingFunction<AuditEvent, Integer, Exception> partition = _ -> null;
        final ThrowingFunction<AuditEvent, UUID, Exception> key = AuditEvent::id;
        final ThrowingFunction<AuditEvent, String, Exception> value = event -> {
            //JWT Token value = base64(header) + “.” + base64(payload) + “.”
            final var json = mapper.writeValueAsString(event);
            final var encodedBody = Base64.getEncoder().withoutPadding().encodeToString(json.getBytes());
            final var encoded = header + "." + encodedBody + ".";
            log.info("encoded: {}", encoded);
            return encoded;
        };
        final ThrowingFunction<AuditEvent, Iterable<Header>, Exception> headers = _ -> memoizedHeader;
        final boolean atomic = !Strings.isNullOrEmpty(transactional);
        return new KafkaEntityEmitterBatchConsumer<>(producer, null, _ -> topic, partition, key, value, headers, atomic, limit);
    }
}
