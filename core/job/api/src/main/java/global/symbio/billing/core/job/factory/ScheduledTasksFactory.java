package global.symbio.billing.core.job.factory;

import com.fasterxml.uuid.NoArgGenerator;
import com.google.common.base.MoreObjects;
import global.symbio.billing.core.i18n.LocalisationSettings;
import global.symbio.billing.core.job.JobBuilder;
import global.symbio.billing.core.job.persistence.api.job.Job;
import global.symbio.billing.core.job.scheduler.TaskScheduler;
import global.symbio.billing.core.job.task.impl.PurgeExpiredJobsTask;
import io.micronaut.context.annotation.Requires;
import io.micronaut.scheduling.annotation.Scheduled;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.util.Objects;
import java.util.Set;
import java.util.function.Consumer;

/**
 * Base framework for scheduled task management across multiple localization contexts.
 *
 * <p>This class provides infrastructure for:</p>
 * <ul>
 *   <li>Creating and scheduling jobs for each supported locale/region</li>
 *   <li>Handling common maintenance tasks like expired job purging</li>
 * </ul>
 *
 * <p>To implement domain-specific scheduled tasks:</p>
 * <ol>
 *   <li>Extend this class</li>
 *   <li>Add {@code @Scheduled} methods for your specific tasks</li>
 *   <li>Use {@code localise()} to create locale-specific jobs</li>
 *   <li>Create jobs with {@code builder.create()} or {@code builder.createLongRunning()}</li>
 *   <li>Use {@code schedule(job)} to submit jobs to the task scheduler with error handling</li>
 * </ol>
 *
 * <p>See {@code global.symbio.billing.invoice.factory.job.InvoiceScheduledTasksFactory} for an example implementation.</p>
 *
 * @see io.micronaut.scheduling.annotation.Scheduled
*/
@Slf4j
@Singleton
@Requires(beans = {LocalisationSettings.class, JobBuilder.class, TaskScheduler.class, NoArgGenerator.class})
public abstract class ScheduledTasksFactory {

    @Nonnull
    protected final Set<LocalisationSettings> settings;

    @Nonnull
    protected final JobBuilder builder;

    // only used inside schedule(), so keep it private for now
    @Nonnull
    private final TaskScheduler scheduler;

    // generates correlation id when using JobBuilder
    @Nonnull
    protected final NoArgGenerator generator;

    @Inject
    public ScheduledTasksFactory(
        @Nonnull Set<LocalisationSettings> settings,
        @Nonnull JobBuilder builder,
        @Nonnull TaskScheduler scheduler,
        @Nonnull NoArgGenerator generator
    ) {
        this.settings = Objects.requireNonNull(settings, "settings");
        this.builder = Objects.requireNonNull(builder, "builder");
        this.scheduler = Objects.requireNonNull(scheduler, "scheduler");
        this.generator = Objects.requireNonNull(generator, "generator");
    }

    /**
     * Default cleanup task.
     */
    @Scheduled(fixedRate = "${job.schedule.purge-expired-jobs-task:1m}")
    public void schedule_purge_expired_jobs_task() {
        final var period = Instant.now();
        final var job = builder.create(PurgeExpiredJobsTask.class, period);
        schedule(job);
    }

    protected void localise(@Nonnull Consumer<LocalisationSettings> schedule) {
        settings.forEach(schedule);
    }

    protected void schedule(@Nonnull Job job) {
        try {
            final var scheduled = scheduler.schedule(job);
            log.info("Scheduled job: {} - {}", scheduled, job);
        } catch (Throwable cause) {
            log.info("Exception scheduling job: {}", job, cause);
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof ScheduledTasksFactory factory)) return false;
        return Objects.equals(settings, factory.settings) && Objects.equals(builder, factory.builder) && Objects.equals(scheduler, factory.scheduler);
    }

    @Override
    public int hashCode() {
        return Objects.hash(settings, builder, scheduler, generator);
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("settings", settings)
            .add("builder", builder)
            .add("scheduler", scheduler)
            .add("generator", generator)
            .toString();
    }
}