package global.symbio.billing.core.job.scheduler.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.MoreObjects;
import global.symbio.billing.core.job.container.TaskContainer;
import global.symbio.billing.core.job.persistence.api.job.Job;
import global.symbio.billing.core.job.persistence.api.job.JobDataAccessObject;
import global.symbio.billing.core.job.persistence.api.job.state.JobState;
import global.symbio.billing.core.job.persistence.api.job.state.Skipped;
import global.symbio.billing.core.job.persistence.api.repository.JobRepository;
import global.symbio.billing.core.job.scheduler.TaskScheduler;
import global.symbio.billing.core.job.task.ParameterisedTask;
import global.symbio.billing.core.job.task.ParameterlessTask;
import global.symbio.billing.core.job.task.TransactionExempt;
import global.symbio.billing.core.job.task.Task;
import global.symbio.billing.core.persistence.api.PersistenceStore;
import io.micronaut.context.annotation.Primary;
import io.micronaut.context.annotation.Requires;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.Objects;
import java.util.Optional;

import static global.symbio.billing.core.persistence.api.PersistenceStore.Capabilities.READ_WRITE;

@Slf4j
@Primary
@Singleton
@Requires(beans = {TaskContainer.class, JobRepository.class, ObjectMapper.class})
public class TransactionalTaskScheduler implements TaskScheduler {

    @Nonnull
    private static final Duration RETRY_INTERVAL = Duration.ofMinutes(1L);
    private static final int RETRY_ATTEMPTS_THRESHOLD = 10;

    @Nonnull
    private final TaskContainer container;

    @Nonnull
    private final JobRepository<JobDataAccessObject> repository;

    @Nonnull
    private final ObjectMapper mapper;

    @Inject
    public TransactionalTaskScheduler(@Nonnull TaskContainer container, @Nonnull @PersistenceStore(READ_WRITE) JobRepository<JobDataAccessObject> repository, @Nonnull ObjectMapper mapper) {
        this.container = Objects.requireNonNull(container, "container");
        this.repository = Objects.requireNonNull(repository, "repository");
        this.mapper = Objects.requireNonNull(mapper, "mapper");
    }

    @Override
    public boolean schedule(@Nonnull Collection<Job> jobs) {
        try {
            doSchedule(jobs);
            return true;
        } catch (Throwable cause) {
            log.info("Unable to save jobs {} jobs: {}", jobs.size(), cause.getMessage());
            return false;
        }
    }

    @Transactional
    protected void doSchedule(@Nonnull Collection<Job> jobs) throws Exception {
        repository.saveAll(jobs.stream().map(Job::data).toList());
    }

    @Override
    @Nonnull
    public JobState execute(@Nonnull Job job) {
        log.info("Executing job: {}", job);
        JobState state;
        Duration retention;
        try {
            final var task = container.lookup(job);
            try {
                final var parameters = getParameters(task);
                final var start = Instant.now();
                if (job.getState() instanceof Skipped) {
                    log.info("Skipping job: {}", job);
                    state = job.getState();
                } else {
                    state = doExecute(task, parameters);
                }
                retention = task.retention(state);
                final var duration = Duration.between(start, Instant.now());
                log.info("Executed job: {} - {} in {}", job.getIdentifier(), job.getJob(), duration);
            } catch (Exception cause) {
                //TODO: exponential backoff / jitter / smarter / https://github.com/Hc747/FailSafe
                //TODO: don't automatically retry if the exception was not handled by the Task
                log.warn("Exception executing job: {}", job, cause);
                state = job.getState().limit(RETRY_ATTEMPTS_THRESHOLD, cause, RETRY_INTERVAL);
                retention = task.retention(state);
            }
        } catch (Exception cause) {
            log.warn("Exception resolving job: {}", job, cause);
            state = job.getState().failure(cause, null);
            retention = null;
        }
        final var expiration = Optional.ofNullable(retention).map(duration -> ZonedDateTime.now().plus(duration)).orElse(null);
        updateJob(job.data().state(state).expiration(expiration));
        log.trace("Executed job: {} - {} - {} - {}", job.getIdentifier(), job.getJob(), expiration, state);
        return state;
    }

    //create a new transaction for saving because when doExecute takes too long, connection in execute becomes idle and will not update job
    @Transactional(value = Transactional.TxType.REQUIRES_NEW)
    protected void updateJob(@Nonnull JobDataAccessObject job) {
        repository.update(job);
    }

    protected <P> JobState doExecute(@Nonnull Task<P> task, @Nullable P parameters) throws Exception {
        log.info("Executing {}: {}", task.getClass().getSimpleName(), parameters);
        // Decide which transaction behavior to use
        if (shouldNotUseTransaction(task)) {
            return executeInNoTransaction(task, parameters);
        } else {
            return executeInNewTransaction(task, parameters);
        }
    }

    @Transactional(value = Transactional.TxType.REQUIRES_NEW)
    protected <P> JobState executeInNewTransaction(@Nonnull Task<P> task, @Nullable P parameters) throws Exception {
        return executeTaskLogic(task, parameters);
    }

    protected <P> JobState executeInNoTransaction(@Nonnull Task<P> task, @Nullable P parameters) throws Exception {
        return executeTaskLogic(task, parameters);
    }

    protected <P> JobState executeTaskLogic(@Nonnull Task<P> task, @Nullable P parameters) throws Exception {
        return switch (task) {
            case ParameterisedTask<P> parameterised -> parameterised.execute(Objects.requireNonNull(parameters, "parameters"));
            case ParameterlessTask parameterless -> parameterless.execute();
        };
    }

    @Nullable
    private <P> P getParameters(@Nonnull Task<P> task) throws Exception {
        return switch (task) {
            case ParameterisedTask<P> parameterised -> mapper.readValue(parameterised.getDefinition().getParameters(), parameterised.type());
            case ParameterlessTask ignored -> null;
        };
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof TransactionalTaskScheduler scheduler)) return false;
        return Objects.equals(container, scheduler.container) && Objects.equals(repository, scheduler.repository) && Objects.equals(mapper, scheduler.mapper);
    }

    @Override
    public int hashCode() {
        return Objects.hash(container, repository, mapper);
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("container", container)
            .add("jobs", repository)
            .add("mapper", mapper)
            .toString();
    }

    /**
     * Determines whether a task should be executed outside of a transaction context.
     *
     * @param task The task to evaluate
     * @return {@code true} if the task should be executed without a transaction
     */
    private boolean shouldNotUseTransaction(@Nonnull Task<?> task) {
        Class<?> taskClass = task.getClass();
        // Check current class and all superclasses
        while (taskClass != null) {
            if (taskClass.isAnnotationPresent(TransactionExempt.class)) {
                return true;
            }
            taskClass = taskClass.getSuperclass();
        }
        return false;
    }
}
