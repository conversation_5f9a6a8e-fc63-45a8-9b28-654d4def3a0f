package global.symbio.billing.core.job.task;

import global.symbio.billing.core.job.persistence.api.job.Job;
import global.symbio.billing.core.job.persistence.api.job.state.JobState;
import io.micrometer.core.instrument.MeterRegistry;
import io.micronaut.context.annotation.Parameter;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

public abstract non-sealed class ParameterisedTask<P> extends Task<P> {

    public ParameterisedTask(@Nonnull @Parameter Job definition) {
        super(definition);
    }

    public ParameterisedTask(@Nonnull @Parameter Job definition, @Nullable MeterRegistry metrics) {
        super(definition, metrics);
    }

    @Nonnull
    public abstract JobState execute(@Nonnull P parameters) throws Exception;

    @Nonnull
    public abstract Class<P> type();
}
