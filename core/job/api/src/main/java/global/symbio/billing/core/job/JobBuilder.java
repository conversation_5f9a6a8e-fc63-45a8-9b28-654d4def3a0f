package global.symbio.billing.core.job;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.uuid.NoArgGenerator;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.MoreObjects;
import com.google.common.base.Preconditions;
import global.symbio.billing.core.country.api.service.CountryService;
import global.symbio.billing.core.country.persistence.api.Country;
import global.symbio.billing.core.exception.sdk.BillingEntityNotFoundException;
import global.symbio.billing.core.i18n.localisation.LocationAware;
import global.symbio.billing.core.job.persistence.api.job.Job;
import global.symbio.billing.core.job.persistence.api.job.JobDataAccessObjectFactory;
import global.symbio.billing.core.job.persistence.api.job.state.JobState;
import global.symbio.billing.core.job.persistence.api.job.state.Pending;
import global.symbio.billing.core.job.task.ParameterisedTask;
import global.symbio.billing.core.job.task.ParameterlessTask;
import global.symbio.billing.core.job.task.Task;
import io.micronaut.context.annotation.Requires;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.time.Instant;
import java.time.ZonedDateTime;
import java.time.temporal.Temporal;
import java.util.*;

@Singleton
@Requires(beans = {JobDataAccessObjectFactory.class, NoArgGenerator.class, ObjectMapper.class, CountryService.class})
public class JobBuilder {

    private static final Country GLOBAL = null;

    @Nonnull
    private final JobDataAccessObjectFactory factory;

    @Nonnull
    private final NoArgGenerator generator;

    @Nonnull
    private final ObjectMapper mapper;

    @Nonnull
    private final CountryService countries;

    @Inject
    public JobBuilder(
        @Nonnull JobDataAccessObjectFactory factory,
        @Nonnull NoArgGenerator generator,
        @Nonnull ObjectMapper mapper,
        @Nonnull CountryService countries
    ) {
        this.factory = Objects.requireNonNull(factory, "factory");
        this.generator = Objects.requireNonNull(generator, "generator");
        this.mapper = Objects.requireNonNull(mapper, "mapper");
        this.countries = Objects.requireNonNull(countries, "countries");
    }

    @Nonnull
    public <P, T extends ParameterisedTask<P>> Job createLongRunning(
        @Nonnull Job parent,
        @Nonnull Class<T> type,
        @Nonnull P parameters,
        @Nullable Temporal period,
        @Nonnull Object... components
    ) {
        return createLongRunning(type, parameters, parent.getCountry(), parent.getCorrelation(), parent.getTags(), period, components);
    }

    @Nonnull
    public <P, T extends ParameterisedTask<P>> Job createLongRunning(
        @Nonnull Class<T> type,
        @Nonnull P parameters,
        @Nullable Temporal period,
        @Nonnull Object... components
    ) {
        return createLongRunning(type, parameters, GLOBAL, period, components);
    }

    @Nonnull
    public <P, T extends ParameterisedTask<P>> Job createLongRunning(
        @Nonnull Class<T> type,
        @Nonnull P parameters,
        @Nonnull LocationAware localised,
        @Nullable Temporal period,
        @Nonnull Object... components
    ) {
        return createLongRunning(type, parameters, localised.country(), period, components);
    }

    @Nonnull
    public <P, T extends ParameterisedTask<P>> Job createLongRunning(
        @Nonnull Class<T> type,
        @Nonnull P parameters,
        @Nullable String country,
        @Nullable Temporal period,
        @Nonnull Object... components
    ) {
        return createLongRunning(type, parameters, lookup(country), period, components);
    }

    @Nonnull
    public <P, T extends ParameterisedTask<P>> Job createLongRunning(
        @Nonnull Class<T> type,
        @Nonnull P parameters,
        @Nullable Country country,
        @Nullable Temporal period,
        @Nonnull Object... components
    ) {
        return createLongRunning(type, parameters, country, null, null, period, components);
    }

    @Nonnull
    public <P, T extends ParameterisedTask<P>> Job createLongRunning(
        @Nonnull Class<T> type,
        @Nonnull P parameters,
        @Nullable Country country,
        @Nullable UUID correlation,
        @Nullable Map<String, ?> tags,
        @Nullable Temporal period,
        @Nonnull Object... components
    ) {
        return createImpl(Pending.getInstance(), type, true, parameters, country, correlation, tags, period, components);
    }

    @Nonnull
    public <P, T extends ParameterisedTask<P>> Job create(
        @Nonnull Job parent,
        @Nonnull Class<T> type,
        @Nonnull P parameters,
        @Nullable Temporal period,
        @Nonnull Object... components
    ) {
        return create(type, parameters, parent.getCountry(), parent.getCorrelation(), parent.getTags(), period, components);
    }

    @Nonnull
    public <P, T extends ParameterisedTask<P>> Job create(
        @Nonnull Job parent,
        @Nonnull Class<T> type,
        @Nonnull P parameters,
        @Nullable UUID correlation,
        @Nullable Map<String, ?> tags,
        @Nullable Temporal period,
        @Nonnull Object... components
    ) {
        return create(type, parameters, parent.getCountry(), correlation, tags, period, components);
    }

    @Nonnull
    public <P, T extends ParameterisedTask<P>> Job create(
        @Nonnull Class<T> type,
        @Nonnull P parameters,
        @Nullable Temporal period,
        @Nonnull Object... components
    ) {
        return create(type, parameters, GLOBAL, period, components);
    }

    @Nonnull
    public <P, T extends ParameterisedTask<P>> Job create(
        @Nonnull Class<T> type,
        @Nonnull P parameters,
        @Nonnull LocationAware localised,
        @Nullable Temporal period,
        @Nonnull Object... components
    ) {
        return create(type, parameters, localised.country(), period, components);
    }

    @Nonnull
    public <P, T extends ParameterisedTask<P>> Job create(
        @Nonnull Class<T> type,
        @Nonnull P parameters,
        @Nullable String country,
        @Nullable Temporal period,
        @Nonnull Object... components
    ) {
        return create(type, parameters, lookup(country), period, components);
    }

    @Nonnull
    public <P, T extends ParameterisedTask<P>> Job create(
        @Nonnull Class<T> type,
        @Nonnull P parameters,
        @Nonnull LocationAware localised,
        @Nullable UUID correlation,
        @Nullable Map<String, ?> tags,
        @Nullable Temporal period,
        @Nonnull Object... components
    ) {
        return create(type, parameters, localised.country(), correlation, tags, period, components);
    }

    @Nonnull
    public <P, T extends ParameterisedTask<P>> Job create(
        @Nonnull Class<T> type,
        @Nonnull P parameters,
        @Nullable String country,
        @Nullable UUID correlation,
        @Nullable Map<String, ?> tags,
        @Nullable Temporal period,
        @Nonnull Object... components
    ) {
        return create(type, parameters, lookup(country), correlation, tags, period, components);
    }

    @Nonnull
    public <P, T extends ParameterisedTask<P>> Job create(
        @Nonnull Class<T> type,
        @Nonnull P parameters,
        @Nullable Country country,
        @Nullable Temporal period,
        @Nonnull Object... components
    ) {
        return create(type, parameters, country, null, null, period, components);
    }

    @Nonnull
    public <P, T extends ParameterisedTask<P>> Job create(
        @Nonnull Class<T> type,
        @Nonnull P parameters,
        @Nullable Country country,
        @Nullable UUID correlation,
        @Nullable Map<String, ?> tags,
        @Nullable Temporal period,
        @Nonnull Object... components
    ) {
        return createImpl(Pending.getInstance(), type, false, parameters, country, correlation, tags, period, components);
    }


    @Nonnull
    public <T extends ParameterlessTask> Job createLongRunning(
        @Nonnull Job parent,
        @Nonnull Class<T> type,
        @Nullable Temporal period,
        @Nonnull Object... components
    ) {
        return createLongRunning(type, parent.getCountry(), period, components);
    }

    @Nonnull
    public <T extends ParameterlessTask> Job createLongRunning(
        @Nonnull Class<T> type,
        @Nullable Temporal period,
        @Nonnull Object... components
    ) {
        return createLongRunning(type, GLOBAL, period, components);
    }

    @Nonnull
    public <T extends ParameterlessTask> Job createLongRunning(
        @Nonnull Class<T> type,
        @Nonnull LocationAware localised,
        @Nullable Temporal period,
        @Nonnull Object... components
    ) {
        return createLongRunning(type, localised.country(), period, components);
    }

    @Nonnull
    public <T extends ParameterlessTask> Job createLongRunning(
        @Nonnull Class<T> type,
        @Nullable String country,
        @Nullable Temporal period,
        @Nonnull Object... components
    ) {
        return createLongRunning(type, lookup(country), period, components);
    }

    @Nonnull
    public <T extends ParameterlessTask> Job createLongRunning(
        @Nonnull Class<T> type,
        @Nullable Country country,
        @Nullable Temporal period,
        @Nonnull Object... components
    ) {
        return createImpl(Pending.getInstance(), type, true, null, country, null, null, period, components);
    }

    @Nonnull
    public <T extends ParameterlessTask> Job create(
        @Nonnull Job parent,
        @Nonnull Class<T> type,
        @Nullable Temporal period,
        @Nonnull Object... components
    ) {
        return create(type, parent.getCountry(), period, components);
    }

    @Nonnull
    public <T extends ParameterlessTask> Job create(
        @Nonnull Class<T> type,
        @Nullable Temporal period,
        @Nonnull Object... components
    ) {
        return create(type, GLOBAL, period, components);
    }

    @Nonnull
    public <T extends ParameterlessTask> Job create(
        @Nonnull Class<T> type,
        @Nonnull LocationAware localised,
        @Nullable Temporal period,
        @Nonnull Object... components
    ) {
        return create(type, localised.country(), period, components);
    }

    @Nonnull
    public <T extends ParameterlessTask> Job create(
        @Nonnull Class<T> type,
        @Nullable String country,
        @Nullable Temporal period,
        @Nonnull Object... components
    ) {
        return create(type, lookup(country), period, components);
    }

    @Nonnull
    public <T extends ParameterlessTask> Job create(
        @Nonnull Class<T> type,
        @Nullable Country country,
        @Nullable Temporal period,
        @Nonnull Object... components
    ) {
        return createImpl(Pending.getInstance(), type, false, null, country, null, null, period, components);
    }

    @Nonnull
    @VisibleForTesting
    <P, T extends Task<P>> Job createImpl(
        @Nonnull JobState state,
        @Nonnull Class<T> type,
        boolean longRunning,
        @Nullable P parameters,
        @Nullable Country country,
        @Nullable UUID correlation,
        @Nullable Map<String, ?> tags,
        @Nullable Temporal period,
        @Nonnull Object... components
    ) {
        Objects.requireNonNull(state, "state");
        Objects.requireNonNull(type, "type");
        final var job = factory
            .create(generator.generate())
            .state(state)
            .type(type)
            .longRunning(longRunning)
            .parameters(encode(type, parameters))
            .country(country)
            .correlation(correlation)
            .tags(tags)
            .job(title(type, components))
            .period(period == null ? Instant.now() : period)
            .expiration(null)
            .timestamp(ZonedDateTime.now());
        return job.entity();
    }

    @Nullable
    @VisibleForTesting
    Country lookup(@Nullable String code) {
        if (code == null) {
            return GLOBAL;
        }
        return Optional.ofNullable(countries.lookup(code)).orElseThrow(() -> new BillingEntityNotFoundException(Country.class, code));
    }

    @Nullable
    @VisibleForTesting
    protected <P, T extends Task<P>> String encode(@Nonnull Class<T> type, @Nullable Object parameters) {
        if (ParameterisedTask.class.isAssignableFrom(type)) {
            Preconditions.checkArgument(parameters != null, "ParameterisedTask parameters must not be null");
            return encodeAsJSON(parameters);
        } else if (ParameterlessTask.class.isAssignableFrom(type)) {
            Preconditions.checkArgument(parameters == null, "ParameterlessTask parameters must be null");
            return null;
        } else {
            throw new IllegalStateException("Unhandled Task type: " + type.getSimpleName());
        }
    }

    @Nonnull
    @VisibleForTesting
    String encodeAsJSON(@Nonnull Object parameters) {
        try {
            return mapper.writeValueAsString(parameters);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof JobBuilder builder)) return false;
        return Objects.equals(factory, builder.factory) && Objects.equals(generator, builder.generator) && Objects.equals(mapper, builder.mapper) && Objects.equals(countries, builder.countries);
    }

    @Override
    public int hashCode() {
        return Objects.hash(factory, generator, mapper, countries);
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("factory", factory)
            .add("generator", generator)
            .add("mapper", mapper)
            .add("countries", countries)
            .toString();
    }

    @Nonnull
    @VisibleForTesting
    static String title(@Nonnull Class<?> type, @Nonnull Object... components) {
        final var joiner = new StringJoiner(":").add(type.getSimpleName());
        for (final var component : components) {
            joiner.add(Objects.toString(component));
        }
        return joiner.toString();
    }

}