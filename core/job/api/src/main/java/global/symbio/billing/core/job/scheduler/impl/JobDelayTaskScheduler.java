package global.symbio.billing.core.job.scheduler.impl;

import global.symbio.billing.core.job.api.service.JobDelayService;
import global.symbio.billing.core.job.persistence.api.job.Job;
import global.symbio.billing.core.job.persistence.api.job.delay.JobDelayDataAccessObject;
import global.symbio.billing.core.job.persistence.api.job.state.JobState;
import global.symbio.billing.core.job.persistence.api.repository.JobDelayRepository;
import global.symbio.billing.core.job.scheduler.TaskScheduler;
import global.symbio.billing.core.persistence.api.PersistenceStore;
import jakarta.annotation.Nonnull;

import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.Objects;

import static global.symbio.billing.core.persistence.api.PersistenceStore.Capabilities.READ_WRITE;

public record JobDelayTaskScheduler(
    @Nonnull TaskScheduler scheduler,
    @Nonnull JobDelayService service,
    @Nonnull @PersistenceStore(READ_WRITE) JobDelayRepository<JobDelayDataAccessObject> repository
) implements TaskScheduler {

    public JobDelayTaskScheduler {
        Objects.requireNonNull(scheduler, "scheduler");
        Objects.requireNonNull(service, "service");
        Objects.requireNonNull(repository, "repository");
    }

    @Override
    public boolean schedule(@Nonnull Job job) {
        return scheduler.schedule(job);
    }

    @Override
    public boolean schedule(@Nonnull Collection<Job> jobs) {
        return scheduler.schedule(jobs);
    }

    @Nonnull
    @Override
    public JobState execute(@Nonnull Job job) {
        final var delay = service.lookup(job.getType(), job.getCountry(), true);

        // No active delay for this job and country combination
        if (delay == null) {
            return scheduler.execute(job);
        }

        final var current = ZonedDateTime.now();
        final var range = delay.range();
        final var skipped = range.contains(current);

        if (skipped) {
            final var state = job.getState().skipped(delay.getReason());
            return scheduler.execute(job.data().state(state).period(Instant.now()).entity());
        } else {
            //only change to inactive if finished
            final var expired = range.hasUpperBound() && range.upperEndpoint().isBefore(current);
            if (expired) {
                service.update(delay, data -> data.active(false)); // no longer active, update
            }
            return scheduler.execute(job); //execute as normal
        }
    }
}