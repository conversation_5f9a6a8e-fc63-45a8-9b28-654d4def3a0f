package global.symbio.billing.core.job;

import com.fasterxml.uuid.impl.UUIDUtil;
import com.google.common.base.MoreObjects;
import com.google.common.base.Preconditions;
import global.symbio.billing.core.job.persistence.api.job.Job;
import global.symbio.billing.core.job.persistence.api.job.JobDataAccessObject;
import global.symbio.billing.core.job.persistence.api.repository.JobRepository;
import global.symbio.billing.core.job.scheduler.TaskScheduler;
import global.symbio.billing.core.micronaut.environment.EnvironmentConfiguration;
import global.symbio.billing.core.persistence.api.PersistenceStore;
import io.micronaut.context.annotation.Property;
import io.micronaut.context.annotation.Requirements;
import io.micronaut.context.annotation.Requires;
import io.micronaut.core.util.StringUtils;
import io.micronaut.scheduling.annotation.Scheduled;
import jakarta.annotation.Nonnull;
import jakarta.annotation.PreDestroy;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;
import lombok.extern.slf4j.Slf4j;

import java.util.Collection;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.ForkJoinWorkerThread;
import java.util.concurrent.atomic.AtomicInteger;

import static global.symbio.billing.core.job.TaskExecutor.PROPERTY_JOB_EXECUTION_ENABLED;
import static global.symbio.billing.core.persistence.api.PersistenceStore.Capabilities.READ_WRITE;

@Slf4j
@Singleton
@Requirements({
    @Requires(beans = {TaskScheduler.class, JobRepository.class}),
    @Requires(property = PROPERTY_JOB_EXECUTION_ENABLED, value = StringUtils.TRUE, defaultValue = StringUtils.FALSE)
})
public class TaskExecutor implements AutoCloseable {

    private static final Set<UUID> NO_ACTIVE_TASKS = Set.of(UUIDUtil.nilUUID());
    private static final String SLOW_TASK_THREAD_POOL_NAME = "slow-tasks";
    private static final String FAST_TASK_THREAD_POOL_NAME = "fast-tasks";
    public static final String PROPERTY_JOB_EXECUTION_ENABLED = "job.execution.enabled";
    public static final String PROPERTY_JOB_EXECUTION_INTERVAL = "job.execution.interval";
    public static final String PROPERTY_JOB_EXECUTION_PARALLELISM = "job.execution.parallelism";
    public static final String PROPERTY_JOB_EXECUTION_CONCURRENCY = "job.execution.concurrency";
    public static final String DEFAULT_JOB_EXECUTION_INTERVAL = "5s";
    public static final String DEFAULT_JOB_EXECUTION_PARALLELISM = "6";
    public static final String DEFAULT_JOB_EXECUTION_CONCURRENCY = "128";

    @Nonnull
    private final TaskScheduler scheduler;

    @Nonnull
    private final JobRepository<JobDataAccessObject> jobs;

    private final int concurrency;

    /**
     * The {@link ExecutorService} used to execute jobs that are short-lived.
     */
    @Nonnull
    private final ExecutorService fast;

    /**
     * The {@link ExecutorService} used to execute jobs that are long-lived.
     */
    @Nonnull
    private final ExecutorService slow;

    /**
     * The identifiers of jobs that are currently executing - shared between both the fast and slow executors.
     * Used to ensure that a job is only executing in a thread-safe manner.
     */
    @Nonnull
    private final Set<UUID> active;

    @Nonnull
    private final EnvironmentConfiguration environment;

    @Inject
    public TaskExecutor(
        @Nonnull TaskScheduler scheduler,
        @Nonnull @PersistenceStore(READ_WRITE) JobRepository<JobDataAccessObject> jobs,
        @Property(name = PROPERTY_JOB_EXECUTION_PARALLELISM, defaultValue = DEFAULT_JOB_EXECUTION_PARALLELISM) int parallelism,
        @Property(name = PROPERTY_JOB_EXECUTION_CONCURRENCY, defaultValue = DEFAULT_JOB_EXECUTION_CONCURRENCY) int concurrency,
        @Nonnull EnvironmentConfiguration environment
    ) {
        //TODO: (JDK-22/JEP-447): ensure concurrency >= parallelism
        this(scheduler, jobs, concurrency,
            executor(FAST_TASK_THREAD_POOL_NAME, parallelism),
            executor(SLOW_TASK_THREAD_POOL_NAME, parallelism),
            ConcurrentHashMap.newKeySet(),
            environment
        );
    }

    protected TaskExecutor(
        @Nonnull TaskScheduler scheduler,
        @Nonnull JobRepository<JobDataAccessObject> jobs,
        int concurrency,
        @Nonnull ExecutorService fast,
        @Nonnull ExecutorService slow,
        @Nonnull Set<UUID> active,
        @Nonnull EnvironmentConfiguration environment
    ) {
        Preconditions.checkArgument(concurrency > 0, "Concurrency must be positive and non-zero.");
        this.scheduler = Objects.requireNonNull(scheduler, "scheduler");
        this.jobs = Objects.requireNonNull(jobs, "jobs");
        this.concurrency = concurrency;
        this.fast = Objects.requireNonNull(fast, "fast");
        this.slow = Objects.requireNonNull(slow, "slow");
        this.active = Objects.requireNonNull(active, "active");
        this.environment = Objects.requireNonNull(environment, "environment");
    }

    @Scheduled(fixedRate = "${" + PROPERTY_JOB_EXECUTION_INTERVAL + ":" + DEFAULT_JOB_EXECUTION_INTERVAL + "}")
    protected void execute_jobs() {
        //TODO: wrap in try-catch?
        execute();
    }

    private void execute() {
        final var jobs = getExecutableJobs();
        log.info("Scheduling {} jobs for execution", jobs.size());
        final var isDevelopment = environment.isDevelopment();
        for (final var job : jobs) {
            if (isLoadableJob(job, isDevelopment)) {
                final var schedulable = active.add(job.getIdentifier());
                if (schedulable) {
                    final var executor = getExecutor(job);
                    executor.execute(() -> execute(job));
                }
            }
        }
    }

    /**
     * In a dev environment, if we encounter any jobs that are not owned by this service, we skip them and don't execute.
     * Added to deal with concurrent task executors in different services running in the same environment where
     * `jobs` table is shared, and they end up picking up another service's jobs.
     * (eg, invoice and ledger)
     *
     * @param job we are checking if our service 'owns' this job
     * @param isDevelopment true if we are in a dev environment, false otherwise.
     * @return false if we can't load this class and isDevelopment is true, true otherwise.
     */
    private boolean isLoadableJob(@Nonnull Job job, boolean isDevelopment) {
        if (isDevelopment) {
            try {
                Class.forName(job.getType());
            } catch (ClassNotFoundException e) {
                log.warn("DEV ONLY. Job not intended for this service. Skipping job: {} - {}. Cause: {} ",
                    job.getIdentifier(), job.getType(), e);
                return false;
            }
        }
        return true;
    }

    @Nonnull
    private Collection<Job> getExecutableJobs() {
        // We take a snapshot of the set of active job identifiers at this point in time as it is subject to concurrent access
        // and may be mutated while we are in the process of querying the database.
        // All subsequent query operations are performed on the snapshot in order to retrieve consistent results.
        final var scheduled = getSnapshotOfScheduledJobs();
        final var executing = scheduled.size();
        final var available = concurrency - executing;
        if (available <= 0) {
            // We artificially constrain the system to only execute/schedule to execute N number of jobs at a given point in time.
            log.trace("No available capacity for execute jobs. Concurrency: {}, Executing: {}, Available: {}", concurrency, executing, available);
            return Set.of();
        }
        log.trace("Fetching up to {} jobs for execution", available);
        return this.jobs.getExecutableJobs(scheduled, available).stream().map(JobDataAccessObject::entity).toList();
    }

    @Nonnull
    private Set<UUID> getSnapshotOfScheduledJobs() {
        final var scheduled = Set.copyOf(active);
        if (!scheduled.isEmpty()) {
            return scheduled;
        }
        // There are currently no jobs executing or scheduled for execution.
        // Querying the jobs table to poll for executable jobs requires a non-null, non-empty set of UUID (job identifiers),
        // therefore, we pass in a sentinel set comprised of the null UUID (00000000-0000-0000-0000-000000000000)
        // which will never occur in the wild as a valid job identifier.
        return NO_ACTIVE_TASKS;
    }

    private void execute(@Nonnull Job job) {
        final var key = job.getIdentifier();
        try {
            final var state = scheduler.execute(job);
            log.info("Executed job: {} - {}", job, state);
        } catch (Exception cause) {
            log.info("Exception executing job: {}", job, cause);
        } finally {
            active.remove(key);
        }
    }

    @Nonnull
    private ExecutorService getExecutor(@Nonnull Job job) {
        if (job.isLongRunning()) {
            return slow;
        }
        return fast;
    }

    @PreDestroy
    @Override
    public void close() {
        // Allow slow tasks to finish gracefully prior to shutting down the fast task executor.
        try {
            slow.close();
        } finally {
            fast.close();
        }
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("concurrency", concurrency)
            .add("scheduler", scheduler)
            .add("jobs", jobs)
            .add("fast", fast)
            .add("slow", slow)
            .add("active", active)
            .toString();
    }

    @Nonnull
    private static ExecutorService executor(@Nonnull final String name, final int parallelism) {
        // At least 1 worker thread in the work pool.
        final var workers = Math.max(parallelism, 1);
        final var factory = new TaskExecutorForkJoinWorkerThreadFactory(name);
        // Construct a fork-join pool as per Executors#newWorkStealingPool.
        // We use work stealing, non-blocking pools rather than synchronous, blocking pools so that each executor
        // is free to execute jobs as sufficient resources become available to do so, as a result, net throughput of the
        // task executing subsystem increases.
        return new ForkJoinPool(workers, factory, null, true);
    }

    private record TaskExecutorForkJoinWorkerThreadFactory(
        @Nonnull String name,
        @Nonnull AtomicInteger sequence
    ) implements ForkJoinPool.ForkJoinWorkerThreadFactory {

        private TaskExecutorForkJoinWorkerThreadFactory {
            Objects.requireNonNull(name, "name");
            Objects.requireNonNull(sequence, "sequence");
        }

        public TaskExecutorForkJoinWorkerThreadFactory(@Nonnull String name) {
            this(name, new AtomicInteger(0));
        }

        @Override
        public ForkJoinWorkerThread newThread(@Nonnull final ForkJoinPool pool) {
            // Delegate construction of the ForkJoinWorkerThread to the default factory, but rename the thread to the parameterised name.
            final var worker = ForkJoinPool.defaultForkJoinWorkerThreadFactory.newThread(pool);
            worker.setName(name + "-" + sequence.getAndIncrement());
            return worker;
        }
    }
}
