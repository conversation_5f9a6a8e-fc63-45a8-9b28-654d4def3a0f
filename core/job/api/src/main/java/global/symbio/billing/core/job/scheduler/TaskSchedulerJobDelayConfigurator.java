package global.symbio.billing.core.job.scheduler;

import global.symbio.billing.core.job.api.service.JobDelayService;
import global.symbio.billing.core.job.persistence.api.job.delay.JobDelayDataAccessObject;
import global.symbio.billing.core.job.persistence.api.repository.JobDelayRepository;
import global.symbio.billing.core.job.scheduler.impl.JobDelayTaskScheduler;
import global.symbio.billing.core.mixin.MixinConfigurator;
import global.symbio.billing.core.persistence.api.PersistenceStore;
import io.micronaut.inject.BeanDefinition;
import io.micronaut.inject.BeanIdentifier;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;

import java.util.Objects;

import static global.symbio.billing.core.persistence.api.PersistenceStore.Capabilities.READ_WRITE;

public class TaskSchedulerJobDelayConfigurator extends MixinConfigurator<TaskScheduler> {

    private static final String COMPONENT_NAME = "delay";

    @Nonnull
    private final JobDelayService service;

    @Nonnull
    private final JobDelayRepository<JobDelayDataAccessObject> repository;

    @Inject
    public TaskSchedulerJobDelayConfigurator(
        @Nonnull JobDelayService service,
        @Nonnull @PersistenceStore(READ_WRITE) JobDelayRepository<JobDelayDataAccessObject> repository
    ) {
        this.service = Objects.requireNonNull(service, "service");
        this.repository = Objects.requireNonNull(repository, "repository");
    }

    @Nonnull
    @Override
    public TaskScheduler mixin(@Nonnull TaskScheduler bean, @Nonnull String name) {
        return new JobDelayTaskScheduler(bean, service, repository);
    }

    @Nonnull
    @Override
    protected String component() {
        return COMPONENT_NAME;
    }

    @Nonnull
    @Override
    protected String getName(@Nonnull TaskScheduler bean, @Nonnull BeanDefinition<TaskScheduler> definition, @Nonnull BeanIdentifier identifier) {
        return identifier.getName();
    }

    @Override
    protected boolean isEnabled() {
        return true;
    }
}