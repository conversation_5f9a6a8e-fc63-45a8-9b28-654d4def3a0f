package global.symbio.billing.core.job.container.impl;

import global.symbio.billing.core.job.container.TaskContainer;
import global.symbio.billing.core.job.persistence.api.job.Job;
import global.symbio.billing.core.job.task.Task;
import global.symbio.billing.core.job.task.TaskTypeException;
import io.micronaut.context.BeanContext;
import io.micronaut.context.annotation.Primary;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import java.util.Objects;

@Primary
@Singleton
public record BeanContextTaskContainer(
    @Inject @Nonnull BeanContext context
) implements TaskContainer {

    public BeanContextTaskContainer {
        Objects.requireNonNull(context, "context");
    }

    @Override
    public Task<?> lookup(@Nonnull Job definition) throws Exception {
        Objects.requireNonNull(definition, "definition");
        Objects.requireNonNull(definition.getType(), "type");
        final var type = Class.forName(definition.getType());
        if (!Task.class.isAssignableFrom(type)) {
            throw new TaskTypeException(type);
        }
        return (Task<?>) context.createBean(type, definition);
    }
}
