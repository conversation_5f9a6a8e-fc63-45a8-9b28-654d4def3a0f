package global.symbio.billing.core.job.task;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.job.persistence.api.job.Job;
import global.symbio.billing.core.job.persistence.api.job.state.*;
import io.micrometer.core.instrument.MeterRegistry;
import io.micronaut.context.annotation.Parameter;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.Getter;

import java.time.Duration;
import java.util.Objects;
import java.util.function.Consumer;

@Getter
public abstract sealed class Task<P> permits ParameterlessTask, ParameterisedTask {

    @Nonnull
    protected final Job definition;

    @Nullable
    private final MeterRegistry metrics;

    public Task(@Nonnull @Parameter Job definition) {
        this(definition, null);
    }

    public Task(@Nonnull @Parameter Job definition, @Nullable MeterRegistry metrics) {
        this.definition = Objects.requireNonNull(definition, "definition");
        this.metrics = metrics;
    }

    /**
     * Provides a uniform mechanism for capturing metrics within a {@link Task}.
     *
     * @param action The action to apply upon the {@link MeterRegistry} if present.
     */
    protected void measure(@Nonnull Consumer<MeterRegistry> action) {
        if (metrics != null) {
            action.accept(metrics);
        }
    }

    /**
     * @param state The state of the {@link Task} after execution. Supplied to allow querying the {@link JobState} of a {@link Task}
     *              after execution in order to return dynamic retention periods.
     * @return {@code null} - to retain the {@link Task} indefinitely.
     * <p>{@link Duration.ZERO} or a negative {@link Duration} - to indicate the {@link Task} is ready for deletion.</p>
     * <p>A positive {@link Duration} - to indicate how long the {@link Task} should be retained for.</p>
     */
    @Nullable
    public Duration retention(@Nonnull JobState state) {
        return null;
    }

    /**
     * @param state   The state of the {@link Task} after execution. See {@link Task#retention(JobState)}.
     * @param success The {@link Duration} to retain this {@link Task} for if the execution results in {@link Success}.
     * @param failure The {@link Duration} to retain this {@link Task} for if the execution results in {@link Failure}.
     * @param pending The {@link Duration} to retain this {@link Task} for if the execution results in {@link Pending}.
     * @return The specified {@link Duration}.
     */
    @Nullable
    protected static Duration retain(@Nonnull JobState state, @Nullable Duration success, @Nullable Duration failure, @Nullable Duration pending) {
        return retain(state, success, failure, pending, null);
    }

    @Nullable
    protected static Duration retain(@Nonnull JobState state, @Nullable Duration success, @Nullable Duration failure, @Nullable Duration pending, @Nullable Duration skipped) {
        return switch (state) {
            case Success ignored -> success;
            case Failure ignored -> failure;
            case Pending ignored -> pending;
            case Skipped ignore -> skipped;
        };
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Task<?> task)) return false;
        return Objects.equals(getDefinition(), task.getDefinition()) && Objects.equals(getMetrics(), task.getMetrics());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getDefinition(), getMetrics());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("definition", definition)
            .add("metrics", metrics)
            .toString();
    }
}
