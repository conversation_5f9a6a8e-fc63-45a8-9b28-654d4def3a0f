package global.symbio.billing.core.job.scheduler;

import global.symbio.billing.core.job.persistence.api.job.Job;
import global.symbio.billing.core.job.persistence.api.job.state.JobState;
import jakarta.annotation.Nonnull;

import java.util.Collection;
import java.util.Collections;

public interface TaskScheduler {

    default boolean schedule(@Nonnull Job job) {
        return schedule(Collections.singleton(job));
    }

    boolean schedule(@Nonnull Collection<Job> jobs);

    @Nonnull
    JobState execute(@Nonnull Job job);
}
