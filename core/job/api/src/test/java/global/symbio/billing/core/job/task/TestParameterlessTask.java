package global.symbio.billing.core.job.task;

import global.symbio.billing.core.job.persistence.api.job.Job;
import global.symbio.billing.core.job.persistence.api.job.state.JobState;
import global.symbio.billing.core.job.persistence.api.job.state.Pending;
import io.micrometer.core.instrument.MeterRegistry;
import io.micronaut.context.annotation.Parameter;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Modifier;
import java.util.function.Consumer;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestParameterlessTask {

    @Mock
    private Job definition;

    @Mock
    private MeterRegistry metrics;

    private ParameterlessTask task;

    @BeforeEach
    public void setup() {
        task = new DefaultParameterlessTask(definition, metrics);
    }

    @Test
    @DisplayName("ParameterlessTask is abstract class")
    public void parameterised_task_is_abstract_class() {
        assertTrue(Modifier.isAbstract(ParameterlessTask.class.getModifiers()));
    }

    @Test
    @DisplayName("ParameterlessTask is non-sealed class")
    public void parameterised_task_is_sealed_class() {
        assertFalse(ParameterlessTask.class.isSealed());
    }

    @Test
    @DisplayName("ParameterlessTask::new rejects null constructor arguments")
    public void parameterless_task_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new DefaultParameterlessTask(null));
        assertThrows(NullPointerException.class, () -> new DefaultParameterlessTask(null, metrics));
        assertDoesNotThrow(() -> new DefaultParameterlessTask(definition, null));
    }

    @Test
    @DisplayName("ParameterlessTask::execute returns JobState object")
    public void parameterless_task_execute_returns_job_state() {
        final var state = assertDoesNotThrow(() -> task.execute());
        assertSame(Pending.getInstance(), state);
    }

    @Test
    @DisplayName("ParameterlessTask::retention defaults to null and does not interact with any local state or parameters")
    public void parameterless_task_retention_defaults_to_null_and_does_not_interact_with_state_or_parameters() {
        final var state = spy(Pending.getInstance());
        final var retention = assertDoesNotThrow(() -> task.retention(state));
        assertNull(retention, "ParameterlessTask::retention should be null");
        verifyNoInteractions(state);
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @DisplayName("ParameterlessTask::measure captures metrics if meter registry is not null")
    public void parameterless_task_measure_captures_metrics_if_meter_registry_is_not_null(boolean hasMetrics) {
        final var action = mock(Consumer.class);
        final var task = new DefaultParameterlessTask(definition, hasMetrics ? metrics : null);
        assertDoesNotThrow(() -> task.measure(action));
        verify(action, hasMetrics ? times(1) : never()).accept(metrics);
    }

    private static class DefaultParameterlessTask extends ParameterlessTask {

        public DefaultParameterlessTask(@Nonnull @Parameter Job definition) {
            super(definition);
        }

        public DefaultParameterlessTask(@Nonnull @Parameter Job definition, @Nullable MeterRegistry metrics) {
            super(definition, metrics);
        }

        @Nonnull
        @Override
        public JobState execute() {
            return Pending.getInstance();
        }
    }
}
