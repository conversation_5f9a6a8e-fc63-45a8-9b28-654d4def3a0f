package global.symbio.billing.core.job.task;

import global.symbio.billing.core.job.persistence.api.job.state.Failure;
import global.symbio.billing.core.job.persistence.api.job.state.JobState;
import global.symbio.billing.core.job.persistence.api.job.state.Pending;
import global.symbio.billing.core.job.persistence.api.job.state.Success;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import java.lang.reflect.Modifier;
import java.time.Duration;
import java.time.temporal.ChronoUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;

public class TestTask {

    @Test
    @DisplayName("Task is abstract class")
    public void task_is_abstract_class() {
        assertTrue(Modifier.isAbstract(Task.class.getModifiers()));
    }

    @Test
    @DisplayName("Task is sealed class")
    public void task_is_sealed_class() {
        assertTrue(Task.class.isSealed());
    }

    @Test
    @DisplayName("Task only permits ParameterlessTask and ParameterisedTask implementations")
    public void task_only_permits_parameterless_and_parameterised_subtypes() {
        final var permitted = Task.class.getPermittedSubclasses();
        final var expected = new Class[]{ ParameterlessTask.class, ParameterisedTask.class };
        assertArrayEquals(expected, permitted);
    }

    @ParameterizedTest
    @ValueSource(classes = { Success.class, Failure.class, Pending.class, })
    @DisplayName("Task::retain returns specified duration for state")
    public void task_retain_returns_duration_for_state(@Nonnull final Class<? extends JobState> type) {
        final var state = mock(type);
        final var retention = assertDoesNotThrow(() -> Task.retain(state, Duration.ZERO, ChronoUnit.HOURS.getDuration(), ChronoUnit.DAYS.getDuration()));
        if (type == Success.class) {
            assertSame(Duration.ZERO, retention);
        } else if (type == Failure.class) {
            assertSame(ChronoUnit.HOURS.getDuration(), retention);
        } else if (type == Pending.class) {
            assertSame(ChronoUnit.DAYS.getDuration(), retention);
        } else {
            fail("Unhandled " + JobState.class.getSimpleName() + ": " + type.getSimpleName());
        }
    }
}
