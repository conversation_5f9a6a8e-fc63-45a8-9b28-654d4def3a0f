package global.symbio.billing.core.job.api.service.impl;

import com.github.benmanes.caffeine.cache.AsyncLoadingCache;
import com.github.benmanes.caffeine.cache.LoadingCache;
import global.symbio.billing.core.job.api.service.JobDelayService;
import global.symbio.billing.core.job.persistence.api.job.delay.JobDelay;
import global.symbio.billing.core.job.persistence.api.job.delay.JobDelayDataAccessObject;
import global.symbio.billing.core.job.persistence.api.job.delay.JobDelayKey;
import global.symbio.billing.core.job.persistence.api.repository.JobDelayRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;
import java.util.function.Function;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestCachingJobDelayService {

    @Mock
    private LoadingCache<JobDelayKey, Optional<JobDelay>> delays;

    @Mock
    private AsyncLoadingCache<JobDelayKey, Optional<JobDelay>> asyncDelays;

    @Mock
    private JobDelayRepository<JobDelayDataAccessObject> repository;

    private JobDelayService service;

    @BeforeEach
    public void setup() {
        service = new CachingJobDelayService(delays, repository);
    }

    @Test
    @DisplayName("CachingJobDelayService::new rejects null constructor arguments")
    public void caching_job_delay_service_new_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new CachingJobDelayService((LoadingCache<JobDelayKey, Optional<JobDelay>>) null, repository));
        assertThrows(NullPointerException.class, () -> new CachingJobDelayService(delays, null));
        assertThrows(NullPointerException.class, () -> new CachingJobDelayService((AsyncLoadingCache<JobDelayKey, Optional<JobDelay>>) null, repository));
        assertThrows(NullPointerException.class, () -> new CachingJobDelayService(asyncDelays, null));
    }

    @Test
    @DisplayName("CachingJobDelayService::lookup returns delay from cache when delay in the cache")
    public void caching_job_delay_service_lookup_returns_delay_from_cache_when_delay_in_the_cache() {
        final var delay = mock(JobDelay.class);
        final var key = new JobDelayKey("test", null, true);
        when(delays.get(any(JobDelayKey.class))).thenReturn(Optional.of(delay));
        final var result = assertDoesNotThrow(() -> service.lookup("test", null, true));
        assertSame(delay, result);
        verify(delays, times(1)).get(eq(key));
    }

    @Test
    @DisplayName("CachingJobDelayService::lookup returns null when delay not in the cache")
    public void caching_job_delay_service_lookup_returns_null_when_delay_not_in_cache() {
        final var key = new JobDelayKey("test", null, true);
        when(delays.get(any(JobDelayKey.class))).thenReturn(Optional.empty());
        final var result = assertDoesNotThrow(() -> service.lookup("test", null, true));
        assertNull(result);
        verify(delays, times(1)).get(eq(key));
    }

    @Test
    @DisplayName("CachingJobDelayService::lookup throws exception when cache hit throws exception")
    public void caching_job_delay_service_lookup_throws_exception_when_cache_hit_throws_exception() {
        final var key = new JobDelayKey("test", null, true);
        final var failure = new RuntimeException("Underlying exception");
        when(delays.get(any(JobDelayKey.class))).thenThrow(failure);
        final var cause = assertThrows(RuntimeException.class, () -> service.lookup("test", null, true));
        assertSame(failure, cause);
        verify(delays, times(1)).get(eq(key));
    }

    @Test
    @DisplayName("CachingJobDelayService::update calls repository and returns delay")
    public void caching_job_delay_service_update_calls_repository_and_returns_delay() {
        final var delay = mock(JobDelayDataAccessObject.class);
        when(repository.update(any(JobDelayDataAccessObject.class))).thenReturn(delay);
        final var entity = mock(JobDelay.class);
        when(delay.entity()).thenReturn(entity);
        final var function = mock(Function.class);
        when(function.apply(any())).thenReturn(delay);
        final var result = assertDoesNotThrow(() -> service.update(entity, function));
        assertSame(entity, result);
        verify(repository, times(1)).update(delay);
    }
}
