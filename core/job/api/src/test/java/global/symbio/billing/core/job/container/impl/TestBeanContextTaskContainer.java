package global.symbio.billing.core.job.container.impl;

import global.symbio.billing.core.job.container.TaskContainer;
import global.symbio.billing.core.job.persistence.api.job.Job;
import global.symbio.billing.core.job.persistence.api.job.JobDataAccessObject;
import global.symbio.billing.core.job.task.TaskTypeException;
import io.micronaut.context.ApplicationContext;
import io.micronaut.context.BeanContext;
import io.micronaut.context.annotation.Property;
import io.micronaut.context.exceptions.DependencyInjectionException;
import io.micronaut.test.extensions.junit5.annotation.MicronautTest;
import jakarta.inject.Inject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static com.fasterxml.uuid.impl.UUIDUtil.nilUUID;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@MicronautTest
@ExtendWith(MockitoExtension.class)
@Property(name = "environment", value = "dev")
@Property(name = "countries", value = "MY,SG,AU,NZ")
public class TestBeanContextTaskContainer {

    @Mock
    private JobDataAccessObject data;

    @Inject
    private BeanContext context;
    private Job job;
    private TaskContainer container;

    @BeforeEach
    public void setup() {
        when(data.getIdentifier()).thenReturn(nilUUID());
        when(data.entity()).thenCallRealMethod();
        job = data.entity();
        container = new BeanContextTaskContainer(context);
    }

    @Test
    @DisplayName("Ensure BeanContext is injected and is of type ApplicationContext")
    public void ensure_bean_context_is_injected() {
        assertNotNull(context);
        // may break with upstream changes by Micronaut
        assertInstanceOf(ApplicationContext.class, context);
    }

    @Test
    @DisplayName("BeanContextTaskContainer::new rejects null constructor arguments")
    public void container_cannot_be_instantiated_with_null_bean_context() {
        assertThrows(NullPointerException.class, () -> new BeanContextTaskContainer(null));
    }

    @Test
    @DisplayName("BeanContextTaskContainer::lookup given null Job throws NullPointerException")
    public void container_lookup_null_job_throws_npe() {
        assertThrows(NullPointerException.class, () -> container.lookup(null));
    }

    @Test
    @DisplayName("BeanContextTaskContainer::lookup given Job with null type throws NullPointerException")
    public void container_lookup_null_type_throws_npe() {
        assertThrows(NullPointerException.class, () -> container.lookup(job));
    }

    @Test
    @DisplayName("BeanContextTaskContainer::lookup with Job with non-class type throws ClassNotFoundException")
    public void container_lookup_unknown_type_throws_class_not_found() {
        when(data.getType()).thenReturn(Tasks.NON_CLASS);
        assertThrows(ClassNotFoundException.class, () -> container.lookup(job));
    }

    @Test
    @DisplayName("BeanContextTaskContainer::lookup with Job with non-task type throws TaskTypeException")
    public void container_lookup_non_task_type_throws_task_type_exception() {
        when(data.getType()).thenReturn(Tasks.NON_TASK);
        assertThrows(TaskTypeException.class, () -> container.lookup(job));
    }

    @Test
    @DisplayName("BeanContextTaskContainer::lookup with Job with correctly annotated task type returns valid Task instance")
    public void container_lookup_annotated_task_type_returns_task() {
        when(data.getType()).thenReturn(Tasks.ANNOTATED_TASK);
        final var task = assertDoesNotThrow(() -> container.lookup(job));
        assertInstanceOf(Tasks.AnnotatedParameterlessTask.class, task);
        assertEquals(job, task.getDefinition());
    }

    @Test
    @DisplayName("BeanContextTaskContainer::lookup with Job with incorrectly annotated task type throws NoSuchBeanException")
    public void container_lookup_unannotated_task_type_returns_task() {
        when(data.getType()).thenReturn(Tasks.UNANNOTATED_TASK);
        assertThrows(DependencyInjectionException.class, () -> container.lookup(job));
    }
}