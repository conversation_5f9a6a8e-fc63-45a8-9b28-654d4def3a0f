package global.symbio.billing.core.job.container.impl;

import global.symbio.billing.core.job.persistence.api.job.Job;
import global.symbio.billing.core.job.persistence.api.job.state.JobState;
import global.symbio.billing.core.job.persistence.api.job.state.Pending;
import global.symbio.billing.core.job.task.ParameterlessTask;
import io.micronaut.context.annotation.Bean;
import io.micronaut.context.annotation.Parameter;
import jakarta.annotation.Nonnull;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.UUID;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class Tasks {

    @Bean
    public static class AnnotatedParameterlessTask extends ParameterlessTask {

        public AnnotatedParameterlessTask(@Nonnull @Parameter Job definition) {
            super(definition);
        }

        @Nonnull
        @Override
        public JobState execute() {
            return Pending.getInstance();
        }
    }

    @Bean
    public static class UnannotatedParameterlessTask extends ParameterlessTask {

        public UnannotatedParameterlessTask(@Nonnull Job definition) {
            super(definition);
        }

        @Nonnull
        @Override
        public JobState execute() {
            return Pending.getInstance();
        }
    }

    public static final String NON_TASK = Object.class.getName();
    public static final String NON_CLASS = UUID.randomUUID().toString();
    public static final String ANNOTATED_TASK = AnnotatedParameterlessTask.class.getName();
    public static final String UNANNOTATED_TASK = UnannotatedParameterlessTask.class.getName();
}