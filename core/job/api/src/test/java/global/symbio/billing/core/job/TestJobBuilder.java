package global.symbio.billing.core.job;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.uuid.NoArgGenerator;
import com.fasterxml.uuid.impl.UUIDUtil;
import global.symbio.billing.core.country.api.service.CountryService;
import global.symbio.billing.core.country.persistence.api.Country;
import global.symbio.billing.core.country.persistence.api.CountryDataAccessObject;
import global.symbio.billing.core.exception.sdk.BillingEntityNotFoundException;
import global.symbio.billing.core.job.persistence.api.job.JobDataAccessObject;
import global.symbio.billing.core.job.persistence.api.job.JobDataAccessObjectFactory;
import global.symbio.billing.core.job.persistence.api.job.state.JobState;
import global.symbio.billing.core.job.persistence.api.job.state.Pending;
import global.symbio.billing.core.job.task.ParameterisedTask;
import global.symbio.billing.core.job.task.ParameterlessTask;
import global.symbio.billing.core.job.task.Task;
import global.symbio.billing.core.persistence.api.DeterministicObjectMapper;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.Getter;
import lombok.Setter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Instant;
import java.time.ZonedDateTime;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestJobBuilder {

    @Mock
    private JobDataAccessObjectFactory factory;

    @Mock
    private NoArgGenerator generator;

    @Mock
    private CountryService countries;

    private ObjectMapper mapper;

    private JobBuilder builder;

    @BeforeEach
    public void setup() {
        mapper = DeterministicObjectMapper.get();
        builder = Mockito.spy(new JobBuilder(factory, generator, mapper, countries));
    }

    @Test
    @DisplayName("JobBuilder::new rejects null parameters")
    public void job_builder_rejects_null_constructor_parameters() {
        assertThrows(NullPointerException.class, () -> new JobBuilder(null, generator, mapper, countries));
        assertThrows(NullPointerException.class, () -> new JobBuilder(factory, null, mapper, countries));
        assertThrows(NullPointerException.class, () -> new JobBuilder(factory, generator, null, countries));
        assertThrows(NullPointerException.class, () -> new JobBuilder(factory, generator, mapper, null));
    }

    @ParameterizedTest
    @MethodSource("createImpl")
    @DisplayName("JobBuilder::createImpl constructs Job entity for given parameters")
    public <P, T extends Task<P>> void job_builder_create_impl_constructs_job_entity_for_given_parameters(
        @Nonnull final Class<T> type,
        final boolean longRunning,
        @Nullable P parameters,
        @Nullable String expectedParameters,
        @Nullable UUID correlation,
        @Nullable Map<String, String> tags
    ) {
        final var identifier = UUIDUtil.nilUUID();
        final var definition = spy(new InMemoryJobDataAccessObject());
        when(factory.create()).thenReturn(definition);
        when(factory.create(any(UUID.class))).thenCallRealMethod();
        when(generator.generate()).thenReturn(identifier);
        final var malaysia = mock(CountryDataAccessObject.class, CALLS_REAL_METHODS);
        when(malaysia.getIdentifier()).thenReturn(458);
        final var state = Pending.getInstance();
        final var country = malaysia.entity();
        final var period = Instant.now();
        final var components = "abcdef";

        final var job = assertDoesNotThrow(() -> builder.createImpl(state, type, longRunning, parameters, country, correlation, tags, period, components));

        assertNotNull(job);
        assertSame(definition, job.data());
        assertSame(identifier, job.getIdentifier());
        assertSame(state, job.getState());
        assertEquals(type.getName(), job.getType());
        assertEquals(longRunning, job.isLongRunning());
        assertEquals(expectedParameters, job.getParameters());
        assertEquals(country, job.getCountry());
        assertEquals(type.getSimpleName() + ":" + components, job.getJob());
        assertEquals(period.toString(), job.getPeriod());
        assertNull(job.getExpiration());
        assertNotNull(job.getTimestamp());
        assertEquals(correlation, job.getCorrelation());
        assertEquals(tags, job.getTags());
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @DisplayName("JobBuilder::create get correlation and tags from parent if not provided")
    public <P, T extends ParameterisedTask<P>> void job_builder_use_correlation_tags_from_parent_if_not_provided(boolean inheritFromParent) {
        final var type = ParameterisedTask.class;
        final boolean longRunning = true;
        final var parameters = Map.of("life", 42);
        final var correlation = UUID.randomUUID();
        final var tags = Map.of("scope", "invoicing");

        final var identifier = UUIDUtil.nilUUID();
        final var definition = spy(new InMemoryJobDataAccessObject());
        when(factory.create()).thenReturn(definition);
        when(factory.create(any(UUID.class))).thenCallRealMethod();
        when(generator.generate()).thenReturn(identifier);
        final var malaysia = mock(CountryDataAccessObject.class, CALLS_REAL_METHODS);
        when(malaysia.getIdentifier()).thenReturn(458);
        final var state = Pending.getInstance();
        final var country = malaysia.entity();
        final var period = Instant.now();
        final var components = "abcdef";

        final var parent = assertDoesNotThrow(() -> builder.createImpl(state, type, longRunning, parameters, country, correlation, tags, period, components));
        assertEquals(correlation, parent.getCorrelation());
        assertEquals(tags, parent.getTags());

        if (inheritFromParent) {
            final var child = assertDoesNotThrow(() -> builder.create(parent, type, parameters, period, components));
            assertEquals(correlation, child.getCorrelation());
            assertEquals(tags, child.getTags());
        } else {
            final var childCorrelation = UUID.randomUUID();
            final var account = UUID.randomUUID();
            final var childTags = Map.of("scope", "newscope", "account", account);
            final var child = assertDoesNotThrow(() -> builder.create(parent, type, parameters, childCorrelation, childTags, period, components));
            assertEquals(childCorrelation, child.getCorrelation());
            assertEquals(childTags, child.getTags());
        }
    }

    @Nonnull
    private static Stream<Arguments> createImpl() {
        return Stream.of(
            Arguments.of(ParameterisedTask.class, true, Map.of("life", 42), "{\"life\":42}", null, null),
            Arguments.of(ParameterisedTask.class, true, Map.of("life", 42), "{\"life\":42}", UUID.randomUUID(), Map.of("scope", "invoicing")),
            Arguments.of(ParameterlessTask.class, true, null, null, null, null),
            Arguments.of(ParameterlessTask.class, true, null, null, UUID.randomUUID(), Map.of("scope", "invoicing"))
        );
    }

    @Test
    @DisplayName("JobBuilder::lookup null country code returns null country")
    public void job_builder_lookup_country_with_null_code_returns_global_null() {
        final var country = assertDoesNotThrow(() -> builder.lookup(null));
        assertNull(country);
        verify(countries, never()).lookup(anyString());
    }

    @Test
    @DisplayName("JobBuilder::lookup with invalid code throws BillingEntityNotFoundException")
    public void job_builder_lookup_country_with_invalid_code_throws_billing_entity_not_found_exception() {
        when(countries.lookup(anyString())).thenReturn(null);
        final var cause = assertThrows(BillingEntityNotFoundException.class, () -> builder.lookup("ZZ"));
        assertEquals("Entity of type Country with identifier ZZ could not be found.", cause.getMessage());
        verify(countries, times(1)).lookup(eq("ZZ"));
    }

    @Test
    @DisplayName("JobBuilder::lookup with valid code returns Country")
    public void job_builder_lookup_country_with_valid_code_returns_country() {
        final var country = mock(Country.class);
        when(countries.lookup(anyString())).thenReturn(country);
        final var result = assertDoesNotThrow(() -> builder.lookup("ZZ"));
        assertSame(country, result);
        verify(countries, times(1)).lookup(eq("ZZ"));
    }

    @Test
    @DisplayName("JobBuilder::encode ParameterisedTask and non-null parameters encodes parameters as JSON")
    public void job_builder_encode_parameterised_task_encodes_parameters_as_json() {
        final var expected = "{\"life\":42}";
        final var parameters = Map.of("life", 42);
        final var result = assertDoesNotThrow(() -> builder.encode(ParameterisedTask.class, parameters));
        assertEquals(expected, result);
        verify(builder, times(1)).encodeAsJSON(eq(parameters));
    }

    @Test
    @DisplayName("JobBuilder::encode ParameterisedTask and null parameters throws IllegalArgumentException")
    public void job_builder_encode_parameterised_task_throws_illegal_argument_exception_if_parameters_are_null() {
        final var cause = assertThrows(IllegalArgumentException.class, () -> builder.encode(ParameterisedTask.class, null));
        assertEquals("ParameterisedTask parameters must not be null", cause.getMessage());
    }

    @Test
    @DisplayName("JobBuilder::encode ParameterlessTask and non-null parameters throws IllegalArgumentException")
    public void job_builder_encode_parameterless_task_throws_illegal_argument_exception_if_parameters_are_not_null() {
        final var parameters = Map.of("life", 42);
        final var cause = assertThrows(IllegalArgumentException.class, () -> builder.encode(ParameterlessTask.class, parameters));
        assertEquals("ParameterlessTask parameters must be null", cause.getMessage());
    }

    @Test
    @DisplayName("JobBuilder::encode ParameterlessTask and null parameters encodes parameters as null")
    public void job_builder_encode_parameterless_task_encodes_parameters_as_null() {
        final var result = assertDoesNotThrow(() -> builder.encode(ParameterlessTask.class, null));
        assertNull(result);
    }

    @ParameterizedTest
    @ValueSource(booleans = {true, false})
    @DisplayName("JobBuilder::encode Task throws IllegalStateException")
    public void job_builder_encode_task_throws_illegal_state_exception(final boolean nullParameters) {
        final var parameters = nullParameters ? null : Map.of("life", 42);
        final var cause = assertThrows(IllegalStateException.class, () -> builder.encode(Task.class, parameters));
        assertEquals("Unhandled Task type: Task", cause.getMessage());
    }

    @Test
    @DisplayName("JobBuilder::encodeAsJSON encodes parameters as JSON")
    public void job_builder_encode_as_json_encodes_parameters_as_json() {
        final var expected = "{\"life\":42}";
        final var parameters = Map.of("life", 42);
        final var encoded = assertDoesNotThrow(() -> builder.encodeAsJSON(parameters));
        assertEquals(expected, encoded);
    }

    @ParameterizedTest
    @MethodSource("title")
    @DisplayName("JobBuilder::title prepends task class name and concatenates all components delimiting them with colons")
    public void job_builder_title_prepend_class_name_and_concatenates_all_components_delimiting_them_with_colons(
        @Nonnull final String expected,
        @Nonnull final Class<?> type,
        @Nonnull final Object[] components
    ) {
        final var title = JobBuilder.title(type, components);
        assertEquals(expected, title);
    }

    @Nonnull
    private static Stream<Arguments> title() {
        return Stream.of(
            Arguments.of("Object", Object.class, new Object[0]),
            Arguments.of("Object:123", Object.class, new Object[]{123}),
            Arguments.of("Object:123:abc", Object.class, new Object[]{123, "abc"}),
            Arguments.of("Object:123:abc:null", Object.class, new Object[]{123, "abc", null})
        );
    }

    @Getter
    @Setter
    private static class InMemoryJobDataAccessObject extends JobDataAccessObject {

        private UUID identifier;
        private String job;
        private String period;
        private JobState state;
        private String type;
        private Boolean longRunning;
        private String parameters;
        private CountryDataAccessObject country;
        private UUID correlation;
        private Map<String, ?> tags;
        private ZonedDateTime expiration;
        private ZonedDateTime timestamp;

        @Nonnull
        @Override
        public JobDataAccessObject identifier(@Nonnull UUID identifier) {
            setIdentifier(identifier);
            return this;
        }

        @Nonnull
        @Override
        public JobDataAccessObject job(@Nonnull String job) {
            setJob(job);
            return this;
        }

        @Nonnull
        @Override
        public JobDataAccessObject period(@Nonnull String period) {
            setPeriod(period);
            return this;
        }

        @Nonnull
        @Override
        public JobDataAccessObject state(@Nonnull JobState state) {
            setState(state);
            return this;
        }

        @Nonnull
        @Override
        public JobDataAccessObject type(@Nonnull String type) {
            setType(type);
            return this;
        }

        @Nonnull
        @Override
        public JobDataAccessObject longRunning(@Nonnull Boolean longRunning) {
            setLongRunning(longRunning);
            return this;
        }

        @Nonnull
        @Override
        public JobDataAccessObject parameters(@Nullable String parameters) {
            setParameters(parameters);
            return this;
        }

        @Nonnull
        @Override
        public JobDataAccessObject country(@Nullable CountryDataAccessObject country) {
            setCountry(country);
            return this;
        }

        @Nonnull
        @Override
        public JobDataAccessObject correlation(@Nullable UUID correlation) {
            setCorrelation(correlation);
            return this;
        }

        @Nonnull
        @Override
        public JobDataAccessObject tags(@Nullable Map<String, ?> tags) {
            setTags(tags);
            return this;
        }

        @Nonnull
        @Override
        public JobDataAccessObject expiration(@Nullable ZonedDateTime expiration) {
            setExpiration(expiration);
            return this;
        }

        @Nonnull
        @Override
        public JobDataAccessObject timestamp(@Nonnull ZonedDateTime timestamp) {
            setTimestamp(timestamp);
            return this;
        }

        @Nonnull
        @Override
        public Boolean isLongRunning() {
            return longRunning;
        }
    }
}
