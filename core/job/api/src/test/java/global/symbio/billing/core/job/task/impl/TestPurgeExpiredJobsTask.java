package global.symbio.billing.core.job.task.impl;

import global.symbio.billing.core.job.persistence.api.job.Job;
import global.symbio.billing.core.job.persistence.api.job.JobDataAccessObject;
import global.symbio.billing.core.job.persistence.api.job.state.*;
import global.symbio.billing.core.job.persistence.api.repository.JobRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Duration;
import java.time.ZonedDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestPurgeExpiredJobsTask {

    @Mock
    private Job definition;

    @Mock
    private JobRepository<JobDataAccessObject> jobs;

    private PurgeExpiredJobsTask task;

    @BeforeEach
    public void setup() {
        task = new PurgeExpiredJobsTask(definition, jobs);
    }

    @Test
    @DisplayName("PurgeExpiredJobsTask::new rejects null constructor arguments")
    public void purge_expired_jobs_task_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new PurgeExpiredJobsTask(null, jobs));
        assertThrows(NullPointerException.class, () -> new PurgeExpiredJobsTask(definition, null));
    }

    @Test
    @DisplayName("PurgeExpiredJobsTask::execute purges expired jobs from the database")
    public void purge_expired_jobs_task_execute_purges_expired_jobs_from_database() {
        when(definition.getState()).thenReturn(Pending.getInstance());
        final var state = assertDoesNotThrow(() -> task.execute());
        assertInstanceOf(Success.class, state);
        verify(jobs, times(1)).purge(any(ZonedDateTime.class));
    }

    @ParameterizedTest
    @ValueSource(classes = {Success.class, Failure.class, Pending.class, Skipped.class})
    @DisplayName("PurgeExpiredJobsTask::retention is dynamic depending on the output of the job")
    public void purge_expired_jobs_task_retention_is_dynamic_depending_on_execution_result(final Class<? extends JobState> type) {
        final var state = mock(type);
        final var retention = assertDoesNotThrow(() -> task.retention(state));
        if (type == Success.class) {
            assertSame(Duration.ZERO, retention);
        } else if (type == Failure.class) {
            assertEquals(Duration.ofDays(7L), retention);
        } else if (type == Pending.class) {
            assertNull(retention);
        } else if (type == Skipped.class) {
            assertSame(Duration.ZERO, retention);
        }
        verifyNoInteractions(state);
    }
}