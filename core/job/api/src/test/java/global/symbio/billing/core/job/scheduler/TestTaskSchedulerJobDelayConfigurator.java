package global.symbio.billing.core.job.scheduler;

import global.symbio.billing.core.job.api.service.JobDelayService;
import global.symbio.billing.core.job.persistence.api.job.delay.JobDelayDataAccessObject;
import global.symbio.billing.core.job.persistence.api.repository.JobDelayRepository;
import global.symbio.billing.core.job.scheduler.impl.JobDelayTaskScheduler;
import io.micronaut.context.event.BeanCreatedEvent;
import io.micronaut.inject.BeanDefinition;
import io.micronaut.inject.BeanIdentifier;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class TestTaskSchedulerJobDelayConfigurator {

    @Mock
    private JobDelayService service;

    @Mock
    private JobDelayRepository<JobDelayDataAccessObject> repository;

    private TaskSchedulerJobDelayConfigurator configurator;

    @BeforeEach
    public void setup() {
        configurator = new TaskSchedulerJobDelayConfigurator(service, repository);
    }

    @Test
    @DisplayName("TaskSchedulerJobDelayConfigurator::new rejects null constructor arguments")
    public void task_scheduler_job_delay_configurator_new_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new TaskSchedulerJobDelayConfigurator(null, repository));
        assertThrows(NullPointerException.class, () -> new TaskSchedulerJobDelayConfigurator(service, null));
    }

    @Test
    @DisplayName("TaskSchedulerJobDelayConfigurator::mixin returns a JobDelayTaskScheduler")
    public void task_scheduler_job_delay_configurator_mixin() {
        final var bean = mock(TaskScheduler.class);
        final var result = configurator.mixin(bean, "name");
        assertInstanceOf(JobDelayTaskScheduler.class, result);
    }

    @Test
    @DisplayName("TaskSchedulerJobDelayConfigurator::name returns bean identifier name")
    public void task_scheduler_job_delay_configurator_name_returns_bean_identifier_name() {
        final var identifier = mock(BeanIdentifier.class);
        final var bean = mock(TaskScheduler.class);
        final var definition = mock(BeanDefinition.class);
        when(identifier.getName()).thenReturn("name");
        final var result = configurator.getName(bean, definition, identifier);
        assertEquals("name", result);
    }

    @Test
    @DisplayName("TaskSchedulerJobDelayConfigurator::componentName returns 'delay'")
    public void task_scheduler_job_delay_configurator_component_name_returns_delay() {
        assertEquals("delay", configurator.component());
    }

    @Test
    @DisplayName("TaskSchedulerJobDelayConfigurator::isEnabled returns true")
    public void task_scheduler_job_delay_configurator_is_enabled_returns_true() {
        assertTrue(configurator.isEnabled());
    }

    @Test
    @DisplayName("TaskSchedulerJobDelayConfigurator::onCreated")
    public void task_scheduler_job_delay_configurator_on_created() {
        final var event = mock(BeanCreatedEvent.class);
        final var identifier = mock(BeanIdentifier.class);
        when(identifier.getName()).thenReturn("name");
        final var bean = mock(TaskScheduler.class);
        when(event.getBeanIdentifier()).thenReturn(identifier);
        when(event.getBean()).thenReturn(bean);

        final var result = configurator.onCreated(event);
        assertInstanceOf(JobDelayTaskScheduler.class, result);
    }

}
