package global.symbio.billing.core.job.api.service.impl;

import com.github.benmanes.caffeine.cache.CacheLoader;
import global.symbio.billing.core.job.persistence.api.job.delay.JobDelay;
import global.symbio.billing.core.job.persistence.api.job.delay.JobDelayDataAccessObject;
import global.symbio.billing.core.job.persistence.api.job.delay.JobDelayKey;
import global.symbio.billing.core.job.persistence.api.repository.JobDelayRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestJobDelayCacheLoader {

    @Mock
    private JobDelayRepository<JobDelayDataAccessObject> repository;

    private CacheLoader<JobDelayKey, Optional<JobDelay>> loader;

    @BeforeEach
    public void setup() {
        loader = new JobDelayCacheLoader(repository);
    }

    @Test
    @DisplayName("JobDelayCacheLoader::new rejects null constructor arguments")
    public void job_delay_cache_loader_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new JobDelayCacheLoader(null));
    }

    @Test
    @DisplayName("JobDelayCacheLoader::load invokes JobDelayRepository lookup by JobDelayKey")
    public void job_delay_cache_loader_load_invokes_job_delay_repository_lookup() {
        final var key = new JobDelayKey("test", null, true);
        final var delay = mock(JobDelayDataAccessObject.class, CALLS_REAL_METHODS);
        when(delay.getIdentifier()).thenReturn(1);
        when(repository.getDelayByTypeCountryActive(key.type(), key.country(), key.active())).thenReturn(Optional.of(delay));
        final var result = assertDoesNotThrow(() -> loader.load(key));
        assertSame(delay, result.get().data());
        verify(repository, times(1)).getDelayByTypeCountryActive(eq(key.type()), eq(key.country()), eq(key.active()));
    }
}