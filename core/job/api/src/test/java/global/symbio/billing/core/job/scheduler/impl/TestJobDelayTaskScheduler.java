package global.symbio.billing.core.job.scheduler.impl;

import com.google.common.collect.Range;
import global.symbio.billing.core.job.api.service.JobDelayService;
import global.symbio.billing.core.job.persistence.api.job.Job;
import global.symbio.billing.core.job.persistence.api.job.JobDataAccessObject;
import global.symbio.billing.core.job.persistence.api.job.delay.JobDelay;
import global.symbio.billing.core.job.persistence.api.job.delay.JobDelayDataAccessObject;
import global.symbio.billing.core.job.persistence.api.job.state.Pending;
import global.symbio.billing.core.job.persistence.api.repository.JobDelayRepository;
import global.symbio.billing.core.job.scheduler.TaskScheduler;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.ZonedDateTime;
import java.time.temporal.Temporal;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestJobDelayTaskScheduler {

    @Mock
    private TaskScheduler scheduler;

    @Mock
    private JobDelayService service;

    @Mock
    private JobDelayRepository<JobDelayDataAccessObject> repository;

    private JobDelayTaskScheduler delay;

    @BeforeEach
    public void setup() {
        delay = new JobDelayTaskScheduler(scheduler, service, repository);
    }

    @Test
    @DisplayName("JobDelayTaskScheduler::new rejects null constructor arguments")
    public void job_delay_task_scheduler_new_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new JobDelayTaskScheduler(null, service, repository));
        assertThrows(NullPointerException.class, () -> new JobDelayTaskScheduler(scheduler, null, repository));
        assertThrows(NullPointerException.class, () -> new JobDelayTaskScheduler(scheduler, service, null));
    }

    @Test
    @DisplayName("JobDelayTaskScheduler::schedule schedules jobs")
    public void job_delay_task_scheduler_schedule_schedules_jobs() {
        final var job = mock(Job.class);
        assertDoesNotThrow(() -> delay.schedule(job));
        verify(scheduler, times(1)).schedule(job);

        assertDoesNotThrow(() -> delay.schedule(List.of(job)));
        verify(scheduler, times(1)).schedule(List.of(job));
    }

    @Test
    @DisplayName("JobDelayTaskScheduler::execute no delay")
    public void job_delay_task_scheduler_execute_no_delay() {
        final var job = mock(Job.class);
        when(service.lookup(any(), any(), anyBoolean())).thenReturn(null);
        assertDoesNotThrow(() -> delay.execute(job));
        verify(service, times(1)).lookup(any(), any(), anyBoolean());
        verify(scheduler, times(1)).execute(any(Job.class));
    }

    @ParameterizedTest(name = "{index} JobDelayTaskScheduler::execute start={0}, expiration={1}")
    @MethodSource("scenarios")
    @DisplayName("JobDelayTaskScheduler::execute executes jobs")
    public void job_delay_task_scheduler_execute_executes_jobs(final ZonedDateTime start, final ZonedDateTime expiration, final boolean updateActive) {
        final var job = mock(Job.class);
        final var jobDelay = mock(JobDelay.class);
        when(service.lookup(any(), any(), anyBoolean())).thenReturn(jobDelay);

        final Range<ZonedDateTime> range;
        if (start == null && expiration == null) {
            range = Range.all();
        } else if (start == null) {
            range = Range.atMost(expiration);
        } else if (expiration == null) {
            range = Range.atLeast(start);
        } else {
            range = Range.closed(start, expiration);
        }

        when(jobDelay.range()).thenReturn(range);

        final var jobDao = mock(JobDataAccessObject.class);
        lenient().when(job.data()).thenReturn(jobDao);
        lenient().when(jobDao.state(any())).thenReturn(jobDao);
        lenient().when(jobDao.period(any(Temporal.class))).thenReturn(jobDao);
        lenient().when(jobDao.entity()).thenReturn(job);

        final var state = mock(Pending.class, RETURNS_DEEP_STUBS);
        lenient().when(job.getState()).thenReturn(state);

        assertDoesNotThrow(() -> delay.execute(job));
        verify(service, times(1)).lookup(any(), any(), anyBoolean());
        verify(scheduler, times(1)).execute(any(Job.class));
        verify(service, times(updateActive ? 1 : 0)).update(any(JobDelay.class), any(Function.class));
    }

    private static Stream<Arguments> scenarios() {
        return Stream.of(
            Arguments.of(null, null, false), //skip job; started, no expiration
            Arguments.of(ZonedDateTime.now().plusDays(1), null, false), //do not skip job; not yet started, no expiration
            Arguments.of(ZonedDateTime.now().minusDays(1), null, false), //skip job; already started
            Arguments.of(null, ZonedDateTime.now().plusDays(1), false), //skip job; started, with expiration
            Arguments.of(null, ZonedDateTime.now().minusDays(1), true), //do not skip job; started, expired already, inactivate flag
            Arguments.of(ZonedDateTime.now().plusDays(1), ZonedDateTime.now().plusDays(5), false), //do not skip job; not yet started, with expiration
            Arguments.of(ZonedDateTime.now().minusDays(1), ZonedDateTime.now().plusDays(5), false), //skip job; started, with expiration
            Arguments.of(ZonedDateTime.now().minusDays(4), ZonedDateTime.now().minusDays(1), true) //do not skip job; started, expired already, inactivate flag

        );
    }
}
