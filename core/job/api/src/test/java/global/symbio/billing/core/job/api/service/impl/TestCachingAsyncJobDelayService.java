package global.symbio.billing.core.job.api.service.impl;

import com.github.benmanes.caffeine.cache.AsyncLoadingCache;
import com.github.benmanes.caffeine.cache.LoadingCache;
import global.symbio.billing.core.job.api.service.AsyncJobDelayService;
import global.symbio.billing.core.job.persistence.api.job.delay.JobDelay;
import global.symbio.billing.core.job.persistence.api.job.delay.JobDelayDataAccessObject;
import global.symbio.billing.core.job.persistence.api.job.delay.JobDelayKey;
import global.symbio.billing.core.job.persistence.api.repository.JobDelayRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.function.Function;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class TestCachingAsyncJobDelayService {

    @Mock
    private AsyncLoadingCache<JobDelayKey, Optional<JobDelay>> delays;

    @Mock
    private JobDelayRepository<JobDelayDataAccessObject> repository;

    private AsyncJobDelayService service;

    @BeforeEach
    public void setup() {
        service = new CachingAsyncJobDelayService(delays, repository);
    }

    @Test
    @DisplayName("CachingAsyncJobDelayService::new rejects null constructor arguments")
    public void caching_async_job_delay_service_new_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new CachingAsyncJobDelayService(null, repository));
        assertThrows(NullPointerException.class, () -> new CachingAsyncJobDelayService(delays, null));
    }

    @Test
    @DisplayName("CachingAsyncJobDelayService::lookup returns delay from cache when key is in the cache")
    public void caching_async_job_delay_service_lookup_returns_delay_from_cache_when_key_in_the_cache() {
        final var delay = mock(JobDelay.class);
        final var key = new JobDelayKey("test", null, true);
        when(delays.get(any(JobDelayKey.class))).thenReturn(CompletableFuture.completedFuture(Optional.of(delay)));
        final var future = assertDoesNotThrow(() -> service.lookup("test", null, true));
        final var result = assertDoesNotThrow(future::join);
        assertSame(delay, result);
        verify(delays, times(1)).get(eq(key));
    }

    @Test
    @DisplayName("CachingAsyncJobDelayService::lookup returns null when delay not in the cache")
    public void caching_async_job_delay_service_lookup_returns_null_when_delay_not_in_cache() {
        final var key = new JobDelayKey("test", null, true);
        when(delays.get(any(JobDelayKey.class))).thenReturn(CompletableFuture.completedFuture(Optional.empty()));
        final var future = assertDoesNotThrow(() -> service.lookup("test", null, true));
        final var result = assertDoesNotThrow(future::join);
        assertNull(result);
        verify(delays, times(1)).get(eq(key));
    }

    @Test
    @DisplayName("CachingAsyncJobDelayService::lookup throws exception when cache hit throws exception")
    public void caching_async_job_delay_service_lookup_throws_exception_when_cache_hit_throws_exception() {
        final var key = new JobDelayKey("test", null, true);
        final var failure = new RuntimeException("Underlying exception");
        when(delays.get(any(JobDelayKey.class))).thenReturn(CompletableFuture.failedFuture(failure));
        final var future = assertDoesNotThrow(() -> service.lookup("test", null, true));
        final var cause = assertThrows(CompletionException.class, future::join);
        assertSame(failure, cause.getCause());
        verify(delays, times(1)).get(eq(key));
    }

    @Test
    @DisplayName("CachingAsyncJobDelayService::update calls repository and returns delay")
    public void caching_async_job_delay_service_update_calls_repository_and_returns_delay() {
        final var delay = mock(JobDelayDataAccessObject.class);
        when(repository.update(any(JobDelayDataAccessObject.class))).thenReturn(delay);
        final var sync = mock(LoadingCache.class);
        when(delays.synchronous()).thenReturn(sync);
        final var entity = mock(JobDelay.class);
        when(delay.entity()).thenReturn(entity);
        final var function = mock(Function.class);
        when(function.apply(any())).thenReturn(delay);
        final var result = assertDoesNotThrow(() -> service.update(entity, function));
        assertSame(entity, result);
        verify(repository, times(1)).update(delay);
    }
}
