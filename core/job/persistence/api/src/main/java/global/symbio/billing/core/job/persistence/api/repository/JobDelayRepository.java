package global.symbio.billing.core.job.persistence.api.repository;

import global.symbio.billing.core.job.persistence.api.job.delay.JobDelayDataAccessObject;
import io.micronaut.data.repository.CrudRepository;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.util.Optional;

public interface JobDelayRepository<T extends JobDelayDataAccessObject> extends CrudRepository<T, Integer> {

    @Nonnull
    Optional<T> getDelayByTypeCountryActive(@Nonnull String type, @Nullable Integer country, boolean active);
}
