package global.symbio.billing.core.job.persistence.api.repository;

import global.symbio.billing.core.job.persistence.api.job.JobDataAccessObject;
import global.symbio.billing.core.job.persistence.api.job.state.Failure;
import global.symbio.billing.core.job.persistence.api.job.state.Pending;
import io.micronaut.data.repository.CrudRepository;
import jakarta.annotation.Nonnull;

import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.Set;
import java.util.UUID;

public interface JobRepository<T extends JobDataAccessObject> extends CrudRepository<T, UUID> {

    @Nonnull
    Collection<T> findExecutableJobs(@Nonnull ZonedDateTime timestamp, @Nonnull String pending, @Nonnull String failure, @Nonnull Set<UUID> identifiers, int limit);

    long purge(@Nonnull ZonedDateTime timestamp);

    @Nonnull
    default Collection<T> getExecutableJobs(@Nonnull Set<UUID> exclusions, int limit) {
        return findExecutableJobs(ZonedDateTime.now(), Pending.TYPE, Failure.TYPE, exclusions, limit);
    }
}
