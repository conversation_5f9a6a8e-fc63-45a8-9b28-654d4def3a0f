package global.symbio.billing.core.job.persistence.api.job;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.country.persistence.api.Country;
import global.symbio.billing.core.country.persistence.api.CountryDataAccessObject;
import global.symbio.billing.core.job.persistence.api.job.state.JobState;
import global.symbio.billing.core.persistence.api.DataAccessObject;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;
import java.time.temporal.Temporal;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

public abstract class JobDataAccessObject implements DataAccessObject<UUID, JobDataAccessObject> {

    @Nonnull
    public abstract String getJob();

    @Nonnull
    public abstract JobDataAccessObject job(@Nonnull String job);

    @Nonnull
    public abstract String getPeriod();

    @Nonnull
    public abstract JobDataAccessObject period(@Nonnull String period);

    @Nonnull
    public JobDataAccessObject period(@Nonnull Temporal period) {
        return period(period.toString());
    }

    @Nonnull
    public abstract JobState getState();

    @Nonnull
    public abstract JobDataAccessObject state(@Nonnull JobState state);

    @Nonnull
    public abstract String getType();

    @Nonnull
    public abstract JobDataAccessObject type(@Nonnull String type);

    @Nonnull
    public JobDataAccessObject type(@Nonnull Class<?> type) {
        return type(type.getName());
    }

    @Nonnull
    public abstract Boolean isLongRunning();

    @Nonnull
    public abstract JobDataAccessObject longRunning(@Nonnull Boolean longRunning);

    @Nullable
    public abstract String getParameters();

    @Nonnull
    public abstract JobDataAccessObject parameters(@Nullable String parameters);

    @Nullable
    public abstract CountryDataAccessObject getCountry();

    @Nonnull
    public abstract JobDataAccessObject country(@Nullable CountryDataAccessObject country);

    @Nonnull
    public JobDataAccessObject country(@Nullable Country country) {
        return country(country == null ? null : country.data());
    }

    @Nullable
    public abstract UUID getCorrelation();

    @Nonnull
    public abstract JobDataAccessObject correlation(@Nullable UUID correlation);

    @Nullable
    public abstract Map<String, ?> getTags();

    @Nonnull
    public abstract JobDataAccessObject tags(@Nullable Map<String, ?> tags);


    @Nullable
    public abstract ZonedDateTime getExpiration();

    @Nonnull
    public abstract JobDataAccessObject expiration(@Nullable ZonedDateTime expiration);

    @Nonnull
    public abstract ZonedDateTime getTimestamp();

    @Nonnull
    public abstract JobDataAccessObject timestamp(@Nonnull ZonedDateTime timestamp);

    @Override
    @Nonnull
    public Job entity() {
        return new Job(this);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof JobDataAccessObject job)) return false;
        return Objects.equals(getIdentifier(), job.getIdentifier())
            && Objects.equals(getJob(), job.getJob())
            && Objects.equals(getPeriod(), job.getPeriod())
            && Objects.equals(getState(), job.getState())
            && Objects.equals(getType(), job.getType())
            && Objects.equals(isLongRunning(), job.isLongRunning())
            && Objects.equals(getParameters(), job.getParameters())
            && Objects.equals(getCountry(), job.getCountry())
            && Objects.equals(getCorrelation(), job.getCorrelation())
            && Objects.equals(getTags(), job.getTags())
            && Objects.equals(getExpiration(), job.getExpiration())
            && Objects.equals(getTimestamp(), job.getTimestamp());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getIdentifier(), getJob(), getPeriod(), getState(), getType(), isLongRunning(), getParameters(), getCountry(), getCorrelation(), getTags(), getExpiration(), getTimestamp());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(getClass())
            .add("identifier", getIdentifier())
            .add("job", getJob())
            .add("period", getPeriod())
            .add("state", getState())
            .add("type", getType())
            .add("longRunning", isLongRunning())
            .add("parameters", getParameters())
            .add("country", getCountry())
            .add("correlation", getCorrelation())
            .add("tags", getTags())
            .add("expiration", getExpiration())
            .add("timestamp", getTimestamp())
            .toString();
    }
}
