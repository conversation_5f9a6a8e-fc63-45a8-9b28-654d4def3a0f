package global.symbio.billing.core.job.persistence.api.job;

import global.symbio.billing.core.country.persistence.api.Country;
import global.symbio.billing.core.exception.sdk.BillingEntityNotFoundException;
import global.symbio.billing.core.job.persistence.api.job.state.JobState;
import global.symbio.billing.core.persistence.api.Entity;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Function;

public class Job extends Entity<UUID, JobDataAccessObject> {

    public Job(@Nonnull JobDataAccessObject data) {
        super(data);
    }

    @Nonnull
    public String getJob() {
        return data().getJob();
    }

    @Nonnull
    public String getPeriod() {
        return data().getPeriod();
    }

    @Nonnull
    public JobState getState() {
        return data().getState();
    }

    @Nonnull
    public String getType() {
        return data().getType();
    }

    @Nonnull
    public Boolean isLongRunning() {
        return data().isLongRunning();
    }

    @Nullable
    public String getParameters() {
        return data().getParameters();
    }

    @Nullable
    public Country getCountry() {
        return unwrap(data().getCountry());
    }

    @Nullable
    public UUID getCorrelation() {
        return data().getCorrelation();
    }

    @Nullable
    public Map<String, ?> getTags() {
        return data().getTags();
    }

    @Nullable
    public ZonedDateTime getExpiration() {
        return data().getExpiration();
    }

    @Nonnull
    public ZonedDateTime getTimestamp() {
        return data().getTimestamp();
    }

    @Nonnull
    public <T> T withCountry(@Nonnull Function<Country, T> operation) {
        return Optional.ofNullable(getCountry()).map(operation).orElseThrow(() -> new BillingEntityNotFoundException(Country.class, getIdentifier()));
    }
}
