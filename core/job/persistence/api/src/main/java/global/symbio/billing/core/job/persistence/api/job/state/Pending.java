package global.symbio.billing.core.job.persistence.api.job.state;

import com.google.common.base.MoreObjects;
import jakarta.annotation.Nonnull;

import java.util.Objects;

public final class Pending extends JobState {

    @Nonnull
    private static final Pending INSTANCE = new Pending();

    public static final String TYPE = "PENDING";

    public Pending() {
        super(TYPE);
    }

    @Override
    public Pending pending() {
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Pending)) return false;
        return super.equals(o);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("type", type)
                .toString();
    }

    @Nonnull
    public static Pending getInstance() {
        return INSTANCE;
    }
}
