package global.symbio.billing.core.job.persistence.api.job.state;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.MoreObjects;
import jakarta.annotation.Nonnull;
import lombok.Getter;

import java.time.ZonedDateTime;
import java.util.Objects;

@Getter
public final class Skipped extends JobState {

    @Nonnull
    public static final String TYPE = "SKIPPED";

    @Nonnull
    private final String reason;

    @Nonnull
    private final ZonedDateTime timestamp;

    @JsonCreator(mode = JsonCreator.Mode.PROPERTIES)
    public Skipped(@Nonnull @JsonProperty("reason") String reason, @Nonnull @JsonProperty("timestamp") ZonedDateTime timestamp) {
        super(TYPE);
        this.reason = Objects.requireNonNull(reason, "reason");
        this.timestamp = Objects.requireNonNull(timestamp, "timestamp");
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Skipped state)) return false;
        if (!super.equals(o)) return false;
        return Objects.equals(getReason(), state.getReason()) && Objects.equals(getTimestamp(), state.getTimestamp());
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), getReason(), getTimestamp());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("type", type)
                .add("reason", reason)
                .add("timestamp", timestamp)
                .toString();
    }
}