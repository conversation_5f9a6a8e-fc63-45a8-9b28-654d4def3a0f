package global.symbio.billing.core.job.persistence.api.job.state;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.google.common.base.MoreObjects;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.Getter;

import java.time.Duration;
import java.time.ZonedDateTime;
import java.util.Objects;

@Getter
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "type")
@JsonSubTypes(
    value = {
        @JsonSubTypes.Type(value = Pending.class, name = Pending.TYPE),
        @JsonSubTypes.Type(value = Success.class, name = Success.TYPE),
        @JsonSubTypes.Type(value = Failure.class, name = Failure.TYPE),
        @JsonSubTypes.Type(value = Skipped.class, name = Skipped.TYPE)
    }
)
public abstract sealed class JobState permits Pending, Success, Failure, Skipped {

    @Nonnull
    protected final String type;

    public JobState(@Nonnull String type) {
        this.type = Objects.requireNonNull(type, "type");
    }

    @Nonnull
    public Pending pending() {
        return Pending.getInstance();
    }

    @Nonnull
    public Success success() {
        return success(ZonedDateTime.now());
    }

    @Nonnull
    public Success success(@Nonnull ZonedDateTime timestamp) {
        return new Success(timestamp);
    }

    @Nonnull
    public Failure failure(@Nonnull Throwable cause, @Nullable Duration retry) {
        return failure(cause, ZonedDateTime.now(), retry);
    }

    @Nonnull
    public Failure failure(@Nonnull Throwable cause, @Nonnull ZonedDateTime timestamp, @Nullable Duration retry) {
        return failure(cause.getMessage(), timestamp, retry);
    }

    @Nonnull
    public Failure failure(@Nullable String reason, @Nullable Duration retry) {
        return failure(reason, ZonedDateTime.now(), retry);
    }

    @Nonnull
    public Failure failure(@Nullable String reason, @Nonnull ZonedDateTime timestamp, @Nullable Duration retry) {
        return new Failure(reason, 1, timestamp, add(timestamp, retry));
    }

    @Nonnull
    public Skipped skipped(@Nonnull String reason) {
        return skipped(reason, ZonedDateTime.now());
    }

    @Nonnull
    public Skipped skipped(@Nonnull String reason, @Nonnull ZonedDateTime timestamp) {
        return new Skipped(reason, timestamp);
    }

    @Nonnull
    public Failure limit(int limit, @Nonnull Throwable cause, @Nullable Duration retry) {
        final var failure = failure(cause, retry);
        if (isRetryStateValid(failure, limit)) {
            return failure;
        }
        return failure(cause, null);
    }

    @Nonnull
    public Failure limit(int limit, @Nonnull Throwable cause, @Nonnull ZonedDateTime timestamp, @Nullable Duration retry) {
        final var failure = failure(cause, timestamp, retry);
        if (isRetryStateValid(failure, limit)) {
            return failure;
        }
        return failure(cause, timestamp, null);
    }

    @Nonnull
    public Failure limit(int limit, @Nullable String reason, @Nullable Duration retry) {
        final var failure = failure(reason, retry);
        if (isRetryStateValid(failure, limit)) {
            return failure;
        }
        return failure(reason, null);
    }

    @Nonnull
    public Failure limit(int limit, @Nullable String reason, @Nonnull ZonedDateTime timestamp, @Nullable Duration retry) {
        final var failure = failure(reason, timestamp, retry);
        if (isRetryStateValid(failure, limit)) {
            return failure;
        }
        return failure(reason, timestamp, null);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof JobState state)) return false;
        return getType().equals(state.getType());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getType());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this).add("type", type).toString();
    }

    private static boolean isRetryStateValid(@Nonnull Failure failure, int limit) {
        return failure.getRetry() == null || limit >= failure.getAttempts();
    }

    @Nullable
    protected static ZonedDateTime add(@Nonnull ZonedDateTime timestamp, @Nullable Duration duration) {
        if (duration == null) {
            return null;
        }
        return timestamp.plus(duration);
    }
}
