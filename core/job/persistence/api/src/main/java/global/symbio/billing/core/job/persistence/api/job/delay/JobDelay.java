package global.symbio.billing.core.job.persistence.api.job.delay;

import com.google.common.collect.Range;
import global.symbio.billing.core.country.persistence.api.Country;
import global.symbio.billing.core.persistence.api.Entity;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;

public class JobDelay extends Entity<Integer, JobDelayDataAccessObject> {

    public JobDelay(@Nonnull JobDelayDataAccessObject data) {
        super(data);
    }

    @Nonnull
    public String getType() {
        return data().getType();
    }

    @Nullable
    public Country getCountry() {
        return unwrap(data().getCountry());
    }

    @Nullable
    public ZonedDateTime getStart() {
        return data().getStart();
    }

    @Nullable
    public ZonedDateTime getExpiration() {
        return data().getExpiration();
    }

    public boolean isActive() {
        return data().isActive();
    }

    @Nonnull
    public String getReason() {
        return data().getReason();
    }

    @Nonnull
    public ZonedDateTime getTimestamp() {
        return data().getTimestamp();
    }

    @Nonnull
    public Range<ZonedDateTime> range() {
        final var start = getStart();
        final var expiration = getExpiration();
        if (start == null && expiration == null) {
            // The delay has neither a start nor finish: (-∞..+∞)
            return Range.all();
        } else if (start == null) {
            // The delay has an expiration but no start: (-∞..expiration]
            return Range.atMost(expiration);
        } else if (expiration == null) {
            // The delay has a start but no expiration:  [start..+∞)
            return Range.atLeast(start);
        } else {
            // The delay has both a start and finish:    [start..expiration]
            return Range.closed(start, expiration);
        }
    }

    public boolean includes(@Nonnull ZonedDateTime timestamp) {
        return range().contains(timestamp);
    }

    @Nonnull
    public JobDelayKey key() {
        return new JobDelayKey(getType(), getCountry() != null ? getCountry().getIdentifier() : null, isActive());
    }
}