package global.symbio.billing.core.job.persistence.api.job.delay;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.country.persistence.api.Country;
import global.symbio.billing.core.country.persistence.api.CountryDataAccessObject;
import global.symbio.billing.core.persistence.api.DataAccessObject;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;
import java.util.Objects;

public abstract class JobDelayDataAccessObject implements DataAccessObject<Integer, JobDelayDataAccessObject> {

    @Nonnull
    public abstract String getType();

    @Nonnull
    public abstract JobDelayDataAccessObject type(@Nonnull String type);

    @Nonnull
    public JobDelayDataAccessObject type(@Nonnull Class<?> type) {
        return type(type.getName());
    }

    @Nullable
    public abstract CountryDataAccessObject getCountry();

    @Nonnull
    public abstract JobDelayDataAccessObject country(@Nullable CountryDataAccessObject country);

    @Nonnull
    public JobDelayDataAccessObject country(@Nullable Country country) {
        return country(country == null ? null : country.data());
    }

    @Nullable
    public abstract ZonedDateTime getStart();

    @Nonnull
    public abstract JobDelayDataAccessObject start(@Nullable ZonedDateTime start);

    @Nullable
    public abstract ZonedDateTime getExpiration();

    @Nonnull
    public abstract JobDelayDataAccessObject expiration(@Nullable ZonedDateTime expiration);

    @Nonnull
    public abstract Boolean isActive();

    @Nonnull
    public abstract JobDelayDataAccessObject active(@Nonnull Boolean active);

    @Nonnull
    public abstract String getReason();

    @Nonnull
    public abstract JobDelayDataAccessObject reason(@Nonnull String reason);

    @Nonnull
    public abstract ZonedDateTime getTimestamp();

    @Nonnull
    public abstract JobDelayDataAccessObject timestamp(@Nonnull ZonedDateTime timestamp);

    @Override
    @Nonnull
    public JobDelay entity() {
        return new JobDelay(this);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof JobDelayDataAccessObject delay)) return false;
        return Objects.equals(getIdentifier(), delay.getIdentifier())
            && Objects.equals(getType(), delay.getType())
            && Objects.equals(getCountry(), delay.getCountry())
            && Objects.equals(getStart(), delay.getStart())
            && Objects.equals(getExpiration(), delay.getExpiration())
            && Objects.equals(isActive(), delay.isActive())
            && Objects.equals(getReason(), delay.getReason())
            && Objects.equals(getTimestamp(), delay.getTimestamp());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getIdentifier(), getType(), getCountry(), getStart(), getExpiration(), isActive(), getReason(), getTimestamp());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(getClass())
            .add("identifier", getIdentifier())
            .add("type", getType())
            .add("country", getCountry())
            .add("start", getStart())
            .add("expiration", getExpiration())
            .add("active", isActive())
            .add("reason", getReason())
            .add("timestamp", getTimestamp())
            .toString();
    }
}