package global.symbio.billing.core.job.persistence.api.job.state;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.MoreObjects;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.Getter;

import java.time.Duration;
import java.time.ZonedDateTime;
import java.util.Objects;

@Getter
public final class Failure extends JobState {

    @Nonnull
    public static final String TYPE = "FAILURE";

    @Nullable
    private final String reason;
    private final int attempts;

    @Nonnull
    private final ZonedDateTime timestamp;

    @Nullable
    private final ZonedDateTime retry;

    @JsonCreator(mode = JsonCreator.Mode.PROPERTIES)
    public Failure(@Nullable @JsonProperty("reason") String reason, @JsonProperty("attempts") int attempts, @Nonnull @JsonProperty("timestamp") ZonedDateTime timestamp, @Nullable @JsonProperty("retry") ZonedDateTime retry) {
        super(TYPE);
        this.reason = reason;
        this.attempts = attempts;
        this.timestamp = Objects.requireNonNull(timestamp, "timestamp");
        this.retry = retry;
    }

    @Nonnull
    @Override
    public Failure failure(@Nullable String reason, @Nonnull ZonedDateTime timestamp, @Nullable Duration retry) {
        return new Failure(reason, attempts + 1, timestamp, add(timestamp, retry));
    }

    @Nonnull
    public Failure onLimitExceeded(@Nonnull Runnable task) {
        if (retry == null) {
            task.run();
        }
        return this;
    }

    @Nonnull
    public Failure onLimitNotExceeded(@Nonnull Runnable task) {
        if (retry != null) {
            task.run();
        }
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Failure state)) return false;
        if (!super.equals(o)) return false;
        return getAttempts() == state.getAttempts() && Objects.equals(getReason(), state.getReason()) && Objects.equals(getTimestamp(), state.getTimestamp()) && Objects.equals(getRetry(), state.getRetry());
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), getAttempts(), getReason(), getTimestamp(), getRetry());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("type", type)
                .add("reason", reason)
                .add("attempts", attempts)
                .add("timestamp", timestamp)
                .add("retry", retry)
                .toString();
    }
}
