package global.symbio.billing.core.job.persistence.state;

import com.fasterxml.jackson.core.JsonProcessingException;
import global.symbio.billing.core.job.persistence.api.job.state.Failure;
import global.symbio.billing.core.job.persistence.api.job.state.JobState;
import global.symbio.billing.core.job.persistence.api.job.state.Pending;
import global.symbio.billing.core.persistence.api.DeterministicObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;

import java.time.Duration;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;

import static org.junit.jupiter.api.Assertions.*;

public class TestFailure {

    private static final ZonedDateTime EPOCH = ZonedDateTime.ofInstant(Instant.EPOCH, ZoneId.systemDefault());
    private static final Duration RETRY = Duration.ofSeconds(1);

    private Failure state;

    @BeforeEach
    public void setup() {
        state = new Failure(null, 0, EPOCH, EPOCH.plus(RETRY));
    }

    @Test
    @DisplayName("Failure::new rejects null timestamp")
    public void failure_rejects_null_timestamp() {
        assertThrows(NullPointerException.class, () -> new Failure(null, 0, null, null));
        assertDoesNotThrow(() -> new Failure(null, 0, EPOCH, null));
    }

    @Test
    @DisplayName("Failure is subclass of State")
    public void failure_is_subtype_of_state() {
        assertInstanceOf(Failure.class, state);
        assertInstanceOf(JobState.class, state);
    }

    @Test
    @DisplayName("Failure#TYPE equals FAILURE and Failure#type equals Failure#TYPE")
    public void failure_type_equals_success() {
        assertEquals("FAILURE", Failure.TYPE);
        assertEquals(Failure.TYPE, state.getType());
        assertSame(Failure.TYPE, state.getType());
    }

    @Test
    @DisplayName("Failure::pending returns Pending::getInstance")
    public void failure_pending_returns_singleton_instance() {
        final var pending = state.pending();
        assertEquals(Pending.getInstance(), pending);
        assertSame(Pending.getInstance(), pending);
    }

    @Test
    @DisplayName("Failure::failure returns new instance")
    public void failure_failure_returns_new_instance_and_increments_attempt_count() {
        final var failure = state.failure(state.getReason(), state.getTimestamp(), RETRY);
        assertNotEquals(state, failure);
        assertNotSame(state, failure);
        assertEquals(state.getReason(), failure.getReason());
        assertEquals(state.getTimestamp(), failure.getTimestamp());
        assertEquals(state.getRetry(), failure.getRetry());
        assertEquals(state.getAttempts() + 1, failure.getAttempts());
    }

    @ParameterizedTest
    @DisplayName("Failure::limit returns Failure instance with or without Failure::retry depending on limit")
    @ValueSource(ints = { 0, 1 })
    public void failure_limit(final int limit) {
        final var failure = state.limit(limit, state.getReason(), state.getTimestamp(), RETRY);
        assertNotEquals(state, failure);
        assertNotSame(state, failure);
        assertEquals(state.getReason(), failure.getReason());
        assertEquals(state.getTimestamp(), failure.getTimestamp());
        assertEquals(state.getAttempts() + 1, failure.getAttempts());

        switch (limit) {
            case 0 -> assertNull(failure.getRetry());
            case 1 -> assertEquals(state.getRetry(), failure.getRetry());
            default -> throw new IllegalStateException("Unexpected value for limit: " + limit);
        }
    }

    @Test
    @DisplayName("Failure state JSON serialisation and deserialisation functions correctly")
    public void failure_json_serialisation_and_deserialisation() throws JsonProcessingException {
        final var mapper = DeterministicObjectMapper.get();
        final var timestamp = mapper.writeValueAsString(ZonedDateTime.now());
        final var retry = mapper.writeValueAsString(ZonedDateTime.now().plus(30, ChronoUnit.SECONDS));
        final var input = "{\"attempts\":1,\"reason\":\"Hello, World!\",\"retry\":" + retry + ",\"timestamp\":" + timestamp + ",\"type\":\"" + Failure.TYPE + "\"}";

        final var base = assertInstanceOf(Failure.class, mapper.readValue(input, JobState.class));
        final var impl = assertInstanceOf(Failure.class, mapper.readValue(input, Failure.class));

        assertEquals(base, impl);

        final var output = mapper.writeValueAsString(impl);
        assertEquals(input, output);
    }

    @Test
    @DisplayName("Failure::onLimitExceeded executes task if retry is null")
    public void failure_on_limit_exceeded_executes_task_with_null_retry() {
        final var taskExecuted = new boolean[] { false };
        final Runnable task = () -> taskExecuted[0] = true;
        final var state = new Failure(null, 0, EPOCH, null);

        final var result = state.onLimitExceeded(task);
        assertTrue(taskExecuted[0]);
        assertEquals(state, result);
    }

    @Test
    @DisplayName("Failure::onLimitExceeded does not execute task if retry is not null")
    public void failure_on_limit_exceeded_does_not_execute_task_with_non_null_retry() {
        final var taskExecuted = new boolean[] { false };
        final Runnable task = () -> taskExecuted[0] = true;

        final var result = state.onLimitExceeded(task);
        assertFalse(taskExecuted[0]);
        assertEquals(state, result);
    }

    @Test
    @DisplayName("Failure::onLimitNotExceeded does not execute task if retry is null")
    public void failure_on_limit_not_exceeded_does_not_execute_task_with_null_retry() {
        final var taskExecuted = new boolean[] { false };
        final Runnable task = () -> taskExecuted[0] = true;
        final var state = new Failure(null, 0, EPOCH, null);

        final var result = state.onLimitNotExceeded(task);
        assertFalse(taskExecuted[0]);
        assertEquals(state, result);
    }

    @Test
    @DisplayName("Failure::onLimitNotExceeded does not execute task if retry is null")
    public void failure_on_limit_not_exceeded_executes_task_with_non_null_retry() {
        final var taskExecuted = new boolean[] { false };
        final Runnable task = () -> taskExecuted[0] = true;

        final var result = state.onLimitNotExceeded(task);
        assertTrue(taskExecuted[0]);
        assertEquals(state, result);
    }
}