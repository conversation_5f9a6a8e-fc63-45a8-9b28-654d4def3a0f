package global.symbio.billing.core.job.persistence.state;

import global.symbio.billing.core.job.persistence.api.job.state.*;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.lang.reflect.Modifier;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class TestJobState {

    @Test
    @DisplayName("JobState is abstract class.")
    public void state_is_abstract_class() {
        assertTrue(Modifier.isAbstract(JobState.class.getModifiers()));
    }

    @Test
    @DisplayName("JobState is sealed class.")
    public void state_is_sealed_class() {
        assertTrue(JobState.class.isSealed());
    }

    @Test
    @DisplayName("JobState only permits Pending, Success, Failure and Skipped implementations.")
    public void state_only_permits_pending_success_and_failure_subtypes() {
        final var permitted = JobState.class.getPermittedSubclasses();
        final var expected = new Class[]{ Pending.class, Success.class, Failure.class, Skipped.class };
        assertArrayEquals(expected, permitted);
    }
}
