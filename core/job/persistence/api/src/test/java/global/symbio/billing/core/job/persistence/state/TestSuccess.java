package global.symbio.billing.core.job.persistence.state;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import global.symbio.billing.core.job.persistence.api.job.state.JobState;
import global.symbio.billing.core.job.persistence.api.job.state.Pending;
import global.symbio.billing.core.job.persistence.api.job.state.Success;
import global.symbio.billing.core.persistence.api.DeterministicObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;

import static org.junit.jupiter.api.Assertions.*;

public class TestSuccess {

    private static final ZonedDateTime EPOCH = ZonedDateTime.ofInstant(Instant.EPOCH, ZoneId.systemDefault());

    private Success state;

    @BeforeEach
    public void setup() {
        state = new Success(EPOCH);
    }

    @Test
    @DisplayName("Success::new rejects null timestamp")
    public void success_rejects_null_timestamp() {
        assertThrows(NullPointerException.class, () -> new Success(null));
    }

    @Test
    @DisplayName("Success is subclass of State")
    public void success_is_subtype_of_state() {
        assertInstanceOf(Success.class, state);
        assertInstanceOf(JobState.class, state);
    }

    @Test
    @DisplayName("Success#TYPE equals SUCCESS and Success#type equals Success#TYPE")
    public void success_type_equals_success() {
        assertEquals("SUCCESS", Success.TYPE);
        assertEquals(Success.TYPE, state.getType());
        assertSame(Success.TYPE, state.getType());
    }

    @Test
    @DisplayName("Success#pending returns Pending#getInstance")
    public void success_pending_returns_singleton_instance() {
        final var pending = state.pending();
        assertEquals(Pending.getInstance(), pending);
        assertSame(Pending.getInstance(), pending);
    }

    @Test
    @DisplayName("Success#success returns new instance")
    public void success_success_returns_new_instance() {
        final var success = state.success(EPOCH);
        assertEquals(state, success);
        assertNotSame(state, success);
    }

    @Test
    @DisplayName("Success state JSON serialisation and deserialisation functions correctly")
    public void success_json_serialisation_and_deserialisation() throws JsonProcessingException {
        final var mapper = DeterministicObjectMapper.get();
        final var timestamp = mapper.writeValueAsString(ZonedDateTime.now());
        final var input = "{\"timestamp\":" + timestamp + ",\"type\":\"" + Success.TYPE + "\"}";

        final var base = assertInstanceOf(Success.class, mapper.readValue(input, JobState.class));
        final var impl = assertInstanceOf(Success.class, mapper.readValue(input, Success.class));

        assertEquals(base, impl);

        final var output = mapper.writeValueAsString(impl);
        assertEquals(input, output);
    }
}
