package global.symbio.billing.core.job.persistence.state;

import com.fasterxml.jackson.core.JsonProcessingException;
import global.symbio.billing.core.job.persistence.api.job.state.JobState;
import global.symbio.billing.core.job.persistence.api.job.state.Skipped;
import global.symbio.billing.core.persistence.api.DeterministicObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.Duration;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;

import static org.junit.jupiter.api.Assertions.*;

public class TestSkipped {

    private static final ZonedDateTime EPOCH = ZonedDateTime.ofInstant(Instant.EPOCH, ZoneId.systemDefault());
    private static final Duration RETRY = Duration.ofSeconds(1);

    private Skipped state;

    @BeforeEach
    public void setup() {
        state = new Skipped("reason", EPOCH);
    }

    @Test
    @DisplayName("Skipped::new rejects null constructor arguments")
    public void skipped_rejects_null_constructor_arguments() {
        assertThrows(NullPointerException.class, () -> new Skipped(null, EPOCH));
        assertThrows(NullPointerException.class, () -> new Skipped("reason", null));
    }

    @Test
    @DisplayName("Skipped is subclass of State")
    public void skipped_is_subtype_of_state() {
        assertInstanceOf(Skipped.class, state);
        assertInstanceOf(JobState.class, state);
    }

    @Test
    @DisplayName("Skipped#TYPE equals SKIPPED and Skipped#type equals Skipped#TYPE")
    public void skipped_type_equals_success() {
        assertEquals("SKIPPED", Skipped.TYPE);
        assertEquals(Skipped.TYPE, state.getType());
        assertSame(Skipped.TYPE, state.getType());
    }

    @Test
    @DisplayName("Skipped state JSON serialisation and deserialisation functions correctly")
    public void skipped_json_serialisation_and_deserialisation() throws JsonProcessingException {
        final var mapper = DeterministicObjectMapper.get();
        final var timestamp = mapper.writeValueAsString(ZonedDateTime.now());
        final var input = "{\"reason\":\"Reason\",\"timestamp\":" + timestamp + ",\"type\":\"" + Skipped.TYPE + "\"}";

        final var base = assertInstanceOf(Skipped.class, mapper.readValue(input, JobState.class));
        final var impl = assertInstanceOf(Skipped.class, mapper.readValue(input, Skipped.class));

        assertEquals(base, impl);

        final var output = mapper.writeValueAsString(impl);
        assertEquals(input, output);
    }

}