package global.symbio.billing.core.job.persistence.spi.jdbc.jpa.job.delay;

import global.symbio.billing.core.job.persistence.api.repository.JobDelayRepository;
import global.symbio.billing.core.persistence.api.Repositories;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

public class TestJobDelayRepository {

    @Test
    @DisplayName("ReadOnlyJobDelayRepository is connected to read-only datasource")
    public void test_read_only_repository() {
        Assertions.assertTrue(JobDelayRepository.class.isAssignableFrom(ReadOnlyJPAJobDelayRepository.class));
        Assertions.assertTrue(Repositories.isReadOnlyRepository(ReadOnlyJPAJobDelayRepository.class));
    }

    @Test
    @DisplayName("ReadWriteJobDelayRepository is connected to read-write datasource")
    public void test_read_write_repository() {
        Assertions.assertTrue(JobDelayRepository.class.isAssignableFrom(ReadWriteJPAJobDelayRepository.class));
        Assertions.assertTrue(Repositories.isReadWriteRepository(ReadWriteJPAJobDelayRepository.class));
    }
}