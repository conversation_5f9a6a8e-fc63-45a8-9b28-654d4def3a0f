package global.symbio.billing.core.job.persistence.spi.jdbc.jpa.job;

import global.symbio.billing.core.job.persistence.api.repository.JobRepository;
import global.symbio.billing.core.persistence.api.Repositories;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

public class TestJobRepository {

    @Test
    @DisplayName("ReadWriteJobRepository is connected to read-write datasource")
    public void test_read_write_repository() {
        Assertions.assertTrue(JobRepository.class.isAssignableFrom(ReadWriteJPAJobRepository.class));
        Assertions.assertTrue(Repositories.isReadWriteRepository(ReadWriteJPAJobRepository.class));
    }
}