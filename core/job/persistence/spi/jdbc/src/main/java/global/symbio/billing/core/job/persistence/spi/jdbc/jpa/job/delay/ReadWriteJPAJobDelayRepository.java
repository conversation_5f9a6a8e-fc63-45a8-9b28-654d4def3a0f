package global.symbio.billing.core.job.persistence.spi.jdbc.jpa.job.delay;

import global.symbio.billing.core.persistence.api.PersistenceStore;
import io.micronaut.context.annotation.Secondary;
import io.micronaut.data.annotation.Repository;
import jakarta.inject.Named;
import jakarta.inject.Singleton;

import static global.symbio.billing.core.persistence.api.StandardDataSourceNames.READ_WRITE;

@Secondary
@Singleton
@Repository(READ_WRITE) @Named(READ_WRITE)
@PersistenceStore(PersistenceStore.Capabilities.READ_WRITE)
public interface ReadWriteJPAJobDelayRepository extends JPAJobDelayRepository {}