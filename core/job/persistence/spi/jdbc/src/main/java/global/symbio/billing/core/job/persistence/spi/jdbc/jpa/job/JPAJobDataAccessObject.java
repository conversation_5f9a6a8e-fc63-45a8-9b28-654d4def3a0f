package global.symbio.billing.core.job.persistence.spi.jdbc.jpa.job;

import global.symbio.billing.core.country.persistence.api.CountryDataAccessObject;
import global.symbio.billing.core.country.persistence.spi.jdbc.jpa.JPACountryDataAccessObject;
import global.symbio.billing.core.job.persistence.api.job.JobDataAccessObject;
import global.symbio.billing.core.job.persistence.api.job.state.JobState;
import global.symbio.billing.core.persistence.api.Types;
import io.hypersistence.utils.hibernate.type.json.JsonBinaryType;
import io.micronaut.core.annotation.Introspected;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Type;

import java.time.ZonedDateTime;
import java.util.Map;
import java.util.UUID;

@Entity
@Table(name = "job")
@Getter @Setter
@Introspected(includedAnnotations = Entity.class)
public class JPAJobDataAccessObject extends JobDataAccessObject {

    @Id @Nonnull
    @Column(name = "id", unique = true, nullable = false, updatable = false)
    private UUID identifier;

    @Nonnull
    @Column(name = "job", nullable = false)
    private String job;

    @Nonnull
    @Column(name = "period", nullable = false)
    private String period;

    @Nonnull
    @Type(JsonBinaryType.class)
    @Column(columnDefinition = "state", nullable = false)
    private JobState state;

    @Nonnull
    @Column(name = "type", nullable = false)
    private String type;

    @Nonnull
    @Column(name = "long_running", nullable = false)
    private Boolean longRunning;

    @Nullable
    @Type(JsonBinaryType.class)
    @Column(name = "parameters", length = Short.MAX_VALUE)
    private String parameters;

    @Nullable
    @ManyToOne
    @JoinColumn(name = "country")
    private JPACountryDataAccessObject country;

    @Nullable
    @Column(name = "correlation")
    private UUID correlation;

    @Nullable
    @Type(JsonBinaryType.class)
    @Column(name = "tags", length = Short.MAX_VALUE)
    private Map<String, ?> tags;

    @Nullable
    @Column(name = "expiration")
    private ZonedDateTime expiration;

    @Nonnull
    @Column(name = "timestamp", nullable = false, updatable = false, insertable = false)
    private ZonedDateTime timestamp;

    @Nonnull
    @Override
    public JobDataAccessObject identifier(@Nonnull UUID identifier) {
        setIdentifier(identifier);
        return this;
    }

    @Nonnull
    @Override
    public JobDataAccessObject job(@Nonnull String job) {
        setJob(job);
        return this;
    }

    @Nonnull
    @Override
    public JobDataAccessObject period(@Nonnull String period) {
        setPeriod(period);
        return this;
    }

    @Nonnull
    @Override
    public JobDataAccessObject state(@Nonnull JobState state) {
        setState(state);
        return this;
    }

    @Nonnull
    @Override
    public JobDataAccessObject type(@Nonnull String type) {
        setType(type);
        return this;
    }

    @Nonnull
    @Override
    public JobDataAccessObject longRunning(@Nonnull Boolean longRunning) {
        setLongRunning(longRunning);
        return this;
    }

    @Nonnull
    @Override
    public JobDataAccessObject parameters(@Nullable String parameters) {
        setParameters(parameters);
        return this;
    }

    @Nonnull
    @Override
    public JobDataAccessObject country(@Nullable CountryDataAccessObject country) {
        final var data = Types.require(country, "country", JPACountryDataAccessObject.class);
        setCountry(data);
        return this;
    }

    @Nonnull
    @Override
    public JobDataAccessObject correlation(@Nullable UUID correlation) {
        setCorrelation(correlation);
        return this;
    }

    @Nonnull
    @Override
    public JobDataAccessObject tags(@Nullable Map<String, ?> tags) {
        setTags(tags);
        return this;
    }

    @Nonnull
    @Override
    public JobDataAccessObject expiration(@Nullable ZonedDateTime expiration) {
        setExpiration(expiration);
        return this;
    }

    @Nonnull
    @Override
    public JobDataAccessObject timestamp(@Nonnull ZonedDateTime timestamp) {
        setTimestamp(timestamp);
        return this;
    }

    @Nonnull
    @Override
    public Boolean isLongRunning() {
        return longRunning;
    }
}
