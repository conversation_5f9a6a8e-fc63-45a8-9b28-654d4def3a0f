package global.symbio.billing.core.job.persistence.spi.jdbc.jpa.job;

import global.symbio.billing.core.job.persistence.api.repository.JobRepository;
import io.micronaut.context.annotation.Executable;
import io.micronaut.data.annotation.Query;
import jakarta.annotation.Nonnull;

import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.Set;
import java.util.UUID;

public interface JPAJobRepository extends JobRepository<JPAJobDataAccessObject> {

    /**
     * Fetches jobs that are in an executable state.
     * Executable jobs are:
     * - not currently executing or scheduled for execution.
     * - in the pending state.
     * - in the failed state AND have a retry timestamp which is less than or equal to the specified timestamp.
     * Returns jobs in the following order:
     * - Pending > Failed
     * - Long running > Quick
     * From the above, we randomise the returned results in order to add jitter/entropy, in order to help prevent the system
     * from getting into a state where it can no longer make progress.
     */
    @Nonnull
    @Override
    @Query(
        value = """
            SELECT * FROM job
            WHERE id NOT IN (:identifiers)
            AND (
                state->>'type' = :pending
                OR
                (
                    state->>'type' = :failure
                    AND
                    state->>'retry' IS NOT NULL
                    AND
                    CAST(REPLACE(state->>'retry', 'T', ' ') AS TIMESTAMPTZ) <= :timestamp
                )
            )
            ORDER BY (state->>'type' = :pending) DESC, long_running DESC, RANDOM()
            LIMIT :limit
            """,
        nativeQuery = true
    )
    @Executable
    Collection<JPAJobDataAccessObject> findExecutableJobs(@Nonnull ZonedDateTime timestamp, @Nonnull String pending, @Nonnull String failure, @Nonnull Set<UUID> identifiers, int limit);

    @Override
    default long purge(@Nonnull ZonedDateTime timestamp) {
        return deleteByExpirationLessThanEquals(timestamp);
    }

    @Executable
    long deleteByExpirationLessThanEquals(@Nonnull ZonedDateTime timestamp);
}
