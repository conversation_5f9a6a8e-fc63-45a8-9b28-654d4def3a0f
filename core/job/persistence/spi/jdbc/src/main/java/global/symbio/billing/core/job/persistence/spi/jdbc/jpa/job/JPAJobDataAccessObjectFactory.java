package global.symbio.billing.core.job.persistence.spi.jdbc.jpa.job;

import global.symbio.billing.core.job.persistence.api.job.JobDataAccessObject;
import global.symbio.billing.core.job.persistence.api.job.JobDataAccessObjectFactory;
import io.micronaut.context.annotation.Primary;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

@Primary
@Singleton
public class JPAJobDataAccessObjectFactory implements JobDataAccessObjectFactory {

    @Nonnull
    @Override
    public Class<? extends JobDataAccessObject> type() {
        return JPAJobDataAccessObject.class;
    }

    @Nonnull
    @Override
    public JobDataAccessObject create() {
        return new JPAJobDataAccessObject();
    }
}