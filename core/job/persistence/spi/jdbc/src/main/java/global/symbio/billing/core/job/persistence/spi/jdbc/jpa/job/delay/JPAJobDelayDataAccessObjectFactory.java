package global.symbio.billing.core.job.persistence.spi.jdbc.jpa.job.delay;

import global.symbio.billing.core.job.persistence.api.job.delay.JobDelayDataAccessObject;
import global.symbio.billing.core.job.persistence.api.job.delay.JobDelayDataAccessObjectFactory;
import io.micronaut.context.annotation.Primary;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

@Primary
@Singleton
public class JPAJobDelayDataAccessObjectFactory implements JobDelayDataAccessObjectFactory {

    @Nonnull
    @Override
    public Class<? extends JobDelayDataAccessObject> type() {
        return JPAJobDelayDataAccessObject.class;
    }

    @Nonnull
    @Override
    public JobDelayDataAccessObject create() {
        return new JPAJobDelayDataAccessObject();
    }
}