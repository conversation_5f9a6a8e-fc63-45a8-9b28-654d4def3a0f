package global.symbio.billing.core.job.persistence.spi.jdbc.jpa.job.delay;

import global.symbio.billing.core.job.persistence.api.repository.JobDelayRepository;
import io.micronaut.context.annotation.Executable;
import io.micronaut.data.annotation.Query;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.util.Optional;

public interface JPAJobDelayRepository extends JobDelayRepository<JPAJobDelayDataAccessObject> {

    @Nonnull
    @Override
    @Query(
        value = """
            SELECT * FROM job_delay as delay
            WHERE delay.type = :type 
            AND delay.active = :active
            AND (
                CASE WHEN CAST(:country AS integer) IS NULL
                    THEN delay.country IS NULL
                    ELSE delay.country = CAST(:country AS integer)
                    END
                )
        """,
        nativeQuery = true
    )
    @Executable
    Optional<JPAJobDelayDataAccessObject> getDelayByTypeCountryActive(@Nonnull String type, @Nullable Integer country, boolean active);
}
