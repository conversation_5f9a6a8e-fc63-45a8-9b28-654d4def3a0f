package global.symbio.billing.core.job.persistence.spi.jdbc.jpa.job.delay;

import global.symbio.billing.core.country.persistence.api.CountryDataAccessObject;
import global.symbio.billing.core.country.persistence.spi.jdbc.jpa.JPACountryDataAccessObject;
import global.symbio.billing.core.job.persistence.api.job.delay.JobDelayDataAccessObject;
import global.symbio.billing.core.persistence.api.Types;
import io.micronaut.core.annotation.Introspected;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.time.ZonedDateTime;

@Entity
@Table(name = "job_delay")
@Getter @Setter
@Introspected(includedAnnotations = Entity.class)
public class JPAJobDelayDataAccessObject extends JobDelayDataAccessObject {

    @Id @Nonnull
    @Column(name = "id", unique = true, nullable = false, updatable = false)
    private Integer identifier;

    @Nonnull
    @Column(name = "type", nullable = false)
    private String type;

    @Nullable
    @ManyToOne
    @JoinColumn(name = "country")
    private JPACountryDataAccessObject country;

    @Nullable
    @Column(name = "start")
    private ZonedDateTime start;

    @Nullable
    @Column(name = "expiration")
    private ZonedDateTime expiration;

    @Nonnull
    @Column(name = "active", nullable = false)
    private Boolean active;

    @Nonnull
    @Column(name = "reason", nullable = false)
    private String reason;

    @Nonnull
    @Column(name = "timestamp", nullable = false, updatable = false, insertable = false)
    private ZonedDateTime timestamp;

    @Nonnull
    @Override
    public JobDelayDataAccessObject identifier(@Nonnull Integer identifier) {
        setIdentifier(identifier);
        return this;
    }

    @Nonnull
    @Override
    public JobDelayDataAccessObject type(@Nonnull String type) {
        setType(type);
        return this;
    }

    @Nonnull
    @Override
    public JobDelayDataAccessObject country(@Nullable CountryDataAccessObject country) {
        final var data = Types.require(country, "country", JPACountryDataAccessObject.class);
        setCountry(data);
        return this;
    }

    @Nonnull
    @Override
    public JobDelayDataAccessObject start(@Nullable ZonedDateTime start) {
        setStart(start);
        return this;
    }

    @Nonnull
    @Override
    public JobDelayDataAccessObject expiration(@Nullable ZonedDateTime expiration) {
        setExpiration(expiration);
        return this;
    }

    @Nonnull
    @Override
    public JobDelayDataAccessObject active(@Nonnull Boolean active) {
        setActive(active);
        return this;
    }

    @Nonnull
    @Override
    public Boolean isActive() {
        return active;
    }

    @Nonnull
    @Override
    public JobDelayDataAccessObject reason(@Nonnull String reason) {
        setReason(reason);
        return this;
    }

    @Nonnull
    @Override
    public JobDelayDataAccessObject timestamp(@Nonnull ZonedDateTime timestamp) {
        setTimestamp(timestamp);
        return this;
    }
}
