services:
  postgres:
    image: postgres:latest
    container_name: postgres
    restart: always
    ports:
      - "5432:5432"
    environment:
      POSTGRES_PASSWORD: postgrespw

  dynamodb-local:
    command: "-jar DynamoDBLocal.jar -sharedDb -dbPath ./data"
    image: "amazon/dynamodb-local:latest"
    container_name: dynamodb-local
    restart: always
    ports:
      - "8000:8000"
    volumes:
      - "./docker/dynamodb:/home/<USER>/data"
    working_dir: /home/<USER>

  # ZooKeeper for SG Cluster
  zookeeper-sg:
    image: confluentinc/cp-zookeeper:latest
    container_name: zookeeper-sg
    restart: always
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000

  # Kafka Broker for SG Cluster
  broker-sg:
    image: confluentinc/cp-kafka:latest
    container_name: broker-sg
    restart: always
    depends_on:
      - zookeeper-sg
    ports:
      - "9091:9091"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper-sg:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9091,PLAINTEXT_HOST://broker-sg:29091
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1

  # ZooKeeper for AU Cluster
  zookeeper-au:
    image: confluentinc/cp-zookeeper:latest
    container_name: zookeeper-au
    restart: always
    environment:
      ZOOKEEPER_CLIENT_PORT: 2182
      ZOOKEEPER_TICK_TIME: 2000

  # Kafka Broker for AU Cluster
  broker-au:
    image: confluentinc/cp-kafka:latest
    container_name: broker-au
    restart: always
    depends_on:
      - zookeeper-au
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 2
      KAFKA_ZOOKEEPER_CONNECT: zookeeper-au:2182
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092,PLAINTEXT_HOST://broker-au:29092
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1

  kafku-ui:
    image: provectuslabs/kafka-ui:latest
    container_name: kafku-ui
    restart: always
    ports:
      - "9090:9090"
    depends_on:
      - broker-sg
      - broker-au
    environment:
      SERVER_PORT: 9090
      KAFKA_CLUSTERS_0_NAME: sg
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: broker-sg:29091
      KAFKA_CLUSTERS_0_ZOOKEEPER: zookeeper-sg:2181
      KAFKA_CLUSTERS_1_NAME: au
      KAFKA_CLUSTERS_1_BOOTSTRAPSERVERS: broker-au:29092
      KAFKA_CLUSTERS_1_ZOOKEEPER: zookeeper-au:2182
  #      #Create connection to uat. Get info and jks file from instaclustr
  #      KAFKA_CLUSTERS_1_NAME: mnf-nonprod-sg-instaclustr-kafka
  #      KAFKA_CLUSTERS_1_BOOTSTRAPSERVERS: ************:9092,***********:9092,*************:9092
  #      KAFKA_CLUSTERS_1_PROPERTIES_SECURITY_PROTOCOL: SASL_SSL
  #      KAFKA_CLUSTERS_1_PROPERTIES_SASL_MECHANISM: SCRAM-SHA-256
  #      KAFKA_CLUSTERS_1_PROPERTIES_SASL_JAAS_CONFIG: org.apache.kafka.common.security.scram.ScramLoginModule required username="<PUT USERNAME HERE>" password="<PUT PASSWORD HERE>";
  #      KAFKA_CLUSTERS_1_PROPERTIES_SSL_TRUSTSTORE_LOCATION: /truststore.jks
  #      KAFKA_CLUSTERS_1_PROPERTIES_SSL_TRUSTSTORE_PASSWORD: instaclustr
  #      KAFKA_CLUSTERS_1_PROPERTIES_SSL_ENDPOINT_IDENTIFICATION_ALGORITHM: ''
  #      KAFKA_CLUSTERS_1_PROPERTIES_SSL_ENABLED_PROTOCOLS: TLSv1.2,TLSv1.1,TLSv1
  #      KAFKA_CLUSTERS_1_PROPERTIES_SSL_PROTOCOL: TLS
  #    volumes:
  #      - ./ssl/truststore.jks:/truststore.jks

  localstack:
    container_name: "${LOCALSTACK_DOCKER_NAME-localstack_main}"
    image: localstack/localstack
    ports:
      - "127.0.0.1:4566:4566"            # LocalStack Gateway
      - "127.0.0.1:4510-4559:4510-4559"  # external services port range
    environment:
      - DEBUG=${DEBUG-}
      - DOCKER_HOST=unix:///var/run/docker.sock
      - AWS_ACCESS_KEY_ID=dummy-access-key
      - AWS_SECRET_ACCESS_KEY=dummy-secret-key
    volumes:
      - "${LOCALSTACK_VOLUME_DIR:-./volume}:/var/lib/localstack"
      - "/var/run/docker.sock:/var/run/docker.sock"
      - "./config/init-aws.sh:/etc/localstack/init/ready.d/init-aws.sh"
      - "./config/data/invoice.pdf:/config/data/invoice.pdf"