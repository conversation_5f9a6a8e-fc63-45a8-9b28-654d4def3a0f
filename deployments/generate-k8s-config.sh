#!/bin/sh

rm -rf ./deployments/k8s/autogenerated
mkdir ./deployments/k8s/autogenerated

echo "ROLE_ARN=${ROLE_ARN}"

if [ -z "${K8S_DEPLOYMENT_FILES}" ]; then
  K8S_DEPLOYMENT_FILES=./deployments/k8s/*.yaml
fi

kubetpl render ${K8S_DEPLOYMENT_FILES} -i .env -s ENVIRONMENT=${ENVIRONMENT} -s K8S_NAMESPACE=${K8S_NAMESPACE} -s SERVICE_NAME=${SERVICE_NAME} -s IMAGE=${DOCKER_IMAGE} -s ROLE_ARN=${ROLE_ARN} -s INGRESS_HOST_NAME=${INGRESS_HOST_NAME} > ./deployments/k8s/autogenerated/all.yaml