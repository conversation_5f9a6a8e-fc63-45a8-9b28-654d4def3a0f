global:
  labels: {}
  annotations: {}
  customName: "payment"


gitlab:
  auth:
    secret: gitlab-auth


serviceAccount:
  enabled: true
  name: payment
  annotations:
    k8sk.amazonaws.com/sts-regional-endpoints: "true"
  imagePullSecrets:
    - gitlab-auth

roles:
  role:
    enabled: true
    rules:
      - apiGroups: [ "" ]
        resources: [ "services", "endpoints", "configmaps", "secrets", "pods" ]
        verbs: [ "get", "watch", "list" ]

roleBindings:
  roleBinding:
    enabled: true
    subjects:
      - kind: ServiceAccount
        name: payment
    roleName: payment-role

deployment:
  enabled: true
  replicas: 1
  serviceAccountName: payment
  app: payment
pod:
  enabled: false
  disruptionBudget:
    enabled: false
containers:
  payment:
    enabled: true
    imagePullPolicy: IfNotPresent
    securityContext:
      runAsUser: 1200
      runAsNonRoot: true
      privileged: false
    resources:
      requests:
        memory: 3048Mi
        cpu: 512m
      limits:
        memory: 3048Mi
    ports:
      - 8080
    probes:
      readinessProbe:
        httpGet:
          path: /health/readiness
          port: 8080
          scheme: HTTP
          httpHeaders:
            # Basic authentication for 'k8s-management' service account
            - name: "Authorization"
              value: "Basic azhzLW1hbmFnZW1lbnQ6dFFQZmhkek5icUQ2QmI0TkM3QjJSZnYzV0pNRncyeHY="
        initialDelaySeconds: 30
        periodSeconds: 3
        failureThreshold: 30
      livenessProbe:
        httpGet:
          path: /health/liveness
          port: 8080
          scheme: HTTP
          httpHeaders:
            # Basic authentication for 'k8s-management' service account
            - name: "Authorization"
              value: "Basic azhzLW1hbmFnZW1lbnQ6dFFQZmhkek5icUQ2QmI0TkM3QjJSZnYzV0pNRncyeHY="
        initialDelaySeconds: 90
        periodSeconds: 10
        failureThreshold: 3
    env:
      env:
        valueFrom:
          configMapKeyRef:
            name: payment-configmap
            key: environment
      SYMBIO_ACCOUNT_UUID_AU:
        valueFrom:
          configMapKeyRef:
            name: payment-configmap
            key: symbio.account.uuid.au
      SYMBIO_ACCOUNT_UUID_NZ:
        valueFrom:
          configMapKeyRef:
            name: payment-configmap
            key: symbio.account.uuid.nz
      SYMBIO_ACCOUNT_UUID_MY:
        valueFrom:
          configMapKeyRef:
            name: payment-configmap
            key: symbio.account.uuid.my
      SYMBIO_ACCOUNT_UUID_SG:
        valueFrom:
          configMapKeyRef:
            name: payment-configmap
            key: symbio.account.uuid.sg
      SYMBIO_ACCOUNT_UUID_TW:
        valueFrom:
          configMapKeyRef:
            name: payment-configmap
            key: symbio.account.uuid.tw
      KAFKA_AUTHENTICATION_CONFIGURATION:
        valueFrom:
          configMapKeyRef:
            name: payment-configmap
            key: kafka_authentication_configuration
      KAFKA_TRUSTSTORE_SG:
        valueFrom:
          configMapKeyRef:
            name: payment-configmap
            key: kafka_truststore_sg
      KAFKA_TRUSTSTORE_AU:
        valueFrom:
          configMapKeyRef:
            name: payment-configmap
            key: kafka_truststore_au
      KAFKA_GROUP_ID:
        valueFrom:
          configMapKeyRef:
            name: payment-configmap
            key: kafka_group_id
      KAFKA_TOPIC_SUFFIX:
        valueFrom:
          configMapKeyRef:
            name: payment-configmap
            key: kafka_topic_suffix
      KAFKA_WORKSPACE_BILLING:
        valueFrom:
          configMapKeyRef:
            name: payment-configmap
            key: kafka_workspace_billing
      KAFKA_WORKSPACE_FINANCE_REPORTING:
        valueFrom:
          configMapKeyRef:
            name: payment-configmap
            key: kafka_workspace_finance_reporting
      KAFKA_WORKSPACE_AUDIT:
        valueFrom:
          configMapKeyRef:
            name: payment-configmap
            key: kafka_workspace_audit
      ELASTIC_APM_SECRET_TOKEN:
        valueFrom:
          configMapKeyRef:
            name: payment-configmap
            key: elastic.apm.secret.token
      ELASTIC_APM_SERVER_URL:
        valueFrom:
          configMapKeyRef:
            name: payment-configmap
            key: elastic.apm.secret.url
      PLATFORM_MAPPING:
        valueFrom:
          configMapKeyRef:
            name: payment-configmap
            key: platform_mapping
      CI_COMMIT_REF_NAME:
        valueFrom:
          configMapKeyRef:
            name: payment-configmap
            key: ci.commit.ref.name
      CI_COMMIT_REF_SLUG:
        valueFrom:
          configMapKeyRef:
            name: payment-configmap
            key: ci.commit.ref.slug
      CI_COMMIT_SHORT_SHA:
        valueFrom:
          configMapKeyRef:
            name: payment-configmap
            key: ci.commit.sha.short
      CI_COMMIT_SHA:
        valueFrom:
          configMapKeyRef:
            name: payment-configmap
            key: ci.commit.sha



autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 3
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80

service:
  main:
    enabled: true
    primary: true
    type: NodePort
    ports:
      rest:
        enabled: true
        primary: true
        port: 8080
        protocol: HTTP
        targetPort: 8080
      grpc:
        enabled: true
        primary: false
        port: 8081
        protocol: TCP
        targetPort: 8081

ingress:
  enabled: true
  annotations:
    external-dns.alpha.kubernetes.io/ingress-hostname-source: annotation-only
  services:
    payment:
      http:
        port: 8080
