#!/bin/bash

set -e
source .env

if [ -z "$ENVIRONMENT" ]; then
  echo "ENVIRONMENT variable must be set."
  exit 1
fi

if [ -z "$K8S_NAMESPACE" ]; then
  K8S_NAMESPACE="default"
fi

if [ -z "$DOCKER_IMAGE" ]; then
  DOCKER_IMAGE="billing/billing"
fi

export CI_COMMIT_REF_NAME=$CI_COMMIT_REF_NAME
export CI_COMMIT_REF_SLUG=$CI_COMMIT_REF_SLUG
export CI_COMMIT_SHA=$CI_COMMIT_SHA
export CI_COMMIT_SHORT_SHA=$CI_COMMIT_SHORT_SHA

echo "Commit Ref: $CI_COMMIT_REF_SLUG | $CI_COMMIT_REF_NAME"
echo "Commit SHA: $CI_COMMIT_SHORT_SHA | $CI_COMMIT_SHA"

export K8S_NAMESPACE=$K8S_NAMESPACE
export SERVICE_NAME=$SERVICE_NAME
export DOCKER_IMAGE=$DOCKER_IMAGE
export ROLE_ARN
export DEPLOYMENT_COUNTRY="GO"

if [[ "$ENVIRONMENT" =~ ^prod.* ]]; then
  export KAFKA_GROUP_ID="main"
  export KAFKA_WORKSPACE_BILLING="prod"
  export KAFKA_WORKSPACE_FINANCE_REPORTING="prod"
  export KAFKA_WORKSPACE_AUDIT="prod"
elif [[ "$ENVIRONMENT" =~ ^uat.* ]]; then
  export KAFKA_GROUP_ID="main"
  export KAFKA_WORKSPACE_BILLING="uat"
  export KAFKA_WORKSPACE_FINANCE_REPORTING="qa"
  export KAFKA_WORKSPACE_AUDIT="uat"
elif [[ "$ENVIRONMENT" =~ ^review.* ]]; then
  export KAFKA_GROUP_ID="$CI_COMMIT_REF_SLUG"
  export KAFKA_TOPIC_SUFFIX=".$CI_COMMIT_REF_SLUG"
  export KAFKA_WORKSPACE_BILLING="uat"
  export KAFKA_WORKSPACE_FINANCE_REPORTING="qa"
  export KAFKA_WORKSPACE_AUDIT="dev"
fi

echo "KAFKA_GROUP_ID=$KAFKA_GROUP_ID"
echo "KAFKA_TOPIC_SUFFIX=$KAFKA_TOPIC_SUFFIX"
echo "KAFKA_WORKSPACE_BILLING=$KAFKA_WORKSPACE_BILLING"
echo "KAFKA_WORKSPACE_FINANCE_REPORTING=$KAFKA_WORKSPACE_FINANCE_REPORTING"
echo "KAFKA_WORKSPACE_AUDIT=$KAFKA_WORKSPACE_AUDIT"
echo "MIXIN_AUDIT_ENABLED=$MIXIN_AUDIT_ENABLED"
echo "LEDGER_INTERESTED_CATEGORIES=$LEDGER_INTERESTED_CATEGORIES"
echo "CDR_INTERESTED_ACCOUNTS=$CDR_INTERESTED_ACCOUNTS"

if [ -z "$COUNTRY" ]; then
  COUNTRY="au"
fi

if [ -z "$TF_VAR_aws_region" ]; then
  REGION="ap-southeast-2"
else
  REGION="$TF_VAR_aws_region"
fi

echo "***** Deploying to $ENVIRONMENT environment and $REGION region and $COUNTRY country and $SERVICE_NAME service"

if [ ! -z "${TERRAFORM_WORKSPACE}" ]; then

  echo "***** Running terraform on ${TERRAFORM_WORKSPACE} environment"
  cd "$SERVICE_NAME"/terraform

  export TF_VAR_country=$COUNTRY

  if [[ "$ENVIRONMENT" =~ ^prod.* ]]; then
    # Production environment terraform state is stored at the top-level.
    export TERRAFORM_WORKSPACE_KEY_PREFIX="key=$SERVICE_NAME-service/${REGION}/${COUNTRY}/tfstate"
  elif [[ "$ENVIRONMENT" =~ ^uat.* ]]; then
    # UAT environment terraform state was previously stored in the 'main' branch, as UAT deployments were triggered off merging to main.
    export TERRAFORM_WORKSPACE_KEY_PREFIX="key=$SERVICE_NAME-service/${REGION}/${COUNTRY}/main/tfstate"
  elif [[ "$ENVIRONMENT" =~ ^review.* ]]; then
    # Review environment terraform state stored per feature branch.
    export TERRAFORM_WORKSPACE_KEY_PREFIX="key=$SERVICE_NAME-service/$REGION/${COUNTRY}/$CI_COMMIT_REF_SLUG/tfstate"
  else
    echo "Unable to determine terraform workspace key prefix for environment: $ENVIRONMENT"
    exit 1
  fi

  echo "Using ${TERRAFORM_WORKSPACE_KEY_PREFIX}"


  terraform init -backend-config="${TERRAFORM_WORKSPACE_KEY_PREFIX}"
  terraform workspace select -or-create ${TERRAFORM_WORKSPACE}
  terraform state replace-provider -auto-approve registry.terraform.io/-/aws registry.terraform.io/hashicorp/aws
  terraform apply -auto-approve=true --var-file=variables/$REGION/$COUNTRY/${TERRAFORM_WORKSPACE}.tfvars

  if [ $SERVICE_NAME == "template" ]; then
    TEMPLATE_BUCKET_NAME=$(terraform output -raw template_bucket_name)
    TEMPLATE_BUCKET_ARN=$(terraform output -raw template_bucket_arn)
    echo "TEMPLATE_BUCKET_NAME=$TEMPLATE_BUCKET_NAME"
    echo "TEMPLATE_BUCKET_ARN=$TEMPLATE_BUCKET_ARN"
    echo "Applied terraform configuration to $SERVICE_NAME - short circuiting deployment."
    exit 0
  fi

  KAFKA_AUTHENTICATION_CONFIGURATION=$(terraform output -raw kafka_authentication_configuration)

  RECORD_NAME=$(terraform output -raw data_record_name)
  HOSTED_ZONE_NAME=$(terraform output -raw data_host_name)
  if [[ $SERVICE_NAME == "payment"  ]]; then
    NLB_INGRESS=$(terraform output -raw nlb_dns)
    export INGRESS_HOST_NAME="${NLB_INGRESS}"
  elif [ -z "$RECORD_NAME" ]; then
    export INGRESS_HOST_NAME="${HOSTED_ZONE_NAME}"
  else
    export INGRESS_HOST_NAME="${RECORD_NAME}.${HOSTED_ZONE_NAME}"
  fi
  echo "INGRESS_HOST_NAME=$INGRESS_HOST_NAME"

  if [[ $SERVICE_NAME != "payment" && $SERVICE_NAME != "template" ]]; then
    POSTGRES_USER=$(terraform output -raw rds_username)
    POSTGRES_PASSWORD=$(terraform output -raw rds_password)
    DB_PRIMARY_URL=$(terraform output -raw rds_url)
    DB_REPLICA_URL=$(terraform output -raw rds_url_replica)
    DB_LOGICAL_NAME=$(terraform output -raw rds_database_name)
    DB_URL_BASE="jdbc:postgresql:"
    DEFAULT_SCHEMA_NAME="public"
    PROTECTED_SCHEMA_NAMES=("information_schema" "pg_catalog" "public")

    if [[ "$ENVIRONMENT" =~ ^uat.* && "$CI_COMMIT_REF_SLUG" != "$CI_DEFAULT_BRANCH" ]]; then
      # if we are in UAT and the branch IS NOT the main branch, we are testing a feature branch in UAT, and want to do so in isolation.
      DB_SCHEMA_NAME="$CI_COMMIT_REF_SLUG"
      if [[ "${PROTECTED_SCHEMA_NAMES[@]}" =~ "${DB_SCHEMA_NAME}" ]]; then
        # if the selected schema name is an internal postgres schema, we will fail the job.
        echo "Invalid schema name: ${DB_SCHEMA_NAME}. Not permitted to be one of: ${PROTECTED_SCHEMA_NAMES[@]}."
        exit 1
      fi
    else
      # in any other situation, the default schema is fine as we do not require namespace isolation.
      DB_SCHEMA_NAME="$DEFAULT_SCHEMA_NAME"
    fi

    DB_CONNECTION_PARAMETERS="currentSchema=$DB_SCHEMA_NAME"

    POSTGRES_HOST="$DB_URL_BASE//$DB_PRIMARY_URL/$DB_LOGICAL_NAME?$DB_CONNECTION_PARAMETERS"
    POSTGRES_HOST_REPLICA="$DB_URL_BASE//$DB_REPLICA_URL/$DB_LOGICAL_NAME?$DB_CONNECTION_PARAMETERS"
    LIQUIBASE_HOST_WITHOUT_SCHEMA="$DB_URL_BASE//$DB_PRIMARY_URL/$DB_LOGICAL_NAME"
    LIQUIBASE_HOST="$LIQUIBASE_HOST_WITHOUT_SCHEMA?$DB_CONNECTION_PARAMETERS"

    echo "POSTGRES_HOST=$POSTGRES_HOST"
    echo "POSTGRES_HOST_REPLICA=$POSTGRES_HOST_REPLICA"
    echo "LIQUIBASE_HOST=$LIQUIBASE_HOST"
    echo "POSTGRES_USER=$POSTGRES_USER"
    echo "DB_SCHEMA_NAME=$DB_SCHEMA_NAME"
  fi

  if [ $SERVICE_NAME == "payment" ]; then
    ROLE_ARN=$(terraform output -raw service-role)
    INVOICE_STORAGE_BUCKET_NAME=$(terraform output -raw invoice_storage_bucket_name)
    S3_URL="https://s3.${REGION}.amazonaws.com"
    echo "ROLE_ARN=$ROLE_ARN"
    echo "INVOICE_STORAGE_BUCKET_NAME=$INVOICE_STORAGE_BUCKET_NAME"
    echo "S3_URL=$S3_URL"
  fi

  if [ $SERVICE_NAME == "invoice" ]; then
    DYNAMODB_TABLE_ENRICHED_TRANSACTION=$(terraform output -raw dynamodb_table_name)
    DYNAMODB_TABLE_SUMMARISED_MODEL=$(terraform output -raw dynamodb_summarised_table_name)
    DYNAMODB_TABLE_SUMMARISED_MODEL_BY_DAY=$(terraform output -raw dynamodb_summarised_by_day_table_name)
    DYNAMODB_TABLE_EXECUTION_STATUS=$(terraform output -raw dynamodb_execution_status_table_name)
    DYNAMODB_URL="https://dynamodb.${REGION}.amazonaws.com"
    INVOICE_STORAGE_BUCKET_NAME=$(terraform output -raw invoice_storage_bucket_name)
    FX_STORAGE_BUCKET_NAME=$(terraform output -raw fx_storage_bucket_name)
    S3_URL="https://s3.${REGION}.amazonaws.com"
    ROLE_ARN=$(terraform output -raw service-role)
    echo "DYNAMODB_TABLE_ENRICHED_TRANSACTION=$DYNAMODB_TABLE_ENRICHED_TRANSACTION"
    echo "DYNAMODB_TABLE_SUMMARISED_MODEL=$DYNAMODB_TABLE_SUMMARISED_MODEL"
    echo "DYNAMODB_TABLE_SUMMARISED_MODEL_BY_DAY=$DYNAMODB_TABLE_SUMMARISED_MODEL_BY_DAY"
    echo "DYNAMODB_TABLE_EXECUTION_STATUS=$DYNAMODB_TABLE_EXECUTION_STATUS"
    echo "DYNAMODB_URL=$DYNAMODB_URL"
    echo "INVOICE_STORAGE_BUCKET_NAME=$INVOICE_STORAGE_BUCKET_NAME"
    echo "FX_STORAGE_BUCKET_NAME=$FX_STORAGE_BUCKET_NAME"
    echo "S3_URL=$S3_URL"
    echo "ROLE_ARN=$ROLE_ARN"
  fi
  cd ../..
fi

K8S_ENDPOINT_LEDGER="ledger.${K8S_NAMESPACE}.svc.cluster.local"
K8S_ENDPOINT_INVOICE="invoice.${K8S_NAMESPACE}.svc.cluster.local"
K8S_ENDPOINT_TEMPLATE="template.${K8S_NAMESPACE}.svc.cluster.local"
K8S_ENDPOINT_PAYMENT="payment.${K8S_NAMESPACE}.svc.cluster.local"
K8S_ENDPOINT_PRODUCT_BRIDGE="product-bridge.${K8S_NAMESPACE}.svc.cluster.local"

HTTP_PORT="8080"
GRPC_PORT="8081"
GRPC_ENDPOINT_LEDGER="${K8S_ENDPOINT_LEDGER}:${GRPC_PORT}"
GRPC_ENDPOINT_INVOICE="${K8S_ENDPOINT_INVOICE}:${GRPC_PORT}"
GRPC_ENDPOINT_TEMPLATE="${K8S_ENDPOINT_TEMPLATE}:${GRPC_PORT}"
GRPC_ENDPOINT_PAYMENT="${K8S_ENDPOINT_PAYMENT}:${GRPC_PORT}"
GRPC_ENDPOINT_PRODUCT_BRIDGE="${K8S_ENDPOINT_PRODUCT_BRIDGE}:${GRPC_PORT}"

K8S_ENDPOINTS=("$K8S_ENDPOINT_LEDGER" "$K8S_ENDPOINT_INVOICE" "$K8S_ENDPOINT_TEMPLATE" "$K8S_ENDPOINT_PAYMENT" "$K8S_ENDPOINT_PRODUCT_BRIDGE")
GRPC_ENDPOINTS=("$GRPC_ENDPOINT_LEDGER" "$GRPC_ENDPOINT_INVOICE" "$GRPC_ENDPOINT_TEMPLATE" "$GRPC_ENDPOINT_PAYMENT" "$GRPC_ENDPOINT_PRODUCT_BRIDGE")

SERVICES_GO_BILLING_INVOICE_HEALTH_URL="http://${K8S_ENDPOINT_INVOICE}:${HTTP_PORT}/health"
SERVICES_GO_BILLING_LEDGER_HEALTH_URL="http://${K8S_ENDPOINT_LEDGER}:${HTTP_PORT}/health"
SERVICES_GO_BILLING_PRODUCT_BRIDGE_HEALTH_URL="http://${K8S_ENDPOINT_PRODUCT_BRIDGE}:${HTTP_PORT}/health"

echo "Kubernetes service endpoints: ${K8S_ENDPOINTS[*]}"
echo "gRPC service endpoints: ${GRPC_ENDPOINTS[*]}"

if [[ $SERVICE_NAME != "payment" && $SERVICE_NAME != "template" ]]; then
  #acting as liquidbase update
  echo 'Liquibase updating....'
  if [[ "$DB_SCHEMA_NAME" != "$DEFAULT_SCHEMA_NAME" ]]; then
    ./gradlew -p "$SERVICE_NAME" executeSql -PliquibaseSql="CREATE SCHEMA IF NOT EXISTS \"$DB_SCHEMA_NAME\"" -Pjdbc.url=$LIQUIBASE_HOST_WITHOUT_SCHEMA -Pjdbc.username=$POSTGRES_USER -Pjdbc.password=$POSTGRES_PASSWORD
  fi
  ./gradlew -p "$SERVICE_NAME" update --no-parallel -Pjdbc.url=$LIQUIBASE_HOST -Pjdbc.username=$POSTGRES_USER -Pjdbc.password=$POSTGRES_PASSWORD -Pjdbc.schema=$DB_SCHEMA_NAME
fi

# Convert the comma separated list of countries to a list of escaped strings
COUNTRIES=$(echo "$COUNTRIES" | sed 's/,/\\,/g')
LEDGER_INTERESTED_CATEGORIES=$(echo "$LEDGER_INTERESTED_CATEGORIES" | sed 's/,/\\,/g')
CDR_INTERESTED_ACCOUNTS=$(echo "$CDR_INTERESTED_ACCOUNTS" | sed 's/,/\\,/g')

echo "Creating k8s namespace"
echo "K8S_NAMESPACE=${K8S_NAMESPACE}"
kubectl create namespace ${K8S_NAMESPACE} --dry-run=client -o yaml | kubectl apply -f -
(kubectl label namespace ${K8S_NAMESPACE} istio-injection=enabled) || true

echo "Creating k8s docker secret"
kubectl -n ${K8S_NAMESPACE} delete secret gitlab-auth || true
kubectl -n ${K8S_NAMESPACE} create secret docker-registry gitlab-auth \
  --docker-server=https://registry.gitlab.com \
  --docker-username=${CI_DEPLOY_USER} \
  --docker-password="${CI_DEPLOY_PASSWORD}" \
  --docker-email=<EMAIL> || true

echo "Converting secrets to base64 format"
POSTGRES_HOST=$(echo -n $POSTGRES_HOST | base64)
POSTGRES_USER=$(echo -n $POSTGRES_USER | base64)
POSTGRES_PASSWORD=$(echo -n $POSTGRES_PASSWORD | base64)
POSTGRES_HOST_REPLICA=$(echo -n $POSTGRES_HOST_REPLICA | base64)
DYNAMODB_TABLE_ENRICHED_TRANSACTION=$(echo -n $DYNAMODB_TABLE_ENRICHED_TRANSACTION | base64)
DYNAMODB_TABLE_SUMMARISED_MODEL=$(echo -n $DYNAMODB_TABLE_SUMMARISED_MODEL | base64)
DYNAMODB_TABLE_SUMMARISED_MODEL_BY_DAY=$(echo -n $DYNAMODB_TABLE_SUMMARISED_MODEL_BY_DAY | base64)
DYNAMODB_TABLE_EXECUTION_STATUS=$(echo -n $DYNAMODB_TABLE_EXECUTION_STATUS | base64)
DYNAMODB_URL=$(echo -n $DYNAMODB_URL | base64)
REGION=$(echo -n $REGION | base64)
EXCHANGE_RATE_FILE_KEY=$(echo -n $EXCHANGE_RATE_FILE_KEY | base64)
TEMPLATE_BUCKET_NAME=$(echo -n $TEMPLATE_BUCKET_NAME | base64)
TEMPLATE_BUCKET_ARN=$(echo -n $TEMPLATE_BUCKET_ARN | base64)
INVOICE_STORAGE_BUCKET_NAME=$(echo -n $INVOICE_STORAGE_BUCKET_NAME | base64)
FX_STORAGE_BUCKET_NAME=$(echo -n $FX_STORAGE_BUCKET_NAME | base64)
S3_URL=$(echo -n $S3_URL | base64)

# Extract the image name
IMAGE_NAME="${DOCKER_IMAGE%:*}"
# Extract the image tag
TAG="${DOCKER_IMAGE##*:}"

echo "Running helm deployment task"
#Helm chart location
CHART_NAME="./deployments/helm/billing-service"

echo "Running helm dependency update"
helm dependency update $CHART_NAME

echo "helm search repo helm-library"
helm search repo helm-library

echo "Installing helm chart"
VALUE_FILE=$CHART_NAME/$SERVICE_NAME-values.yaml

if [[ "$SERVICE_NAME" == "invoice" || "$SERVICE_NAME" == "payment" ]]; then
  ADDITIONAL_SERVICE_ACCOUNT_SET_LINE="--set serviceAccount.annotations.k8sk\.amazonaws\.com\/role-arn=${ROLE_ARN}"
fi

helm upgrade --install --wait --timeout 10m \
   $SERVICE_NAME $CHART_NAME \
   -f  $VALUE_FILE \
   -n $K8S_NAMESPACE \
   --set "secrets.db.enabled=true" \
     --set "secrets.db.data.datasources\.default\.url=${POSTGRES_HOST}" \
     --set "secrets.db.data.datasources\.default\.username=${POSTGRES_USER}" \
     --set "secrets.db.data.datasources\.default\.password=${POSTGRES_PASSWORD}" \
     --set "secrets.db.data.datasources\.replica\.url=${POSTGRES_HOST_REPLICA}" \
     --set "secrets.db.data.datasources\.replica\.username=${POSTGRES_USER}" \
     --set "secrets.db.data.datasources\.replica\.password=${POSTGRES_PASSWORD}" \
     --set "secrets.db.data.dynamodb\.table\.enriched-transaction=${DYNAMODB_TABLE_ENRICHED_TRANSACTION}" \
     --set "secrets.db.data.dynamodb\.table\.summarised-model=${DYNAMODB_TABLE_SUMMARISED_MODEL}" \
     --set "secrets.db.data.dynamodb\.table\.summarised-model-by-day=${DYNAMODB_TABLE_SUMMARISED_MODEL_BY_DAY}" \
     --set "secrets.db.data.dynamodb\.table\.execution-status=${DYNAMODB_TABLE_EXECUTION_STATUS}" \
     --set "secrets.db.data.dynamodb\.url=${DYNAMODB_URL}" \
     --set "secrets.db.data.dynamodb\.region=${REGION}" \
     --set "secrets.db.data.exchange-rates\.file\.key=${EXCHANGE_RATE_FILE_KEY}" \
     --set "secrets.db.data.micronaut\.object-storage\.aws\.template\.bucket=${TEMPLATE_BUCKET_NAME}" \
     --set "secrets.db.data.micronaut\.object-storage\.aws\.template\.bucket\.arn=${TEMPLATE_BUCKET_ARN}" \
     --set "secrets.db.data.s3\.bucket\.invoice-storage=${INVOICE_STORAGE_BUCKET_NAME}" \
     --set "secrets.db.data.s3\.bucket\.exchange-rates=${FX_STORAGE_BUCKET_NAME}" \
     --set "secrets.db.data.s3\.url=${S3_URL}" \
     --set "secrets.db.data.s3\.region=${REGION}" \
     --set "configmaps.configmap.enabled=true" \
     --set "configmaps.configmap.data.kafka_workspace_billing=${KAFKA_WORKSPACE_BILLING}" \
     --set "configmaps.configmap.data.kafka_workspace_finance_reporting=${KAFKA_WORKSPACE_FINANCE_REPORTING}" \
     --set "configmaps.configmap.data.kafka_workspace_audit=${KAFKA_WORKSPACE_AUDIT}" \
     --set "configmaps.configmap.data.kafka_truststore_sg=${KAFKA_TRUSTSTORE_SG}" \
     --set "configmaps.configmap.data.kafka_truststore_au=${KAFKA_TRUSTSTORE_AU}" \
     --set "configmaps.configmap.data.kafka_group_id=${KAFKA_GROUP_ID}" \
     --set "configmaps.configmap.data.kafka_authentication_configuration=${KAFKA_AUTHENTICATION_CONFIGURATION}" \
     --set "configmaps.configmap.data.kafka_topic_suffix=${KAFKA_TOPIC_SUFFIX}" \
     --set "configmaps.configmap.data.environment=${ENVIRONMENT}" \
     --set "configmaps.configmap.data.countries=${COUNTRIES}" \
     --set "configmaps.configmap.data.workspace\.name=${TERRAFORM_WORKSPACE}" \
     --set "configmaps.configmap.data.country\.name=${DEPLOYMENT_COUNTRY}" \
     --set "configmaps.configmap.data.billing\.cycle\.interval=${BILLING_CYCLE_INTERVAL}" \
     --set "configmaps.configmap.data.finance\.email=${FINANCE_EMAIL}" \
     --set "configmaps.configmap.data.invoice\.notification\.email=${INVOICE_NOTIFICATION_EMAIL}" \
     --set "configmaps.configmap.data.ledger\.interested\.categories=${LEDGER_INTERESTED_CATEGORIES}" \
     --set "configmaps.configmap.data.cdr\.interested\.accounts=${CDR_INTERESTED_ACCOUNTS}" \
     --set-string "configmaps.configmap.data.micronaut\.server\.port=${HTTP_PORT}" \
     --set-string "configmaps.configmap.data.grpc\.server\.port=${GRPC_PORT}" \
     --set "configmaps.configmap.data.grpc\.channels\.ledger\.address=${GRPC_ENDPOINT_LEDGER}" \
     --set "configmaps.configmap.data.grpc\.channels\.invoice\.address=${GRPC_ENDPOINT_INVOICE}" \
     --set "configmaps.configmap.data.grpc\.channels\.payment\.address=${GRPC_ENDPOINT_INVOICE}" \
     --set "configmaps.configmap.data.platform_mapping=${PLATFORM_MAPPING}" \
     --set "configmaps.configmap.data.ci\.commit\.ref\.name=${CI_COMMIT_REF_NAME}" \
     --set "configmaps.configmap.data.ci\.commit\.ref\.slug=${CI_COMMIT_REF_SLUG}" \
     --set "configmaps.configmap.data.ci\.commit\.sha=${CI_COMMIT_SHA}" \
     --set "configmaps.configmap.data.ci\.commit\.sha\.short=${CI_COMMIT_SHORT_SHA}" \
     --set "configmaps.configmap.data.symbio\.account\.uuid\.au=${SYMBIO_ACCOUNT_UUID_AU}" \
     --set "configmaps.configmap.data.symbio\.account\.uuid\.nz=${SYMBIO_ACCOUNT_UUID_NZ}" \
     --set "configmaps.configmap.data.symbio\.account\.uuid\.my=${SYMBIO_ACCOUNT_UUID_MY}" \
     --set "configmaps.configmap.data.symbio\.account\.uuid\.sg=${SYMBIO_ACCOUNT_UUID_SG}" \
     --set "configmaps.configmap.data.symbio\.account\.uuid\.tw=${SYMBIO_ACCOUNT_UUID_TW}" \
     --set "configmaps.configmap.data.services\.go\.plexus\.notification\.url=${SERVICES_GO_PLEXUS_NOTIFICATION_URL}" \
     --set "configmaps.configmap.data.services\.go\.plexus\.notification\.environment=${SERVICES_GO_PLEXUS_NOTIFICATION_ENVIRONMENT}" \
     --set "configmaps.configmap.data.services\.go\.plexus\.audit\.url=${SERVICES_GO_PLEXUS_AUDIT_URL}" \
     --set "configmaps.configmap.data.services\.my\.plexus\.gateway\.token=${SERVICES_MY_PLEXUS_GATEWAY_TOKEN}" \
     --set "configmaps.configmap.data.services\.my\.plexus\.gateway\.url=${SERVICES_MY_PLEXUS_GATEWAY_URL}" \
     --set "configmaps.configmap.data.services\.my\.sonar\.customer\.bridge\.url=${SERVICES_MY_SONAR_BRIDGE_URL}" \
     --set "configmaps.configmap.data.services\.my\.sonar\.customer\.bridge\.token=${SERVICES_MY_SONAR_BRIDGE_TOKEN}" \
     --set "configmaps.configmap.data.services\.sg\.plexus\.gateway\.token=${SERVICES_SG_PLEXUS_GATEWAY_TOKEN}" \
     --set "configmaps.configmap.data.services\.sg\.plexus\.gateway\.url=${SERVICES_SG_PLEXUS_GATEWAY_URL}" \
     --set "configmaps.configmap.data.services\.sg\.sonar\.customer\.bridge\.url=${SERVICES_SG_SONAR_BRIDGE_URL}" \
     --set "configmaps.configmap.data.services\.sg\.sonar\.customer\.bridge\.token=${SERVICES_SG_SONAR_BRIDGE_TOKEN}" \
     --set "configmaps.configmap.data.services\.my\.plexus\.account\.health\.url=${SERVICES_MY_PLEXUS_ACCOUNT_HEALTH_URL}" \
     --set "configmaps.configmap.data.services\.sg\.plexus\.account\.health\.url=${SERVICES_SG_PLEXUS_ACCOUNT_HEALTH_URL}" \
     --set "configmaps.configmap.data.services\.go\.plexus\.customer\.health\.url=${SERVICES_GO_PLEXUS_CUSTOMER_HEALTH_URL}" \
     --set "configmaps.configmap.data.services\.go\.billing\.invoice\.health\.url=${SERVICES_GO_BILLING_INVOICE_HEALTH_URL}" \
     --set "configmaps.configmap.data.services\.go\.billing\.ledger\.health\.url=${SERVICES_GO_BILLING_LEDGER_HEALTH_URL}" \
     --set "configmaps.configmap.data.services\.go\.billing\.product\.bridge\.health\.url=${SERVICES_GO_BILLING_PRODUCT_BRIDGE_HEALTH_URL}" \
     --set "configmaps.configmap.data.services\.au\.plexus\.gateway\.token=${SERVICES_AU_PLEXUS_GATEWAY_TOKEN}" \
     --set "configmaps.configmap.data.services\.au\.plexus\.gateway\.url=${SERVICES_AU_PLEXUS_GATEWAY_URL}" \
     --set "configmaps.configmap.data.services\.au\.sonar\.customer\.bridge\.url=${SERVICES_AU_SONAR_BRIDGE_URL}" \
     --set "configmaps.configmap.data.services\.au\.sonar\.customer\.bridge\.token=${SERVICES_AU_SONAR_BRIDGE_TOKEN}" \
     --set "configmaps.configmap.data.services\.au\.plexus\.account\.health\.url=${SERVICES_AU_PLEXUS_ACCOUNT_HEALTH_URL}" \
     --set-string "configmaps.configmap.data.mixin\.audit\.enabled=${MIXIN_AUDIT_ENABLED:-true}" \
     --set "configmaps.configmap.data.elastic\.apm\.secret\.token=${ELASTIC_APM_SECRET_TOKEN}" \
     --set "configmaps.configmap.data.elastic\.apm\.secret\.url=${ELASTIC_APM_SERVER_URL}" \
     --set "containers.$SERVICE_NAME.image.repository=${IMAGE_NAME}" \
     --set "containers.$SERVICE_NAME.image.tag=${TAG}" \
     --set "service.main.selector.app=$SERVICE_NAME" \
     --set "ingress.services.$SERVICE_NAME.host=${INGRESS_HOST_NAME}" \
     $ADDITIONAL_SERVICE_ACCOUNT_SET_LINE

echo "Helm deployment task completed"
