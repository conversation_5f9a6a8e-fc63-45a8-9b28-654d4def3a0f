data "terraform_remote_state" "networking" {
  workspace = var.remote_workspace
  backend   = "s3"

  config = {
    bucket = var.remote_state_bucket
    region = var.remote_state_region
    key    = local.networking_key
  }
}

data "terraform_remote_state" "security_groups" {
  workspace = var.remote_workspace
  backend   = "s3"

  config = {
    bucket = var.remote_state_bucket
    region = var.remote_state_region
    key    = local.security_group_key
  }
}

data "terraform_remote_state" "k8s" {
  backend = "s3"
  config  = {
    bucket = "mnfgroup-terraform-remotestate"
    region = var.remote_state_region
    key    = var.k8s_tfstate_key[var.k8s_cluster_name]
  }
}

data "terraform_remote_state" "billing-infrastructure" {
  workspace = terraform.workspace
  backend   = "s3"
  config    = {
    bucket               = var.remote_state_bucket
    region               = var.remote_state_region
    key                  = "tfstate"
    workspace_key_prefix = local.billing_infrastructure_key
  }
}

data "aws_iam_role" "rds_monitoring_role" {
  name = "rds-monitoring-role"
}

data "aws_caller_identity" "current" {}

locals {
  // Networking Key
  networking_key = var.aws_region == "ap-southeast-2" ? "networking/tfstate" : var.country == "sg" ? "networking/${var.aws_region}/tfstate" : "networking/${var.aws_region}/${var.country}/tfstate"

  // Security Group Key
  security_group_key = var.aws_region == "ap-southeast-2" ? "security_groups/tfstate" : var.country == "sg" ? "security_groups/${var.aws_region}/tfstate" : "security_groups/${var.aws_region}/${var.country}/tfstate"

  // Infrastructure Key
  billing_infrastructure_key = "billing/infrastructure"
}