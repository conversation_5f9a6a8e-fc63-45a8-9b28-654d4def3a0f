output "data_terraform_remote_state_networking" {
  value = data.terraform_remote_state.networking
}

output "data_terraform_remote_state_security_groups" {
  value = data.terraform_remote_state.security_groups
}

output "data_terraform_remote_state_k8s" {
  value = data.terraform_remote_state.k8s
}

output "data_aws_iam_role_rds_monitoring_role" {
  value = data.aws_iam_role.rds_monitoring_role
}

output "data_aws_caller_identity_current" {
  value = data.aws_caller_identity.current
}

output "data_host_name" {
  value = data.terraform_remote_state.billing-infrastructure.outputs.hosted_zone_name
}

output "data_host_id" {
  value = data.terraform_remote_state.billing-infrastructure.outputs.hosted_zone_id
}

output "data_nlb_host" {
  value = data.terraform_remote_state.billing-infrastructure.outputs.nlb_host
}

output "data_nlb_zone_id" {
  value = data.terraform_remote_state.billing-infrastructure.outputs.nlb_zone_id
}

output "data_haproxy_nginx_ingress" {
  value = data.terraform_remote_state.billing-infrastructure.outputs.haproxy_nginx_ingress
}

output "kafka_authentication_configuration" {
  sensitive = true
  value     = var.kafka_authentication_configuration
}