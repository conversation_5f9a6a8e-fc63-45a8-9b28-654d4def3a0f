variable "aws_region" {
  type        = string
  description = "The region of aws infra"
}

variable "remote_workspace" {
  type        = string
  description = "Terraform remote workspace"
}

variable "remote_state_region" {
  description = "Global remote state s3 bucket region, same for all projects"
}

variable "remote_state_bucket" {
  description = "Global remote state bucket, same for all projects"
}

variable "country" {
  description = "Country variable for different regions for deployment"
}

variable k8s_tfstate_key {
  type    = map(string)
  default = {
    "sg-nonprod-k8s"  = "core-k8sk/sg-nonprod/iam_role_for_serviceaccount/terraform.tfstate"
    "sg-prod-k8s"     = "core-k8sk/sg-prod/iam_role_for_serviceaccount/terraform.tfstate"
    "syd-nonprod-k8s" = "core-k8sk/syd-nonprod/iam_role_for_serviceaccount/terraform.tfstate"
    "syd-prod-k8s"    = "core-k8sk/syd-nonprod/iam_role_for_serviceaccount/terraform.tfstate"
  }
}

variable "k8s_cluster_name" {
  default = "sg-nonprod-k8s"
}

variable "kafka_authentication_configuration" {
  type        = string
  description = "Configuration for kafka authentication to clusters"
  sensitive   = true
}