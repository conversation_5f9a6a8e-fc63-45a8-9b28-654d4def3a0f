data "aws_rds_certificate" "ca-certificate" {
  id = "rds-ca-rsa2048-g1"
}

resource "aws_rds_cluster" "rds_cluster" {
  count                = var.production ? 1 : 0
  cluster_identifier   = replace("${substr(var.prefix, 0, 60)}-rds", "--", "-")
  engine               = var.rds_engine
  engine_version       = var.rds_engine_version
  database_name        = var.database_name
  db_subnet_group_name = var.db_subnet_group_name
  master_username      = var.rds_username
  master_password      = var.rds_password
  port                 = var.rds_port

  vpc_security_group_ids = [
    var.rds_sg.id,
    var.trs_sg_allow_office_private_postgres_id
  ]

  backup_retention_period         = var.rds_retention_period_days
  preferred_backup_window         = "13:00-14:00"
  ## Weekend has the lowest amount of traffic in production, as a result this should be a good maintenance window
  ## for nonprod there is a DB shutdown period, we can only perform updates when DBs are up
  preferred_maintenance_window    = var.production ? "sat:10:00-sat:12:00" : "fri:06:00-fri:07:00"
  kms_key_id                      = aws_kms_key.kms_db_key.arn
  storage_encrypted               = var.rds_storage_encrypted
  deletion_protection             = var.rds_deletion_protection
  final_snapshot_identifier       = "${var.prefix}-cluster-final-snapshot-${formatdate("YYYY-MM-DD'T'hh-mm-ssZ", timestamp())}"
  skip_final_snapshot             = !var.rds_snapshot_on_deletion
  enabled_cloudwatch_logs_exports = ["postgresql"]
  availability_zones              = ["${var.aws_region}a", "${var.aws_region}b", "${var.aws_region}c"]

  tags = merge(
    var.common_tags,
    {
      "Name"           = "${var.prefix}-rds-cluster"
      "persist"        = var.persist_db
      "schedule_start" = var.auto_start
    }
  )

  lifecycle {
    ignore_changes = [
      engine_version
    ]
  }

  depends_on = [var.rds_sg]
}

resource "aws_rds_cluster_instance" "rds_cluster_instance" {
  count                        = var.production ? var.rds_instance_count : 0
  identifier                   = replace("${substr(var.prefix, 0, 52)}-instance-${count.index}", "--", "-")
  ca_cert_identifier           = data.aws_rds_certificate.ca-certificate.id
  engine                       = aws_rds_cluster.rds_cluster[0].engine
  cluster_identifier           = aws_rds_cluster.rds_cluster[0].id
  instance_class               = var.rds_instance_class
  availability_zone            = tolist(aws_rds_cluster.rds_cluster[0].availability_zones)[count.index]
  performance_insights_enabled = var.rds_enable_performance_insights
  monitoring_interval          = var.rds_enhanced_monitoring_interval
  monitoring_role_arn          = var.rds_enhanced_monitoring_interval == 0 ? "" : var.iam_rds_monitoring_role
  promotion_tier               = count.index
  auto_minor_version_upgrade   = var.rds_enable_auto_minor_version_upgrade
  preferred_maintenance_window = var.production ? "sat:12:00-sat:14:00" : "fri:07:00-fri:08:00"

  tags = merge(
    var.common_tags,
    {
      "Name"           = "${var.prefix}-rds-instance-${count.index}"
      "persist"        = var.persist_db
      "schedule_start" = var.auto_start
    }
  )
}

resource "aws_db_instance" "rds_instance" {
  count                        = var.production ? 0 : 1
  identifier                   = replace("${substr(var.prefix, 0, 52)}-instance", "--", "-")
  ca_cert_identifier           = data.aws_rds_certificate.ca-certificate.id
  engine                       = var.rds_engine
  engine_version               = var.rds_engine_version
  instance_class               = var.rds_instance_class
  allocated_storage            = var.rds_allocated_storage
  storage_type                 = var.rds_storage_type
  storage_encrypted            = var.rds_storage_encrypted
  backup_retention_period      = var.rds_retention_period_days
  db_name                      = var.database_name
  username                     = var.rds_username
  password                     = var.rds_password
  deletion_protection          = var.rds_deletion_protection
  kms_key_id                   = aws_kms_key.kms_db_key.arn
  performance_insights_enabled = var.rds_enable_performance_insights
  monitoring_interval          = var.rds_enhanced_monitoring_interval
  monitoring_role_arn          = var.rds_enhanced_monitoring_interval == 0 ? "" : var.iam_rds_monitoring_role
  auto_minor_version_upgrade   = var.rds_enable_auto_minor_version_upgrade
  maintenance_window           = var.production ? "sat:12:00-sat:14:00" : "fri:07:00-fri:08:00"
  db_subnet_group_name         = var.db_subnet_group_name
  vpc_security_group_ids       = [
    var.rds_sg.id,
    var.trs_sg_allow_office_private_postgres_id
  ]
  final_snapshot_identifier = "${var.prefix}-rds-final-snapshot-${formatdate("YYYY-MM-DD'T'hh-mm-ssZ", timestamp())}"
  skip_final_snapshot       = !var.rds_snapshot_on_deletion
  multi_az                  = var.multi_az_enabled
  tags                      = merge(
    var.common_tags,
    {
      "name"           = "${var.prefix}-rds-instance"
      "environment"    = terraform.workspace
      "persist"        = var.persist_db
      "schedule_start" = var.auto_start
    }
  )
  copy_tags_to_snapshot = true
  depends_on            = [var.rds_sg]
}

resource "aws_kms_key" "kms_db_key" {
  description             = var.kms_key_description
  deletion_window_in_days = 7

  policy = <<POLICY
  {
    "Version": "2012-10-17",
    "Id": "key-default-1",
    "Statement": [
      {
          "Sid": "Enable IAM User Permissions",
          "Effect": "Allow",
          "Principal": {
              "AWS": "arn:aws:iam::${var.kms_caller_identity_current_account_id}:root"
          },
          "Action": "kms:*",
          "Resource": "*"
      }
    ]
  }
POLICY

  tags = merge(
    var.common_tags,
    {
      "Name" = "${var.prefix}-kms-key"
    }
  )
}

resource "aws_kms_alias" "kms_db_key_alias" {
  name = "alias/${substr(var.prefix, 0, 236)}-kms-key-alias"
  target_key_id = aws_kms_key.kms_db_key.key_id
}