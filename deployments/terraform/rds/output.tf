##################################################################################
# RDS Properties
##################################################################################
output "rds_url" {
  value = var.production ? "${aws_rds_cluster.rds_cluster[0].endpoint}:${aws_rds_cluster.rds_cluster[0].port}" : aws_db_instance.rds_instance[0].endpoint
}

output "rds_username" {
  sensitive = true
  value     = var.production ? aws_rds_cluster.rds_cluster[0].master_username : aws_db_instance.rds_instance[0].username
}

output "rds_password" {
  sensitive = true
  value     = var.production ? aws_rds_cluster.rds_cluster[0].master_password : aws_db_instance.rds_instance[0].password
}

output "rds_database_name" {
  value = var.production ? aws_rds_cluster.rds_cluster[0].database_name : aws_db_instance.rds_instance[0].db_name
}

output "rds_url_replica" {
  value = var.production ? "${aws_rds_cluster.rds_cluster[0].reader_endpoint}:${aws_rds_cluster.rds_cluster[0].port}" : aws_db_instance.rds_instance[0].endpoint
}