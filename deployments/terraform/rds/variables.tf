variable "common_tags" {
  description = "Common tags for resources"
}

variable "aws_region" {
  type        = string
  description = "The region of aws infra"
  default     = "ap-southeast-1"
}

variable "prefix" {
  description = "Prefix used for naming"
}

variable "production" {
  description = "If for prod or not"
}

variable "rds_engine" {
  type        = string
  description = "The underlying database engine for the RDS instance."
}

variable "rds_engine_version" {
  type        = string
  description = "The version of the database engine for the RDS instance."
}

variable "database_name" {
  type        = string
  description = "The name for the database"
}

variable "db_subnet_group_name" {
  type        = string
  description = "The name of the db subnet group"
}

variable "rds_username" {
  type        = string
  description = "service username for RDS"
}

variable "rds_password" {
  type        = string
  description = "service password for RDS"
  sensitive   = true
}

variable "rds_port" {
  type        = number
  description = "Port number for RDS"
  default     = 5432
}

variable "rds_sg" {
  description = "RDS Security Group"
}

variable "trs_sg_allow_office_private_postgres_id" {
  description = "Terraform remote state security group allow office private postgres"
}

variable "multi_az_enabled" {
  type        = string
  default     = "false"
  description = "Whether to deploy this database across multiple AZ's"
}

variable "rds_retention_period_days" {
  type        = number
  description = "The number of days to retain snapshots."
  default     = 7
}

variable "kms_key_description" {
  description = "KMS Key Description"
}

variable "rds_storage_encrypted" {
  type        = bool
  description = "Whether the database instance is encrypted at rest."
  default     = true
}

variable "rds_deletion_protection" {
  type        = string
  description = "A flag to determine if the RDS instance can be deleted on destroyed"
}

variable "rds_snapshot_on_deletion" {
  type        = bool
  description = "Whether to create a snapshot of the databases storage upon deletion of the RDS instance."
}

variable "persist_db" {
  type        = string
  default     = "false"
  description = "Whether to prevent the auto shutdown task from shutting down this RDS instance"
}

variable "auto_start" {
  type        = string
  default     = "true"
  description = "Whether to have this RDS instance automatically started up each morning"
}

variable "rds_instance_count" {
  description = "Number of instances to create for Aurora DB Cluster"
  default     = 1
}

variable "rds_instance_class" {
  type        = string
  description = "The instance class for the RDS instance."
}

variable "rds_allocated_storage" {
  type        = string
  description = "Allocated storage for the RDS instance."
}

variable "rds_storage_type" {
  type        = string
  description = "The underlying storage mechanism for the RDS instance."
}

variable "rds_enable_performance_insights" {
  type        = bool
  description = "Whether the RDS instance monitors performance."
}

variable "rds_enhanced_monitoring_interval" {
  type        = number
  description = "Flag to enable enhanced monitoring for DB"
  default     = 0
}

variable "iam_rds_monitoring_role" {
  description = "aws_iam_role monitoring role for rds"
}

variable "rds_enable_auto_minor_version_upgrade" {
  type        = bool
  description = "Allow automatic database minor version upgrades to be applied by RDS."
  default     = false
}

variable "kms_caller_identity_current_account_id" {
  description = "Account in which terraform is authorized"
}