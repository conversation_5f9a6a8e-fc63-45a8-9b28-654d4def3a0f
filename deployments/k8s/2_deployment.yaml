# kubetpl:syntax:go-template
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .SERVICE_NAME }}
spec:
  replicas: {{ get "K8S_REPLICAS" 1 }}
  selector:
    matchLabels:
      app: {{ .SERVICE_NAME }}
  template:
    metadata:
      labels:
        app: {{ .SERVICE_NAME }}
    spec:
      serviceAccountName: billing-{{ .SERVICE_NAME }}
      containers:
        - name: {{ .SERVICE_NAME }}
          image: {{ .IMAGE }}
          imagePullPolicy: IfNotPresent
          securityContext:
            runAsUser: 1200
            privileged: false
          ports:
            - containerPort: 8080
          {{ if regexMatch "^local$" .ENVIRONMENT }}
          {{ else }}
          readinessProbe:
            httpGet:
              path: /health/readiness
              port: 8080
              httpHeaders:
                # Basic authentication for 'k8s-management' service account
                - name: "Authorization"
                  value: "Basic azhzLW1hbmFnZW1lbnQ6dFFQZmhkek5icUQ2QmI0TkM3QjJSZnYzV0pNRncyeHY="
            initialDelaySeconds: 30
            periodSeconds: 3
            failureThreshold: 30
          livenessProbe:
            httpGet:
              path: /health/liveness
              port: 8080
              httpHeaders:
                # Basic authentication for 'k8s-management' service account
                - name: "Authorization"
                  value: "Basic azhzLW1hbmFnZW1lbnQ6dFFQZmhkek5icUQ2QmI0TkM3QjJSZnYzV0pNRncyeHY="
            initialDelaySeconds: 90
            periodSeconds: 10
            failureThreshold: 3
          {{ end }}
          resources:
            requests:
              memory: {{ get "K8S_MEMORY_REQUEST" "3048Mi" }}
              cpu: {{ get "K8S_CPU_REQUEST" "512m" }}
            limits:
              memory: {{ get "K8S_MEMORY_LIMIT" "3048Mi" }}
#              cpu: {{ get "K8S_CPU_LIMIT" "512m" }} # intentionally commented out as per: https://home.robusta.dev/blog/kubernetes-memory-limit
          env:
            - name: env
              valueFrom:
                configMapKeyRef:
                  name: {{ .SERVICE_NAME }}
                  key: environment
            - name: SYMBIO_ACCOUNT_UUID_AU
              valueFrom:
                configMapKeyRef:
                  name: {{ .SERVICE_NAME }}
                  key: symbio.account.uuid.au
            - name: SYMBIO_ACCOUNT_UUID_NZ
              valueFrom:
                configMapKeyRef:
                  name: {{ .SERVICE_NAME }}
                  key: symbio.account.uuid.nz
            - name: SYMBIO_ACCOUNT_UUID_MY
              valueFrom:
                configMapKeyRef:
                  name: {{ .SERVICE_NAME }}
                  key: symbio.account.uuid.my
            - name: SYMBIO_ACCOUNT_UUID_SG
              valueFrom:
                configMapKeyRef:
                  name: {{ .SERVICE_NAME }}
                  key: symbio.account.uuid.sg
            - name: SYMBIO_ACCOUNT_UUID_TW
              valueFrom:
                configMapKeyRef:
                  name: {{ .SERVICE_NAME }}
                  key: symbio.account.uuid.tw
            - name: KAFKA_AUTHENTICATION_CONFIGURATION
              valueFrom:
                configMapKeyRef:
                  name: {{ .SERVICE_NAME }}
                  key: kafka_authentication_configuration
            - name: KAFKA_TRUSTSTORE_SG
              valueFrom:
                configMapKeyRef:
                  name: {{ .SERVICE_NAME }}
                  key: kafka_truststore_sg
            - name: KAFKA_TRUSTSTORE_AU
              valueFrom:
                configMapKeyRef:
                  name: {{ .SERVICE_NAME }}
                  key: kafka_truststore_au
            - name: KAFKA_GROUP_ID
              valueFrom:
                configMapKeyRef:
                  name: {{ .SERVICE_NAME }}
                  key: kafka_group_id
            - name: KAFKA_TOPIC_SUFFIX
              valueFrom:
                configMapKeyRef:
                  name: {{ .SERVICE_NAME }}
                  key: kafka_topic_suffix
            - name: KAFKA_WORKSPACE_BILLING
              valueFrom:
                configMapKeyRef:
                  name: {{ .SERVICE_NAME }}
                  key: kafka_workspace_billing
            - name: KAFKA_WORKSPACE_FINANCE_REPORTING
              valueFrom:
                configMapKeyRef:
                  name: {{ .SERVICE_NAME }}
                  key: kafka_workspace_finance_reporting
            - name: ELASTIC_APM_SECRET_TOKEN
              valueFrom:
                configMapKeyRef:
                  name: {{ .SERVICE_NAME }}
                  key: elastic.apm.secret.token
            - name: ELASTIC_APM_SERVER_URL
              valueFrom:
                configMapKeyRef:
                  name: {{ .SERVICE_NAME }}
                  key: elastic.apm.secret.url
            - name: PLATFORM_MAPPING
              valueFrom:
                configMapKeyRef:
                  name: {{ .SERVICE_NAME }}
                  key: platform_mapping
            - name: CI_COMMIT_REF_NAME
              valueFrom:
                configMapKeyRef:
                  name: {{ .SERVICE_NAME }}
                  key: ci.commit.ref.name
            - name: CI_COMMIT_REF_SLUG
              valueFrom:
                configMapKeyRef:
                  name: {{ .SERVICE_NAME }}
                  key: ci.commit.ref.slug
            - name: CI_COMMIT_SHA
              valueFrom:
                configMapKeyRef:
                  name: {{ .SERVICE_NAME }}
                  key: ci.commit.sha
            - name: CI_COMMIT_SHORT_SHA
              valueFrom:
                configMapKeyRef:
                  name: {{ .SERVICE_NAME }}
                  key: ci.commit.sha.short