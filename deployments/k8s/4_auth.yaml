# kubetpl:syntax:go-template
#The following role/role binding is to allow Micronaut to access k8s configmaps/secrets within the deployed namespace
kind: Role
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: {{ .SERVICE_NAME }}-auth
  namespace: {{ .K8S_NAMESPACE }}
rules:
  - apiGroups: [""]
    resources: ["services", "endpoints", "configmaps", "secrets", "pods"]
    verbs: ["get", "watch", "list"]
---
kind: RoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: {{ .SERVICE_NAME }}-auth
  namespace: {{ .K8S_NAMESPACE }}
subjects:
  - kind: ServiceAccount
    name: billing-{{ .SERVICE_NAME }}
    namespace: {{ .K8S_NAMESPACE }}
roleRef:
  kind: Role
  name: {{ .SERVICE_NAME }}-auth
  apiGroup: rbac.authorization.k8s.io
