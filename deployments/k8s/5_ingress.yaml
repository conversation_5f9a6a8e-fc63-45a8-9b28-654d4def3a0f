# kubetpl:syntax:go-template

apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    external-dns.alpha.kubernetes.io/ingress-hostname-source: annotation-only
  name: {{ .SERVICE_NAME }}
spec:
  ingressClassName: nginx
  rules:
    - http:
        paths:
          - backend:
              service:
                name: {{ .SERVICE_NAME }}
                port:
                  number: 8080
            path: /
            pathType: Prefix
      host: {{ .INGRESS_HOST_NAME }}
