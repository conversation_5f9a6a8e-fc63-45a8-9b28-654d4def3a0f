#!/bin/bash

services="invoice ledger payment product-bridge"

for SERVICE_NAME in $services; do
  echo "SERVICE_NAME=$SERVICE_NAME"
  echo "K8S_NAMESPACE=$K8S_NAMESPACE"

  # Check if the Helm chart exists
  if helm list --namespace "$K8S_NAMESPACE" | grep -q "$SERVICE_NAME"; then
    # Uninstall the Helm chart
    helm uninstall "$SERVICE_NAME" --namespace "$K8S_NAMESPACE"

    # Check if the Helm chart was uninstalled successfully
    if [ $? -eq 0 ]; then
      echo "Helm chart $SERVICE_NAME uninstalled successfully from namespace '$K8S_NAMESPACE'."
    else
      echo "Failed to uninstall Helm chart $SERVICE_NAME from namespace '$K8S_NAMESPACE'."
    fi
  else
    echo "Helm chart $SERVICE_NAME does not exist in namespace '$K8S_NAMESPACE'."
  fi
done

echo "***** Tearing down namespace ${K8S_NAMESPACE}"
kubectl delete namespace "${K8S_NAMESPACE}" || true