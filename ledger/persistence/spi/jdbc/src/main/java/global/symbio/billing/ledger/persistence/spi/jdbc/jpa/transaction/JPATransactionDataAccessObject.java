package global.symbio.billing.ledger.persistence.spi.jdbc.jpa.transaction;

import global.symbio.billing.core.country.persistence.api.CountryDataAccessObject;
import global.symbio.billing.core.country.persistence.spi.jdbc.jpa.JPACountryDataAccessObject;
import global.symbio.billing.core.currency.persistence.api.CurrencyDataAccessObject;
import global.symbio.billing.core.currency.persistence.spi.jdbc.jpa.JPACurrencyDataAccessObject;
import global.symbio.billing.core.persistence.api.Types;
import global.symbio.billing.ledger.persistence.api.transaction.TransactionDataAccessObject;
import global.symbio.billing.ledger.persistence.api.transaction.category.TransactionCategoryDataAccessObject;
import global.symbio.billing.ledger.persistence.spi.jdbc.jpa.transaction.category.JPATransactionCategoryDataAccessObject;
import io.micronaut.core.annotation.Introspected;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Generated;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.UUID;

@Entity
@Table(name = "transaction_ledger", indexes = {@Index(columnList = "credit"), @Index(columnList = "debit"), @Index(columnList = "currency"), @Index(columnList = "category"), @Index(columnList = "country"), @Index(columnList = "timestamp")})
@Getter @Setter
@Introspected(includedAnnotations = Entity.class)
public class JPATransactionDataAccessObject extends TransactionDataAccessObject {

    @Id
    @Nonnull
    @Column(name = "id", unique = true, nullable = false, updatable = false)
    private UUID identifier;

    @Nonnull
    @Generated
    @Column(name = "sequence", unique = true, nullable = false, updatable = false, insertable = false)
    private Long sequence;

    @Nonnull
    @ManyToOne
    @JoinColumn(name = "currency")
    private JPACurrencyDataAccessObject currency;

    @Nonnull
    @ManyToOne
    @JoinColumn(name = "category")
    private JPATransactionCategoryDataAccessObject category;

    @Nonnull
    @Column(name = "amount", nullable = false, updatable = false, scale = 32, precision = 12)
    private BigDecimal amount;

    @Nonnull
    @Column(name = "taxation", nullable = false, updatable = false, scale = 32, precision = 12)
    private BigDecimal taxation;

    @Nonnull
    @Column(name = "debit", nullable = false, updatable = false)
    private UUID debit;

    @Nonnull
    @Column(name = "credit", nullable = false, updatable = false)
    private UUID credit;

    @Nullable
    @Column(name = "description", nullable = true, updatable = false, length = 128)
    private String description;

    @Nonnull
    @Column(name = "ref", nullable = false, updatable = false, length = 512)
    private String ref;

    @Nonnull
    @ManyToOne
    @JoinColumn(name = "country")
    private JPACountryDataAccessObject country;

    @Nonnull
    @Column(name = "timestamp", nullable = false, updatable = false, insertable = false)
    private ZonedDateTime timestamp;

    @Nullable
    @Column(name = "dwh_reporting_timestamp", nullable = true)
    private ZonedDateTime reportedTimestamp;

    @Nonnull
    @Override
    public TransactionDataAccessObject identifier(@Nonnull UUID identifier) {
        setIdentifier(identifier);
        return this;
    }

    @Nonnull
    @Override
    public TransactionDataAccessObject sequence(@Nonnull Long sequence) {
        setSequence(sequence);
        return this;
    }

    @Nonnull
    @Override
    public TransactionDataAccessObject currency(@Nonnull CurrencyDataAccessObject currency) {
        final var data = Types.require(currency, "currency", JPACurrencyDataAccessObject.class);
        setCurrency(data);
        return this;
    }

    @Nonnull
    @Override
    public TransactionDataAccessObject category(@Nonnull TransactionCategoryDataAccessObject category) {
        final var data = Types.require(category, "category", JPATransactionCategoryDataAccessObject.class);
        setCategory(data);
        return this;
    }

    @Nonnull
    @Override
    public TransactionDataAccessObject amount(@Nonnull BigDecimal amount) {
        setAmount(amount);
        return this;
    }

    @Nonnull
    @Override
    public TransactionDataAccessObject taxation(@Nonnull BigDecimal taxation) {
        setTaxation(taxation);
        return this;
    }

    @Nonnull
    @Override
    public TransactionDataAccessObject debit(@Nonnull UUID account) {
        setDebit(account);
        return this;
    }

    @Nonnull
    @Override
    public TransactionDataAccessObject credit(@Nonnull UUID account) {
        setCredit(account);
        return this;
    }

    @Nonnull
    @Override
    public TransactionDataAccessObject description(@Nullable String description) {
        setDescription(description);
        return this;
    }

    @Nonnull
    @Override
    public TransactionDataAccessObject ref(@Nonnull String plexid) {
        setRef(plexid);
        return this;
    }

    @Nonnull
    @Override
    public TransactionDataAccessObject country(@Nonnull CountryDataAccessObject country) {
        final var data = Types.require(country, "country", JPACountryDataAccessObject.class);
        setCountry(data);
        return this;
    }

    @Nonnull
    @Override
    public TransactionDataAccessObject timestamp(@Nonnull ZonedDateTime timestamp) {
        setTimestamp(timestamp);
        return this;
    }

    @Nonnull
    @Override
    public TransactionDataAccessObject reportedTimestamp(@Nullable ZonedDateTime timestamp) {
        setReportedTimestamp(timestamp);
        return this;
    }
}
