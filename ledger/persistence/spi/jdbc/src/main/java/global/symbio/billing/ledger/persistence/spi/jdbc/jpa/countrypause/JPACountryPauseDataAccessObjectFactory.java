package global.symbio.billing.ledger.persistence.spi.jdbc.jpa.countrypause;

import global.symbio.billing.ledger.persistence.api.countrypause.CountryPauseDataAccessObject;
import global.symbio.billing.ledger.persistence.api.countrypause.CountryPauseDataAccessObjectFactory;
import io.micronaut.context.annotation.Primary;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

@Primary
@Singleton
public class JPACountryPauseDataAccessObjectFactory implements CountryPauseDataAccessObjectFactory {

    @Nonnull
    @Override
    public Class<? extends CountryPauseDataAccessObject> type() {
        return JPACountryPauseDataAccessObject.class;
    }

    @Nonnull
    @Override
    public CountryPauseDataAccessObject create() {
        return new JPACountryPauseDataAccessObject();
    }
}