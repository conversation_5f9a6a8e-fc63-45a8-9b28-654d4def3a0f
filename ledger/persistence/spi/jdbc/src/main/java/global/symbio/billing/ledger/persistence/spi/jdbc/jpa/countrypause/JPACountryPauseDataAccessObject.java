package global.symbio.billing.ledger.persistence.spi.jdbc.jpa.countrypause;

import global.symbio.billing.ledger.persistence.api.countrypause.CountryPauseDataAccessObject;
import io.micronaut.core.annotation.Introspected;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Immutable;

import java.time.OffsetDateTime;

@Immutable @Entity
@Table(name = "country_pause")
@Getter @Setter
@Introspected(includedAnnotations = Entity.class)
public class JPACountryPauseDataAccessObject extends CountryPauseDataAccessObject {

    @Id @Nonnull
    @Column(name = "country_code", unique = true, nullable = false, length = 2)
    private String countryCode;

    @Nonnull
    @Column(name = "timezone", nullable = false, length = 128)
    private String timezone;

    @Nonnull
    @Column(name = "buffer_period_minutes", nullable = false)
    private Integer bufferPeriodMinutes;

    @Nonnull
    @Column(name = "pause", nullable = false)
    private Boolean pause;

    @Nonnull
    @Column(name = "pause_last_updated", nullable = false)
    private OffsetDateTime pauseLastUpdated;

    @Nonnull
    @Override
    public CountryPauseDataAccessObject identifier(@Nonnull String identifier) {
        setCountryCode(identifier);
        return this;
    }

    @Nonnull
    @Override
    public String getIdentifier() {
        return getCountryCode();
    }

    @Nonnull
    @Override
    public CountryPauseDataAccessObject countryCode(@Nonnull String countryCode) {
        setCountryCode(countryCode);
        return this;
    }

    @Nonnull
    @Override
    public CountryPauseDataAccessObject timezone(@Nonnull String timezone) {
        setTimezone(timezone);
        return this;
    }

    @Nonnull
    @Override
    public CountryPauseDataAccessObject bufferPeriodMinutes(@Nonnull Integer bufferPeriodMinutes) {
        setBufferPeriodMinutes(bufferPeriodMinutes);
        return this;
    }

    @Nonnull
    @Override
    public CountryPauseDataAccessObject pause(@Nonnull Boolean pause) {
        setPause(pause);
        return this;
    }

    @Nonnull
    @Override
    public CountryPauseDataAccessObject pauseLastUpdated(@Nonnull OffsetDateTime pauseLastUpdated) {
        setPauseLastUpdated(pauseLastUpdated);
        return this;
    }
}
