package global.symbio.billing.ledger.persistence.spi.jdbc.jpa.invoice.transaction;

import global.symbio.billing.ledger.persistence.api.invoice.transaction.InvoiceTransactionIdentifier;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Objects;
import java.util.UUID;

@Getter
@Setter
@Embeddable
public class JPAInvoiceTransactionIdentifier extends InvoiceTransactionIdentifier implements Serializable {

    @Nonnull
    @Column(name = "invoice", nullable = false, updatable = false)
    private UUID invoice;

    @Nonnull
    @Column(name = "transaction", nullable = false, updatable = false)
    private UUID transaction;

    public JPAInvoiceTransactionIdentifier() {}

    public JPAInvoiceTransactionIdentifier(@Nonnull UUID invoice, @Nonnull UUID transaction) {
        this.invoice = Objects.requireNonNull(invoice, "invoice");
        this.transaction = Objects.requireNonNull(transaction, "transaction");
    }
}