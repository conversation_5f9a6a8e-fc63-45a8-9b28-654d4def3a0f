package global.symbio.billing.ledger.persistence.spi.jdbc.jpa.transaction;

import global.symbio.billing.ledger.persistence.api.transaction.TransactionDataAccessObject;
import global.symbio.billing.ledger.persistence.api.transaction.TransactionDataAccessObjectFactory;
import io.micronaut.context.annotation.Primary;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

@Primary
@Singleton
public class JPATransactionDataAccessObjectFactory implements TransactionDataAccessObjectFactory {

    @Nonnull
    @Override
    public Class<? extends TransactionDataAccessObject> type() {
        return JPATransactionDataAccessObject.class;
    }

    @Nonnull
    @Override
    public TransactionDataAccessObject create() {
        return new JPATransactionDataAccessObject();
    }
}
