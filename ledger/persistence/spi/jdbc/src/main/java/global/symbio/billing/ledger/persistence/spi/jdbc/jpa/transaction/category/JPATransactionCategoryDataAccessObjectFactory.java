package global.symbio.billing.ledger.persistence.spi.jdbc.jpa.transaction.category;

import global.symbio.billing.ledger.persistence.api.transaction.category.TransactionCategoryDataAccessObject;
import global.symbio.billing.ledger.persistence.api.transaction.category.TransactionCategoryDataAccessObjectFactory;
import io.micronaut.context.annotation.Primary;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

@Primary
@Singleton
public class JPATransactionCategoryDataAccessObjectFactory implements TransactionCategoryDataAccessObjectFactory {

    @Nonnull
    @Override
    public Class<? extends TransactionCategoryDataAccessObject> type() {
        return JPATransactionCategoryDataAccessObject.class;
    }

    @Nonnull
    @Override
    public TransactionCategoryDataAccessObject create() {
        return new JPATransactionCategoryDataAccessObject();
    }
}
