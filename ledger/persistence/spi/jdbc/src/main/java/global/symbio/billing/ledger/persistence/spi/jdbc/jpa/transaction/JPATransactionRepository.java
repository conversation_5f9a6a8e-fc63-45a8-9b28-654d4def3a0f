package global.symbio.billing.ledger.persistence.spi.jdbc.jpa.transaction;

import global.symbio.billing.ledger.persistence.api.repository.TransactionRepository;
import io.micronaut.context.annotation.Executable;
import io.micronaut.data.annotation.Query;
import io.micronaut.data.model.Pageable;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import org.hibernate.query.TypedParameterValue;

import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.List;
import java.util.UUID;

public interface JPATransactionRepository extends TransactionRepository<JPATransactionDataAccessObject> {

    // TODO: this logic must remain consist with the transaction "linking" logic for the invoice_transaction table
    // TODO: invoice parameter is no longer used as we aren't traversing the `invoice_transaction` table anymore. We saw an approximately 14x (and growing) performance decrease.
    @Nonnull
    @Override
    @Query(
        value = """
            SELECT t.identifier as identifier, t.sequence as sequence FROM JPATransactionDataAccessObject t
            WHERE :account IN (t.debit, t.credit)
            AND t.sequence BETWEEN :start AND :end
            ORDER BY t.sequence ASC
            """
//        value = """
//                SELECT t.identifier as identifier, t.sequence as sequence FROM JPATransactionDataAccessObject t
//                INNER JOIN JPAInvoiceTransactionDataAccessObject i ON i.identifier.transaction = t.identifier
//                WHERE i.identifier.invoice = :invoice
//                AND i.account = :account
//                AND t.sequence BETWEEN :start AND :end
//                ORDER BY t.sequence ASC
//                """
    )
    @Executable
    List<IdentifierAndSequence> findInvoiceTransactions(@Nonnull UUID invoice, @Nonnull UUID account, long start, long end, @Nonnull Pageable pagination);

    @Nonnull
    @Override
    @Query(
        value = """
            SELECT * FROM transaction_ledger t
            WHERE (CAST(:timestamp AS TIMESTAMPTZ) IS NULL OR t.timestamp >= CAST(:timestamp AS TIMESTAMPTZ))
            AND t.dwh_reporting_timestamp IS NULL
            """,
        nativeQuery = true
    )
    @Executable
    Collection<JPATransactionDataAccessObject> getUnreportedTransactions(@Nullable ZonedDateTime timestamp, @Nonnull Pageable pagination);

    @Override
    @Executable
    @Query(
        value = """
            UPDATE transaction_ledger
            SET dwh_reporting_timestamp = :timestamp
            WHERE id = ANY(:identifiers)
            """,
        nativeQuery = true
    )
    long updateReportedTimestamp(@Nonnull TypedParameterValue<UUID[]> identifiers, @Nullable ZonedDateTime timestamp);
}