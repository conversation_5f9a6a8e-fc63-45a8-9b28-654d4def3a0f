package global.symbio.billing.ledger.persistence.spi.jdbc.jpa.invoice.transaction;

import global.symbio.billing.ledger.persistence.api.invoice.transaction.InvoiceTransactionDataAccessObject;
import global.symbio.billing.ledger.persistence.api.invoice.transaction.InvoiceTransactionDataAccessObjectFactory;
import io.micronaut.context.annotation.Primary;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

import java.util.UUID;

@Primary
@Singleton
public class JPAInvoiceTransactionDataAccessObjectFactory implements InvoiceTransactionDataAccessObjectFactory {

    @Nonnull
    @Override
    public Class<? extends InvoiceTransactionDataAccessObject> type() {
        return JPAInvoiceTransactionDataAccessObject.class;
    }

    @Nonnull
    @Override
    public InvoiceTransactionDataAccessObject create() {
        return new JPAInvoiceTransactionDataAccessObject();
    }

    @Nonnull
    @Override
    public InvoiceTransactionDataAccessObject create(@Nonnull UUID invoice, @Nonnull UUID transaction) {
        final var identifier = new JPAInvoiceTransactionIdentifier(invoice, transaction);
        return create(identifier);
    }
}
