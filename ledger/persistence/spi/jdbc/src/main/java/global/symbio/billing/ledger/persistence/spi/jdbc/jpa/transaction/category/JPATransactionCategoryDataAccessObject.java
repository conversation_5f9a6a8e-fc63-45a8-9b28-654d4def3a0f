package global.symbio.billing.ledger.persistence.spi.jdbc.jpa.transaction.category;

import global.symbio.billing.ledger.persistence.api.transaction.category.TransactionCategoryDataAccessObject;
import io.micronaut.core.annotation.Introspected;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Immutable;

@Immutable @Entity
@Table(name = "transaction_category")
@Getter @Setter
@Introspected(includedAnnotations = Entity.class)
public class JPATransactionCategoryDataAccessObject extends TransactionCategoryDataAccessObject {

    @Id @Nonnull
    @Column(name = "id", unique = true, nullable = false, updatable = false)
    private Integer identifier;

    @Nonnull
    @Column(name = "name", unique = true, nullable = false, length = 64)
    private String name;

    @Nonnull
    @Override
    public TransactionCategoryDataAccessObject identifier(@Nonnull Integer identifier) {
        setIdentifier(identifier);
        return this;
    }

    @Nonnull
    @Override
    public TransactionCategoryDataAccessObject name(@Nonnull String name) {
        setName(name);
        return this;
    }
}
