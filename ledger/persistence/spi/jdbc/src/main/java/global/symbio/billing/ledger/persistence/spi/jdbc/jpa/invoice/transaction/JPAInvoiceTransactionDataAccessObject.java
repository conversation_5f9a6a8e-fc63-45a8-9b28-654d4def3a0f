package global.symbio.billing.ledger.persistence.spi.jdbc.jpa.invoice.transaction;

import global.symbio.billing.core.persistence.api.Types;
import global.symbio.billing.ledger.persistence.api.invoice.transaction.InvoiceTransactionDataAccessObject;
import global.symbio.billing.ledger.persistence.api.invoice.transaction.InvoiceTransactionIdentifier;
import io.micronaut.core.annotation.Introspected;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;

import java.time.ZonedDateTime;
import java.util.UUID;

@Entity
@Table(name = "invoice_transaction")
@Getter @Setter
@Introspected(includedAnnotations = Entity.class)
public class JPAInvoiceTransactionDataAccessObject extends InvoiceTransactionDataAccessObject {

    @Nonnull
    @EmbeddedId
    private JPAInvoiceTransactionIdentifier identifier;

    @Nonnull
    @Column(name = "account", nullable = false, updatable = false)
    private UUID account;

    @Nonnull
    @CreationTimestamp
    @Column(name = "timestamp", nullable = false, updatable = false, insertable = false)
    private ZonedDateTime timestamp;

    @Nullable
    @Column(name = "dwh_reporting_timestamp", nullable = true)
    private ZonedDateTime reportedTimestamp;

    @Nonnull
    @Override
    public InvoiceTransactionDataAccessObject identifier(@Nonnull InvoiceTransactionIdentifier identifier) {
        final var data = Types.require(identifier, "identifier", JPAInvoiceTransactionIdentifier.class);
        setIdentifier(data);
        return this;
    }

    @Nonnull
    @Override
    public InvoiceTransactionDataAccessObject account(@Nonnull UUID account) {
        setAccount(account);
        return this;
    }

    @Nonnull
    @Override
    public InvoiceTransactionDataAccessObject timestamp(@Nonnull ZonedDateTime timestamp) {
        setTimestamp(timestamp);
        return this;
    }

    @Nonnull
    @Override
    public InvoiceTransactionDataAccessObject reportedTimestamp(@Nullable ZonedDateTime timestamp) {
        setReportedTimestamp(timestamp);
        return this;
    }
}
