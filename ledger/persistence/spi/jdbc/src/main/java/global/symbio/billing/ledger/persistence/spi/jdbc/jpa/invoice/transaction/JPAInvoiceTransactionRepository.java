package global.symbio.billing.ledger.persistence.spi.jdbc.jpa.invoice.transaction;

import global.symbio.billing.ledger.persistence.api.repository.InvoiceTransactionRepository;
import io.micronaut.context.annotation.Executable;
import io.micronaut.data.annotation.Query;
import io.micronaut.data.model.Pageable;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import org.hibernate.query.TypedParameterValue;

import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.UUID;

public interface JPAInvoiceTransactionRepository extends InvoiceTransactionRepository<JPAInvoiceTransactionIdentifier, JPAInvoiceTransactionDataAccessObject> {

    @Override
    default long unlink(@Nonnull UUID invoice) {
        return deleteInvoiceTransactions(invoice);
    }

    @Override
    default long link(@Nonnull UUID invoice, @Nonnull UUID account, long start, long end) {
        return insertInvoiceTransaction(invoice, account, start, end);
    }

    @Query(
        value = "DELETE FROM invoice_transaction WHERE invoice = :invoice",
        nativeQuery = true
    )
    @Executable
    long deleteInvoiceTransactions(@Nonnull UUID invoice);

    @Query(
        value = """
            INSERT INTO invoice_transaction (invoice, transaction, account)
            SELECT :invoice, t.id, :account FROM transaction_ledger t
            WHERE (t.sequence BETWEEN :start AND :end)
            AND (t.debit = :account OR t.credit = :account)
            ON CONFLICT (invoice, transaction) DO NOTHING
            """,
        nativeQuery = true
    )
    @Executable
    long insertInvoiceTransaction(@Nonnull UUID invoice, @Nonnull UUID account, long start, long end);

    @Nonnull
    @Override
    @Query(
        value = """
            SELECT * FROM invoice_transaction it
            WHERE (CAST(:timestamp AS TIMESTAMPTZ) IS NULL OR it.timestamp >= CAST(:timestamp AS TIMESTAMPTZ))
            AND it.dwh_reporting_timestamp IS NULL
            """,
        nativeQuery = true
    )
    @Executable
    Collection<JPAInvoiceTransactionDataAccessObject> getUnreportedInvoiceTransactions(@Nullable ZonedDateTime timestamp, @Nonnull Pageable pagination);

    @Override
    @Query(
        value = """
            UPDATE invoice_transaction
            SET dwh_reporting_timestamp = :timestamp
            WHERE (invoice, transaction) = ANY(
                SELECT *
                FROM UNNEST(
                    :invoices,
                    :transactions
                ) AS x(inv, tx)
            )
            """,
        nativeQuery = true
    )
    @Executable
    long updateReportedTimestamp(@Nonnull TypedParameterValue<UUID[]> invoices, @Nonnull TypedParameterValue<UUID[]> transactions, @Nullable ZonedDateTime timestamp);
}