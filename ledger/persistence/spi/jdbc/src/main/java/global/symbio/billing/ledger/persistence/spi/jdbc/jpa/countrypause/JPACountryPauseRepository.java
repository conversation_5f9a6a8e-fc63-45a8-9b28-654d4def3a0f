package global.symbio.billing.ledger.persistence.spi.jdbc.jpa.countrypause;

import global.symbio.billing.ledger.persistence.api.repository.CountryPauseRepository;
import io.micronaut.context.annotation.Executable;
import io.micronaut.data.annotation.Query;
import jakarta.annotation.Nonnull;

public interface JPACountryPauseRepository extends CountryPauseRepository<JPACountryPauseDataAccessObject> {
    @Override
    @Query(
        value = """
            UPDATE country_pause
            SET pause = true, pause_last_updated = NOW()
            WHERE country_code = :countryCode
            """,
        nativeQuery = true
    )
    @Executable
    long pauseCountry(@Nonnull String countryCode);
}