package global.symbio.billing.ledger.persistence.spi.jdbc.jpa.countrypause;


import global.symbio.billing.core.persistence.api.Repositories;
import global.symbio.billing.ledger.persistence.api.repository.CountryPauseRepository;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

public class TestCountryPauseRepository {

    @Test
    @DisplayName("ReadOnlyCountryPauseRepository is connected to read-only datasource")
    public void test_read_only_repository() {
        Assertions.assertTrue(CountryPauseRepository.class.isAssignableFrom(ReadOnlyJPACountryPauseRepository.class));
        Assertions.assertTrue(Repositories.isReadOnlyRepository(ReadOnlyJPACountryPauseRepository.class));
    }

    @Test
    @DisplayName("ReadWriteCountryPauseRepository is connected to read-write datasource")
    public void test_read_write_repository() {
        Assertions.assertTrue(CountryPauseRepository.class.isAssignableFrom(ReadWriteJPACountryPauseRepository.class));
        Assertions.assertTrue(Repositories.isReadWriteRepository(ReadWriteJPACountryPauseRepository.class));
    }
}
