package global.symbio.billing.ledger.persistence.spi.jdbc.jpa.transaction;

import global.symbio.billing.core.persistence.api.Repositories;
import global.symbio.billing.ledger.persistence.api.repository.TransactionRepository;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

public class TestTransactionRepository {

    @Test
    @DisplayName("ReadOnlyTransactionRepository is connected to read-only datasource")
    public void test_read_only_repository() {
        Assertions.assertTrue(TransactionRepository.class.isAssignableFrom(ReadOnlyJPATransactionRepository.class));
        Assertions.assertTrue(Repositories.isReadOnlyRepository(ReadOnlyJPATransactionRepository.class));
    }

    @Test
    @DisplayName("ReadWriteTransactionRepository is connected to read-write datasource")
    public void test_read_write_repository() {
        Assertions.assertTrue(TransactionRepository.class.isAssignableFrom(ReadWriteJPATransactionRepository.class));
        Assertions.assertTrue(Repositories.isReadWriteRepository(ReadWriteJPATransactionRepository.class));
    }
}