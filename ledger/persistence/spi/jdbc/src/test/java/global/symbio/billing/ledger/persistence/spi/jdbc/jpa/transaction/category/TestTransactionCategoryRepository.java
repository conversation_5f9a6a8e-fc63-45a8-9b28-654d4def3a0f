package global.symbio.billing.ledger.persistence.spi.jdbc.jpa.transaction.category;

import global.symbio.billing.core.persistence.api.Repositories;
import global.symbio.billing.ledger.persistence.api.repository.TransactionCategoryRepository;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

public class TestTransactionCategoryRepository {

    @Test
    @DisplayName("ReadOnlyTransactionCategoryRepository is connected to read-only datasource")
    public void test_read_only_repository() {
        Assertions.assertTrue(TransactionCategoryRepository.class.isAssignableFrom(ReadOnlyJPATransactionCategoryRepository.class));
        Assertions.assertTrue(Repositories.isReadOnlyRepository(ReadOnlyJPATransactionCategoryRepository.class));
    }

    @Test
    @DisplayName("ReadWriteTransactionCategoryRepository is connected to read-write datasource")
    public void test_read_write_repository() {
        Assertions.assertTrue(TransactionCategoryRepository.class.isAssignableFrom(ReadWriteJPATransactionCategoryRepository.class));
        Assertions.assertTrue(Repositories.isReadWriteRepository(ReadWriteJPATransactionCategoryRepository.class));
    }
}