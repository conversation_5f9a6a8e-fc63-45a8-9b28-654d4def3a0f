package global.symbio.billing.ledger.persistence.spi.jdbc.jpa.invoice.transaction;

import global.symbio.billing.core.persistence.api.Repositories;
import global.symbio.billing.ledger.persistence.api.repository.InvoiceTransactionRepository;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

public class TestInvoiceTransactionRepository {

    @Test
    @DisplayName("ReadOnlyInvoiceTransactionRepository is connected to read-only datasource")
    public void test_read_only_repository() {
        Assertions.assertTrue(InvoiceTransactionRepository.class.isAssignableFrom(ReadOnlyJPAInvoiceTransactionRepository.class));
        Assertions.assertTrue(Repositories.isReadOnlyRepository(ReadOnlyJPAInvoiceTransactionRepository.class));
    }

    @Test
    @DisplayName("ReadWriteInvoiceTransactionRepository is connected to read-write datasource")
    public void test_read_write_repository() {
        Assertions.assertTrue(InvoiceTransactionRepository.class.isAssignableFrom(ReadWriteJPAInvoiceTransactionRepository.class));
        Assertions.assertTrue(Repositories.isReadWriteRepository(ReadWriteJPAInvoiceTransactionRepository.class));
    }
}
