package global.symbio.billing.ledger.persistence.api.invoice.transaction;

import com.google.common.base.MoreObjects;
import jakarta.annotation.Nonnull;

import java.util.Comparator;
import java.util.Objects;
import java.util.UUID;

public abstract class InvoiceTransactionIdentifier implements Comparable<InvoiceTransactionIdentifier> {

    @Nonnull
    private static final Comparator<InvoiceTransactionIdentifier> COMPARATOR = Comparator
        .comparing(InvoiceTransactionIdentifier::getInvoice)
        .thenComparing(InvoiceTransactionIdentifier::getTransaction);

    @Nonnull
    public abstract UUID getInvoice();

    @Nonnull
    public abstract UUID getTransaction();

    @Override
    public boolean equals(Object other) {
        if (this == other) return true;
        if (!(other instanceof InvoiceTransactionIdentifier identifier)) return false;
        return Objects.equals(getInvoice(), identifier.getInvoice()) && Objects.equals(getTransaction(), identifier.getTransaction());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getInvoice(), getTransaction());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this).add("invoice", getInvoice()).add("transaction", getTransaction()).toString();
    }

    @Override
    public int compareTo(@Nonnull InvoiceTransactionIdentifier other) {
        return COMPARATOR.compare(this, other);
    }
}