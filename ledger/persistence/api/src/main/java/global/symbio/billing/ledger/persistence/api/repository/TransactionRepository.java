package global.symbio.billing.ledger.persistence.api.repository;

import global.symbio.billing.ledger.persistence.api.transaction.TransactionDataAccessObject;
import io.micronaut.context.annotation.Executable;
import io.micronaut.core.annotation.Introspected;
import io.micronaut.data.model.Pageable;
import io.micronaut.data.repository.PageableRepository;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import org.hibernate.query.TypedParameterValue;

import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface TransactionRepository<T extends TransactionDataAccessObject> extends PageableRepository<T, UUID> {

    @Nonnull
    @Executable
    Optional<Number> findMaxSequence();

    @Nonnull
    @Executable
    Optional<Number> findMaxSequenceByTimestampLessThanEquals(@Nonnull ZonedDateTime timestamp);

    @Nonnull
    @Executable
    Optional<Number> findMaxSequenceByDebitOrCredit(@Nonnull UUID debit, @Nonnull UUID credit);

    @Nonnull
    @Executable
    Optional<Number> findMaxSequenceByDebitOrCreditAndTimestampLessThanEquals(@Nonnull UUID debit, @Nonnull UUID credit, @Nonnull ZonedDateTime timestamp);

    @Nonnull
    @Executable
    Collection<UUID> findIdentifierByDebitOrCreditAndSequenceBetween(@Nonnull UUID debit, @Nonnull UUID credit, long start, long end);

    @Nonnull
    @Executable
    Collection<T> findByIdentifierIn(@Nonnull Collection<UUID> identifiers);

    @Nonnull
    @Executable
    Collection<T> getUnreportedTransactions(@Nullable ZonedDateTime timestamp, @Nonnull Pageable pagination);

    @Executable
    long updateReportedTimestamp(@Nonnull TypedParameterValue<UUID[]> identifiers, @Nullable ZonedDateTime timestamp);

    @Nonnull
    List<IdentifierAndSequence> findInvoiceTransactions(@Nonnull UUID invoice, @Nonnull UUID account, long start, long end, @Nonnull Pageable pagination);

    @Introspected
    record IdentifierAndSequence(@Nonnull UUID identifier, long sequence) {}
}