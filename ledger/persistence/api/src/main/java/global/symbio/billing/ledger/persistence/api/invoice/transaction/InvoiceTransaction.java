package global.symbio.billing.ledger.persistence.api.invoice.transaction;

import global.symbio.billing.core.persistence.api.Entity;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;
import java.util.UUID;

public class InvoiceTransaction extends Entity<InvoiceTransactionIdentifier, InvoiceTransactionDataAccessObject> {

    public InvoiceTransaction(@Nonnull InvoiceTransactionDataAccessObject data) {
        super(data);
    }

    @Nonnull
    public UUID getInvoice() {
        return getIdentifier().getInvoice();
    }

    @Nonnull
    public UUID getTransaction() {
        return getIdentifier().getTransaction();
    }

    @Nonnull
    public UUID getAccount() {
        return data().getAccount();
    }

    @Nonnull
    public ZonedDateTime getTimestamp() {
        return data().getTimestamp();
    }

    @Nullable
    public ZonedDateTime getReportedTimestamp() {
        return data().getReportedTimestamp();
    }
}