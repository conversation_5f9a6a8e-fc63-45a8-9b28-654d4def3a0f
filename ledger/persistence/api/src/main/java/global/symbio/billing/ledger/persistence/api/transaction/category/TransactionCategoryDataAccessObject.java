package global.symbio.billing.ledger.persistence.api.transaction.category;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.persistence.api.DataAccessObject;
import jakarta.annotation.Nonnull;

import java.util.Objects;

public abstract class TransactionCategoryDataAccessObject implements DataAccessObject<Integer, TransactionCategoryDataAccessObject> {

    @Nonnull
    public abstract String getName();

    @Nonnull
    public abstract TransactionCategoryDataAccessObject name(@Nonnull String name);

    @Override @Nonnull
    public TransactionCategory entity() {
        return new TransactionCategory(this);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof TransactionCategoryDataAccessObject category)) return false;
        return Objects.equals(getIdentifier(), category.getIdentifier()) && Objects.equals(getName(), category.getName());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getIdentifier(), getName());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this).add("identifier", getIdentifier()).add("name", getName()).toString();
    }
}
