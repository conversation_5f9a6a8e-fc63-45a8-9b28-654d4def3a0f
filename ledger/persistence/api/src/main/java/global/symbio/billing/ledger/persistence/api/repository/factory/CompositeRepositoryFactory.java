package global.symbio.billing.ledger.persistence.api.repository.factory;

import global.symbio.billing.core.persistence.api.CompositeRepository;
import global.symbio.billing.core.persistence.api.PersistenceStore;
import global.symbio.billing.ledger.persistence.api.invoice.transaction.InvoiceTransactionDataAccessObject;
import global.symbio.billing.ledger.persistence.api.invoice.transaction.InvoiceTransactionIdentifier;
import global.symbio.billing.ledger.persistence.api.repository.InvoiceTransactionRepository;
import global.symbio.billing.ledger.persistence.api.repository.TransactionCategoryRepository;
import global.symbio.billing.ledger.persistence.api.repository.TransactionRepository;
import global.symbio.billing.ledger.persistence.api.transaction.TransactionDataAccessObject;
import global.symbio.billing.ledger.persistence.api.transaction.category.TransactionCategoryDataAccessObject;
import io.micronaut.context.annotation.Factory;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import static global.symbio.billing.core.persistence.api.PersistenceStore.Capabilities.READ_ONLY;
import static global.symbio.billing.core.persistence.api.PersistenceStore.Capabilities.READ_WRITE;

@Factory
public class CompositeRepositoryFactory {

    @Inject
    @Singleton
    public CompositeRepository<InvoiceTransactionRepository<InvoiceTransactionIdentifier, InvoiceTransactionDataAccessObject>> create(@Nonnull @PersistenceStore(READ_ONLY) InvoiceTransactionRepository<InvoiceTransactionIdentifier, InvoiceTransactionDataAccessObject> read, @Nonnull @PersistenceStore(READ_WRITE) InvoiceTransactionRepository<InvoiceTransactionIdentifier, InvoiceTransactionDataAccessObject> write) {
        return new CompositeRepository<>(read, write);
    }

    @Inject
    @Singleton
    public CompositeRepository<TransactionCategoryRepository<TransactionCategoryDataAccessObject>> create(@Nonnull @PersistenceStore(READ_ONLY) TransactionCategoryRepository<TransactionCategoryDataAccessObject> read, @Nonnull @PersistenceStore(READ_WRITE) TransactionCategoryRepository<TransactionCategoryDataAccessObject> write) {
        return new CompositeRepository<>(read, write);
    }

    @Inject
    @Singleton
    public CompositeRepository<TransactionRepository<TransactionDataAccessObject>> create(@Nonnull @PersistenceStore(READ_ONLY) TransactionRepository<TransactionDataAccessObject> read, @Nonnull @PersistenceStore(READ_WRITE) TransactionRepository<TransactionDataAccessObject> write) {
        return new CompositeRepository<>(read, write);
    }
}