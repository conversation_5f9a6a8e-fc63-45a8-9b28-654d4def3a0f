package global.symbio.billing.ledger.persistence.api.countrypause;

import global.symbio.billing.core.persistence.api.Entity;
import jakarta.annotation.Nonnull;

import java.time.OffsetDateTime;
import java.time.ZoneId;

public class CountryPause extends Entity<String, CountryPauseDataAccessObject> {

    public CountryPause(@Nonnull CountryPauseDataAccessObject data) {
        super(data);
    }

    @Nonnull
    public String getCountryCode() {
        return data().getCountryCode();
    }

    @Nonnull
    public ZoneId getTimezone() {
        return ZoneId.of(data.getTimezone());
    }

    @Nonnull
    public Integer getBufferPeriodMinutes() {
        return data().getBufferPeriodMinutes();
    }

    @Nonnull
    public Boolean getPause() {
        return data().getPause();
    }

    @Nonnull
    public OffsetDateTime getPauseLastUpdated() {
        return data().getPauseLastUpdated();
    }
}
