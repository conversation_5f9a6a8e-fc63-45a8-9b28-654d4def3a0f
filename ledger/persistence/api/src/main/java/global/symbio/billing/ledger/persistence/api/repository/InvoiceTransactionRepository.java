package global.symbio.billing.ledger.persistence.api.repository;

import global.symbio.billing.ledger.persistence.api.invoice.transaction.InvoiceTransactionDataAccessObject;
import global.symbio.billing.ledger.persistence.api.invoice.transaction.InvoiceTransactionIdentifier;
import io.micronaut.context.annotation.Executable;
import io.micronaut.data.model.Pageable;
import io.micronaut.data.repository.PageableRepository;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import org.hibernate.query.TypedParameterValue;

import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.UUID;

public interface InvoiceTransactionRepository<I extends InvoiceTransactionIdentifier, T extends InvoiceTransactionDataAccessObject> extends PageableRepository<T, I> {

    @Executable
    long unlink(@Nonnull UUID invoice);

    @Executable
    long link(@Nonnull UUID invoice, @Nonnull UUID account, long start, long end);

    @Nonnull
    @Executable
    Collection<T> getUnreportedInvoiceTransactions(@Nullable ZonedDateTime timestamp, @Nonnull Pageable pagination);

    @Executable
    long updateReportedTimestamp(@Nonnull TypedParameterValue<UUID[]> invoices, @Nonnull TypedParameterValue<UUID[]> transactions, @Nullable ZonedDateTime timestamp);
}