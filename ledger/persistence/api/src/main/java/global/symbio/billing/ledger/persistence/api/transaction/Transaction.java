package global.symbio.billing.ledger.persistence.api.transaction;

import global.symbio.billing.core.country.persistence.api.Country;
import global.symbio.billing.core.currency.persistence.api.Currency;
import global.symbio.billing.core.persistence.api.Entity;
import global.symbio.billing.ledger.persistence.api.transaction.category.TransactionCategory;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.UUID;

public class Transaction extends Entity<UUID, TransactionDataAccessObject> {

    public Transaction(@Nonnull TransactionDataAccessObject data) {
        super(data);
    }

    @Nonnull
    public Long getSequence() {
        return data().getSequence();
    }

    @Nonnull
    public Currency getCurrency() {
        return unwrap(data().getCurrency());
    }

    @Nonnull
    public TransactionCategory getCategory() {
        return unwrap(data().getCategory());
    }

    @Nonnull
    public BigDecimal getAmount() {
        return data().getAmount();
    }

    @Nonnull
    public BigDecimal getTaxation() {
        return data().getTaxation();
    }

    @Nonnull
    public UUID getDebit() {
        return data().getDebit();
    }

    @Nonnull
    public UUID getCredit() {
        return data().getCredit();
    }

    @Nullable
    public String getDescription() {
        return data().getDescription();
    }

    @Nonnull
    public String getRef() {
        return data().getRef();
    }

    @Nonnull
    public Country getCountry() {
        return unwrap(data().getCountry());
    }

    @Nonnull
    public ZonedDateTime getTimestamp() {
        return data().getTimestamp();
    }

    @Nullable
    public ZonedDateTime getReportedTimestamp() {
        return data().getReportedTimestamp();
    }
}
