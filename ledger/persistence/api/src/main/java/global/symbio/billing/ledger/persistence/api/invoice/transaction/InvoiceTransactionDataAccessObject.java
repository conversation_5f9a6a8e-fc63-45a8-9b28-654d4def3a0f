package global.symbio.billing.ledger.persistence.api.invoice.transaction;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.persistence.api.DataAccessObject;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.time.ZonedDateTime;
import java.util.Objects;
import java.util.UUID;

public abstract class InvoiceTransactionDataAccessObject implements DataAccessObject<InvoiceTransactionIdentifier, InvoiceTransactionDataAccessObject> {

    @Nonnull
    public abstract UUID getAccount();

    @Nonnull
    public abstract InvoiceTransactionDataAccessObject account(@Nonnull UUID account);

    @Nonnull
    public abstract ZonedDateTime getTimestamp();

    @Nonnull
    public abstract InvoiceTransactionDataAccessObject timestamp(@Nonnull ZonedDateTime timestamp);

    @Nullable
    public abstract ZonedDateTime getReportedTimestamp();

    @Nonnull
    public abstract InvoiceTransactionDataAccessObject reportedTimestamp(@Nullable ZonedDateTime timestamp);

    @Nonnull
    @Override
    public InvoiceTransaction entity() {
        return new InvoiceTransaction(this);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (!(obj instanceof InvoiceTransactionDataAccessObject link)) return false;
        return Objects.equals(getIdentifier(), link.getIdentifier())
            && Objects.equals(getAccount(), link.getAccount())
            && Objects.equals(getTimestamp(), link.getTimestamp())
            && Objects.equals(getReportedTimestamp(), link.getReportedTimestamp());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getIdentifier(), getAccount(), getTimestamp(), getReportedTimestamp());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("identifier", getIdentifier())
            .add("account", getAccount())
            .add("timestamp", getTimestamp())
            .add("reportedTimestamp", getReportedTimestamp())
            .toString();
    }
}
