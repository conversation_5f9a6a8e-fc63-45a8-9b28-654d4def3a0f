package global.symbio.billing.ledger.persistence.api.repository;

import global.symbio.billing.ledger.persistence.api.countrypause.CountryPauseDataAccessObject;
import io.micronaut.context.annotation.Executable;
import io.micronaut.data.repository.CrudRepository;
import jakarta.annotation.Nonnull;

import java.util.Optional;

/**
 * Repository for {@link CountryPauseDataAccessObject}.
 * Used to access the country_pause table.
 */
public interface CountryPauseRepository<T extends CountryPauseDataAccessObject> extends CrudRepository<T, String> {

    /**
     * Given a countryCode, set its `pause` field to true. Un-pausing will be done manually for now,
     * until we automate the flow to resume writing to transaction_ledger.
     * @param countryCode 2-character ISO country code - only MY, SG, AU, NZ are supported for now.
     * @return number of affected rows. This should always be 1 if successful, or 0 if it failed to update the record.
     */
    @Executable
    long pauseCountry(@Nonnull String countryCode);

    /**
     * Given a countryCode, return the CountryPause object for that country.
     */
    @Nonnull
    @Executable
    Optional<T> findByCountryCode(@Nonnull String countryCode);
}
