package global.symbio.billing.ledger.persistence.api.countrypause;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.persistence.api.DataAccessObject;
import jakarta.annotation.Nonnull;

import java.time.OffsetDateTime;
import java.util.Objects;

public abstract class CountryPauseDataAccessObject implements DataAccessObject<String, CountryPauseDataAccessObject> {

    @Nonnull
    public abstract String getCountryCode();

    @Nonnull
    public abstract CountryPauseDataAccessObject countryCode(@Nonnull String countryCode);

    @Nonnull
    public abstract String getTimezone();

    @Nonnull
    public abstract CountryPauseDataAccessObject timezone(@Nonnull String timezone);

    @Nonnull
    public abstract Integer getBufferPeriodMinutes();

    @Nonnull
    public abstract CountryPauseDataAccessObject bufferPeriodMinutes(@Nonnull Integer bufferPeriodMinutes);

    @Nonnull
    public abstract Boolean getPause();

    @Nonnull
    public abstract CountryPauseDataAccessObject pause(@Nonnull Boolean pause);

    @Nonnull
    public abstract OffsetDateTime getPauseLastUpdated();

    @Nonnull
    public abstract CountryPauseDataAccessObject pauseLastUpdated(@Nonnull OffsetDateTime pauseLastUpdated);

    @Override @Nonnull
    public CountryPause entity() {
        return new CountryPause(this);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof CountryPauseDataAccessObject countryPause)) return false;
        return Objects.equals(getIdentifier(), countryPause.getIdentifier()) && 
               Objects.equals(getCountryCode(), countryPause.getCountryCode()) &&
               Objects.equals(getTimezone(), countryPause.getTimezone()) &&
               Objects.equals(getBufferPeriodMinutes(), countryPause.getBufferPeriodMinutes()) &&
               Objects.equals(getPause(), countryPause.getPause()) &&
               Objects.equals(getPauseLastUpdated(), countryPause.getPauseLastUpdated());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getIdentifier(), getCountryCode(), getTimezone(), 
                           getBufferPeriodMinutes(), getPause(), getPauseLastUpdated());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("identifier", getIdentifier())
            .add("countryCode", getCountryCode())
            .add("timezone", getTimezone())
            .add("bufferPeriodMinutes", getBufferPeriodMinutes())
            .add("pause", getPause())
            .add("pauseLastUpdated", getPauseLastUpdated())
            .toString();
    }
}
