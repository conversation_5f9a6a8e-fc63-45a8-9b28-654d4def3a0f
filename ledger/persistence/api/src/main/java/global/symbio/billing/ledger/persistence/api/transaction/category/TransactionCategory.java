package global.symbio.billing.ledger.persistence.api.transaction.category;

import global.symbio.billing.core.persistence.api.Entity;
import jakarta.annotation.Nonnull;

public class TransactionCategory extends Entity<Integer, TransactionCategoryDataAccessObject> {

    public TransactionCategory(@Nonnull TransactionCategoryDataAccessObject data) {
        super(data);
    }

    @Nonnull
    public String getName() {
        return data().getName();
    }
}
