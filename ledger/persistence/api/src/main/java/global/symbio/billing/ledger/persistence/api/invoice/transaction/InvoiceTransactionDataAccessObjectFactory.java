package global.symbio.billing.ledger.persistence.api.invoice.transaction;

import global.symbio.billing.core.persistence.api.DataAccessObjectFactory;
import jakarta.annotation.Nonnull;

import java.util.UUID;

public interface InvoiceTransactionDataAccessObjectFactory extends DataAccessObjectFactory<InvoiceTransactionIdentifier, InvoiceTransactionDataAccessObject> {

    @Nonnull
    InvoiceTransactionDataAccessObject create(@Nonnull UUID invoice, @Nonnull UUID transaction);
}
