package global.symbio.billing.ledger.persistence.api.transaction;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.country.persistence.api.Country;
import global.symbio.billing.core.country.persistence.api.CountryDataAccessObject;
import global.symbio.billing.core.currency.persistence.api.Currency;
import global.symbio.billing.core.currency.persistence.api.CurrencyDataAccessObject;
import global.symbio.billing.core.persistence.api.DataAccessObject;
import global.symbio.billing.ledger.persistence.api.transaction.category.TransactionCategory;
import global.symbio.billing.ledger.persistence.api.transaction.category.TransactionCategoryDataAccessObject;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Objects;
import java.util.UUID;

public abstract class TransactionDataAccessObject implements DataAccessObject<UUID, TransactionDataAccessObject> {

    @Nonnull
    public abstract Long getSequence();

    @Nonnull
    public abstract TransactionDataAccessObject sequence(@Nonnull Long sequence);

    @Nonnull
    public abstract CurrencyDataAccessObject getCurrency();

    @Nonnull
    public abstract TransactionDataAccessObject currency(@Nonnull CurrencyDataAccessObject currency);

    @Nonnull
    public TransactionDataAccessObject currency(@Nonnull Currency entity) {
        return currency(entity.data());
    }

    @Nonnull
    public abstract TransactionCategoryDataAccessObject getCategory();

    @Nonnull
    public abstract TransactionDataAccessObject category(@Nonnull TransactionCategoryDataAccessObject category);

    @Nonnull
    public TransactionDataAccessObject category(@Nonnull TransactionCategory entity) {
        return category(entity.data());
    }

    @Nonnull
    public abstract BigDecimal getAmount();

    @Nonnull
    public abstract TransactionDataAccessObject amount(@Nonnull BigDecimal amount);

    @Nonnull
    public abstract BigDecimal getTaxation();

    @Nonnull
    public abstract TransactionDataAccessObject taxation(@Nonnull BigDecimal taxation);

    @Nonnull
    public abstract UUID getDebit();

    @Nonnull
    public abstract TransactionDataAccessObject debit(@Nonnull UUID account);

    @Nonnull
    public abstract UUID getCredit();

    @Nonnull
    public abstract TransactionDataAccessObject credit(@Nonnull UUID account);

    @Nullable
    public abstract String getDescription();

    @Nonnull
    public abstract TransactionDataAccessObject description(@Nullable String description);

    @Nonnull
    public abstract String getRef();

    @Nonnull
    public abstract TransactionDataAccessObject ref(@Nonnull String plexid);

    @Nonnull
    public abstract CountryDataAccessObject getCountry();

    @Nonnull
    public abstract TransactionDataAccessObject country(@Nonnull CountryDataAccessObject country);

    @Nonnull
    public TransactionDataAccessObject country(@Nonnull Country country) {
        return country(country.data());
    }

    @Nonnull
    public abstract ZonedDateTime getTimestamp();

    @Nonnull
    public abstract TransactionDataAccessObject timestamp(@Nonnull ZonedDateTime timestamp);

    @Nullable
    public abstract ZonedDateTime getReportedTimestamp();

    @Nonnull
    public abstract TransactionDataAccessObject reportedTimestamp(@Nullable ZonedDateTime timestamp);

    @Override
    @Nonnull
    public Transaction entity() {
        return new Transaction(this);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof TransactionDataAccessObject tx)) return false;
        return Objects.equals(getIdentifier(), tx.getIdentifier())
            && Objects.equals(getSequence(), tx.getSequence())
            && Objects.equals(getCurrency(), tx.getCurrency())
            && Objects.equals(getCategory(), tx.getCategory())
            && Objects.equals(getAmount(), tx.getAmount())
            && Objects.equals(getTaxation(), tx.getTaxation())
            && Objects.equals(getDebit(), tx.getDebit())
            && Objects.equals(getCredit(), tx.getCredit())
            && Objects.equals(getDescription(), tx.getDescription())
            && Objects.equals(getRef(), tx.getRef())
            && Objects.equals(getCountry(), tx.getCountry())
            && Objects.equals(getTimestamp(), tx.getTimestamp())
            && Objects.equals(getReportedTimestamp(), tx.getReportedTimestamp());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getIdentifier(), getSequence(), getCurrency(), getCategory(), getAmount(), getTaxation(), getDebit(), getCredit(), getDescription(), getRef(), getCountry(), getTimestamp(), getReportedTimestamp());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("identifier", getIdentifier())
            .add("sequence", getSequence())
            .add("currency", getCurrency())
            .add("category", getCategory())
            .add("amount", getAmount())
            .add("taxation", getTaxation())
            .add("debit", getDebit())
            .add("credit", getCredit())
            .add("description", getDescription())
            .add("ref", getRef())
            .add("country", getCountry())
            .add("timestamp", getTimestamp())
            .add("reportedTimestamp", getReportedTimestamp())
            .toString();
    }
}
