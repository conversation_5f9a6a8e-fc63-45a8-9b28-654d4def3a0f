import com.google.common.base.Strings

buildscript {
    repositories {
        mavenCentral()
    }
    dependencies {
        classpath "org.liquibase:liquibase-core:$liquibase_runtime_version"
        classpath "org.liquibase:liquibase-gradle-plugin:$liquibase_gradle_version"
    }
}

plugins {
    id 'io.freefair.lombok' version '8.14' apply false
    id 'org.liquibase.gradle' version "$liquibase_gradle_version" apply false
    id 'com.github.johnrengelman.shadow' version '8.1.1' apply false
    id 'io.micronaut.application' version '4.4.3' apply false
    id 'com.google.protobuf' version '0.9.5' apply false
    id 'net.razvan.jacoco-to-cobertura' version '1.1.2' apply false
    id 'com.github.ben-manes.versions' version '0.52.0' apply false
    id 'org.owasp.dependencycheck' version '12.1.3' apply false
    id 'me.champeau.jmh' version '0.7.3' apply false
    id 'org.barfuin.gradle.jacocolog' version '3.1.0'
}

group = 'global.symbio.billing'

final def isCIEnvironment = !Strings.isNullOrEmpty(System.getenv('CI'))

allprojects {
    apply plugin: 'idea'
    apply plugin: 'base'
    apply plugin: 'java-library'
    apply plugin: 'jvm-test-suite'
    apply plugin: 'me.champeau.jmh'
    apply plugin: 'io.freefair.lombok'
    apply plugin: 'com.github.johnrengelman.shadow'
    apply plugin: 'com.github.ben-manes.versions'

    if (isCIEnvironment) {
        // Conditionally enable jacoco and reporting plugins only when running the build in a CI environment
        apply plugin: 'jacoco'
        apply plugin: 'net.razvan.jacoco-to-cobertura' //TODO: deprecate when Gitlab supports Jacoco: https://gitlab.com/gitlab-org/gitlab/-/issues/227345
    }

    idea {
        module {
            downloadSources = !isCIEnvironment
        }
    }

    repositories {
        mavenCentral()
    }

    configurations.configureEach {
        resolutionStrategy.eachDependency { final DependencyResolveDetails details ->
            final def group = details.requested.group
            final def version = details.requested.version
            if (group == 'com.google.protobuf' && version != grpc_protoc_version) {
                details.useVersion grpc_protoc_version
                details.because 'Use latest and consistent protocol buffers version'
            } else if (group == 'io.grpc' && version != grpc_version) {
                details.useVersion grpc_version
                details.because 'Use latest and consistent gRPC Java version'
            } else if (group == 'org.openjdk.jmh' && version != jmh_version) {
                details.useVersion jmh_version
                details.because 'Use latest and consistent JMH version'
            }
        }
    }

    // Global dependencies (keep this to a minimum)
    dependencies {
        // Logging backends and frontends
        api "org.slf4j:slf4j-api:$slf4j_version"
        implementation "org.apache.logging.log4j:log4j-core:$log4j2_version"
        implementation "org.apache.logging.log4j:log4j-slf4j2-impl:$log4j2_version"
        implementation platform("org.apache.logging.log4j:log4j-bom:$log4j2_version")
        runtimeOnly "org.apache.logging.log4j:log4j-layout-template-json"

        // Micronaut Bill of Materials (BoM)
        annotationProcessor platform(mn.micronaut.core.bom)
        implementation platform(mn.micronaut.core.bom)

        // AWS SDK Bill of Materials (BoM)
        implementation platform("software.amazon.awssdk:bom:$aws_sdk_version")

        // Micronaut annotation processing / dependency injection / metrics
        annotationProcessor 'org.projectlombok:lombok'
        annotationProcessor "jakarta.persistence:jakarta.persistence-api:$jakarta_persistence_version"
        annotationProcessor mn.micronaut.data.processor
        annotationProcessor mn.micronaut.inject.java
        annotationProcessor mn.micronaut.validation.processor
        annotationProcessor mn.micronaut.micrometer.annotation
        implementation mn.micronaut.data.processor
        implementation mn.micronaut.inject
        implementation mn.micronaut.micrometer.core

        // Utility frameworks (Jackson, Google Guava and Java UUID Generator)
        implementation mn.micronaut.jackson.databind
        implementation "com.google.guava:guava:$guava_version"
        implementation "com.fasterxml.uuid:java-uuid-generator:$java_uuid_generator_version"

        // JMH Benchmarking framework
        jmh "org.openjdk.jmh:jmh-core:$jmh_version"
        jmh "org.openjdk.jmh:jmh-generator-annprocess:$jmh_version"
    }

    java {
        toolchain {
            vendor = JvmVendorSpec.AMAZON
            languageVersion = JavaLanguageVersion.of(23)
        }
    }

    testing {
        suites {
            test {
                useJUnitJupiter(junit_version)
                dependencies {
                    // Testing dependencies
                    annotationProcessor 'org.projectlombok:lombok'
                    annotationProcessor mn.micronaut.inject.java
                    implementation mn.micronaut.inject
                    implementation mn.micronaut.test.junit5
                    implementation "org.junit.jupiter:junit-jupiter-api:$junit_version"
                    implementation "org.junit.jupiter:junit-jupiter-params:$junit_version"
                    implementation "org.junit.jupiter:junit-jupiter-engine:$junit_version"
                    implementation "org.mockito:mockito-core:$mockito_version"
                    implementation "org.mockito:mockito-junit-jupiter:$mockito_version"
                    implementation "org.apache.logging.log4j:log4j-core-test:$log4j2_version"
                }
            }
        }
    }

    test {
        ignoreFailures = true
    }

    final def isNonStableDependencyVersion = { String version ->
        final def stableKeyword = ['RELEASE', 'FINAL', 'GA', 'JRE'].any { version.toUpperCase().contains(it) }
        final def regex = /^[0-9,.v-]+(-r)?$/
        return !stableKeyword && !(version ==~ regex)
    }

    tasks.named("dependencyUpdates").configure {
        rejectVersionIf {
            isNonStableDependencyVersion(it.candidate.version)
        }
    }

    if (isCIEnvironment) {
        // Conditionally configure jacoco test reports when running in a CI environment
        jacocoTestReport {
            afterEvaluate {
                classDirectories.setFrom(files(classDirectories.files.collect {
                    fileTree(
                            dir: it,
                            include: [
                                    '**/global/symbio/**/*'
                            ],
                            exclude: [
                                    '**/global/symbio/**/grpc/**/*',
                                    '**/global/symbio/**/factory/**/*',
                                    '**/global/symbio/**/persistence/**/*',
                                    '**/global/symbio/**/*Model*',
                                    '**/global/symbio/**/model/**/*'
                            ])
                }))
            }
            reports {
                xml.required = true
                csv.required = false
                html.required = false
            }
        }

        jacocoToCobertura {
            splitByPackage.set(true)
            inputFile.set(layout.buildDirectory.file("reports/jacoco/test/jacocoTestReport.xml"))
            outputFile.set(layout.buildDirectory.file("reports/cobertura/test/cobertura.xml"))
        }

        tasks.jacocoToCobertura.dependsOn(jacocoTestReport)

        task copyJUnitXMLTestReportsToTopLevel(type: Copy, group: 'reports') {
            dependsOn test
            from test
            into "$rootDir/build/reports/test"
            include '*.xml'
        }

        task copyJacocoXMLTestReportsToTopLevel(type: Copy, group: 'reports') {
            dependsOn jacocoTestReport
            from jacocoTestReport
            into "$rootDir/build/reports/jacoco"
            include '*.xml'
            rename { file -> "${path.replace(':', '-').replace(name, '').substring(1)}${file}" }
        }

        task copyCoberturaXMLTestReportsToTopLevel(type: Copy, group: 'reports') {
            dependsOn tasks.jacocoToCobertura
            from "$buildDir/reports/cobertura/test/"
            into "$rootDir/build/reports/cobertura"
            include '*.xml'
        }

        task copyTestReportsToTopLevel(group: 'reports') {
            dependsOn copyJUnitXMLTestReportsToTopLevel, copyJacocoXMLTestReportsToTopLevel, copyCoberturaXMLTestReportsToTopLevel
        }
    }
}

subprojects {
    if (isCIEnvironment) {
        apply plugin: 'org.owasp.dependencycheck'

        dependencyCheck {
            suppressionFile = "${rootDir}/dependency-check-suppressions.xml"

            //requested from here https://nvd.nist.gov/developers/request-an-api-key
            //no API key will result to "An NVD API Key was not provided - it is highly recommended to use an NVD API key as the update can take a VERY long time without an API Key"
            def nvdKeyProvider = providers.gradleProperty('nvdApiKey')
            if (nvdKeyProvider.isPresent()) {
                nvd.apiKey = nvdKeyProvider.get()
            }

            autoUpdate = true
            format = 'JUNIT'
            junitFailOnCVSS = 7.0
            // A value of 7.0 or greater denotes HIGH/CRITICAL as per: https://nvd.nist.gov/vuln-metrics/cvss
            // failBuildOnCVSS = 7.0 // A value of 7.0 or greater denotes HIGH/CRITICAL as per: https://nvd.nist.gov/vuln-metrics/cvss

            analyzers {
                experimentalEnabled = false
                archiveEnabled = true
                jarEnabled = true
                opensslEnabled = true
                nuspecEnabled = false
                assemblyEnabled = false
                msbuildEnabled = false
                golangDepEnabled = false
                golangModEnabled = false
                cocoapodsEnabled = false
                swiftEnabled = false
                dartEnabled = false
                swiftPackageResolvedEnabled = false
                bundleAuditEnabled = false
                pyDistributionEnabled = false
                pyPackageEnabled = false
                rubygemsEnabled = false
                cmakeEnabled = false
                autoconfEnabled = false
                composerEnabled = false
                cpanEnabled = false
                nodeEnabled = false
                nugetconfEnabled = false
                centralEnabled = false //central analysis is always failing
            }
        }
    }

    // Ensure that sub-project coordinates are unique based upon module structure
    final def subgroup = path.replace(':', '.').replace('-', '_')
    group = "${rootProject.group}${subgroup}"
    base {
        // Archive naming scheme: {module}-{version}.{extension}
        // I.e., 'core-persistence-api-1.0.0-SNAPSHOT.jar'
        final def module = project.group.replace(rootProject.group + '.', '').replace('.', '-')
        archivesName = module
    }
}
