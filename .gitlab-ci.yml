image: registry.gitlab.com/mnf-group/billing/base-image/corretto-pipeline:latest

include:
  - local: config/gitlab/Code-Quality.gitlab-ci.yml

stages:
  - test
  - badges
  - build
  - containers
  - scan dependencies review
  - scan dependencies uat
  - scan dependencies production
  - scan containers review
  - scan containers uat
  - scan containers production
  - plan review
  - plan uat
  - plan production
  - deploy review
  - deploy uat
  - deploy production
  - destroy review
  - destroy uat
  - destroy production

variables:
  GIT_DEPTH: 0
  REPOSITORY_NAME: billing
  REGISTRY: registry.gitlab.com/mnf-group/billing/${REPOSITORY_NAME}
  DOCKER_HOST: tcp://localhost:2376
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"
  DOCKER_TLS_VERIFY: 1
  DOCKER_CERT_PATH: "$DOCKER_TLS_CERTDIR/client"
  GRADLE_USER_HOME: cache/
  CI: 1
  HELM_REGISTRY: https://gitlab.com/api/v4/projects/********/packages/helm/stable


.common_variables: &common_variables
  TF_VAR_commit_ref: $CI_COMMIT_REF_SLUG
  TF_VAR_commit_sha: $CI_COMMIT_SHA
  TF_VAR_commit_tag: $CI_COMMIT_TAG
  ACCOUNT: $OIDC_ACCOUNT
  TEAM_ROLE_ARN: $OIDC_ROLE_default

.nonprod_variables: &nonprod_variables
  OIDC_ACCOUNT: "nonprod"
  <<: *common_variables

.prod_variables: &prod_variables
  OIDC_ACCOUNT: "prod"
  <<: *common_variables

.my_variables: &my_variables
  REGION: ap-southeast-1
  TF_VAR_aws_region: $REGION
  COUNTRY: my

.au_variables: &au_variables
  REGION: ap-southeast-2
  TF_VAR_aws_region: $REGION
  COUNTRY: au

.review_deployment_variables: &review_deployment_variables
  COUNTRIES: 'MY,SG,AU,NZ'
  TF_VAR_remote_workspace: uat
  TERRAFORM_WORKSPACE: dev

.uat_deployment_variables: &uat_deployment_variables
  COUNTRIES: 'MY,SG,AU,NZ'
  TF_VAR_remote_workspace: uat
  TERRAFORM_WORKSPACE: uat

.prod_deployment_variables: &prod_deployment_variables
  COUNTRIES: 'MY,SG,AU,NZ'
  TF_VAR_remote_workspace: prod
  TERRAFORM_WORKSPACE: prod

.plan_deployment: &plan_deployment
  extends: .execute_OIDC_aws_access
  script:
    - chmod +x deployments/plan.sh
    - sh deployments/plan.sh

.apply_deployment: &apply_deployment
  extends: .execute_OIDC_aws_access
  script:
    - chmod +x deployments/deploy.sh
    - deployments/deploy.sh

.destroy_deployment: &destroy_deployment
  extends: .execute_OIDC_aws_access
  script:
    - chmod +x deployments/undeploy.sh
    - deployments/undeploy.sh

.login_to_docker: &login_to_docker
  before_script:
    - until docker info; do sleep 1; done
    - echo "$CI_DEPENDENCY_PROXY_PASSWORD" | docker login $CI_DEPENDENCY_PROXY_SERVER --username $CI_DEPENDENCY_PROXY_USER --password-stdin
    - echo "$CI_REGISTRY_PASSWORD" | docker login $REGISTRY --username $CI_REGISTRY_USER --password-stdin

##################################################################################
# Caches
##################################################################################
.gradle-build-cache: &gradle-build-cache
  key: billing-build-cache-$CI_COMMIT_REF_SLUG
  fallback_keys: [ billing-build-cache-$CI_DEFAULT_BRANCH ]
  paths: [ cache/caches/, cache/notifications/, cache/wrapper/ ]
  policy: pull

.gradle-test-cache: &gradle-test-cache
  key: billing-test-cache-$CI_COMMIT_REF_SLUG
  fallback_keys: [ billing-test-cache-$CI_DEFAULT_BRANCH ]
  paths: [ cache/caches/, cache/notifications/, cache/wrapper/ ]
  policy: pull-push

##################################################################################
# Parallel Matrix, Services and Tags
##################################################################################
.deployments: &deployments
  parallel:
    matrix:
      - SERVICE_NAME: [ ledger, invoice, template, payment, product-bridge ]

.deployments-review: &deployments_review
  parallel:
    matrix:
      - SERVICE_NAME: [ ledger, invoice, template, product-bridge ]

.builds: &builds
  parallel:
    matrix:
      - SERVICE_NAME: [ ledger, invoice, payment, product-bridge ]

.scan_dependencies_modules: &scan_dependencies_modules
  parallel:
    matrix:
      - MODULE: [ ledger, invoice, payment, product-bridge, core ]

.common-tags: &common-tags
  tags: [ core-platform-runners ]

.docker-tags: &docker-tags
  tags: [ core-platform-runners, core-cloud-k8s-runners, feature:dind ]

.kafka-tags: &kafka-tags
  tags: [ core-cloud-ec2-runners ]

.deployment-tags-nonprod: &deployment-tags-nonprod
  tags: [ core-platform-runners, core-cloud-k8s-runners ]

.deployment-tags-production: &deployment-tags-production
  tags: [ core-platform-runners, core-cloud-k8s-runners ]

.services-dind: &services-dind
  services:
    - $CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX/docker:27-dind

##################################################################################
# OIDC AWS ACCESS
##################################################################################
.execute_OIDC_aws_access:
  id_tokens:
    GITLAB_OIDC_TOKEN:
      aud: https://gitlab.com
  before_script:
    - echo "machine gitlab.com login gitlab-ci-token password ${CI_JOB_TOKEN}" >> ${HOME}/.netrc
    - export ENVIRONMENT=${CI_ENVIRONMENT_SLUG}
    - export K8S_NAMESPACE=billing-${ENVIRONMENT}
    - |
      if [ -z "$TERRAFORM_WORKSPACE" ]; then
        export TERRAFORM_WORKSPACE=${ENVIRONMENT}
      fi
    - |
      if [ ! -z "$env" ]; then
        cp $env .env
      fi
    - echo ${ACCOUNT}
    - echo ${TEAM_ROLE_ARN}
    - unset AWS_ACCESS_KEY_ID
    - unset AWS_SECRET_ACCESS_KEY
    - grep -rl git::ssh://************** . | xargs sed -i "s|git::ssh://**************|git::https://gitlab-ci-token:${CI_JOB_TOKEN}@gitlab.com|g"
    - echo -e "\e[0Ksection_start:`date +%s`:my_first_section[collapsed=true]\r\e[0KIdentity Account section"
    - mkdir -p ~/.aws
    - echo "${GITLAB_OIDC_TOKEN}" > /tmp/web_identity_token
    - echo -e "[profile oidc]\nrole_arn=${TEAM_ROLE_ARN}\nweb_identity_token_file=/tmp/web_identity_token" > ~/.aws/config
    - export AWS_PROFILE=oidc
    - aws sts get-caller-identity
    - echo -e "\e[0Ksection_end:`date +%s`:my_first_section\r\e[0K"
    - echo -e "\e[0Ksection_start:`date +%s`:my_2nd_section[collapsed=true]\r\e[0KTarget Account section"
    - export ctrl="gitlab_oidc_role"
    - export role="\$${ctrl}_${ACCOUNT}"
    - eval echo $role > ~/.aws/config.sts_role
    - export sts_role=`cat ~/.aws/config.sts_role| tr -d '"'`
    - echo "${GITLAB_USER_ID}-${ACCOUNT}-${CI_PROJECT_ID}-${CI_PIPELINE_ID}-${CI_JOB_ID}@oidc" > ~/.aws/.cfg.sts_sn
    - export sts_sn=`cat ~/.aws/.cfg.sts_sn| tr -d '"'`
    - aws sts assume-role --role-arn $sts_role --role-session-name $sts_sn > ~/aws_session_creds.txt
    - export AWS_ACCESS_KEY_ID=$(cat ~/aws_session_creds.txt | jq .Credentials.AccessKeyId | xargs)
    - export AWS_SECRET_ACCESS_KEY=$(cat ~/aws_session_creds.txt | jq .Credentials.SecretAccessKey | xargs)
    - export AWS_SESSION_TOKEN=$(cat ~/aws_session_creds.txt | jq .Credentials.SessionToken | xargs)
    - aws sts get-caller-identity
    - echo -e "\e[0Ksection_end:`date +%s`:my_2nd_section\r\e[0K"
    - echo "assume role in $ACCOUNT with session name - $sts_sn"
    - echo "running helm repo add helm-base"
    - helm repo add --username "${CI_REGISTRY_USER}" --password "${CI_REGISTRY_PASSWORD}" helm-base "${HELM_REGISTRY}"
    - echo "helm repo add helm-base is completed successfully"

##################################################################################
# Kafka Command Template
##################################################################################
.execute_kafka_commands:
  needs: [ ]
  image:
    name: $CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX/bitnami/kafka:latest
    entrypoint: [ "" ]
  variables:
    GIT_STRATEGY: none
  before_script:
    - KAFKA_TRUSTSTORE_FILE_LOCATION=truststore.jks
    - KAFKA_PROPERTIES_FILE=kafka.properties
    - KAFKA_PROPERTIES_REPORTING_FILE=kafka_reporting.properties
    - echo "Decoding Kafka Truststore into $KAFKA_TRUSTSTORE_FILE_LOCATION"
    - echo $KAFKA_TRUSTSTORE_AU | base64 -d > $KAFKA_TRUSTSTORE_FILE_LOCATION
    - echo "Decoding Kafka Properties into $KAFKA_PROPERTIES_FILE"
    - echo $KAFKA_PROPERTIES | base64 -d > $KAFKA_PROPERTIES_FILE
    - echo "Decoding Kafka Properties Reporting into $KAFKA_PROPERTIES_REPORTING_FILE"
    - echo $KAFKA_PROPERTIES_REPORTING | base64 -d > $KAFKA_PROPERTIES_REPORTING_FILE
  <<: *kafka-tags

create review kafka topics:
  extends: .execute_kafka_commands
  stage: deploy review
  when: manual
  only: [ merge_requests ]
  environment:
    name: review/$CI_COMMIT_REF_SLUG
    on_stop: destroy review kafka topics
  variables:
    KAFKA_REPLICATION_FACTOR: 3
    KAFKA_PARTITIONS: 32
  script:
    - KAFKA_TOPICS=("uat.billing.transaction.$CI_COMMIT_REF_SLUG" "uat.billing.transaction.deadletter.$CI_COMMIT_REF_SLUG")
    - |
      for topic in "${KAFKA_TOPICS[@]}"
      do
        echo "Creating Kafka Topic: $topic"
        kafka-topics.sh --bootstrap-server $KAFKA_BOOTSTRAP_SERVERS --command-config $KAFKA_PROPERTIES_FILE --create --topic $topic --replication-factor $KAFKA_REPLICATION_FACTOR --partitions $KAFKA_PARTITIONS --if-not-exists
        kafka-topics.sh --bootstrap-server $KAFKA_BOOTSTRAP_SERVERS --command-config $KAFKA_PROPERTIES_FILE --describe --topic $topic
      done
    - KAFKA_TOPICS_REPORTING=("uat.billing.reporting.invoice.$CI_COMMIT_REF_SLUG" "uat.billing.reporting.invoice_transaction.$CI_COMMIT_REF_SLUG" "uat.billing.reporting.transaction.$CI_COMMIT_REF_SLUG")
    - |
      for topic in "${KAFKA_TOPICS_REPORTING[@]}"
      do
        echo "Creating Kafka Topic: $topic"
        kafka-topics.sh --bootstrap-server $KAFKA_BOOTSTRAP_SERVERS --command-config $KAFKA_PROPERTIES_REPORTING_FILE --create --topic $topic --replication-factor $KAFKA_REPLICATION_FACTOR --partitions $KAFKA_PARTITIONS --if-not-exists
        kafka-topics.sh --bootstrap-server $KAFKA_BOOTSTRAP_SERVERS --command-config $KAFKA_PROPERTIES_REPORTING_FILE --describe --topic $topic
      done

destroy review kafka topics:
  extends: .execute_kafka_commands
  stage: destroy review
  only: [ merge_requests ]
  when: manual
  environment:
    name: review/$CI_COMMIT_REF_SLUG
    action: stop
  script:
    - KAFKA_TOPICS=("uat.billing.transaction.$CI_COMMIT_REF_SLUG" "uat.billing.transaction.deadletter.$CI_COMMIT_REF_SLUG")
    - |
      for topic in "${KAFKA_TOPICS[@]}"
      do
        echo "Deleting Kafka Topic: $topic"
        kafka-topics.sh --bootstrap-server $KAFKA_BOOTSTRAP_SERVERS --command-config $KAFKA_PROPERTIES_FILE --delete --topic $topic --if-exists
      done
    - KAFKA_TOPICS_REPORTING=("uat.billing.reporting.invoice.$CI_COMMIT_REF_SLUG" "uat.billing.reporting.invoice_transaction.$CI_COMMIT_REF_SLUG" "uat.billing.reporting.transaction.$CI_COMMIT_REF_SLUG")
    - |
      for topic in "${KAFKA_TOPICS_REPORTING[@]}"
      do
        echo "Deleting Kafka Topic: $topic"
        kafka-topics.sh --bootstrap-server $KAFKA_BOOTSTRAP_SERVERS --command-config $KAFKA_PROPERTIES_REPORTING_FILE --delete --topic $topic --if-exists
      done

##################################################################################
# Test
##################################################################################
test:
  stage: test
  interruptible: true
  cache:
    <<: *gradle-test-cache
  script:
    - ./gradlew --build-cache -q javaToolchains
    - ./gradlew --build-cache check jacocoAggregatedReport copyTestReportsToTopLevel --continue --stacktrace --scan
  coverage: '/    - Instruction Coverage: ([0-9.]+)%/'
  artifacts:
    when: always
    paths:
      - ./build/reports/test/*.xml
      - ./build/reports/jacoco/*.xml
      - ./build/reports/cobertura/*.xml
    reports:
      junit:
        - ./build/reports/test/*.xml
      coverage_report:
        coverage_format: cobertura
        path: ./build/reports/cobertura/*.xml
  needs: [ ]
  only: [ merge_requests, main, tags ]
  <<: *common-tags

##################################################################################
# Code Coverage
##################################################################################
code quality:
  extends: .code_quality
  stage: test
  interruptible: true
  allow_failure: true
  <<: *login_to_docker
  <<: *services-dind
  artifacts:
    reports:
      codequality: gl-code-quality-report.json
    expire_in: 1 week
  needs: [ ]
  only: [ merge_requests, main, tags ]
  when: manual
  <<: *docker-tags

##################################################################################
# Update Gitlab Badges
##################################################################################
.base_update_badge: &base_update_badge
  stage: badges
  needs: [ ]
  only: [ main ]
  <<: *common-tags

"update badges: [terraform]":
  extends: .base_update_badge
  script:
    - echo "Updating Terraform badge"
    - TERRAFORM_BADGE_ID=$(curl --header "PRIVATE-TOKEN:${BADGE_TOKEN}" https://gitlab.com/api/v4/projects/${CI_PROJECT_ID}/badges | jq '.[] | select(.name != null) | select(.name | contains ("terraform")).id')
    - TF_VERSION=$(terraform -v | { read _ v; echo ${v#v}; })
    - curl --request PUT --header "PRIVATE-TOKEN:${BADGE_TOKEN}" --data "image_url=https://img.shields.io/badge/Terraform-${TF_VERSION}-844FBA?logo=terraform" https://gitlab.com/api/v4/projects/${CI_PROJECT_ID}/badges/${TERRAFORM_BADGE_ID}

"update badges: [java]":
  extends: .base_update_badge
  script:
    - echo "Updating Java badge"
    - JAVA_BADGE_ID=$(curl --header "PRIVATE-TOKEN:${BADGE_TOKEN}" https://gitlab.com/api/v4/projects/${CI_PROJECT_ID}/badges | jq '.[] | select(.name != null) | select(.name | contains ("java")).id')
    - JAVA_VERSION=$(java -version 2>&1 | head -n 1 | { read _ _ v _; echo ${v};} | sed 's/"//g' )
    - curl --request PUT --header "PRIVATE-TOKEN:${BADGE_TOKEN}" --data "image_url=https://img.shields.io/badge/Java-${JAVA_VERSION}-5382a1?logo=openjdk" https://gitlab.com/api/v4/projects/${CI_PROJECT_ID}/badges/${JAVA_BADGE_ID}

"update badges: [gradle]":
  extends: .base_update_badge
  script:
    - echo "Updating Gradle badge"
    - GRADLE_BADGE_ID=$(curl --header "PRIVATE-TOKEN:${BADGE_TOKEN}" https://gitlab.com/api/v4/projects/${CI_PROJECT_ID}/badges | jq '.[] | select(.name != null) | select(.name | contains ("gradle")).id')
    - GRADLE_VERSION=$(./gradlew --version | grep -E '^Gradle' | { read _ v; echo ${v}; }  | sed 's/-/--/g')
    - curl --request PUT --header "PRIVATE-TOKEN:${BADGE_TOKEN}" --data "image_url=https://img.shields.io/badge/Gradle-${GRADLE_VERSION}-02303A?logo=gradle" https://gitlab.com/api/v4/projects/${CI_PROJECT_ID}/badges/${GRADLE_BADGE_ID}

##################################################################################
# Build
##################################################################################
build jars:
  stage: build
  interruptible: true
  cache:
    <<: *gradle-build-cache
    policy: pull-push
  script:
    - ./gradlew --build-cache -q javaToolchains
    - ./gradlew --build-cache assemble --stacktrace --scan
  artifacts:
    paths: [ '**/app/build/libs/*.jar' ]
    expire_in: 14 days
  needs: [ ]
  only: [ merge_requests, main, tags ]
  <<: *common-tags

.build:
  stage: containers
  interruptible: true
  <<: *login_to_docker
  <<: *services-dind
  script:
    - if [ $SERVICE_NAME == "template" ]; then exit 0; fi; # short-circuit build job if template module.
    - export DOCKER_IMAGE="${REGISTRY}/${SERVICE_NAME}"
    - docker pull $DOCKER_IMAGE:$CI_COMMIT_REF_SLUG || true
    - docker build --load --cache-from $DOCKER_IMAGE:$CI_COMMIT_REF_SLUG --tag $DOCKER_IMAGE:$CI_COMMIT_SHA --tag $DOCKER_IMAGE:$CI_COMMIT_REF_SLUG $SERVICE_NAME
    - docker push --all-tags $DOCKER_IMAGE
  needs: [ 'build jars' ]
  only: [ merge_requests, main, tags ]

build:
  extends: .build
  parallel: !reference [ .builds, parallel ]
  <<: *docker-tags

##################################################################################
# Plan
##################################################################################
.plan:
  <<: *plan_deployment
  needs: [ ]
  when: manual
  <<: *common-tags

.plan_review:
  stage: plan review
  extends: .plan
  environment:
    name: review-$COUNTRY/$CI_COMMIT_REF_SLUG
    deployment_tier: development
  variables:
    <<: *nonprod_variables
    <<: *review_deployment_variables
  only: [ merge_requests ]
  when: on_success

.plan_uat:
  stage: plan uat
  extends: .plan
  environment:
    name: uat-$COUNTRY
    deployment_tier: staging
  variables:
    <<: *nonprod_variables
    <<: *uat_deployment_variables
  only: [ main, tags ]

.plan_production:
  stage: plan production
  extends: .plan
  environment:
    name: prod-$COUNTRY
    deployment_tier: production
  variables:
    <<: *prod_variables
    <<: *prod_deployment_variables
  only: [ main ]

# Malaysia (my) Jobs
plan review my:
  extends: .plan_review
  parallel: !reference [ .deployments-review, parallel ]
  variables:
    <<: *my_variables

"plan review my: [payment]":
  extends: .plan_review
  variables:
    SERVICE_NAME: payment
    <<: *my_variables
  when: manual

plan uat my:
  extends: .plan_uat
  parallel: !reference [ .deployments, parallel ]
  variables:
    <<: *my_variables

plan production my:
  extends: .plan_production
  parallel: !reference [ .deployments, parallel ]
  variables:
    <<: *my_variables
    TF_VAR_k8s_cluster_name: sg-prod-k8s

# Australia (au) Jobs
plan review au:
  extends: .plan_review
  parallel: !reference [ .deployments-review, parallel ]
  variables:
    <<: *au_variables
    TF_VAR_k8s_cluster_name: syd-nonprod-k8s

"plan review au: [payment]":
  extends: .plan_review
  variables:
    SERVICE_NAME: payment
    <<: *au_variables
    TF_VAR_k8s_cluster_name: syd-nonprod-k8s
  when: manual

plan uat au:
  extends: .plan_uat
  parallel: !reference [ .deployments, parallel ]
  variables:
    <<: *au_variables
    TF_VAR_k8s_cluster_name: syd-nonprod-k8s

#plan production au:
#  extends: .plan_production
#  parallel: !reference [ .deployments, parallel ]
#  variables:
#    <<: *au_variables
#    TF_VAR_k8s_cluster_name: syd-prod-k8s
##################################################################################
# Scan
##################################################################################
.scan_dependency_module:
  interruptible: true
  cache:
    <<: *gradle-build-cache
  script:
    - CAMEL_CASE_MODULE=$(echo "$MODULE" | sed -E 's/(^|-)([a-z])/\U\2/g')
    - GRADLE_TASK="dependencyCheck$CAMEL_CASE_MODULE"
    - ./gradlew --build-cache -q javaToolchains
    - ./gradlew --build-cache $GRADLE_TASK -PnvdApiKey=$NVD_API_KEY --continue --stacktrace
    - chmod +x config/gitlab/pagerduty.sh
    - |
      CRITICAL=false
      HIGH=false
      
      REPORT_FILE="$MODULE/build/reports/dependency-check-junit.xml"
      
      # Case insensitive and regardless of version
      # Ex output we're trying to match <failure message="cvssV3: HIGH, score: 7.5 (CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H/E:3.9/RC:R/MAV:A)"/>
      if grep -iq 'cvss.*critical' "$REPORT_FILE"; then CRITICAL=true; fi
      if grep -iq 'cvss.*high' "$REPORT_FILE"; then HIGH=true; fi
      
      # Only alert on critical and if env is not review
      if [ "$CRITICAL" = "true" ] && ! echo "$CI_ENVIRONMENT_NAME" | grep -qE '^review-'; then
        echo "Critical dependency found — triggering PagerDuty"
        sh config/gitlab/pagerduty.sh \
          "Dependency Scan Alert: Critical vulnerability in $MODULE" \
          "$CI_ENVIRONMENT_NAME" \
          "$CI_JOB_URL"
      fi
      
      # Fail the job on either severity
      if [ "$CRITICAL" = "true" ] || [ "$HIGH" = "true" ]; then
        echo "Build failed due to high/critical dependency vulnerabilities. HIGH=$HIGH, CRITICAL=$CRITICAL"
        exit 1
      fi
      
      echo "No high or critical vulnerabilities in dependency scan."
  artifacts:
    when: always
    paths:
      - "**/build/reports/dependency-check-junit.xml"
  needs: [ ]
  only: [ merge_requests, main, tags ]
  <<: *common-tags

scan dependencies review:
  stage: scan dependencies review
  extends: .scan_dependency_module
  parallel: !reference [ .scan_dependencies_modules, parallel ]
  environment:
    name: review/$CI_COMMIT_REF_SLUG
    deployment_tier: development
  only: [ merge_requests ]

scan dependencies uat:
  stage: scan dependencies uat
  extends: .scan_dependency_module
  parallel: !reference [ .scan_dependencies_modules, parallel ]
  environment:
    name: uat
    deployment_tier: staging
  only: [ main, tags ]

scan dependencies prod:
  stage: scan dependencies production
  extends: .scan_dependency_module
  parallel: !reference [ .scan_dependencies_modules, parallel ]
  environment:
    name: prod
    deployment_tier: production
  only: [ main ]

.scan_container:
  interruptible: true
  image:
    name: $CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX/aquasec/trivy:latest
    entrypoint: [ "" ]
  variables:
    TRIVY_USERNAME: $CI_REGISTRY_USER
    TRIVY_PASSWORD: $CI_REGISTRY_PASSWORD
    TRIVY_AUTH_URL: $CI_REGISTRY
    DOCKER_IMAGE: $REGISTRY/$SERVICE_NAME:$CI_COMMIT_SHA
  before_script:
    - apk add --no-cache curl jq
  script:
    - chmod +x config/gitlab/trivy.sh
    - config/gitlab/trivy.sh
    - chmod +x config/gitlab/pagerduty.sh
    - |
      CRITICAL=false
      HIGH=false
      
      for severity in $(jq -r '.vulnerabilities[]?.severity' "$CI_PROJECT_DIR/gl-container-scanning-report.json"); do
        if [ "$severity" = "Critical" ]; then CRITICAL=true; fi
        if [ "$severity" = "High" ]; then HIGH=true; fi
      done
      
      # Only alert on critical and if env is not review
      if [ "$CRITICAL" = "true" ] && ! echo "$CI_ENVIRONMENT_NAME" | grep -qE '^review-'; then
        echo "Triggering PagerDuty..."
        sh config/gitlab/pagerduty.sh \
          "Container Scan Alert: Critical vulnerability in $SERVICE_NAME" \
          "$CI_ENVIRONMENT_NAME" \
          "$CI_JOB_URL"
      fi
      
      if [ "$CRITICAL" = "true" ] || [ "$HIGH" = "true" ]; then
        echo "Build failed due to high/critical vulnerabilities. HIGH=$HIGH, CRITICAL=$CRITICAL"
        exit 1
      fi
  artifacts:
    when: always
    reports:
      container_scanning: gl-container-scanning-report.json
  only: [ merge_requests, main, tags ]
  <<: *common-tags

.scan_review:
  stage: scan containers review
  extends: .scan_container
  environment:
    name: review/$CI_COMMIT_REF_SLUG
    deployment_tier: development
  only: [ merge_requests ]

.scan_uat:
  stage: scan containers uat
  extends: .scan_container
  environment:
    name: uat
    deployment_tier: staging
  only: [ main, tags ]

.scan_production:
  stage: scan containers production
  extends: .scan_container
  environment:
    name: prod
    deployment_tier: production
  only: [ main ]

"scan review: [ledger]":
  extends: .scan_review
  variables:
    SERVICE_NAME: ledger
  needs: [ "build: [ledger]" ]

"scan review: [invoice]":
  extends: .scan_review
  variables:
    SERVICE_NAME: invoice
  needs: [ "build: [invoice]" ]

"scan review: [payment]":
  extends: .scan_review
  variables:
    SERVICE_NAME: payment
  needs: [ "build: [payment]" ]

"scan review: [product-bridge]":
  extends: .scan_review
  variables:
    SERVICE_NAME: product-bridge
  needs: [ "build: [product-bridge]" ]

"scan uat: [ledger]":
  extends: .scan_uat
  variables:
    SERVICE_NAME: ledger
  needs: [ "build: [ledger]" ]

"scan uat: [invoice]":
  extends: .scan_uat
  variables:
    SERVICE_NAME: invoice
  needs: [ "build: [invoice]" ]

"scan uat: [payment]":
  extends: .scan_uat
  variables:
    SERVICE_NAME: payment
  needs: [ "build: [payment]" ]

"scan uat: [product-bridge]":
  extends: .scan_uat
  variables:
    SERVICE_NAME: product-bridge
  needs: [ "build: [product-bridge]" ]

"scan production: [ledger]":
  extends: .scan_production
  variables:
    SERVICE_NAME: ledger
  needs: [ "build: [ledger]" ]

"scan production: [invoice]":
  extends: .scan_production
  variables:
    SERVICE_NAME: invoice
  needs: [ "build: [invoice]" ]

"scan production: [payment]":
  extends: .scan_production
  variables:
    SERVICE_NAME: payment
    PAGERDUTY_ROUTING_KEY: $BFF_ROUTING_KEY
  needs: [ "build: [payment]" ]

"scan production: [product-bridge]":
  extends: .scan_production
  variables:
    SERVICE_NAME: product-bridge
  needs: [ "build: [product-bridge]" ]


##################################################################################
# Deploy
##################################################################################
.deploy:
  #  cache:
  #    <<: *gradle-build-cache
  variables:
    DOCKER_IMAGE: $REGISTRY/$SERVICE_NAME:$CI_COMMIT_SHA
  <<: *apply_deployment
  when: manual

.deploy_review:
  stage: deploy review
  extends: .deploy
  environment:
    name: review-$COUNTRY/$CI_COMMIT_REF_SLUG
    deployment_tier: development
  #    on_stop: "destroy review $COUNTRY: [$SERVICE_NAME]"
  variables:
    <<: *nonprod_variables
    <<: *review_deployment_variables
  only: [ merge_requests ]

.deploy_uat:
  stage: deploy uat
  extends: .deploy
  environment:
    name: uat-$COUNTRY
    deployment_tier: staging
  #    on_stop: destroy uat $COUNTRY: [$SERVICE_NAME]
  variables:
    <<: *nonprod_variables
    <<: *uat_deployment_variables
  only: [ main, tags ]

.deploy_production:
  stage: deploy production
  extends: .deploy
  environment:
    name: prod-$COUNTRY
    deployment_tier: production
  #    on_stop: destroy prod $COUNTRY: [$SERVICE_NAME]
  variables:
    <<: *prod_variables
    <<: *prod_deployment_variables
  only: [ main ]
##################################################################################
# Deploy Ledger
##################################################################################
"deploy review my: [ledger]":
  extends: .deploy_review
  variables:
    <<: *my_variables
    SERVICE_NAME: ledger
  needs: [ "create review kafka topics", "build: [ledger]", "plan review my: [ledger]" ]
  <<: *deployment-tags-nonprod

"deploy uat my: [ledger]":
  extends: .deploy_uat
  variables:
    <<: *my_variables
    SERVICE_NAME: ledger
  needs: [ "build: [ledger]", "plan uat my: [ledger]" ]
  <<: *deployment-tags-nonprod

"deploy production my: [ledger]":
  extends: .deploy_production
  variables:
    <<: *my_variables
    SERVICE_NAME: ledger
    TF_VAR_k8s_cluster_name: sg-prod-k8s
  needs: [ "build: [ledger]", "plan production my: [ledger]" ]
  <<: *deployment-tags-production

"deploy review au: [ledger]":
  extends: .deploy_review
  variables:
    <<: *au_variables
    SERVICE_NAME: ledger
    TF_VAR_k8s_cluster_name: syd-nonprod-k8s
  needs: [ "create review kafka topics", "build: [ledger]", "plan review au: [ledger]" ]
  <<: *deployment-tags-nonprod

#"deploy uat au: [ledger]":
#  extends: .deploy_uat
#  variables:
#    SERVICE_NAME: ledger
#    TF_VAR_k8s_cluster_name: syd-nonprod-k8s
#    <<: *au_variables
#  needs: [ "build: [ledger]", "plan uat au: [ledger]" ]
#  <<: *deployment-tags-nonprod
#
#"deploy production au: [ledger]":
#  extends: .deploy_production
#  variables:
#    SERVICE_NAME: ledger
#    TF_VAR_k8s_cluster_name: syd-prod-k8s
#    <<: *au_variables
#  needs: [ "build: [ledger]", "plan production au: [ledger]" ]
#  <<: *deployment-tags-production

##################################################################################
# Deploy Invoice
##################################################################################
"deploy review my: [invoice]":
  extends: .deploy_review
  variables:
    <<: *my_variables
    SERVICE_NAME: invoice
  needs: [ "create review kafka topics", "build: [invoice]", "plan review my: [invoice]" ]
  <<: *deployment-tags-nonprod

"deploy uat my: [invoice]":
  extends: .deploy_uat
  variables:
    <<: *my_variables
    SERVICE_NAME: invoice
  needs: [ "build: [invoice]", "plan uat my: [invoice]" ]
  <<: *deployment-tags-nonprod

"deploy production my: [invoice]":
  extends: .deploy_production
  variables:
    <<: *my_variables
    SERVICE_NAME: invoice
    TF_VAR_k8s_cluster_name: sg-prod-k8s
  needs: [ "build: [invoice]", "plan production my: [invoice]" ]
  <<: *deployment-tags-production

"deploy review au: [invoice]":
  extends: .deploy_review
  variables:
    <<: *au_variables
    SERVICE_NAME: invoice
    TF_VAR_k8s_cluster_name: syd-nonprod-k8s
  needs: [ "create review kafka topics", "build: [invoice]", "plan review au: [invoice]" ]
  <<: *deployment-tags-nonprod

#"deploy uat au: [invoice]":
#  extends: .deploy_uat
#  variables:
#    <<: *au_variables
#    SERVICE_NAME: invoice
#    TF_VAR_k8s_cluster_name: syd-nonprod-k8s
#  needs: [ "build: [invoice]", "plan uat au: [invoice]" ]
#  <<: *deployment-tags-nonprod
#
#"deploy production au: [invoice]":
#  extends: .deploy_production
#  variables:
#    <<: *au_variables
#    SERVICE_NAME: invoice
#    TF_VAR_k8s_cluster_name: syd-prod-k8s
#  needs: [ "build: [invoice]", "plan production au: [invoice]" ]
#  <<: *deployment-tags-production

##################################################################################
# Deploy Template
##################################################################################
"deploy review my: [template]":
  extends: .deploy_review
  variables:
    <<: *my_variables
    SERVICE_NAME: template
    TF_VAR_k8s_cluster_name: syd-nonprod-k8s
  needs: [ "create review kafka topics", "plan review my: [template]" ]
  <<: *deployment-tags-nonprod

"deploy uat my: [template]":
  extends: .deploy_uat
  variables:
    <<: *my_variables
    SERVICE_NAME: template
  needs: [ "plan uat my: [template]" ]
  <<: *deployment-tags-nonprod

"deploy production my: [template]":
  extends: .deploy_production
  variables:
    <<: *my_variables
    SERVICE_NAME: template
    TF_VAR_k8s_cluster_name: sg-prod-k8s
  needs: [ "plan production my: [template]" ]
  <<: *deployment-tags-production

"deploy review au: [template]":
  extends: .deploy_review
  variables:
    <<: *au_variables
    SERVICE_NAME: template
    TF_VAR_k8s_cluster_name: syd-nonprod-k8s
  needs: [ "create review kafka topics", "plan review au: [template]" ]
  <<: *deployment-tags-nonprod
#
#"deploy uat au: [template]":
#  extends: .deploy_uat
#  variables:
#    <<: *au_variables
#    SERVICE_NAME: template
#    TF_VAR_k8s_cluster_name: syd-nonprod-k8s
#  needs: [ "plan uat au: [template]" ]
#  <<: *deployment-tags-nonprod
#
#"deploy production au: [template]":
#  extends: .deploy_production
#  variables:
#    <<: *au_variables
#    SERVICE_NAME: template
#    TF_VAR_k8s_cluster_name: syd-prod-k8s
#  needs: [ "plan production au: [template]" ]
#  <<: *deployment-tags-production

##################################################################################
# Deploy Payment
##################################################################################
"deploy review my: [payment]":
  extends: .deploy_review
  variables:
    <<: *my_variables
    SERVICE_NAME: payment
    TF_VAR_k8s_cluster_name: sg-nonprod-k8s
  needs: [ "create review kafka topics", "build: [payment]", "plan review my: [payment]" ]
  <<: *deployment-tags-nonprod

"deploy uat my: [payment]":
  extends: .deploy_uat
  variables:
    <<: *my_variables
    SERVICE_NAME: payment
  needs: [ "build: [payment]", "plan uat my: [payment]" ]
  <<: *deployment-tags-nonprod

"deploy production my: [payment]":
  extends: .deploy_production
  variables:
    <<: *my_variables
    SERVICE_NAME: payment
    TF_VAR_k8s_cluster_name: sg-prod-k8s
  needs: [ "build: [payment]", "plan production my: [payment]" ]
  <<: *deployment-tags-production

"deploy review au: [payment]":
  extends: .deploy_review
  variables:
    <<: *au_variables
    SERVICE_NAME: payment
    TF_VAR_k8s_cluster_name: syd-nonprod-k8s
  needs: [ "create review kafka topics", "build: [payment]", "plan review au: [payment]" ]
  <<: *deployment-tags-nonprod
#
#"deploy uat au: [payment]":
#  extends: .deploy_uat
#  variables:
#    <<: *au_variables
#    SERVICE_NAME: payment
#    TF_VAR_k8s_cluster_name: syd-nonprod-k8s
#  needs: [ "build: [payment]", "plan uat my: [payment]" ]
#  <<: *deployment-tags-nonprod
#
#"deploy production au: [payment]":
#  extends: .deploy_production
#  variables:
#    <<: *au_variables
#    SERVICE_NAME: payment
#    TF_VAR_k8s_cluster_name: syd-prod-k8s
#  needs: [ "build: [payment]", "plan production my: [payment]" ]
#  <<: *deployment-tags-production

##################################################################################
# Deploy Product Bridge
##################################################################################
"deploy review my: [product-bridge]":
  extends: .deploy_review
  variables:
    <<: *my_variables
    SERVICE_NAME: product-bridge
    TF_VAR_k8s_cluster_name: sg-nonprod-k8s
  needs: [ "create review kafka topics", "build: [product-bridge]", "plan review my: [product-bridge]" ]
  <<: *deployment-tags-nonprod

"deploy uat my: [product-bridge]":
  extends: .deploy_uat
  variables:
    <<: *my_variables
    SERVICE_NAME: product-bridge
  needs: [ "build: [product-bridge]", "plan uat my: [product-bridge]" ]
  <<: *deployment-tags-nonprod

"deploy production my: [product-bridge]":
  extends: .deploy_production
  variables:
    <<: *my_variables
    SERVICE_NAME: product-bridge
    TF_VAR_k8s_cluster_name: sg-prod-k8s
  needs: [ "build: [product-bridge]", "plan production my: [product-bridge]" ]
  <<: *deployment-tags-production

"deploy review au: [product-bridge]":
  extends: .deploy_review
  variables:
    <<: *au_variables
    SERVICE_NAME: product-bridge
    TF_VAR_k8s_cluster_name: syd-nonprod-k8s
  needs: [ "create review kafka topics", "build: [product-bridge]", "plan review au: [product-bridge]" ]
  <<: *deployment-tags-nonprod
#
#"deploy uat au: [product-bridge]":
#  extends: .deploy_uat
#  variables:
#    <<: *au_variables
#    SERVICE_NAME: product-bridge
#    TF_VAR_k8s_cluster_name: syd-nonprod-k8s
#  needs: [ "build: [product-bridge]", "plan uat my: [product-bridge]" ]
#  <<: *deployment-tags-nonprod
#
#"deploy production au: [product-bridge]":
#  extends: .deploy_production
#  variables:
#    <<: *au_variables
#    SERVICE_NAME: product-bridge
#    TF_VAR_k8s_cluster_name: sg-prod-k8s
#  needs: [ "build: [product-bridge]", "plan production my: [product-bridge]" ]
#  <<: *deployment-tags-production

##################################################################################
# Destroy
##################################################################################
.destroy:
  environment:
    action: stop
  needs: [ ]
  when: manual
  <<: *destroy_deployment

.destroy_review:
  stage: destroy review
  extends: .destroy
  environment:
    name: review-$COUNTRY/$CI_COMMIT_REF_SLUG
    deployment_tier: development
  variables:
    <<: *nonprod_variables
    <<: *review_deployment_variables
  only: [ merge_requests ]

.destroy_uat:
  stage: destroy uat
  extends: .destroy
  environment:
    deployment_tier: staging
  variables:
    <<: *nonprod_variables
    <<: *uat_deployment_variables
  only: [ main, tags ]

.destroy_prod:
  stage: destroy production
  extends: .destroy
  environment:
    deployment_tier: production
  variables:
    <<: *prod_variables
    <<: *prod_deployment_variables
  only: [ main ]

destroy review my:
  extends: .destroy_review
  parallel: !reference [ .deployments, parallel ]
  variables:
    <<: *my_variables
  needs: [ ]
  <<: *deployment-tags-nonprod

destroy review au:
  extends: .destroy_review
  parallel: !reference [ .deployments, parallel ]
  variables:
    <<: *au_variables
  needs: [ ]
  <<: *deployment-tags-nonprod

##################################################################################
# Destroy Namespace
##################################################################################
.destroy_namespace:
  extends: .execute_OIDC_aws_access
  environment:
    action: stop
  script:
    - chmod +x deployments/destroy.sh
    - sh deployments/destroy.sh
  needs: [ ]
  when: manual

.destroy_namespace_review:
  stage: destroy review
  extends: .destroy_namespace
  environment:
    name: review-$COUNTRY/$CI_COMMIT_REF_SLUG
    deployment_tier: development
  variables:
    <<: *nonprod_variables
    <<: *review_deployment_variables
  only: [ merge_requests ]

.destroy_namespace_uat:
  stage: destroy uat
  extends: .destroy_namespace
  environment:
    name: uat-$COUNTRY
    deployment_tier: staging
  variables:
    <<: *nonprod_variables
    <<: *uat_deployment_variables
  only: [ main, tags ]

.destroy_namespace_production:
  stage: destroy production
  extends: .destroy_namespace
  environment:
    name: prod-$COUNTRY
    deployment_tier: production
  variables:
    <<: *prod_variables
    <<: *prod_deployment_variables
  only: [ main ]

destroy namespace my review:
  extends: .destroy_namespace_review
  variables:
    <<: *my_variables
  <<: *deployment-tags-nonprod

destroy namespace my uat:
  extends: .destroy_namespace_uat
  variables:
    <<: *my_variables
  <<: *deployment-tags-nonprod

destroy namespace my production:
  extends: .destroy_namespace_production
  variables:
    <<: *my_variables
  <<: *deployment-tags-production

destroy namespace au review:
  extends: .destroy_namespace_review
  variables:
    <<: *au_variables
    TF_VAR_k8s_cluster_name: syd-nonprod-k8s
  <<: *deployment-tags-nonprod

destroy namespace au uat:
  extends: .destroy_namespace_uat
  variables:
    <<: *au_variables
    TF_VAR_k8s_cluster_name: syd-nonprod-k8s
  <<: *deployment-tags-nonprod

destroy namespace au production:
  extends: .destroy_namespace_production
  variables:
    <<: *au_variables
    TF_VAR_k8s_cluster_name: syd-prod-k8s
  <<: *deployment-tags-production