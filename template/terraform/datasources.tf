locals {
  // ap-southeast-2 - AU
  // ap-southeast-1 - SG / MY
  production    = terraform.workspace == "prod"
  uat           = terraform.workspace == "uat"
  mainline      = local.production || local.uat
  mainline_slug = join("-", ["billing", var.project_name, terraform.workspace])
  slug          = local.mainline ? lower(local.mainline_slug) : lower(join("-", [local.mainline_slug, var.commit_ref]))
  prefix        = replace(replace(replace(local.slug, "_", "-"), "/", "-"), "--", "-")

  //Vault only has prod/dev
  vault_environment = terraform.workspace == "prod" ? "prod" : "dev"

  #Tags for Template service
  common_tags = module.required_tags.tags
}

data "vault_generic_secret" "billing" {
  path = "secrets/${local.vault_environment}/kv2/billing/common/${terraform.workspace}"
}