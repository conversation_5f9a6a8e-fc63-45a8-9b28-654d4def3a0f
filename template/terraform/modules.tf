module "required_tags" {
  source = "git::https://gitlab.com/mnf-group/billing/ecosystem/terraform/modules/required-tags.git?ref=1.0.2"

  name        = "billing-core-services"
  country     = var.country
  application = "billing-template-service"
  git-project = "https://gitlab.com/mnf-group/billing/billing"
  region      = var.aws_region
}

module "data_config" {
  source = "../../deployments/terraform/datasources"

  aws_region                         = var.aws_region
  country                            = var.country
  remote_state_bucket                = var.remote_state_bucket
  remote_state_region                = var.remote_state_region
  remote_workspace                   = var.remote_workspace
  k8s_cluster_name                   = var.k8s_cluster_name
  kafka_authentication_configuration = data.vault_generic_secret.billing.data["kafka_authentication_configuration"]
}