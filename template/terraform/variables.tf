variable "aws_region" {
  type        = string
  description = "The region of aws infra"
}

variable "remote_workspace" {
  type        = string
  description = "Terraform remote workspace"
}

variable "rds_storage_type" {
  type        = string
  description = "The underlying storage mechanism for the RDS instance."
}

variable "rds_engine" {
  type        = string
  description = "The underlying database engine for the RDS instance."
  default     = "aurora-postgresql"
}

variable "rds_engine_version" {
  type        = string
  description = "The version of the database engine for the RDS instance."
}

variable "rds_allow_major_version_upgrade" {
  type        = bool
  description = "Allow for database major version upgrades to be applied when specified."
}

variable "rds_enable_auto_minor_version_upgrade" {
  type        = bool
  description = "Allow automatic database minor version upgrades to be applied by RDS."
}

variable "rds_storage_encrypted" {
  type        = bool
  description = "Whether the database instance is encrypted at rest."
}

variable "rds_retention_period_days" {
  type        = number
  description = "The number of days to retain snapshots."
}

variable "rds_allocated_storage" {
  type        = string
  description = "Allocated storage for the RDS instance."
}

variable "rds_instance_class" {
  type        = string
  description = "The instance class for the RDS instance."
}

variable "rds_port" {
  type        = number
  description = "Port number for RDS"
  default     = 5432
}

variable "rds_deletion_protection" {
  type        = string
  description = "A flag to determine if the RDS instance. can be deleted on destroyed"
}

variable "rds_snapshot_on_deletion" {
  type        = bool
  description = "Whether to create a snapshot of the databases storage upon deletion of the RDS instance."
}

variable "rds_enhanced_monitoring_interval" {
  type        = number
  description = "Flag to enable enhanced monitoring for DB"
}

variable "rds_enable_performance_insights" {
  type        = bool
  description = "Whether the RDS instance monitors performance."
}

variable "database_name" {
  type        = string
  description = "The name for the database"
  default     = "symbio_template"
}

variable "rds_instance_count" {
  description = "Number of instances to create for Aurora DB Cluster"
  default     = 1
}

variable "remote_state_region" {
  description = "Global remote state s3 bucket region, same for all projects"
  default     = "ap-southeast-2"
}

variable "remote_state_bucket" {
  description = "Global remote state bucket, same for all projects"
  default     = "mnfgroup-terraform-remotestate"
}

variable "project_name" {
  type        = string
  description = "The project name specified"
  default     = "template-service"
}

variable "commit_ref" {
  type        = string
  description = "The commit ref name"
}

variable "multi_az_enabled" {
  type        = string
  default     = "false"
  description = "Whether to deploy this database across multiple AZ's"
}

variable "persist_db" {
  type        = string
  default     = "false"
  description = "Whether to prevent the auto shutdown task from shutting down this RDS instance"
}

variable "auto_start" {
  type        = string
  default     = "true"
  description = "Whether to have this RDS instance automatically started up each morning"
}

variable "country" {
  default     = "my"
  description = "Country variable for different regions for deployment"
}

variable "production" {
  type        = bool
  description = "If for prod or not"
  default     = false
}

variable "k8s_cluster_name" {
  default = "sg-nonprod-k8s"
}

##################################################################################
# S3 Properties
##################################################################################
variable "force_destroy_s3" {
  description = "Flag to force destroy the environment's S3 bucket"
  default     = true
}