resource "aws_s3_bucket" "template-bucket" {
  bucket        = replace("${substr(local.prefix, 0, 60)}-s3", "--", "-")
  force_destroy = var.force_destroy_s3

  tags = merge(
    local.common_tags,
    { "Name" : "${local.prefix}-s3" }
  )
}

resource "aws_s3_bucket_ownership_controls" "template-bucket-ownership-controls" {
  bucket = aws_s3_bucket.template-bucket.id
  rule {
    object_ownership = "ObjectWriter"
  }
}

resource "aws_s3_bucket_versioning" "template-bucket-versioning" {
  bucket = aws_s3_bucket.template-bucket.id
  versioning_configuration {
    status = "Enabled"
  }
}

resource "aws_s3_bucket_server_side_encryption_configuration" "template-bucket-encryption" {
  bucket = aws_s3_bucket.template-bucket.id
  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

resource "aws_s3_object" "template-files" {
  bucket = aws_s3_bucket.template-bucket.id

  for_each = fileset("templates/", "**/*.docx")

  key          = each.value
  source       = "templates/${each.value}"
  content_type = "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
  etag         = filemd5("templates/${each.value}")
}