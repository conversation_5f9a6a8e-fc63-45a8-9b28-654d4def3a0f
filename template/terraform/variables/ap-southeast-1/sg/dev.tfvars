rds_instance_class                = "db.t4g.micro"
rds_engine                        = "postgres"
rds_engine_version                = "16.8"
rds_storage_type                  = "gp2"
rds_instance_count                = 1
rds_allocated_storage             = 20
rds_retention_period_days         = 7
rds_deletion_protection           = false
rds_snapshot_on_deletion          = false
rds_storage_encrypted             = true
rds_enable_performance_insights   = true
rds_allow_major_version_upgrade = true
rds_enable_auto_minor_version_upgrade = true
rds_enhanced_monitoring_interval  = 0
force_destroy_s3                   = true
