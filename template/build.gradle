plugins {
    id 'org.liquibase.gradle'
}

dependencies {
    // Liquibase database change manager
    liquibaseRuntime("org.liquibase:liquibase-core:$liquibase_runtime_version")
    liquibaseRuntime("info.picocli:picocli:$picocli_version")
    liquibaseRuntime("$jdbc_driver")
}

// setting up liquibase
liquibase {
    activities {
        all {
            searchPath "$projectDir"
            changelogFile "install/changelog.xml"
            driver "$jdbc_driver_class"
            liquibaseSchemaName project.hasProperty('jdbc.schema') ? project.getProperty('jdbc.schema') : 'public'
        }

        register('main') {
            url project.hasProperty('jdbc.url') ? project.getProperty('jdbc.url') : '*****************************************'
            username project.hasProperty('jdbc.username') ? project.getProperty('jdbc.username') : 'postgres'
            password project.hasProperty('jdbc.password') ? project.getProperty('jdbc.password') : 'postgrespw'
        }
    }
}