FROM registry.gitlab.com/mnf-group/billing/base-image/corretto-runtime:latest

# Setup env variables
ENV PROJECT_NAME=billing
ENV SERVICE_NAME=template

RUN mkdir /opt/${PROJECT_NAME}
RUN mkdir /opt/${PROJECT_NAME}/${SERVICE_NAME}
RUN mkdir /opt/${PROJECT_NAME}/${SERVICE_NAME}/libs

RUN addgroup -g 1200 billing && adduser -D -u 1200 -G billing -s /bin/sh -h /opt/${PROJECT_NAME} billing

ADD config/startup.sh /opt/${PROJECT_NAME}/${SERVICE_NAME}/startup.sh
ADD --chown=billing:billing app/build/libs/${SERVICE_NAME}.jar /opt/${PROJECT_NAME}/${SERVICE_NAME}/libs/${SERVICE_NAME}.jar
RUN chmod +x /opt/${PROJECT_NAME}/${SERVICE_NAME}/startup.sh

# Enable jemalloc
ENV LD_PRELOAD=/usr/lib/libjemalloc.so.2

EXPOSE 8080
ENTRYPOINT /opt/${PROJECT_NAME}/${SERVICE_NAME}/startup.sh