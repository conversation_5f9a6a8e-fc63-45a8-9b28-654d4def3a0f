package global.symbio.billing.template.persistence.api.template;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.persistence.api.DataAccessObject;

import jakarta.annotation.Nonnull;
import java.time.ZonedDateTime;
import java.util.Objects;
import java.util.UUID;

public abstract class TemplateDataAccessObject implements DataAccessObject<UUID, TemplateDataAccessObject> {

    @Nonnull
    public abstract String getName();

    @Nonnull
    public abstract TemplateDataAccessObject name(@Nonnull String name);

    @Nonnull
    public abstract String getVersion();

    @Nonnull
    public abstract TemplateDataAccessObject version(@Nonnull String version);

    @Nonnull
    public abstract ZonedDateTime getTimestamp();

    @Nonnull
    public abstract TemplateDataAccessObject timestamp(@Nonnull ZonedDateTime timestamp);

    @Override @Nonnull
    public Template entity() {
        return new Template(this);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof TemplateDataAccessObject template)) return false;
        return Objects.equals(getIdentifier(), template.getIdentifier()) && Objects.equals(getName(), template.getName()) && Objects.equals(getVersion(), template.getVersion()) && Objects.equals(getTimestamp(), template.getTimestamp());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getIdentifier(), getName(), getVersion(), getTimestamp());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this).add("identifier", getIdentifier()).add("name", getName()).add("version", getVersion()).add("timestamp", getTimestamp()).toString();
    }
}
