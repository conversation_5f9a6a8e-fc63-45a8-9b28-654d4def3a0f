package global.symbio.billing.template.persistence.api.template;

import global.symbio.billing.core.persistence.api.Entity;

import jakarta.annotation.Nonnull;
import java.time.ZonedDateTime;
import java.util.UUID;

public class Template extends Entity<UUID, TemplateDataAccessObject> {

    public Template(@Nonnull TemplateDataAccessObject data) {
        super(data);
    }

    @Nonnull
    public String getName() {
        return data().getName();
    }

    @Nonnull
    public String getVersion() {
        return data().getVersion();
    }

    @Nonnull
    public ZonedDateTime getTimestamp() {
        return data().getTimestamp();
    }
}
