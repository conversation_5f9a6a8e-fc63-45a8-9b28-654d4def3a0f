package global.symbio.billing.template.persistence.spi.jdbc.jpa.template;

import global.symbio.billing.template.persistence.api.template.TemplateDataAccessObject;
import io.micronaut.core.annotation.Introspected;
import lombok.Getter;
import lombok.Setter;

import jakarta.annotation.Nonnull;
import jakarta.persistence.*;
import java.time.ZonedDateTime;
import java.util.UUID;

@Entity
@Table(name = "template", uniqueConstraints = { @UniqueConstraint(columnNames = { "name", "version" })})
@Getter @Setter
@Introspected(includedAnnotations = Entity.class)
public class JPATemplateDataAccessObject extends TemplateDataAccessObject {

    @Id @Nonnull
    @Column(name = "id", unique = true, nullable = false, updatable = false)
    private UUID identifier;

    @Nonnull
    @Column(name = "name", nullable = false, length = 255)
    private String name;

    @Nonnull
    @Column(name = "version", nullable = false, length = 1024)
    private String version;

    @Nonnull
    @Column(name = "timestamp", nullable = false, updatable = false, insertable = false)
    private ZonedDateTime timestamp;

    @Nonnull
    @Override
    public TemplateDataAccessObject identifier(@Nonnull UUID identifier) {
        setIdentifier(identifier);
        return this;
    }

    @Nonnull
    @Override
    public TemplateDataAccessObject name(@Nonnull String name) {
        setName(name);
        return this;
    }

    @Nonnull
    @Override
    public TemplateDataAccessObject version(@Nonnull String version) {
        setVersion(version);
        return this;
    }

    @Nonnull
    @Override
    public TemplateDataAccessObject timestamp(@Nonnull ZonedDateTime timestamp) {
        setTimestamp(timestamp);
        return this;
    }
}
