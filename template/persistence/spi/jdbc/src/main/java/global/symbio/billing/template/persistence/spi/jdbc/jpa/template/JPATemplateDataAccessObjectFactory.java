package global.symbio.billing.template.persistence.spi.jdbc.jpa.template;

import global.symbio.billing.template.persistence.api.template.TemplateDataAccessObject;
import global.symbio.billing.template.persistence.api.template.TemplateDataAccessObjectFactory;
import io.micronaut.context.annotation.Primary;
import jakarta.inject.Singleton;

import jakarta.annotation.Nonnull;

@Primary
@Singleton
public class JPATemplateDataAccessObjectFactory implements TemplateDataAccessObjectFactory {

    @Nonnull
    @Override
    public Class<? extends TemplateDataAccessObject> type() {
        return JPATemplateDataAccessObject.class;
    }

    @Nonnull
    @Override
    public TemplateDataAccessObject create() {
        return new JPATemplateDataAccessObject();
    }
}
