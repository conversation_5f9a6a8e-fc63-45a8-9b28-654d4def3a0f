import com.github.jengelman.gradle.plugins.shadow.transformers.Log4j2PluginsCacheFileTransformer

plugins {
    id 'io.micronaut.application'
    id 'java'
}

micronaut {
    enableNativeImage false
    runtime 'netty'
    testRuntime 'junit5'
    processing {
        incremental true
        annotations 'jakarta.persistence.*'
    }
}

dependencies {
    implementation project(':core:micronaut:environment')
    implementation project(':invoice:persistence:api')
    runtimeOnly project(':invoice:persistence:spi:jdbc')
    runtimeOnly project(':core:reporting:persistence:spi:jdbc')
    implementation project(':core:country:api')
    implementation project(':core:currency:api')
    runtimeOnly project(':core:country:persistence:spi:jdbc')
    runtimeOnly project(':core:currency:persistence:spi:jdbc')
    implementation project(':core:payment:api')
    implementation project(':core:payment:persistence:api')
    implementation project(':core:payment:persistence:spi:jdbc') //TODO: change to runtime dependency

    implementation project(':core:annotation')
    implementation project(':core:i18n')
    implementation project(':core:exception')
    implementation project(':core:exchange')
    implementation project(':core:kafka:api')
    implementation project(':core:metrics-mixins:all')
    implementation project(':core:pipeline:spi:disruptor')
    implementation project(':core:pipeline:spi:disruptor:consumer:dynamo')
    implementation project(':core:pipeline:spi:disruptor:consumer:jdbc:generic')
    implementation project(':core:pipeline:spi:disruptor:consumer:jdbc:hibernate')
    implementation project(':core:pipeline:spi:disruptor:consumer:kafka')
    implementation project(':core:pipeline:spi:disruptor:consumer:validation')
    implementation project(':core:reporting:api')
    implementation project(':core:reporting:api:pipeline')
    implementation project(':core:validation')
    implementation project(':core:validation:validator:consumer-record')
    implementation project(':core:validation:validator:java-bean')
    implementation project(':core:services:rest')
    implementation project(':core:health')

    implementation project(':core:job:api')
    implementation project(':core:job:persistence:api')
    runtimeOnly project(':core:job:persistence:spi:jdbc')

    implementation project(':core:services:rest:plexus')
    implementation project(':core:services:grpc:ledger')
    implementation project(':core:services:grpc:invoice')
    implementation project(':core:services:grpc:payment')
    implementation project(':core:services:storage')
    implementation project(':core:util:range')
    implementation project(':core:util:plexid')
    implementation project(':core:util:sanitization')
    implementation project(':core:util:constants')
    implementation project(':core:util:metrics')
    implementation project(':core:util:transaction')
    implementation project(':core:util:mapper')
    implementation project(':core:util:compression')
    implementation project(':core:reference:api')
    implementation project(':core:reference:persistence:api')
    runtimeOnly project(':core:reference:persistence:spi:jdbc')

    annotationProcessor(mn.micronaut.data.hibernate.jpa)
    annotationProcessor(mn.micronaut.http.validation)
    annotationProcessor(mn.micronaut.security.annotations)

    implementation(mn.micronaut.core)
    implementation(mn.micronaut.protobuff.support)
    implementation(mn.micronaut.management)
    implementation(mn.micronaut.runtime)
    implementation(mn.micronaut.grpc.server.runtime)
    implementation(mn.micronaut.http.client)
    implementation(mn.micronaut.security)
    implementation(mn.micronaut.data.hibernate.jpa)
    implementation(mn.micronaut.jdbc.hikari)
    implementation(mn.micronaut.validation)
    implementation(mn.micronaut.kubernetes.discovery.client)
    implementation(mn.micronaut.management)

    implementation "com.github.ben-manes.caffeine:caffeine:$caffeine_version"
    implementation "com.lmax:disruptor:$lmax_disruptor_version"
    implementation "org.apache.commons:commons-lang3:$commons_lang_version"
    implementation "org.apache.commons:commons-csv:$commons_csv_version"
    implementation "software.amazon.awssdk:sts"
    implementation "software.amazon.awssdk:s3"

    runtimeOnly "$jdbc_driver"
}

application {
    mainClass.set('global.symbio.billing.invoice.Application')
}

// Task to create the jar containing all the libraries
shadowJar {
    zip64 = true
    mergeServiceFiles()
    archiveFileName = 'invoice.jar'
    transform(Log4j2PluginsCacheFileTransformer)
}
