package global.symbio.billing.invoice.persistence.spi.jdbc.jpa.notification;

import global.symbio.billing.invoice.persistence.api.notification.NotificationDataAccessObject;
import global.symbio.billing.invoice.persistence.api.notification.NotificationDataAccessObjectFactory;
import io.micronaut.context.annotation.Primary;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

@Primary
@Singleton
public class JPANotificationDataAccessObjectFactory implements NotificationDataAccessObjectFactory {

    @Nonnull
    @Override
    public Class<? extends NotificationDataAccessObject> type() {
        return JPANotificationDataAccessObject.class;
    }

    @Nonnull
    @Override
    public NotificationDataAccessObject create() {
        return new JPANotificationDataAccessObject();
    }
}
