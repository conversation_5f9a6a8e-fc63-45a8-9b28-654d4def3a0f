package global.symbio.billing.invoice.persistence.spi.jdbc.jpa.invoice;

import com.google.common.collect.Range;
import global.symbio.billing.invoice.persistence.api.repository.InvoiceRepository;
import io.micronaut.context.annotation.Executable;
import io.micronaut.data.annotation.Query;
import io.micronaut.data.model.Page;
import io.micronaut.data.model.Pageable;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import org.hibernate.query.TypedParameterValue;

import java.time.ZonedDateTime;
import java.util.*;

public interface JPAInvoiceRepository extends InvoiceRepository<JPAInvoiceDataAccessObject> {

    @Nonnull
    @Override
    @Executable
    Optional<JPAInvoiceDataAccessObject> findByAccountOrderByNumberDesc(@Nonnull UUID account);

    @Nonnull
    @Override
    @Query(
        value = """
               SELECT * FROM invoice
               WHERE
               (CAST(:account AS UUID) IS NULL OR invoice.account = CAST(:account AS UUID))
               AND ((:paymentStates) IS NULL OR invoice.payment_state->>'state' IN (:paymentStates))
               AND ((:generationStates) IS NULL OR invoice.generation_state->>'state' IN (:generationStates))
               AND (CAST(:start AS TIMESTAMPTZ) IS NULL OR lower(invoice.period) >= CAST(:start AS TIMESTAMPTZ))
               AND (CAST(:end AS TIMESTAMPTZ) IS NULL OR upper(invoice.period) <= CAST(:end AS TIMESTAMPTZ))
               AND (CAST(:reference AS VARCHAR) IS NULL OR EXISTS (
                    SELECT * FROM reference
                    WHERE reference.id = invoice.id
                    AND reference.type = 'INVOICE'::reference_type
                    AND reference.reference ILIKE CONCAT('%', CAST(:reference AS VARCHAR), '%')
                    )
                )
               ORDER BY invoice.period DESC
            """,
        countQuery = """
                SELECT COUNT(*) FROM invoice
                WHERE
                (CAST(:account AS UUID) IS NULL OR invoice.account = CAST(:account AS UUID))
                AND ((:paymentStates) IS NULL OR invoice.payment_state->>'state' IN (:paymentStates))
                AND ((:generationStates) IS NULL OR invoice.generation_state->>'state' IN (:generationStates))
                AND (CAST(:start AS TIMESTAMPTZ) IS NULL OR lower(invoice.period) >= CAST(:start AS TIMESTAMPTZ))
                AND (CAST(:end AS TIMESTAMPTZ) IS NULL OR upper(invoice.period) <= CAST(:end AS TIMESTAMPTZ))
                AND (CAST(:reference AS VARCHAR) IS NULL OR EXISTS (
                    SELECT * FROM reference
                    WHERE reference.id = invoice.id
                    AND reference.type = 'INVOICE'::reference_type
                    AND reference.reference ILIKE CONCAT('%', CAST(:reference AS VARCHAR), '%')
                    )
                )
            """,
        nativeQuery = true
    )
    @Executable
    Page<JPAInvoiceDataAccessObject> getInvoices(@Nullable UUID account, @Nullable Set<String> paymentStates, @Nullable Set<String> generationStates, @Nullable String reference, @Nullable ZonedDateTime start, @Nullable ZonedDateTime end, @Nonnull Pageable pageable);

    @Nonnull
    @Override
    @Query(
        value = """
                SELECT i.* FROM invoice i 
                WHERE (CAST(:timestamp AS TIMESTAMPTZ) IS NULL OR i.timestamp >= CAST(:timestamp AS TIMESTAMPTZ))
                AND i.dwh_reporting_timestamp IS NULL
                AND i.payment_state->>'state' <> 'UNINVOICED'
            """,
        nativeQuery = true
    )
    @Executable
    Collection<JPAInvoiceDataAccessObject> getUnreportedInvoices(ZonedDateTime timestamp, @Nonnull Pageable pagination);

    @Override
    @Executable
    @Query(
        value = """
            UPDATE invoice
            SET dwh_reporting_timestamp = :timestamp
            WHERE id = ANY(:identifiers)
            """,
        nativeQuery = true
    )
    long updateReportedTimestamp(@Nonnull TypedParameterValue<UUID[]> identifiers, @Nullable ZonedDateTime timestamp);

    //use '&&' overlap (have points in common) in getting invoices that have billing periods within the range
    //if range = ["2024-04-01 00:00:00+08","2024-05-01 00:00:00+08"),
    //invoices with ["2024-04-01 00:00:00+08","2024-05-01 00:00:00+08") or ["2024-03-05 00:00:00+08","2024-04-05 00:00:00+08") will be returned
    @Nonnull
    @Override
    @Query(
        value = """
                SELECT * FROM invoice
                WHERE
                    country = :country
                    AND period && :period
            """,
        nativeQuery = true
    )
    @Executable
    List<JPAInvoiceDataAccessObject> getInvoiceReportByBillingPeriod(@Nonnull Integer country, @Nonnull Range<ZonedDateTime> period);

    //use '<@' (element is contained by) to get invoices generated within the period
    @Nonnull
    @Override
    @Query(
        value = """
                SELECT * FROM invoice
                WHERE
                    country = :country
                    AND timestamp <@ :period
            """,
        nativeQuery = true
    )
    @Executable
    List<JPAInvoiceDataAccessObject> getInvoiceReportByGeneratedDate(@Nonnull Integer country, @Nonnull Range<ZonedDateTime> period);

    /**
     * Get invoices that are open - i.e. invoices with an unbounded upperbound sequence range that have not been completely linked with their transactions
     * OR
     * closed invoices with last linked sequence + 1 (next transaction sequence to link) is less than the invoice's upperbound sequence (exclusive value)
     * indicating linking of the invoice is not yet complete
     *
     * @return JPAInvoiceDataAccessObject
     */
    @Nonnull
    @Override
    @Query(
        value = """
        SELECT i.* FROM invoice i
        INNER JOIN invoice_link_progress ip ON i.id = ip.id
        WHERE (
            upper(i.sequences) IS NULL AND ip.last_linked_sequence < :sequence
        ) OR (
            lower(i.sequences) IS NOT NULL and upper(i.sequences) IS NOT NULL AND ip.last_linked_sequence + 1 < upper(i.sequences)
        )
        """,
        nativeQuery = true
    )
    Collection<JPAInvoiceDataAccessObject> getInvoicesToLink(long sequence);
}
