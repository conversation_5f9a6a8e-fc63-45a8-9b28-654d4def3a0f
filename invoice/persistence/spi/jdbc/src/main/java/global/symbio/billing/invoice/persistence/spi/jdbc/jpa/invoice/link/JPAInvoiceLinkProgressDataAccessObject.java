package global.symbio.billing.invoice.persistence.spi.jdbc.jpa.invoice.link;

import global.symbio.billing.invoice.persistence.api.invoice.link.InvoiceLinkProgressDataAccessObject;
import io.micronaut.core.annotation.Introspected;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.time.ZonedDateTime;
import java.util.UUID;

@Entity
@Table(name = "invoice_link_progress")
@Getter
@Setter
@Introspected(includedAnnotations = Entity.class)
public class JPAInvoiceLinkProgressDataAccessObject extends InvoiceLinkProgressDataAccessObject {

    @Id
    @Nonnull
    @Column(name = "id", unique = true, nullable = false, updatable = false)
    private UUID identifier;

    @Nonnull
    @Column(name = "last_linked_sequence", nullable = false)
    private Long lastLinkedSequence;

    @Nonnull
    @Column(name = "last_updated", nullable = false)
    private ZonedDateTime lastUpdated;

    @Nonnull
    @Override
    public InvoiceLinkProgressDataAccessObject identifier(@Nonnull UUID identifier) {
        setIdentifier(identifier);
        return this;
    }

    @Nonnull
    @Override
    public InvoiceLinkProgressDataAccessObject lastLinkedSequence(@Nonnull Long sequence) {
        setLastLinkedSequence(sequence);
        return this;
    }

    @Nonnull
    @Override
    public InvoiceLinkProgressDataAccessObject lastUpdated(@Nonnull ZonedDateTime timestamp) {
        setLastUpdated(timestamp);
        return this;
    }
}
