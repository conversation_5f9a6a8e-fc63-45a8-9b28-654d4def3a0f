package global.symbio.billing.invoice.persistence.spi.jdbc.jpa.invoice.summary;

import global.symbio.billing.invoice.persistence.api.invoice.summary.InvoiceSummaryDataAccessObject;
import global.symbio.billing.invoice.persistence.api.invoice.summary.InvoiceSummaryDataAccessObjectFactory;
import io.micronaut.context.annotation.Primary;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

@Primary
@Singleton
public class JPAInvoiceSummaryDataAccessObjectFactory implements InvoiceSummaryDataAccessObjectFactory {

    @Nonnull
    @Override
    public Class<? extends InvoiceSummaryDataAccessObject> type() {
        return JPAInvoiceSummaryDataAccessObject.class;
    }

    @Nonnull
    @Override
    public InvoiceSummaryDataAccessObject create() {
        return new JPAInvoiceSummaryDataAccessObject();
    }
}