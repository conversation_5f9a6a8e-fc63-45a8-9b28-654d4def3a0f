package global.symbio.billing.invoice.persistence.spi.jdbc.jpa.invoice;

import global.symbio.billing.invoice.persistence.api.invoice.InvoiceDataAccessObject;
import global.symbio.billing.invoice.persistence.api.invoice.InvoiceDataAccessObjectFactory;
import io.micronaut.context.annotation.Primary;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

@Primary
@Singleton
public class JPAInvoiceDataAccessObjectFactory implements InvoiceDataAccessObjectFactory {

    @Nonnull
    @Override
    public Class<? extends InvoiceDataAccessObject> type() {
        return JPAInvoiceDataAccessObject.class;
    }

    @Nonnull
    @Override
    public InvoiceDataAccessObject create() {
        return new JPAInvoiceDataAccessObject();
    }
}