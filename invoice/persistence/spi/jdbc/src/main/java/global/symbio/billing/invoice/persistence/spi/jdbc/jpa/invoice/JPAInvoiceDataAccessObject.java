package global.symbio.billing.invoice.persistence.spi.jdbc.jpa.invoice;

import com.google.common.collect.Range;
import global.symbio.billing.core.country.persistence.api.CountryDataAccessObject;
import global.symbio.billing.core.country.persistence.spi.jdbc.jpa.JPACountryDataAccessObject;
import global.symbio.billing.core.persistence.api.Types;
import global.symbio.billing.core.reference.persistence.api.reference.ReferenceDataAccessObject;
import global.symbio.billing.core.reference.persistence.spi.jdbc.jpa.reference.JPAReferenceDataAccessObject;
import global.symbio.billing.invoice.persistence.api.invoice.InvoiceDataAccessObject;
import global.symbio.billing.invoice.persistence.api.invoice.state.generation.GenerationState;
import global.symbio.billing.invoice.persistence.api.invoice.state.payment.PaymentState;
import global.symbio.billing.invoice.persistence.api.invoice.summary.InvoiceSummaryDataAccessObject;
import global.symbio.billing.invoice.persistence.spi.jdbc.jpa.invoice.summary.JPAInvoiceSummaryDataAccessObject;
import io.hypersistence.utils.hibernate.type.json.JsonBinaryType;
import io.hypersistence.utils.hibernate.type.range.guava.PostgreSQLGuavaRangeType;
import io.micronaut.core.annotation.Introspected;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Type;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.UUID;

@Entity
@Table(name = "invoice", indexes = @Index(columnList = "account, sequences, period"), uniqueConstraints = @UniqueConstraint(columnNames = { "account", "number" }))
@Getter @Setter
@Introspected(includedAnnotations = Entity.class)
public class JPAInvoiceDataAccessObject extends InvoiceDataAccessObject {

    @Id @Nonnull
    @Column(name = "id", unique = true, nullable = false, updatable = false)
    private UUID identifier;

    @Nonnull
    @Column(name = "account", nullable = false, updatable = false)
    private UUID account;

    @Nonnull
    @Column(name = "number", nullable = false, updatable = false)
    private Integer number;

    @Nonnull
    @Type(PostgreSQLGuavaRangeType.class)
    @Column(name = "sequences", columnDefinition = "int8range", nullable = false)
    private Range<Long> sequences;

    @Nonnull
    @Type(PostgreSQLGuavaRangeType.class)
    @Column(name = "period", columnDefinition = "tstzrange", nullable = false, updatable = false)
    private Range<ZonedDateTime> period;

    @Nonnull
    @Column(name = "opening_balance", nullable = false, scale = 32, precision = 12)
    private BigDecimal openingBalance;

    @Nonnull
    @Column(name = "closing_balance", nullable = false, scale = 32, precision = 12)
    private BigDecimal closingBalance;

    @Nonnull
    @Type(JsonBinaryType.class)
    @Column(name = "payment_state", nullable = false)
    private PaymentState payment;

    @Nonnull
    @Type(JsonBinaryType.class)
    @Column(name="generation_state", nullable = false)
    private GenerationState generation;

    @Nonnull
    @ManyToOne
    @JoinColumn(name = "country")
    private JPACountryDataAccessObject country;

    @PrimaryKeyJoinColumn
    @OneToOne(cascade = CascadeType.ALL)
    private JPAReferenceDataAccessObject reference;

    @PrimaryKeyJoinColumn
    @OneToOne(cascade = CascadeType.ALL)
    private JPAInvoiceSummaryDataAccessObject summary;

    @Nullable
    @Column(name = "due_date")
    private ZonedDateTime dueDate;

    @Nonnull
    @Column(name = "timestamp", nullable = false, updatable = false, insertable = false)
    private ZonedDateTime timestamp;

    @Nullable
    @Column(name = "dwh_reporting_timestamp", nullable = true)
    private ZonedDateTime reportedTimestamp;

    @Nonnull
    @Override
    public InvoiceDataAccessObject identifier(@Nonnull UUID identifier) {
        setIdentifier(identifier);
        return this;
    }

    @Nonnull
    @Override
    public InvoiceDataAccessObject account(@Nonnull UUID account) {
        setAccount(account);
        return this;
    }

    @Nonnull
    @Override
    public InvoiceDataAccessObject number(@Nonnull Integer number) {
        setNumber(number);
        return this;
    }

    @Nonnull
    @Override
    public InvoiceDataAccessObject sequences(@Nonnull Range<Long> sequences) {
        setSequences(sequences);
        return this;
    }

    @Nonnull
    @Override
    public InvoiceDataAccessObject period(@Nonnull Range<ZonedDateTime> period) {
        setPeriod(period);
        return this;
    }

    @Nonnull
    @Override
    public InvoiceDataAccessObject openingBalance(@Nonnull BigDecimal balance) {
        setOpeningBalance(balance);
        return this;
    }

    @Nonnull
    @Override
    public InvoiceDataAccessObject closingBalance(@Nonnull BigDecimal balance) {
        setClosingBalance(balance);
        return this;
    }

    @Nonnull
    @Override
    public InvoiceDataAccessObject payment(@Nonnull PaymentState payment) {
        setPayment(payment);
        return this;
    }

    @Nonnull
    @Override
    public InvoiceDataAccessObject generation(@Nonnull GenerationState generation) {
        setGeneration(generation);
        return this;
    }

    @Nonnull
    @Override
    public InvoiceDataAccessObject country(@Nonnull CountryDataAccessObject country) {
        final var data = Types.require(country, "country", JPACountryDataAccessObject.class);
        setCountry(data);
        return this;
    }

    @Nonnull
    @Override
    public InvoiceDataAccessObject reference(@Nullable ReferenceDataAccessObject reference) {
        final var data = Types.require(reference, "reference", JPAReferenceDataAccessObject.class);
        setReference(data);
        return this;
    }

    @Nonnull
    @Override
    public InvoiceDataAccessObject summary(@Nullable InvoiceSummaryDataAccessObject summary) {
        final var data = Types.require(summary, "summary", JPAInvoiceSummaryDataAccessObject.class);
        setSummary(data);
        return this;
    }

    @Nonnull
    @Override
    public InvoiceDataAccessObject dueDate(@Nullable ZonedDateTime dueDate) {
        setDueDate(dueDate);
        return this;
    }

    @Nonnull
    @Override
    public InvoiceDataAccessObject timestamp(@Nonnull ZonedDateTime timestamp) {
        setTimestamp(timestamp);
        return this;
    }

    @Nonnull
    @Override
    public InvoiceDataAccessObject reportedTimestamp(@Nullable ZonedDateTime timestamp) {
        setReportedTimestamp(timestamp);
        return this;
    }
}