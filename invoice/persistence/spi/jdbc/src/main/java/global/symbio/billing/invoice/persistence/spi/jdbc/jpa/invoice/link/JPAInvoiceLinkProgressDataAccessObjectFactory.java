package global.symbio.billing.invoice.persistence.spi.jdbc.jpa.invoice.link;

import global.symbio.billing.invoice.persistence.api.invoice.link.InvoiceLinkProgressDataAccessObject;
import global.symbio.billing.invoice.persistence.api.invoice.link.InvoiceLinkProgressDataAccessObjectFactory;
import io.micronaut.context.annotation.Primary;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

@Primary
@Singleton
public class JPAInvoiceLinkProgressDataAccessObjectFactory implements InvoiceLinkProgressDataAccessObjectFactory {

    @Nonnull
    @Override
    public Class<? extends InvoiceLinkProgressDataAccessObject> type() {
        return JPAInvoiceLinkProgressDataAccessObject.class;
    }

    @Nonnull
    @Override
    public InvoiceLinkProgressDataAccessObject create() {
        return new JPAInvoiceLinkProgressDataAccessObject();
    }
}

