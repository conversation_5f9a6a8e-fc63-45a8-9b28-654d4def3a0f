package global.symbio.billing.invoice.persistence.spi.jdbc.jpa.invoice.summary;

import global.symbio.billing.invoice.persistence.api.invoice.summary.InvoiceSummaryDataAccessObject;
import io.hypersistence.utils.hibernate.type.json.JsonBinaryType;
import io.micronaut.core.annotation.Introspected;
import jakarta.annotation.Nonnull;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Type;

import java.time.ZonedDateTime;
import java.util.UUID;

@Entity
@Table(name = "invoice_summary")
@Getter @Setter
@Introspected(includedAnnotations = Entity.class)
public class JPAInvoiceSummaryDataAccessObject extends InvoiceSummaryDataAccessObject {

    @Id
    @Nonnull
    @Column(name = "id", unique = true, nullable = false, updatable = false)
    private UUID identifier;

    @Nonnull
    @Type(JsonBinaryType.class)
    @Column(name="summary", nullable = false)
    private String summary;

    @Nonnull
    @Column(name = "timestamp", nullable = false, updatable = false, insertable = false)
    private ZonedDateTime timestamp;

    @Nonnull
    @Override
    public InvoiceSummaryDataAccessObject identifier(@Nonnull UUID identifier) {
        setIdentifier(identifier);
        return this;
    }

    @Nonnull
    @Override
    public InvoiceSummaryDataAccessObject summary(@Nonnull String summary) {
        setSummary(summary);
        return this;
    }
    @Nonnull
    @Override
    public InvoiceSummaryDataAccessObject timestamp(@Nonnull ZonedDateTime timestamp) {
        setTimestamp(timestamp);
        return this;
    }
}
