package global.symbio.billing.invoice.persistence.spi.jdbc.jpa.notification;

import global.symbio.billing.invoice.persistence.api.notification.NotificationDataAccessObject;
import global.symbio.billing.invoice.persistence.api.notification.NotificationType;
import global.symbio.billing.invoice.persistence.api.notification.state.NotificationState;
import io.hypersistence.utils.hibernate.type.json.JsonBinaryType;
import io.micronaut.core.annotation.Introspected;
import jakarta.annotation.Nonnull;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.Type;
import org.hibernate.type.SqlTypes;

import java.time.ZonedDateTime;

@Entity
@Table(name="notification")
@Getter @Setter
@Introspected
public class JPANotificationDataAccessObject extends NotificationDataAccessObject {

    @Id @Nonnull
    @Column(name = "id", unique = true, nullable = false, updatable = false)
    private String identifier;

    @Nonnull
    @Column(name = "type", columnDefinition = "notification_type", nullable = false)
    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    private NotificationType type;

    @Nonnull
    @Type(JsonBinaryType.class)
    @Column(name="state", nullable = false)
    private NotificationState state;

    @Nonnull
    @Column(name = "timestamp", nullable = false, updatable = false, insertable = false)
    private ZonedDateTime timestamp;

    @Nonnull
    @Override
    public NotificationDataAccessObject identifier(@Nonnull String identifier) {
        setIdentifier(identifier);
        return this;
    }

    @Nonnull
    @Override
    public NotificationDataAccessObject type(@Nonnull NotificationType type) {
        setType(type);
        return this;
    }

    @Nonnull
    @Override
    public NotificationDataAccessObject state(@Nonnull NotificationState state) {
        setState(state);
        return this;
    }

    @Nonnull
    @Override
    public NotificationDataAccessObject timestamp(@Nonnull ZonedDateTime timestamp) {
        setTimestamp(timestamp);
        return this;
    }
}
