package global.symbio.billing.invoice.persistence.spi.jdbc.jpa.invoice.notification;

import global.symbio.billing.invoice.persistence.api.invoice.notification.InvoiceNotificationDataAccessObject;
import io.micronaut.core.annotation.Introspected;
import jakarta.annotation.Nonnull;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.time.ZonedDateTime;
import java.util.UUID;

@Entity
@Table(name="invoice_notification", uniqueConstraints = @UniqueConstraint(columnNames={ "id", "invoice" }))
@Getter @Setter
@Introspected
public class JPAInvoiceNotificationDataAccessObject extends InvoiceNotificationDataAccessObject {

    @Id
    @Nonnull
    @Column(name = "id", unique = true, nullable = false, updatable = false)
    private String identifier;

    @Nonnull
    @Column(name = "invoice", nullable = false, updatable = false)
    private UUID invoice;

    @Nonnull
    @Column(name = "timestamp", nullable = false, updatable = false, insertable = false)
    private ZonedDateTime timestamp;

    @Nonnull
    @Override
    public InvoiceNotificationDataAccessObject identifier(@Nonnull String identifier) {
        setIdentifier(identifier);
        return this;
    }

    @Nonnull
    @Override
    public InvoiceNotificationDataAccessObject invoice(@Nonnull UUID invoice) {
        setInvoice(invoice);
        return this;
    }

    @Nonnull
    @Override
    public InvoiceNotificationDataAccessObject timestamp(@Nonnull ZonedDateTime timestamp) {
        setTimestamp(timestamp);
        return this;
    }
}