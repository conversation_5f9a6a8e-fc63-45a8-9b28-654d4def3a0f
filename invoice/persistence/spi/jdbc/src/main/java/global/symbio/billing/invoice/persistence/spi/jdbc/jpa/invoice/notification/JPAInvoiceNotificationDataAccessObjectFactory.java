package global.symbio.billing.invoice.persistence.spi.jdbc.jpa.invoice.notification;

import global.symbio.billing.invoice.persistence.api.invoice.notification.InvoiceNotificationDataAccessObject;
import global.symbio.billing.invoice.persistence.api.invoice.notification.InvoiceNotificationDataAccessObjectFactory;
import io.micronaut.context.annotation.Primary;
import jakarta.annotation.Nonnull;
import jakarta.inject.Singleton;

@Primary
@Singleton
public class JPAInvoiceNotificationDataAccessObjectFactory implements InvoiceNotificationDataAccessObjectFactory {

    @Nonnull
    @Override
    public Class<? extends InvoiceNotificationDataAccessObject> type() {
        return JPAInvoiceNotificationDataAccessObject.class;
    }

    @Nonnull
    @Override
    public InvoiceNotificationDataAccessObject create() {
        return new JPAInvoiceNotificationDataAccessObject();
    }
}
