package global.symbio.billing.invoice.persistence.spi.jdbc.jpa.invoice;

import global.symbio.billing.core.persistence.api.Repositories;
import global.symbio.billing.invoice.persistence.api.repository.InvoiceRepository;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

public class TestInvoiceRepository {

    @Test
    @DisplayName("ReadOnlyInvoiceRepository is connected to read-only datasource")
    public void test_read_only_repository() {
        Assertions.assertTrue(InvoiceRepository.class.isAssignableFrom(ReadOnlyJPAInvoiceRepository.class));
        Assertions.assertTrue(Repositories.isReadOnlyRepository(ReadOnlyJPAInvoiceRepository.class));
    }

    @Test
    @DisplayName("ReadWriteInvoiceRepository is connected to read-write datasource")
    public void test_read_write_repository() {
        Assertions.assertTrue(InvoiceRepository.class.isAssignableFrom(ReadWriteJPAInvoiceRepository.class));
        Assertions.assertTrue(Repositories.isReadWriteRepository(ReadWriteJPAInvoiceRepository.class));
    }
}