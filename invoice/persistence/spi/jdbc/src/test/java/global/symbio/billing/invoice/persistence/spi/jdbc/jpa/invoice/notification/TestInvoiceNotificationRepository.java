package global.symbio.billing.invoice.persistence.spi.jdbc.jpa.invoice.notification;

import global.symbio.billing.core.persistence.api.Repositories;
import global.symbio.billing.invoice.persistence.api.repository.InvoiceNotificationRepository;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

public class TestInvoiceNotificationRepository {

    @Test
    @DisplayName("ReadOnlyInvoiceNotificationRepository is connected to read-only datasource")
    public void test_read_only_repository() {
        Assertions.assertTrue(InvoiceNotificationRepository.class.isAssignableFrom(ReadOnlyJPAInvoiceNotificationRepository.class));
        Assertions.assertTrue(Repositories.isReadOnlyRepository(ReadOnlyJPAInvoiceNotificationRepository.class));
    }

    @Test
    @DisplayName("ReadWriteInvoiceNotificationRepository is connected to read-write datasource")
    public void test_read_write_repository() {
        Assertions.assertTrue(InvoiceNotificationRepository.class.isAssignableFrom(ReadWriteJPAInvoiceNotificationRepository.class));
        Assertions.assertTrue(Repositories.isReadWriteRepository(ReadWriteJPAInvoiceNotificationRepository.class));
    }
}