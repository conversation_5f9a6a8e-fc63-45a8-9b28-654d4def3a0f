package global.symbio.billing.invoice.persistence.spi.jdbc.jpa.invoice.link;

import global.symbio.billing.core.persistence.api.Repositories;
import global.symbio.billing.invoice.persistence.api.repository.InvoiceLinkProgressRepository;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

public class TestInvoiceLinkProgressRepository {

    @Test
    @DisplayName("ReadOnlyInvoiceLinkProgressRepository is connected to read-only datasource")
    public void test_read_only_repository() {
        Assertions.assertTrue(InvoiceLinkProgressRepository.class.isAssignableFrom(ReadOnlyJPAInvoiceLinkProgressRepository.class));
        Assertions.assertTrue(Repositories.isReadOnlyRepository(ReadOnlyJPAInvoiceLinkProgressRepository.class));
    }

    @Test
    @DisplayName("ReadWriteInvoiceLinkProgressRepository is connected to read-write datasource")
    public void test_read_write_repository() {
        Assertions.assertTrue(InvoiceLinkProgressRepository.class.isAssignableFrom(ReadWriteJPAInvoiceLinkProgressRepository.class));
        Assertions.assertTrue(Repositories.isReadWriteRepository(ReadWriteJPAInvoiceLinkProgressRepository.class));
    }
}