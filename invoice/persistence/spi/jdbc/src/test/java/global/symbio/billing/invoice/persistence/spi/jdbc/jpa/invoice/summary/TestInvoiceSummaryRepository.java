package global.symbio.billing.invoice.persistence.spi.jdbc.jpa.invoice.summary;

import global.symbio.billing.core.persistence.api.Repositories;
import global.symbio.billing.invoice.persistence.api.repository.InvoiceSummaryRepository;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

public class TestInvoiceSummaryRepository {

    @Test
    @DisplayName("ReadOnlyInvoiceSummaryRepository is connected to read-only datasource")
    public void test_read_only_repository() {
        Assertions.assertTrue(InvoiceSummaryRepository.class.isAssignableFrom(ReadOnlyJPAInvoiceSummaryRepository.class));
        Assertions.assertTrue(Repositories.isReadOnlyRepository(ReadOnlyJPAInvoiceSummaryRepository.class));
    }

    @Test
    @DisplayName("ReadWriteInvoiceSummaryRepository is connected to read-write datasource")
    public void test_read_write_repository() {
        Assertions.assertTrue(InvoiceSummaryRepository.class.isAssignableFrom(ReadWriteJPAInvoiceSummaryRepository.class));
        Assertions.assertTrue(Repositories.isReadWriteRepository(ReadWriteJPAInvoiceSummaryRepository.class));
    }
}