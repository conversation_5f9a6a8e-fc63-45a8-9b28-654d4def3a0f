package global.symbio.billing.invoice.persistence.spi.jdbc.jpa.notification;

import global.symbio.billing.core.persistence.api.Repositories;
import global.symbio.billing.invoice.persistence.api.repository.NotificationRepository;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

public class TestNotificationRepository {

    @Test
    @DisplayName("ReadOnlyNotificationRepository is connected to read-only datasource")
    public void test_read_only_repository() {
        Assertions.assertTrue(NotificationRepository.class.isAssignableFrom(ReadOnlyJPANotificationRepository.class));
        Assertions.assertTrue(Repositories.isReadOnlyRepository(ReadOnlyJPANotificationRepository.class));
    }

    @Test
    @DisplayName("ReadWriteNotificationRepository is connected to read-write datasource")
    public void test_read_write_repository() {
        Assertions.assertTrue(NotificationRepository.class.isAssignableFrom(ReadWriteJPANotificationRepository.class));
        Assertions.assertTrue(Repositories.isReadWriteRepository(ReadWriteJPANotificationRepository.class));
    }
}
