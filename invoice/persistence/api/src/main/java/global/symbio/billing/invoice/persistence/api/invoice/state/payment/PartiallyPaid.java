package global.symbio.billing.invoice.persistence.api.invoice.state.payment;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.annotation.Nonnull;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.Objects;

@Getter
public final class PartiallyPaid extends PaymentState {

    public static final String TYPE = "PARTIALLY_PAID";

    @Nonnull
    private final BigDecimal paid;

    @Nonnull
    private final BigDecimal due;

    @JsonCreator(mode = JsonCreator.Mode.PROPERTIES)
    public PartiallyPaid(@Nonnull @JsonProperty("total") BigDecimal total, @Nonnull @JsonProperty("paid") BigDecimal paid, @Nonnull @JsonProperty("due") BigDecimal due) {
        super(TYPE, total);
        this.paid = Objects.requireNonNull(paid, "paid");
        this.due = Objects.requireNonNull(due, "due");
    }

    @Nonnull
    @Override
    public PaymentState pay(@Nonnull BigDecimal amount) {
        ensurePositive(amount);
        final var payment = amount.compareTo(due);
        if (payment > 0) {
            throw new IllegalStateException("Unable to create a payment greater than the due amount. Due: " + due + ", Requested: " + amount + ".");
        } else if (payment == 0) {
            return new Paid(total, paid.add(amount));
        }
        return new PartiallyPaid(total, paid.add(amount), due.subtract(amount));
    }

    @Nonnull
    @Override
    public PaymentState refund(@Nonnull BigDecimal amount) {
        ensurePositive(amount);
        final var refund = amount.compareTo(paid);
        if (refund > 0) {
            throw new IllegalStateException("Unable to refund more than what has been paid. Paid: " + paid + ", Requested: " + amount + ".");
        } else if (refund == 0) {
            return new Unpaid(total, due.add(amount));
        }
        return new PartiallyPaid(total, paid.subtract(amount), due.add(amount));
    }
}
