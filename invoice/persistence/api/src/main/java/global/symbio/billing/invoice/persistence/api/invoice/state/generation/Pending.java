package global.symbio.billing.invoice.persistence.api.invoice.state.generation;

import com.google.common.base.MoreObjects;
import jakarta.annotation.Nonnull;

import java.util.Objects;

public final class Pending extends GenerationState {

    public static final String TYPE = "PENDING";

    @Nonnull
    private static final Pending INSTANCE = new Pending();

    public Pending() {
        super(TYPE);
    }

    public static Pending getInstance() {
        return INSTANCE;
    }

    @Override
    public Pending pending() {
        return this;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Pending)) return false;
        return super.equals(o);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("state", state)
                .toString();
    }
}
