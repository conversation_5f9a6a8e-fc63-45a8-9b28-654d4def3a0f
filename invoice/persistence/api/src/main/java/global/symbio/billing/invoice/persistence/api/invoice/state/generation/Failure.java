package global.symbio.billing.invoice.persistence.api.invoice.state.generation;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.MoreObjects;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.Getter;

import java.time.ZonedDateTime;
import java.util.Objects;

@Getter
public final class Failure extends GenerationState {

    public static final String TYPE = "FAILURE";

    @Nonnull
    private final String reason;

    @Nonnull
    private final ZonedDateTime timestamp;

    @JsonCreator(mode = JsonCreator.Mode.PROPERTIES)
    public Failure(@Nullable @JsonProperty("reason") String reason, @Nonnull @JsonProperty("timestamp") ZonedDateTime timestamp) {
        super(TYPE);
        this.reason = reason;
        this.timestamp = Objects.requireNonNull(timestamp, "timestamp");
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), getReason(), getTimestamp());
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Failure failure)) return false;
        if (!super.equals(o)) return false;
        return Objects.equals(getReason(), failure.getReason()) && Objects.equals(getTimestamp(), failure.getTimestamp());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("state", state)
                .add("reason", reason)
                .add("timestamp", timestamp)
                .toString();
    }
}
