package global.symbio.billing.invoice.persistence.api.invoice.state.payment;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.annotation.Nonnull;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.Objects;

@Getter
public final class Unpaid extends PaymentState {

    public static final String TYPE = "UNPAID";

    @Nonnull
    private final BigDecimal due;

    @JsonCreator(mode = JsonCreator.Mode.PROPERTIES)
    public Unpaid(@Nonnull @JsonProperty("total") BigDecimal total, @Nonnull @JsonProperty("due") BigDecimal due) {
        super(TYPE, total);
        this.due = Objects.requireNonNull(due, "due");
    }

    @Nonnull
    @Override
    public BigDecimal getPaid() {
        return BigDecimal.ZERO;
    }

    @Nonnull
    @Override
    public PaymentState pay(@Nonnull BigDecimal amount) {
        ensurePositive(amount);
        final var payment = amount.compareTo(due);
        if (payment > 0) {
            throw new IllegalStateException("Unable to create a payment greater than the due amount. Due: " + due + ", Requested: " + amount + ".");
        } else if (payment == 0) {
            return new Paid(total, amount);
        }
        return new PartiallyPaid(total, amount, due.subtract(amount));
    }

    @Nonnull
    @Override
    public PaymentState refund(@Nonnull BigDecimal amount) {
        ensurePositive(amount);
        throw new IllegalStateException("Unable to refund an unpaid invoice.");
    }
}
