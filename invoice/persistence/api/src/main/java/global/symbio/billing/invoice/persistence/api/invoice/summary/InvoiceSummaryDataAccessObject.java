package global.symbio.billing.invoice.persistence.api.invoice.summary;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.persistence.api.DataAccessObject;
import jakarta.annotation.Nonnull;

import java.time.ZonedDateTime;
import java.util.Objects;
import java.util.UUID;

public abstract class InvoiceSummaryDataAccessObject implements DataAccessObject<UUID, InvoiceSummaryDataAccessObject> {

    @Nonnull
    public abstract String getSummary();

    @Nonnull
    public abstract InvoiceSummaryDataAccessObject summary(@Nonnull String summary);

    @Nonnull
    public abstract ZonedDateTime getTimestamp();

    @Nonnull
    public abstract InvoiceSummaryDataAccessObject timestamp(@Nonnull ZonedDateTime timestamp);

    @Override @Nonnull
    public InvoiceSummary entity() {
        return new InvoiceSummary(this);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof InvoiceSummaryDataAccessObject summary)) return false;
        return Objects.equals(getIdentifier(), summary.getIdentifier())
                && Objects.equals(getSummary(), summary.getSummary())
                && Objects.equals(getTimestamp(), summary.getTimestamp());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getIdentifier(), getSummary(), getTimestamp());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("identifier", getIdentifier())
                .add("summary", getSummary())
                .add("timestamp", getTimestamp()).toString();
    }
}
