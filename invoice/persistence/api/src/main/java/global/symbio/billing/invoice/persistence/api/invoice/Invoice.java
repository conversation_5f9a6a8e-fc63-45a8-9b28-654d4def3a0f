package global.symbio.billing.invoice.persistence.api.invoice;

import com.google.common.collect.DiscreteDomain;
import com.google.common.collect.Range;
import global.symbio.billing.core.country.persistence.api.Country;
import global.symbio.billing.core.persistence.api.Entity;
import global.symbio.billing.core.reference.persistence.api.reference.Reference;
import global.symbio.billing.core.util.range.Ranges;
import global.symbio.billing.core.util.range.discrete.TemporalDiscreteDomain;
import global.symbio.billing.invoice.persistence.api.invoice.state.generation.GenerationState;
import global.symbio.billing.invoice.persistence.api.invoice.state.payment.PaymentState;
import global.symbio.billing.invoice.persistence.api.invoice.summary.InvoiceSummary;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.math.BigDecimal;
import java.time.Period;
import java.time.ZonedDateTime;
import java.util.UUID;

public class Invoice extends Entity<UUID, InvoiceDataAccessObject> {

    public static final DiscreteDomain<ZonedDateTime> INVOICE_PERIOD_DOMAIN = new TemporalDiscreteDomain<>(Period.ofDays(1));

    public Invoice(@Nonnull InvoiceDataAccessObject data) {
        super(data);
    }

    @Nonnull
    public UUID getAccount() {
        return data().getAccount();
    }

    @Nonnull
    public Integer getNumber() {
        return data().getNumber();
    }

    @Nullable
    public Long getSequenceLowerBound() {
        return getSequences().hasLowerBound() ? Ranges.lowerbound(getSequences()) : null;
    }

    @Nullable
    public Long getSequenceUpperBound() {
        return getSequences().hasUpperBound() ? Ranges.upperbound(getSequences()) : null;
    }

    @Nonnull
    public Range<Long> getSequences() {
        return data().getSequences();
    }

    @Nullable
    public ZonedDateTime getPeriodLowerBound() {
        return getPeriod().hasLowerBound() ? Ranges.lowerbound(getPeriod(), INVOICE_PERIOD_DOMAIN) : null;
    }

    @Nullable
    public ZonedDateTime getPeriodUpperBound() {
        return getPeriod().hasUpperBound() ? Ranges.upperbound(getPeriod(), INVOICE_PERIOD_DOMAIN) : null;
    }

    @Nonnull
    public Range<ZonedDateTime> getPeriod() {
        return data().getPeriod();
    }

    @Nonnull
    public BigDecimal getOpeningBalance() {
        return data().getOpeningBalance();
    }

    @Nonnull
    public BigDecimal getClosingBalance() {
        return data().getClosingBalance();
    }

    @Nonnull
    public PaymentState getPayment() {
        return data().getPayment();
    }

    @Nonnull
    public GenerationState getGeneration() {
        return data().getGeneration();
    }

    @Nullable
    public ZonedDateTime getDueDate() {
        return data().getDueDate();
    }

    @Nonnull
    public ZonedDateTime getTimestamp() {
        return data().getTimestamp();
    }

    @Nonnull
    public Country getCountry() {
        return unwrap(data().getCountry());
    }

    @Nullable
    public Reference getReference() {
        return unwrap(data().getReference());
    }

    @Nullable
    public InvoiceSummary getSummary() {
        return unwrap(data().getSummary());
    }

    @Nullable
    public ZonedDateTime getReportedTimestamp() {
        return data().getReportedTimestamp();
    }
}