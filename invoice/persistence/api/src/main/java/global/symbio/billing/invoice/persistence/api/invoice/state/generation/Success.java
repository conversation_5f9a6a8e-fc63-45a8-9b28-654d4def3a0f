package global.symbio.billing.invoice.persistence.api.invoice.state.generation;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.MoreObjects;
import jakarta.annotation.Nonnull;
import lombok.Getter;

import java.time.ZonedDateTime;
import java.util.Objects;

@Getter
public final class Success extends GenerationState {

    public static final String TYPE = "SUCCESS";

    @Nonnull
    private final ZonedDateTime timestamp;

    @JsonCreator(mode = JsonCreator.Mode.PROPERTIES)
    public Success(@Nonnull @JsonProperty("timestamp") ZonedDateTime timestamp) {
        super(TYPE);
        this.timestamp = Objects.requireNonNull(timestamp, "timestamp");
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Success success)) return false;
        if (!super.equals(o)) return false;
        return getTimestamp().equals(success.getTimestamp());
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), getTimestamp());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("state", state)
                .add("timestamp", timestamp)
                .toString();
    }
}
