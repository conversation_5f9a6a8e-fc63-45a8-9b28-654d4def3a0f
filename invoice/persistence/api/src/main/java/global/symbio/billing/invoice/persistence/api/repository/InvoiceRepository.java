package global.symbio.billing.invoice.persistence.api.repository;

import com.google.common.collect.Range;
import global.symbio.billing.invoice.persistence.api.invoice.InvoiceDataAccessObject;
import io.micronaut.data.model.Page;
import io.micronaut.data.model.Pageable;
import io.micronaut.data.repository.PageableRepository;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import org.hibernate.query.TypedParameterValue;

import java.time.ZonedDateTime;
import java.util.Collection;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

public interface InvoiceRepository<T extends InvoiceDataAccessObject> extends PageableRepository<T, UUID> {

    @Nonnull
    Optional<T> findByAccountOrderByNumberDesc(@Nonnull UUID account);

    @Nonnull
    Page<T> getInvoices(@Nullable UUID account, @Nullable Set<String> paymentStates, @Nullable Set<String> generationStates, @Nullable String reference, @Nullable ZonedDateTime start, @Nullable ZonedDateTime end, @Nonnull Pageable pageable);

    @Nonnull
    Collection<T> getUnreportedInvoices(@Nullable ZonedDateTime timestamp, @Nonnull Pageable pagination);

    long updateReportedTimestamp(@Nonnull TypedParameterValue<UUID[]> identifiers, @Nullable ZonedDateTime timestamp);

    @Nonnull
    Collection<T> getInvoiceReportByBillingPeriod(@Nonnull Integer country, @Nonnull Range<ZonedDateTime> period);

    @Nonnull
    Collection<T> getInvoiceReportByGeneratedDate(@Nonnull Integer country, @Nonnull Range<ZonedDateTime> period);

    @Nonnull
    Collection<T> getInvoicesToLink(long sequence);
}