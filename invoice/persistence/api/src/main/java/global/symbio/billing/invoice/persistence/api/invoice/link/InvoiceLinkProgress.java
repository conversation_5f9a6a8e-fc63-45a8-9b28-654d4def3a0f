package global.symbio.billing.invoice.persistence.api.invoice.link;

import global.symbio.billing.core.persistence.api.Entity;
import jakarta.annotation.Nonnull;

import java.time.ZonedDateTime;
import java.util.UUID;

public class InvoiceLinkProgress extends Entity<UUID, InvoiceLinkProgressDataAccessObject> {

    public InvoiceLinkProgress(@Nonnull InvoiceLinkProgressDataAccessObject data) {
        super(data);
    }

    @Nonnull
    public Long getLastLinkedSequence() {
        return data().getLastLinkedSequence();
    }

    @Nonnull
    public ZonedDateTime getLastUpdated() {
        return data().getLastUpdated();
    }
}
