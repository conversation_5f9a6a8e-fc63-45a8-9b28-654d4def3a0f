package global.symbio.billing.invoice.persistence.api.invoice.summary;

import com.google.common.base.Strings;
import global.symbio.billing.core.persistence.api.Entity;
import jakarta.annotation.Nonnull;

import java.time.ZonedDateTime;
import java.util.UUID;

public class InvoiceSummary extends Entity<UUID, InvoiceSummaryDataAccessObject> {

    public InvoiceSummary(@Nonnull InvoiceSummaryDataAccessObject data) {
        super(data);
    }

    @Nonnull
    public String getSummary() {
        return data().getSummary();
    }

    @Nonnull
    public ZonedDateTime getTimestamp() {
        return data().getTimestamp();
    }

    public boolean isSummarised() {
        return !Strings.isNullOrEmpty(getSummary());
    }
}
