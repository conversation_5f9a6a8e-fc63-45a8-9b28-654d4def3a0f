package global.symbio.billing.invoice.persistence.api.invoice.state.generation;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.google.common.base.MoreObjects;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.Getter;

import java.time.ZonedDateTime;
import java.util.Objects;

@Getter
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "state")
@JsonSubTypes(
    value = {
        @JsonSubTypes.Type(value = Pending.class, name = Pending.TYPE),
        @JsonSubTypes.Type(value = Success.class, name = Success.TYPE),
        @JsonSubTypes.Type(value = Failure.class, name = Failure.TYPE),
    }
)
public abstract sealed class GenerationState permits Pending, Success, Failure {

    @Nonnull
    protected final String state;

    public GenerationState(@Nonnull String state) {
        this.state = Objects.requireNonNull(state, "state must not be null");
    }

    @Nonnull
    public Pending pending() {
        return Pending.getInstance();
    }

    @Nonnull
    public Success success() {
        return success(ZonedDateTime.now());
    }

    @Nonnull
    public Success success(@Nonnull ZonedDateTime timestamp) {
        return new Success(timestamp);
    }

    @Nonnull
    public Failure failure(@Nullable String reason) {
        return failure(reason, ZonedDateTime.now());
    }

    @Nonnull
    public Failure failure(@Nullable String reason, @Nonnull ZonedDateTime timestamp) {
        return new Failure(reason, timestamp);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof GenerationState generation)) return false;
        return getState().equals(generation.getState());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getState());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this).add("state", getState()).toString();
    }
}
