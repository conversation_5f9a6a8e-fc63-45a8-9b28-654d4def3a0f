package global.symbio.billing.invoice.persistence.api.invoice.link;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.persistence.api.DataAccessObject;
import jakarta.annotation.Nonnull;

import java.time.ZonedDateTime;
import java.util.Objects;
import java.util.UUID;

public abstract class InvoiceLinkProgressDataAccessObject implements DataAccessObject<UUID, InvoiceLinkProgressDataAccessObject> {

    @Nonnull
    public abstract Long getLastLinkedSequence();

    @Nonnull
    public abstract InvoiceLinkProgressDataAccessObject lastLinkedSequence(@Nonnull Long sequence);

    @Nonnull
    public abstract ZonedDateTime getLastUpdated();

    @Nonnull
    public abstract InvoiceLinkProgressDataAccessObject lastUpdated(@Nonnull ZonedDateTime timestamp);

    @Override
    @Nonnull
    public InvoiceLinkProgress entity() {
        return new InvoiceLinkProgress(this);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof InvoiceLinkProgressDataAccessObject invoice)) return false;
        return Objects.equals(getIdentifier(), invoice.getIdentifier())
            && Objects.equals(getLastLinkedSequence(), invoice.getLastLinkedSequence())
            && Objects.equals(getLastUpdated(), invoice.getLastUpdated());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getIdentifier(), getLastLinkedSequence(), getLastUpdated());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
                .add("identifier", getIdentifier())
                .add("lastLinkedSequence", getLastLinkedSequence())
                .add("lastUpdated", getLastUpdated())
                .toString();
    }
}
