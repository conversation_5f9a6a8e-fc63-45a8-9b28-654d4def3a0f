package global.symbio.billing.invoice.persistence.api.notification.state;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.MoreObjects;
import jakarta.annotation.Nonnull;
import lombok.Getter;

import java.time.ZonedDateTime;
import java.util.Objects;

@Getter
public final class Delivered extends NotificationState {

    public static final String TYPE = "DELIVERED";

    @Nonnull
    private final ZonedDateTime timestamp;

    @JsonCreator(mode = JsonCreator.Mode.PROPERTIES)
    public Delivered(@Nonnull @JsonProperty("timestamp") ZonedDateTime timestamp) {
        super(TYPE);
        this.timestamp = Objects.requireNonNull(timestamp, "timestamp");
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), getTimestamp());
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Delivered delivered)) return false;
        if (!super.equals(o)) return false;
        return getTimestamp().equals(delivered.getTimestamp());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("state", state)
            .add("timestamp", timestamp)
            .toString();
    }
}
