package global.symbio.billing.invoice.persistence.api.invoice.state.payment;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.google.common.base.MoreObjects;
import jakarta.annotation.Nonnull;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.Objects;

@Getter
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "state")
@JsonSubTypes(
    value = {
        @JsonSubTypes.Type(value = Uninvoiced.class, name = Uninvoiced.TYPE),
        @JsonSubTypes.Type(value = Unpaid.class, name = Unpaid.TYPE),
        @JsonSubTypes.Type(value = PartiallyPaid.class, name = PartiallyPaid.TYPE),
        @JsonSubTypes.Type(value = Paid.class, name = Paid.TYPE)
    }
)
public abstract sealed class PaymentState permits Uninvoiced, Unpaid, PartiallyPaid, Paid {

    @Nonnull
    protected final String state;

    @Nonnull
    protected final BigDecimal total;

    public PaymentState(@Nonnull String state, @Nonnull BigDecimal total) {
        this.state = Objects.requireNonNull(state, "state");
        this.total = Objects.requireNonNull(total, "total");
    }

    @Nonnull
    public final BigDecimal getTotal() {
        return total;
    }

    @Nonnull
    public abstract BigDecimal getPaid();

    @Nonnull
    public abstract BigDecimal getDue();

    @Nonnull
    public abstract PaymentState pay(@Nonnull BigDecimal amount);

    @Nonnull
    public abstract PaymentState refund(@Nonnull BigDecimal amount);

    protected static void ensurePositive(@Nonnull BigDecimal amount) {
        final var comparison = amount.compareTo(BigDecimal.ZERO);
        if (comparison <= 0) {
            throw new IllegalStateException("Amount is required to be positive and greater than 0.");
        }
    }

    @Nonnull
    public static PaymentState create(@Nonnull BigDecimal total, @Nonnull BigDecimal due) {
        if (due.compareTo(BigDecimal.ZERO) <= 0) {
            return new Paid(total, due);
        }
        return new Unpaid(total, due);
    }

    @Override
    public final boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof PaymentState payment)) return false;
        return Objects.equals(getState(), payment.getState()) && Objects.equals(getTotal(), payment.getTotal()) && Objects.equals(getPaid(), payment.getPaid()) && Objects.equals(getDue(), payment.getDue());
    }

    @Override
    public final int hashCode() {
        return Objects.hash(getState(), getTotal(), getPaid(), getDue());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this).add("type", getState()).add("total", getTotal()).add("paid", getPaid()).add("due", getDue()).toString();
    }
}
