package global.symbio.billing.invoice.persistence.api.notification.state;

import jakarta.annotation.Nonnull;

import java.util.Objects;

public final class Pending extends NotificationState {

    public static final String TYPE = "PENDING";

    @Nonnull
    private static final Pending INSTANCE = new Pending();

    public Pending() {
        super(TYPE);
    }

    @Nonnull
    @Override
    public Pending pending() {
        return this;
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode());
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof Pending)) return false;
        return super.equals(o);
    }

    @Nonnull
    public static Pending getInstance() {
        return INSTANCE;
    }
}
