package global.symbio.billing.invoice.persistence.api.invoice.notification;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.persistence.api.DataAccessObject;
import global.symbio.billing.invoice.persistence.api.invoice.Invoice;

import javax.annotation.Nonnull;
import java.time.ZonedDateTime;
import java.util.Objects;
import java.util.UUID;

public abstract class InvoiceNotificationDataAccessObject implements DataAccessObject<String, InvoiceNotificationDataAccessObject> {

    @Nonnull
    public abstract UUID getInvoice();

    @Nonnull
    public InvoiceNotificationDataAccessObject invoice(@Nonnull Invoice invoice) {
        return invoice(invoice.getIdentifier());
    }

    @Nonnull
    public abstract InvoiceNotificationDataAccessObject invoice(@Nonnull UUID invoice);

    @Nonnull
    public abstract ZonedDateTime getTimestamp();

    @Nonnull
    public abstract InvoiceNotificationDataAccessObject timestamp(@Nonnull ZonedDateTime timestamp);

    @Nonnull
    @Override
    public InvoiceNotification entity() {
        return new InvoiceNotification(this);
    }

    @Override
    public int hashCode() {
        return Objects.hash(getIdentifier(), getInvoice(), getTimestamp());
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof InvoiceNotificationDataAccessObject notification)) return false;
        return Objects.equals(getIdentifier(), notification.getIdentifier()) && Objects.equals(getInvoice(), notification.getInvoice()) && Objects.equals(getTimestamp(), notification.getTimestamp());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(getClass())
            .add("identifier", getIdentifier())
            .add("invoice", getInvoice())
            .add("timestamp", getTimestamp())
            .toString();
    }
}