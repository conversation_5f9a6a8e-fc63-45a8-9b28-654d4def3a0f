package global.symbio.billing.invoice.persistence.api.notification;

import com.google.common.base.MoreObjects;
import global.symbio.billing.core.persistence.api.DataAccessObject;
import global.symbio.billing.invoice.persistence.api.notification.state.NotificationState;
import jakarta.annotation.Nonnull;

import java.time.ZonedDateTime;
import java.util.Objects;

public abstract class NotificationDataAccessObject implements DataAccessObject<String, NotificationDataAccessObject> {

    @Nonnull
    public abstract NotificationType getType();

    @Nonnull
    public abstract NotificationDataAccessObject type(@Nonnull NotificationType type);

    @Nonnull
    public abstract NotificationState getState();

    @Nonnull
    public abstract NotificationDataAccessObject state(@Nonnull NotificationState state);

    @Nonnull
    public abstract ZonedDateTime getTimestamp();

    @Nonnull
    public abstract NotificationDataAccessObject timestamp(@Nonnull ZonedDateTime timestamp);

    @Override @Nonnull
    public Notification entity() {
        return new Notification(this);
    }

    @Override
    public int hashCode() {
        return Objects.hash(getIdentifier(), getType(), getState(), getTimestamp());
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof NotificationDataAccessObject notification)) return false;
        return Objects.equals(getIdentifier(), notification.getIdentifier()) && Objects.equals(getType(), notification.getType()) && Objects.equals(getState(), notification.getState()) && Objects.equals(getTimestamp(), notification.getTimestamp());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(getClass())
            .add("identifier", getIdentifier())
            .add("type", getType())
            .add("state", getState())
            .add("timestamp", getTimestamp())
            .toString();
    }
}
