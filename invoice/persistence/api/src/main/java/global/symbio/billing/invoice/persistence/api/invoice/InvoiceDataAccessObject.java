package global.symbio.billing.invoice.persistence.api.invoice;

import com.google.common.base.MoreObjects;
import com.google.common.collect.Range;
import global.symbio.billing.core.country.persistence.api.Country;
import global.symbio.billing.core.country.persistence.api.CountryDataAccessObject;
import global.symbio.billing.core.persistence.api.DataAccessObject;
import global.symbio.billing.core.reference.persistence.api.reference.Reference;
import global.symbio.billing.core.reference.persistence.api.reference.ReferenceDataAccessObject;
import global.symbio.billing.invoice.persistence.api.invoice.state.generation.GenerationState;
import global.symbio.billing.invoice.persistence.api.invoice.state.payment.PaymentState;
import global.symbio.billing.invoice.persistence.api.invoice.summary.InvoiceSummary;
import global.symbio.billing.invoice.persistence.api.invoice.summary.InvoiceSummaryDataAccessObject;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Objects;
import java.util.UUID;

public abstract class InvoiceDataAccessObject implements DataAccessObject<UUID, InvoiceDataAccessObject> {

    @Nonnull
    public abstract UUID getAccount();

    @Nonnull
    public abstract InvoiceDataAccessObject account(@Nonnull UUID account);

    @Nonnull
    public abstract Integer getNumber();

    @Nonnull
    public abstract InvoiceDataAccessObject number(@Nonnull Integer number);

    @Nonnull
    public abstract Range<Long> getSequences();

    @Nonnull
    public InvoiceDataAccessObject sequences(@Nonnull Long lower, @Nonnull Long upper) {
        return sequences(Range.closed(lower, upper));
    }

    @Nonnull
    public abstract InvoiceDataAccessObject sequences(@Nonnull Range<Long> range);

    @Nonnull
    public abstract Range<ZonedDateTime> getPeriod();

    @Nonnull
    public InvoiceDataAccessObject period(@Nonnull ZonedDateTime start, @Nonnull ZonedDateTime end) {
        return period(Range.closedOpen(start, end));
    }

    @Nonnull
    public abstract InvoiceDataAccessObject period(@Nonnull Range<ZonedDateTime> range);

    @Nonnull
    public abstract BigDecimal getOpeningBalance();

    @Nonnull
    public abstract InvoiceDataAccessObject openingBalance(@Nonnull BigDecimal balance);

    @Nonnull
    public abstract BigDecimal getClosingBalance();

    @Nonnull
    public abstract InvoiceDataAccessObject closingBalance(@Nonnull BigDecimal balance);

    @Nonnull
    public abstract PaymentState getPayment();

    @Nonnull
    public abstract InvoiceDataAccessObject payment(@Nonnull PaymentState payment);

    @Nonnull
    public abstract GenerationState getGeneration();

    @Nonnull
    public abstract InvoiceDataAccessObject generation(@Nonnull GenerationState generation);

    @Nonnull
    public abstract CountryDataAccessObject getCountry();

    @Nonnull
    public abstract InvoiceDataAccessObject country(@Nonnull CountryDataAccessObject country);

    @Nonnull
    public InvoiceDataAccessObject country(@Nonnull Country country) {
        return country(country.data());
    }

    @Nullable
    public abstract ReferenceDataAccessObject getReference();

    @Nonnull
    public abstract InvoiceDataAccessObject reference(@Nullable ReferenceDataAccessObject reference);

    @Nonnull
    public InvoiceDataAccessObject reference(@Nonnull Reference entity) {
        return reference(entity.data());
    }

    @Nullable
    public abstract InvoiceSummaryDataAccessObject getSummary();

    @Nonnull
    public abstract InvoiceDataAccessObject summary(@Nullable InvoiceSummaryDataAccessObject summary);

    @Nonnull
    public InvoiceDataAccessObject summary(@Nonnull InvoiceSummary entity) {
        return summary(entity.data());
    }

    @Nullable
    public abstract ZonedDateTime getDueDate();

    @Nonnull
    public abstract InvoiceDataAccessObject dueDate(@Nullable ZonedDateTime dueDate);

    @Nonnull
    public abstract ZonedDateTime getTimestamp();

    @Nonnull
    public abstract InvoiceDataAccessObject timestamp(@Nonnull ZonedDateTime timestamp);

    @Nullable
    public abstract ZonedDateTime getReportedTimestamp();

    @Nonnull
    public abstract InvoiceDataAccessObject reportedTimestamp(@Nullable ZonedDateTime timestamp);

    @Override
    @Nonnull
    public Invoice entity() {
        return new Invoice(this);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof InvoiceDataAccessObject invoice)) return false;
        return Objects.equals(getIdentifier(), invoice.getIdentifier())
            && Objects.equals(getAccount(), invoice.getAccount())
            && Objects.equals(getNumber(), invoice.getNumber())
            && Objects.equals(getSequences(), invoice.getSequences())
            && Objects.equals(getPeriod(), invoice.getPeriod())
            && Objects.equals(getOpeningBalance(), invoice.getOpeningBalance())
            && Objects.equals(getClosingBalance(), invoice.getClosingBalance())
            && Objects.equals(getPayment(), invoice.getPayment())
            && Objects.equals(getGeneration(), invoice.getGeneration())
            && Objects.equals(getCountry(), invoice.getCountry())
            && Objects.equals(getReference(), invoice.getReference())
            && Objects.equals(getSummary(), invoice.getSummary())
            && Objects.equals(getDueDate(), invoice.getDueDate())
            && Objects.equals(getTimestamp(), invoice.getTimestamp())
            && Objects.equals(getReportedTimestamp(), invoice.getReportedTimestamp());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getIdentifier(), getAccount(), getNumber(), getSequences(), getPeriod(), getOpeningBalance(), getClosingBalance(), getPayment(), getGeneration(), getCountry(), getReference(), getSummary(), getDueDate(), getTimestamp(), getReportedTimestamp());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this)
            .add("identifier", getIdentifier())
            .add("account", getAccount())
            .add("number", getNumber())
            .add("sequences", getSequences())
            .add("period", getPeriod())
            .add("openingBalance", getOpeningBalance())
            .add("closingBalance", getClosingBalance())
            .add("payment", getPayment())
            .add("generation", getGeneration())
            .add("country", getCountry())
            .add("reference", getReference())
            .add("summary", getSummary())
            .add("dueDate", getDueDate())
            .add("timestamp", getTimestamp())
            .add("reportedTimestamp", getReportedTimestamp())
            .toString();
    }
}