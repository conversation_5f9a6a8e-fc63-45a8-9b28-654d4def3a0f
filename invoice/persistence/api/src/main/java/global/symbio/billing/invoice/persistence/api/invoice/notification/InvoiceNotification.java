package global.symbio.billing.invoice.persistence.api.invoice.notification;

import global.symbio.billing.core.persistence.api.Entity;
import jakarta.annotation.Nonnull;

import java.time.ZonedDateTime;
import java.util.UUID;

public class InvoiceNotification extends Entity<String, InvoiceNotificationDataAccessObject> {

    public InvoiceNotification(@Nonnull InvoiceNotificationDataAccessObject data) {
        super(data);
    }

    @Nonnull
    public UUID getInvoice() {
        return data().getInvoice();
    }

    @Nonnull
    public ZonedDateTime getTimestamp() {
        return data().getTimestamp();
    }
}
