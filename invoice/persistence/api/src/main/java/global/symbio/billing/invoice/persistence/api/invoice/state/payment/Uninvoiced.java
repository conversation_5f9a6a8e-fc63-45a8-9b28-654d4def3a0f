package global.symbio.billing.invoice.persistence.api.invoice.state.payment;

import jakarta.annotation.Nonnull;

import java.math.BigDecimal;

public final class Uninvoiced extends PaymentState {

    @Nonnull
    private static final Uninvoiced INSTANCE = new Uninvoiced();

    public static final String TYPE = "UNINVOICED";

    public Uninvoiced() {
        super(TYPE, BigDecimal.ZERO);
    }

    @Nonnull
    @Override
    public BigDecimal getPaid() {
        return BigDecimal.ZERO;
    }

    @Nonnull
    @Override
    public BigDecimal getDue() {
        return BigDecimal.ZERO;
    }

    @Nonnull
    @Override
    public PaymentState pay(@Nonnull BigDecimal amount) {
        ensurePositive(amount);
        throw new IllegalStateException("Unable to pay an uninvoiced invoice.");
    }

    @Nonnull
    @Override
    public PaymentState refund(@Nonnull BigDecimal amount) {
        ensurePositive(amount);
        throw new IllegalStateException("Unable to refund an uninvoiced invoice.");
    }

    @Nonnull
    public static Uninvoiced getInstance() {
        return INSTANCE;
    }
}
