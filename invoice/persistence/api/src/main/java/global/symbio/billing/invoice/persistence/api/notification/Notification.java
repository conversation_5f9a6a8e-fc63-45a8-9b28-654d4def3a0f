package global.symbio.billing.invoice.persistence.api.notification;

import global.symbio.billing.core.persistence.api.Entity;
import global.symbio.billing.invoice.persistence.api.notification.state.NotificationState;
import jakarta.annotation.Nonnull;

import java.time.ZonedDateTime;

public class Notification extends Entity<String, NotificationDataAccessObject> {

    public Notification(@Nonnull NotificationDataAccessObject data) { super(data); }

    @Nonnull
    public NotificationType getType() { return data().getType(); }

    @Nonnull
    public NotificationState getState() {
        return data().getState();
    }

    @Nonnull
    public ZonedDateTime getTimestamp() {
        return data().getTimestamp();
    }
}