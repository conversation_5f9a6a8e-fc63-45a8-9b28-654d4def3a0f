package global.symbio.billing.invoice.persistence.api.repository;

import global.symbio.billing.invoice.persistence.api.invoice.notification.InvoiceNotificationDataAccessObject;
import io.micronaut.data.repository.CrudRepository;
import jakarta.annotation.Nonnull;

import java.util.Collection;
import java.util.Set;
import java.util.UUID;

public interface InvoiceNotificationRepository<T extends InvoiceNotificationDataAccessObject> extends CrudRepository<T, String> {

    @Nonnull
    Collection<T> findAllByInvoiceInOrderByTimestampDesc(@Nonnull Set<UUID> ids);
}