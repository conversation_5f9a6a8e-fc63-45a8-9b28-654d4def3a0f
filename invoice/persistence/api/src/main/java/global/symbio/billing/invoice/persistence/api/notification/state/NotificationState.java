package global.symbio.billing.invoice.persistence.api.notification.state;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.google.common.base.MoreObjects;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.Getter;

import java.time.ZonedDateTime;
import java.util.Objects;

@Getter
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "state")
@JsonSubTypes(
    value = {
        @JsonSubTypes.Type(value = Pending.class, name = Pending.TYPE),
        @JsonSubTypes.Type(value = Sent.class, name = Sent.TYPE),
        @JsonSubTypes.Type(value = PartiallyDelivered.class, name = PartiallyDelivered.TYPE),
        @JsonSubTypes.Type(value = Delivered.class, name = Delivered.TYPE),
        @JsonSubTypes.Type(value = Failure.class, name = Failure.TYPE)
    }
)
public abstract sealed class NotificationState permits Pending, Sent, PartiallyDelivered, Delivered, Failure {

    @Nonnull
    protected final String state;

    public NotificationState(@Nonnull String state) {
        this.state = Objects.requireNonNull(state, "state");
    }

    @Nonnull
    public Pending pending() {
        return Pending.getInstance();
    }

    @Nonnull
    public Sent sent() {
        return new Sent(ZonedDateTime.now());
    }

    @Nonnull
    public Sent sent(@Nonnull ZonedDateTime timestamp) {
        return new Sent(timestamp);
    }

    @Nonnull
    public PartiallyDelivered partiallyDelivered() {
        return new PartiallyDelivered(ZonedDateTime.now());
    }

    @Nonnull
    public PartiallyDelivered partiallyDelivered(@Nonnull ZonedDateTime timestamp) {
        return new PartiallyDelivered(timestamp);
    }

    @Nonnull
    public Delivered delivered() {
        return new Delivered(ZonedDateTime.now());
    }

    @Nonnull
    public Delivered delivered(@Nonnull ZonedDateTime timestamp) {
        return new Delivered(timestamp);
    }

    @Nonnull
    public Failure failure(@Nullable String reason) {
        return new Failure(reason, ZonedDateTime.now());
    }

    @Nonnull
    public Failure failure(@Nullable String reason, @Nonnull ZonedDateTime timestamp) {
        return new Failure(reason, timestamp);
    }

    @Override
    public int hashCode() {
        return Objects.hash(getState());
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof NotificationState notification)) return false;
        return getState().equals(notification.getState());
    }

    @Override
    public String toString() {
        return MoreObjects.toStringHelper(this).add("state", getState()).toString();
    }
}
