package global.symbio.billing.invoice.persistence.api.repository.factory;

import global.symbio.billing.core.payment.persistence.api.allocation.account.AccountAllocationDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.payment.PaymentAccountAllocationDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.transfer.TransferInAccountAllocationDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.allocation.account.subtype.transfer.TransferOutAccountAllocationDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.allocation.invoice.InvoiceAllocationDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.allocation.invoice.InvoiceAllocationIdentifier;
import global.symbio.billing.core.payment.persistence.api.cancellation.PaymentCancellationDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.payment.PaymentDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.payment.method.PaymentMethodDataAccessObject;
import global.symbio.billing.core.payment.persistence.api.repository.*;
import global.symbio.billing.core.payment.persistence.api.transfer.TransferDataAccessObject;
import global.symbio.billing.core.persistence.api.CompositeRepository;
import global.symbio.billing.core.persistence.api.PersistenceStore;
import global.symbio.billing.core.reference.persistence.api.reference.ReferenceDataAccessObject;
import global.symbio.billing.core.reference.persistence.api.repository.ReferenceRepository;
import global.symbio.billing.invoice.persistence.api.invoice.InvoiceDataAccessObject;
import global.symbio.billing.invoice.persistence.api.invoice.link.InvoiceLinkProgressDataAccessObject;
import global.symbio.billing.invoice.persistence.api.invoice.notification.InvoiceNotificationDataAccessObject;
import global.symbio.billing.invoice.persistence.api.invoice.summary.InvoiceSummaryDataAccessObject;
import global.symbio.billing.invoice.persistence.api.notification.NotificationDataAccessObject;
import global.symbio.billing.invoice.persistence.api.repository.*;
import io.micronaut.context.annotation.Factory;
import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import static global.symbio.billing.core.persistence.api.PersistenceStore.Capabilities.READ_ONLY;
import static global.symbio.billing.core.persistence.api.PersistenceStore.Capabilities.READ_WRITE;

@Factory
public class CompositeRepositoryFactory {

    @Inject
    @Singleton
    public CompositeRepository<InvoiceNotificationRepository<InvoiceNotificationDataAccessObject>> create(@Nonnull @PersistenceStore(READ_ONLY) InvoiceNotificationRepository<InvoiceNotificationDataAccessObject> read, @Nonnull @PersistenceStore(READ_WRITE) InvoiceNotificationRepository<InvoiceNotificationDataAccessObject> write) {
        return new CompositeRepository<>(read, write);
    }

    @Inject
    @Singleton
    public CompositeRepository<InvoiceRepository<InvoiceDataAccessObject>> create(@Nonnull @PersistenceStore(READ_ONLY) InvoiceRepository<InvoiceDataAccessObject> read, @Nonnull @PersistenceStore(READ_WRITE) InvoiceRepository<InvoiceDataAccessObject> write) {
        return new CompositeRepository<>(read, write);
    }

    @Inject
    @Singleton
    public CompositeRepository<InvoiceSummaryRepository<InvoiceSummaryDataAccessObject>> create(@Nonnull @PersistenceStore(READ_ONLY) InvoiceSummaryRepository<InvoiceSummaryDataAccessObject> read, @Nonnull @PersistenceStore(READ_WRITE) InvoiceSummaryRepository<InvoiceSummaryDataAccessObject> write) {
        return new CompositeRepository<>(read, write);
    }

    @Inject
    @Singleton
    public CompositeRepository<NotificationRepository<NotificationDataAccessObject>> create(@Nonnull @PersistenceStore(READ_ONLY) NotificationRepository<NotificationDataAccessObject> read, @Nonnull @PersistenceStore(READ_WRITE) NotificationRepository<NotificationDataAccessObject> write) {
        return new CompositeRepository<>(read, write);
    }

    @Inject
    @Singleton
    public CompositeRepository<PaymentMethodRepository<PaymentMethodDataAccessObject>> create(@Nonnull @PersistenceStore(READ_ONLY) PaymentMethodRepository<PaymentMethodDataAccessObject> read, @Nonnull @PersistenceStore(READ_WRITE) PaymentMethodRepository<PaymentMethodDataAccessObject> write) {
        return new CompositeRepository<>(read, write);
    }

    @Inject
    @Singleton
    public CompositeRepository<PaymentRepository<PaymentDataAccessObject>> create(@Nonnull @PersistenceStore(READ_ONLY) PaymentRepository<PaymentDataAccessObject> read, @Nonnull @PersistenceStore(READ_WRITE) PaymentRepository<PaymentDataAccessObject> write) {
        return new CompositeRepository<>(read, write);
    }

    @Inject
    @Singleton
    public CompositeRepository<ReferenceRepository<ReferenceDataAccessObject>> create(@Nonnull @PersistenceStore(READ_ONLY) ReferenceRepository<ReferenceDataAccessObject> read, @Nonnull @PersistenceStore(READ_WRITE) ReferenceRepository<ReferenceDataAccessObject> write) {
        return new CompositeRepository<>(read, write);
    }

    @Inject
    @Singleton
    public <T extends PaymentAccountAllocationDataAccessObject<T>> CompositeRepository<PaymentAccountAllocationRepository<T>> create(@Nonnull @PersistenceStore(READ_ONLY) PaymentAccountAllocationRepository<T> read, @Nonnull @PersistenceStore(READ_WRITE) PaymentAccountAllocationRepository<T> write) {
        return new CompositeRepository<>(read, write);
    }

    @Inject
    @Singleton
    public <T extends TransferInAccountAllocationDataAccessObject<T>> CompositeRepository<TransferInAccountAllocationRepository<T>> create(@Nonnull @PersistenceStore(READ_ONLY) TransferInAccountAllocationRepository<T> read, @Nonnull @PersistenceStore(READ_WRITE) TransferInAccountAllocationRepository<T> write) {
        return new CompositeRepository<>(read, write);
    }

    @Inject
    @Singleton
    public <T extends TransferOutAccountAllocationDataAccessObject<T>> CompositeRepository<TransferOutAccountAllocationRepository<T>> create(@Nonnull @PersistenceStore(READ_ONLY) TransferOutAccountAllocationRepository<T> read, @Nonnull @PersistenceStore(READ_WRITE) TransferOutAccountAllocationRepository<T> write) {
        return new CompositeRepository<>(read, write);
    }

    @Inject
    @Singleton
    public <T extends AccountAllocationDataAccessObject<T>> CompositeRepository<AccountAllocationRepository<T>> create(@Nonnull @PersistenceStore(READ_ONLY) AccountAllocationRepository<T> read, @Nonnull @PersistenceStore(READ_WRITE) AccountAllocationRepository<T> write) {
        return new CompositeRepository<>(read, write);
    }

    @Inject
    @Singleton
    public <I extends InvoiceAllocationIdentifier, P extends InvoiceAllocationDataAccessObject> CompositeRepository<InvoiceAllocationRepository<I, P>> create(@Nonnull @PersistenceStore(READ_ONLY) InvoiceAllocationRepository<I, P> read, @Nonnull @PersistenceStore(READ_WRITE) InvoiceAllocationRepository<I, P> write) {
        return new CompositeRepository<>(read, write);
    }

    @Inject
    @Singleton
    public CompositeRepository<TransferRepository<TransferDataAccessObject>> create(@Nonnull @PersistenceStore(READ_ONLY) TransferRepository<TransferDataAccessObject> read, @Nonnull @PersistenceStore(READ_WRITE) TransferRepository<TransferDataAccessObject> write) {
        return new CompositeRepository<>(read, write);
    }

    @Inject
    @Singleton
    public CompositeRepository<PaymentCancellationRepository<PaymentCancellationDataAccessObject>> create(@Nonnull @PersistenceStore(READ_ONLY) PaymentCancellationRepository<PaymentCancellationDataAccessObject> read, @Nonnull @PersistenceStore(READ_WRITE) PaymentCancellationRepository<PaymentCancellationDataAccessObject> write) {
        return new CompositeRepository<>(read, write);
    }

    @Inject
    @Singleton
    public CompositeRepository<InvoiceLinkProgressRepository<InvoiceLinkProgressDataAccessObject>> create(@Nonnull @PersistenceStore(READ_ONLY) InvoiceLinkProgressRepository<InvoiceLinkProgressDataAccessObject> read, @Nonnull @PersistenceStore(READ_WRITE) InvoiceLinkProgressRepository<InvoiceLinkProgressDataAccessObject> write) {
        return new CompositeRepository<>(read, write);
    }
}