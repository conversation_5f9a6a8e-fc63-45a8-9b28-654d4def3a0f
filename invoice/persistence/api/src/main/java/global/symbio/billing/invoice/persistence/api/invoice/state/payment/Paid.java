package global.symbio.billing.invoice.persistence.api.invoice.state.payment;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.annotation.Nonnull;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.Objects;

@Getter
public final class Paid extends PaymentState {

    public static final String TYPE = "PAID";

    @Nonnull
    private final BigDecimal paid;

    @JsonCreator(mode = JsonCreator.Mode.PROPERTIES)
    public Paid(@Nonnull @JsonProperty("total") BigDecimal total, @Nonnull @JsonProperty("paid") BigDecimal paid) {
        super(TYPE, total);
        this.paid = Objects.requireNonNull(paid, "paid");
    }

    @Nonnull @Override
    public BigDecimal getDue() {
        return BigDecimal.ZERO;
    }

    @Nonnull
    @Override
    public PaymentState pay(@Nonnull BigDecimal amount) {
        ensurePositive(amount);
        throw new IllegalStateException("Unable to pay a completely paid invoice.");
    }

    @Nonnull
    @Override
    public PaymentState refund(@Nonnull BigDecimal amount) {
        ensurePositive(amount);
        final var payment = amount.compareTo(paid);
        if (payment > 0) {
            throw new IllegalStateException("Unable to refund more than what has been paid. Paid: " + paid + ", Requested: " + amount + ".");
        } else if (payment == 0) {
            return new Unpaid(total, amount);
        }
        return new PartiallyPaid(total, paid.subtract(amount), amount);
    }
}
