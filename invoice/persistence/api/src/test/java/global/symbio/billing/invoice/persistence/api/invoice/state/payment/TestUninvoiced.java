package global.symbio.billing.invoice.persistence.api.invoice.state.payment;

import com.fasterxml.jackson.core.JsonProcessingException;
import global.symbio.billing.core.persistence.api.DeterministicObjectMapper;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;

public class TestUninvoiced {

    private Uninvoiced state;

    @BeforeEach
    public void setup() {
        state = new Uninvoiced();
    }

    @Test
    @DisplayName("Uninvoiced is subclass of PaymentState")
    public void uninvoiced_is_subtype_of_payment_state() {
        assertInstanceOf(Uninvoiced.class, state);
        assertInstanceOf(PaymentState.class, state);
    }

    @Test
    @DisplayName("Uninvoiced#TYPE equals UNINVOICED and Uninvoiced#type equals Uninvoiced#TYPE")
    public void uninvoiced_type_equals_uninvoiced() {
        assertEquals("UNINVOICED", Uninvoiced.TYPE);
        assertEquals(Uninvoiced.TYPE, state.getState());
        assertSame(Uninvoiced.TYPE, state.getState());
    }

    @Test
    @DisplayName("Uninvoiced::pay throws IllegalStateException")
    public void uninvoiced_pay_throws_illegal_state_exception() {
        final var cause = assertThrows(IllegalStateException.class, () -> state.pay(BigDecimal.TEN));
        assertEquals("Unable to pay an uninvoiced invoice.", cause.getMessage());
    }

    @ParameterizedTest
    @CsvSource(value = { "-10", "-1", "0" })
    @DisplayName("Uninvoiced::pay with negative or zero amount throws IllegalStateException")
    public void uninvoiced_pay_with_negative_or_zero_amount_throws_illegal_state_exception(@Nonnull BigDecimal amount) {
        final var cause = assertThrows(IllegalStateException.class, () -> state.pay(amount));
        assertEquals("Amount is required to be positive and greater than 0.", cause.getMessage());
    }

    @Test
    @DisplayName("Uninvoiced::refund throws IllegalStateException")
    public void uninvoiced_refund_throws_illegal_state_exception() {
        final var cause = assertThrows(IllegalStateException.class, () -> state.refund(BigDecimal.TEN));
        assertEquals("Unable to refund an uninvoiced invoice.", cause.getMessage());
    }

    @ParameterizedTest
    @CsvSource(value = { "-10", "-1", "0" })
    @DisplayName("Uninvoiced::refund with negative or zero amount throws IllegalStateException")
    public void uninvoiced_refund_with_negative_or_zero_amount_throws_illegal_state_exception(@Nonnull BigDecimal amount) {
        final var cause = assertThrows(IllegalStateException.class, () -> state.refund(amount));
        assertEquals("Amount is required to be positive and greater than 0.", cause.getMessage());
    }

    @Test
    @DisplayName("Uninvoiced state JSON serialisation and deserialisation functions correctly")
    public void uninvoiced_json_serialisation_and_deserialisation() throws JsonProcessingException {
        final var mapper = DeterministicObjectMapper.get();
        final var input = "{\"due\":0,\"paid\":0,\"state\":\"" + Uninvoiced.TYPE + "\",\"total\":0}";

        final var base = assertInstanceOf(Uninvoiced.class, mapper.readValue(input, PaymentState.class));
        final var impl = assertInstanceOf(Uninvoiced.class, mapper.readValue(input, Uninvoiced.class));

        assertEquals(base, impl);

        final var output = mapper.writeValueAsString(impl);
        assertEquals(input, output);
    }
}