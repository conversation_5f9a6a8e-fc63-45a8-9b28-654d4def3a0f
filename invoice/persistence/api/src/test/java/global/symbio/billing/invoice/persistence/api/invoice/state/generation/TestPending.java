package global.symbio.billing.invoice.persistence.api.invoice.state.generation;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import global.symbio.billing.core.persistence.api.DeterministicObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.ZonedDateTime;

import static org.junit.jupiter.api.Assertions.*;

public class TestPending {

    private Pending state;

    @BeforeEach
    public void setup() {
        state = new Pending();
    }

    @Test
    @DisplayName("Pending is subclass of State")
    public void pending_is_subtype_of_state() {
        assertInstanceOf(Pending.class, state);
        assertInstanceOf(GenerationState.class, state);
    }

    @Test
    @DisplayName("Pending#TYPE equals PENDING and Pending#type equals Pending#TYPE")
    public void pending_type_equals_pending() {
        assertEquals("PENDING", Pending.TYPE);
        assertEquals(Pending.TYPE, state.getState());
        assertSame(Pending.TYPE, state.getState());
    }

    @Test
    @DisplayName("Pending#pending returns `this` instance")
    public void pending_pending_returns_this_instance() {
        final var pending = state.pending();
        assertEquals(state, pending);
        assertSame(state, pending);
    }

    @Test
    @DisplayName("Pending#success returns a Success instance")
    public void pending_success_returns_success_instance() {
        var success = state.success(ZonedDateTime.now());
        assertInstanceOf(Success.class, success);

        success = state.success();
        assertInstanceOf(Success.class, success);
    }

    @Test
    @DisplayName("Pending#failure returns a Failure instance")
    public void pending_failure_returns_failure_instance() {
        var failure = state.failure("reason", ZonedDateTime.now());
        assertInstanceOf(Failure.class, failure);

        failure = state.failure("reason");
        assertInstanceOf(Failure.class, failure);
    }

    @Test
    @DisplayName("Pending state JSON serialisation and deserialisation functions correctly")
    public void pending_json_serialisation_and_deserialisation() throws JsonProcessingException {
        final var mapper = DeterministicObjectMapper.get();
        final var input = "{\"state\":\"" + Pending.TYPE + "\"}";

        final var base = assertInstanceOf(Pending.class, mapper.readValue(input, GenerationState.class));
        final var impl = assertInstanceOf(Pending.class, mapper.readValue(input, Pending.class));

        assertEquals(base, impl);

        final var output = mapper.writeValueAsString(impl);
        assertEquals(input, output);
    }
}
