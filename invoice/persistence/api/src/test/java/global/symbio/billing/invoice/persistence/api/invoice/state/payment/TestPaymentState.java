package global.symbio.billing.invoice.persistence.api.invoice.state.payment;

import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.lang.reflect.Modifier;
import java.math.BigDecimal;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;

public class TestPaymentState {

    @Test
    @DisplayName("PaymentState is abstract class.")
    public void state_is_abstract_class() {
        assertTrue(Modifier.isAbstract(PaymentState.class.getModifiers()));
    }

    @Test
    @DisplayName("PaymentState is sealed class.")
    public void state_is_sealed_class() {
        assertTrue(PaymentState.class.isSealed());
    }

    @Test
    @DisplayName("PaymentState only permits Uninvoiced, Unpaid and PartiallyPaid and Paid implementations.")
    public void state_only_permits_uninvoiced_unpaid_partially_paid_and_paid_subtypes() {
        final var permitted = PaymentState.class.getPermittedSubclasses();
        final var expected = new Class[]{ Uninvoiced.class, Unpaid.class, PartiallyPaid.class, Paid.class };
        assertArrayEquals(expected, permitted);
    }

    @ParameterizedTest
    @MethodSource("parameters")
    @DisplayName("PaymentState::create factory method creates Paid/Unpaid payment states")
    public void payment_state_create_creates_paid_or_unpaid_state(@Nonnull BigDecimal due, @Nonnull Class<? extends PaymentState> type) {
        final var state = PaymentState.create(due, due);
        assertInstanceOf(type, state);
    }

    private static Stream<Arguments> parameters() {
        return Stream.of(
            Arguments.of(BigDecimal.ONE.negate(), Paid.class),
            Arguments.of(BigDecimal.ZERO, Paid.class),
            Arguments.of(BigDecimal.ONE, Unpaid.class)
        );
    }
}
