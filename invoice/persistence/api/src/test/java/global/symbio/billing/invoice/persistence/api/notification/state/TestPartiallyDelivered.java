package global.symbio.billing.invoice.persistence.api.notification.state;

import com.fasterxml.jackson.core.JsonProcessingException;
import global.symbio.billing.core.persistence.api.DeterministicObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;

import static org.junit.jupiter.api.Assertions.*;

public class TestPartiallyDelivered {

    private static final ZonedDateTime EPOCH = ZonedDateTime.ofInstant(Instant.EPOCH, ZoneId.systemDefault());

    private PartiallyDelivered state;

    @BeforeEach
    public void setup() {
        state = new PartiallyDelivered(EPOCH);
    }

    @Test
    @DisplayName("PartiallyDelivered::new rejects null timestamp")
    public void partially_delivered_rejects_null_timestamp() {
        assertThrows(NullPointerException.class, () -> new PartiallyDelivered(null));
    }

    @Test
    @DisplayName("PartiallyDelivered is subclass of State")
    public void partially_delivered_is_subtype_of_state() {
        assertInstanceOf(PartiallyDelivered.class, state);
        assertInstanceOf(PartiallyDelivered.class, state);
    }

    @Test
    @DisplayName("PartiallyDelivered::TYPE equals PARTIALLY_DELIVERED")
    public void partially_delivered_type_equals_partially_delivered() {
        assertEquals("PARTIALLY_DELIVERED", PartiallyDelivered.TYPE);
        assertEquals(PartiallyDelivered.TYPE, state.getState());
        assertSame(PartiallyDelivered.TYPE, state.getState());
    }

    @Test
    @DisplayName("PartiallyDelivered::pending returns Pending#getInstance")
    public void partially_delivered_pending_returns_singleton_instance() {
        final var pending = state.pending();
        assertEquals(Pending.getInstance(), pending);
        assertSame(Pending.getInstance(), pending);
    }

    @Test
    @DisplayName("PartiallyDelivered::PartiallyDelivered returns new instance")
    public void partially_delivered_returns_new_instance() {
        final var PartiallyDelivered = state.partiallyDelivered(EPOCH);
        assertEquals(state, PartiallyDelivered);
        assertNotSame(state, PartiallyDelivered);
    }

    @Test
    @DisplayName("PartiallyDelivered state JSON serialisation and deserialisation functions correctly")
    public void partially_delivered_json_serialisation_and_deserialisation() throws JsonProcessingException {
        final var mapper = DeterministicObjectMapper.get();
        final var timestamp = mapper.writeValueAsString(ZonedDateTime.now());
        final var input = "{\"state\":\"" + PartiallyDelivered.TYPE + "\",\"timestamp\":" + timestamp + "}";

        final var base = assertInstanceOf(PartiallyDelivered.class, mapper.readValue(input, NotificationState.class));
        final var impl = assertInstanceOf(PartiallyDelivered.class, mapper.readValue(input, PartiallyDelivered.class));

        assertEquals(base, impl);

        final var output = mapper.writeValueAsString(impl);
        assertEquals(input, output);
    }
}
