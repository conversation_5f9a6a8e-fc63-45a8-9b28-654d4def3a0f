package global.symbio.billing.invoice.persistence.api.invoice.state.payment;

import com.fasterxml.jackson.core.JsonProcessingException;
import global.symbio.billing.core.persistence.api.DeterministicObjectMapper;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;

public class TestPaid {

    private Paid state;

    @BeforeEach
    public void setup() {
        state = new Paid(BigDecimal.TEN, BigDecimal.TEN);
    }

    @Test
    @DisplayName("Paid is subclass of PaymentState")
    public void paid_is_subtype_of_payment_state() {
        assertInstanceOf(Paid.class, state);
        assertInstanceOf(PaymentState.class, state);
    }

    @Test
    @DisplayName("Paid#TYPE equals PAID and Paid#type equals Paid#TYPE")
    public void paid_type_equals_paid() {
        assertEquals("PAID", Paid.TYPE);
        assertEquals(Paid.TYPE, state.getState());
        assertSame(Paid.TYPE, state.getState());
    }

    @Test
    @DisplayName("Paid::pay throws IllegalStateException")
    public void paid_pay_throws_illegal_state_exception() {
        final var cause = assertThrows(IllegalStateException.class, () -> state.pay(BigDecimal.TEN));
        assertEquals("Unable to pay a completely paid invoice.", cause.getMessage());
    }

    @ParameterizedTest
    @CsvSource(value = { "-10", "-1", "0" })
    @DisplayName("Paid::pay with negative or zero amount throws IllegalStateException")
    public void paid_pay_with_negative_or_zero_amount_throws_illegal_state_exception(@Nonnull BigDecimal amount) {
        final var cause = assertThrows(IllegalStateException.class, () -> state.pay(amount));
        assertEquals("Amount is required to be positive and greater than 0.", cause.getMessage());
    }

    @Test
    @DisplayName("Paid::refund greater than amount paid throws IllegalStateException")
    public void paid_refund_greater_than_amount_paid_throws_illegal_state_exception() {
        final var amount = BigDecimal.TEN.multiply(BigDecimal.TEN);
        assertEquals(state.getPaid(), BigDecimal.TEN);
        final var cause = assertThrows(IllegalStateException.class, () -> state.refund(amount));
        assertEquals("Unable to refund more than what has been paid. Paid: " + BigDecimal.TEN + ", Requested: " + amount + ".", cause.getMessage());
    }

    @Test
    @DisplayName("Paid::refund amount paid transitions to Unpaid state")
    public void paid_refund_amount_paid_throws_transitions_to_unpaid_state() {
        assertEquals(state.getPaid(), BigDecimal.TEN);
        final var unpaid = assertDoesNotThrow(() -> state.refund(state.getPaid()));
        assertInstanceOf(Unpaid.class, unpaid);
        assertEquals(unpaid.getDue(), state.getPaid());
        assertEquals(unpaid.getPaid(), BigDecimal.ZERO);
    }

    @Test
    @DisplayName("Paid::refund amount less than paid transitions to PartiallyPaid state")
    public void paid_refund_amount_less_than_paid_throws_transitions_to_partially_paid_state() {
        final var amount = BigDecimal.ONE;
        assertEquals(state.getPaid(), BigDecimal.TEN);
        final var partial = assertDoesNotThrow(() -> state.refund(amount));
        assertInstanceOf(PartiallyPaid.class, partial);
        assertEquals(partial.getDue(), amount);
        assertEquals(partial.getPaid(), state.getPaid().subtract(amount));
    }

    @ParameterizedTest
    @CsvSource(value = { "-10", "-1", "0" })
    @DisplayName("Paid::refund with negative or zero amount throws IllegalStateException")
    public void paid_refund_with_negative_or_zero_amount_throws_illegal_state_exception(@Nonnull BigDecimal amount) {
        final var cause = assertThrows(IllegalStateException.class, () -> state.refund(amount));
        assertEquals("Amount is required to be positive and greater than 0.", cause.getMessage());
    }

    @Test
    @DisplayName("Paid state JSON serialisation and deserialisation functions correctly")
    public void paid_json_serialisation_and_deserialisation() throws JsonProcessingException {
        final var mapper = DeterministicObjectMapper.get();
        final var input = "{\"due\":0,\"paid\":0,\"state\":\"" + Paid.TYPE + "\",\"total\":0}";

        final var base = assertInstanceOf(Paid.class, mapper.readValue(input, PaymentState.class));
        final var impl = assertInstanceOf(Paid.class, mapper.readValue(input, Paid.class));

        assertEquals(base, impl);

        final var output = mapper.writeValueAsString(impl);
        assertEquals(input, output);
    }
}
