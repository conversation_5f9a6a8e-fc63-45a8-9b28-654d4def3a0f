package global.symbio.billing.invoice.persistence.api.invoice.state.payment;

import com.fasterxml.jackson.core.JsonProcessingException;
import global.symbio.billing.core.persistence.api.DeterministicObjectMapper;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;

public class TestUnpaid {

    private Unpaid state;

    @BeforeEach
    public void setup() {
        state = new Unpaid(BigDecimal.TEN, BigDecimal.TEN);
    }

    @Test
    @DisplayName("Unpaid is subclass of PaymentState")
    public void unpaid_is_subtype_of_payment_state() {
        assertInstanceOf(Unpaid.class, state);
        assertInstanceOf(PaymentState.class, state);
    }

    @Test
    @DisplayName("Unpaid#TYPE equals UNPAID and Unpaid#type equals Unpaid#TYPE")
    public void unpaid_type_equals_paid() {
        assertEquals("UNPAID", Unpaid.TYPE);
        assertEquals(Unpaid.TYPE, state.getState());
        assertSame(Unpaid.TYPE, state.getState());
    }

    @Test
    @DisplayName("Unpaid::pay greater than amount due throws IllegalStateException")
    public void unpaid_pay_greater_than_amount_due_throws_illegal_state_exception() {
        final var amount = BigDecimal.TEN.multiply(BigDecimal.TEN);
        assertEquals(BigDecimal.TEN, state.getDue());
        final var cause = assertThrows(IllegalStateException.class, () -> state.pay(amount));
        assertEquals("Unable to create a payment greater than the due amount. Due: " + state.getDue() + ", Requested: " + amount + ".", cause.getMessage());
    }

    @Test
    @DisplayName("Unpaid::pay amount due transitions to Paid state")
    public void unpaid_pay_amount_due_transitions_to_paid_state() {
        assertEquals(BigDecimal.TEN, state.getDue());
        final var paid = assertDoesNotThrow(() -> state.pay(state.getDue()));
        assertInstanceOf(Paid.class, paid);
        assertEquals(paid.getDue(), BigDecimal.ZERO);
        assertEquals(paid.getPaid(), state.getDue());
    }

    @Test
    @DisplayName("Unpaid::pay less than amount due transitions to PartiallyPaid state")
    public void unpaid_pay_less_than_amount_due_transitions_to_partially_paid_state() {
        final var amount = BigDecimal.ONE;
        assertEquals(BigDecimal.TEN, state.getDue());
        final var partial = assertDoesNotThrow(() -> state.pay(amount));
        assertInstanceOf(PartiallyPaid.class, partial);
        assertEquals(partial.getDue(), state.getDue().subtract(amount));
        assertEquals(partial.getPaid(), amount);
    }

    @ParameterizedTest
    @CsvSource(value = { "-10", "-1", "0" })
    @DisplayName("Unpaid::pay with negative or zero amount throws IllegalStateException")
    public void unpaid_pay_with_negative_or_zero_amount_throws_illegal_state_exception(@Nonnull BigDecimal amount) {
        final var cause = assertThrows(IllegalStateException.class, () -> state.pay(amount));
        assertEquals("Amount is required to be positive and greater than 0.", cause.getMessage());
    }

    @Test
    @DisplayName("Unpaid::refund throws IllegalStateException")
    public void unpaid_refund_throws_illegal_state_exception() {
        final var cause = assertThrows(IllegalStateException.class, () -> state.refund(BigDecimal.TEN));
        assertEquals("Unable to refund an unpaid invoice.", cause.getMessage());
    }

    @ParameterizedTest
    @CsvSource(value = { "-10", "-1", "0" })
    @DisplayName("Unpaid::refund with negative or zero amount throws IllegalStateException")
    public void unpaid_refund_with_negative_or_zero_amount_throws_illegal_state_exception(@Nonnull BigDecimal amount) {
        final var cause = assertThrows(IllegalStateException.class, () -> state.refund(amount));
        assertEquals("Amount is required to be positive and greater than 0.", cause.getMessage());
    }

    @Test
    @DisplayName("Unpaid state JSON serialisation and deserialisation functions correctly")
    public void unpaid_json_serialisation_and_deserialisation() throws JsonProcessingException {
        final var mapper = DeterministicObjectMapper.get();
        final var input = "{\"due\":0,\"paid\":0,\"state\":\"" + Unpaid.TYPE + "\",\"total\":0}";

        final var base = assertInstanceOf(Unpaid.class, mapper.readValue(input, PaymentState.class));
        final var impl = assertInstanceOf(Unpaid.class, mapper.readValue(input, Unpaid.class));

        assertEquals(base, impl);

        final var output = mapper.writeValueAsString(impl);
        assertEquals(input, output);
    }
}
