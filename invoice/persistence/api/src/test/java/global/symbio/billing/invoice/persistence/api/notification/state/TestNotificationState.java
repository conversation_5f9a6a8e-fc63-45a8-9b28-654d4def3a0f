package global.symbio.billing.invoice.persistence.api.notification.state;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.lang.reflect.Modifier;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class TestNotificationState {

    @Test
    @DisplayName("NotificationState is abstract class.")
    public void state_is_abstract_class() {
        assertTrue(Modifier.isAbstract(NotificationState.class.getModifiers()));
    }

    @Test
    @DisplayName("NotificationState is sealed class.")
    public void state_is_sealed_class() {
        assertTrue(NotificationState.class.isSealed());
    }

    @Test
    @DisplayName("NotificationState only permits Pending, Sent, PartiallyDelivered, Delivered, Failure implementations.")
    public void state_only_permits_pending_sent_partially_delivered_delivered_and_failure_subtypes() {
        final var permitted = NotificationState.class.getPermittedSubclasses();
        final var expected = new Class[]{ Pending.class, Sent.class, PartiallyDelivered.class, Delivered.class, Failure.class };
        assertArrayEquals(expected, permitted);
    }
}
