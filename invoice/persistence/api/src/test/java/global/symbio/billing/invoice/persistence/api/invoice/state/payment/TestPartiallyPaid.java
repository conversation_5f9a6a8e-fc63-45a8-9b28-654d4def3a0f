package global.symbio.billing.invoice.persistence.api.invoice.state.payment;

import com.fasterxml.jackson.core.JsonProcessingException;
import global.symbio.billing.core.persistence.api.DeterministicObjectMapper;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;

public class TestPartiallyPaid {

    private PartiallyPaid state;

    @BeforeEach
    public void setup() {
        state = new PartiallyPaid(BigDecimal.TEN, BigDecimal.TEN.subtract(BigDecimal.ONE), BigDecimal.ONE);
    }

    @Test
    @DisplayName("PartiallyPaid is subclass of PaymentState")
    public void partially_paid_is_subtype_of_payment_state() {
        assertInstanceOf(PartiallyPaid.class, state);
        assertInstanceOf(PaymentState.class, state);
    }

    @Test
    @DisplayName("PartiallyPaid#TYPE equals PARTIALLY_PAID and PartiallyPaid#type equals PartiallyPaid#TYPE")
    public void partially_paid_type_equals_paid() {
        assertEquals("PARTIALLY_PAID", PartiallyPaid.TYPE);
        assertEquals(PartiallyPaid.TYPE, state.getState());
        assertSame(PartiallyPaid.TYPE, state.getState());
    }

    @Test
    @DisplayName("PartiallyPaid::pay greater than amount due throws IllegalStateException")
    public void partially_paid_pay_greater_than_amount_due_throws_illegal_state_exception() {
        final var amount = BigDecimal.TEN.multiply(BigDecimal.TEN);
        assertEquals(BigDecimal.ONE, state.getDue());
        final var cause = assertThrows(IllegalStateException.class, () -> state.pay(amount));
        assertEquals("Unable to create a payment greater than the due amount. Due: " + state.getDue() + ", Requested: " + amount + ".", cause.getMessage());
    }

    @Test
    @DisplayName("PartiallyPaid::pay amount due transitions to Paid state")
    public void partially_paid_pay_amount_due_transitions_to_paid_state() {
        assertEquals(BigDecimal.ONE, state.getDue());
        final var paid = assertDoesNotThrow(() -> state.pay(state.getDue()));
        assertInstanceOf(Paid.class, paid);
        assertEquals(paid.getDue(), BigDecimal.ZERO);
        assertEquals(paid.getPaid(), state.getTotal());
    }

    @Test
    @DisplayName("PartiallyPaid::pay less than amount due transitions to PartiallyPaid state")
    public void partially_paid_pay_less_than_amount_due_transitions_to_partially_paid_state() {
        final var amount = new BigDecimal("0.5");
        assertEquals(BigDecimal.ONE, state.getDue());
        final var partial = assertDoesNotThrow(() -> state.pay(amount));
        assertInstanceOf(PartiallyPaid.class, partial);
        assertEquals(partial.getDue(), state.getDue().subtract(amount));
        assertEquals(partial.getPaid(), state.getPaid().add(amount));
    }

    @ParameterizedTest
    @CsvSource(value = { "-10", "-1", "0" })
    @DisplayName("PartiallyPaid::pay with negative or zero amount throws IllegalStateException")
    public void partially_paid_pay_with_negative_or_zero_amount_throws_illegal_state_exception(@Nonnull BigDecimal amount) {
        final var cause = assertThrows(IllegalStateException.class, () -> state.pay(amount));
        assertEquals("Amount is required to be positive and greater than 0.", cause.getMessage());
    }

    @Test
    @DisplayName("PartiallyPaid::refund greater than amount paid throws IllegalStateException")
    public void partially_paid_refund_greater_than_amount_paid_throws_illegal_state_exception() {
        final var amount = BigDecimal.TEN.multiply(BigDecimal.TEN);
        assertEquals(BigDecimal.TEN.subtract(BigDecimal.ONE), state.getPaid());
        final var cause = assertThrows(IllegalStateException.class, () -> state.refund(amount));
        assertEquals("Unable to refund more than what has been paid. Paid: " + state.getPaid() + ", Requested: " + amount + ".", cause.getMessage());
    }

    @Test
    @DisplayName("PartiallyPaid::refund amount paid transitions to Unpaid state")
    public void partially_paid_refund_amount_paid_transitions_to_unpaid_state() {
        assertEquals(BigDecimal.TEN.subtract(BigDecimal.ONE), state.getPaid());
        final var unpaid = assertDoesNotThrow(() -> state.refund(state.getPaid()));
        assertInstanceOf(Unpaid.class, unpaid);
        assertEquals(unpaid.getDue(), state.getTotal());
        assertEquals(unpaid.getPaid(), BigDecimal.ZERO);
    }

    @Test
    @DisplayName("PartiallyPaid::refund less than amount paid transitions to PartiallyPaid state")
    public void partially_paid_refund_less_than_amount_paid_transitions_to_partially_paid_state() {
        final var amount = new BigDecimal("0.5");
        assertEquals(BigDecimal.ONE, state.getDue());
        final var partial = assertDoesNotThrow(() -> state.refund(amount));
        assertInstanceOf(PartiallyPaid.class, partial);
        assertEquals(partial.getDue(), state.getDue().add(amount));
        assertEquals(partial.getPaid(), state.getPaid().subtract(amount));
    }

    @ParameterizedTest
    @CsvSource(value = { "-10", "-1", "0" })
    @DisplayName("PartiallyPaid::refund with negative or zero amount throws IllegalStateException")
    public void partially_paid_refund_with_negative_or_zero_amount_throws_illegal_state_exception(@Nonnull BigDecimal amount) {
        final var cause = assertThrows(IllegalStateException.class, () -> state.refund(amount));
        assertEquals("Amount is required to be positive and greater than 0.", cause.getMessage());
    }

    @Test
    @DisplayName("PartiallyPaid state JSON serialisation and deserialisation functions correctly")
    public void partially_paid_json_serialisation_and_deserialisation() throws JsonProcessingException {
        final var mapper = DeterministicObjectMapper.get();
        final var input = "{\"due\":0,\"paid\":0,\"state\":\"" + PartiallyPaid.TYPE + "\",\"total\":0}";

        final var base = assertInstanceOf(PartiallyPaid.class, mapper.readValue(input, PaymentState.class));
        final var impl = assertInstanceOf(PartiallyPaid.class, mapper.readValue(input, PartiallyPaid.class));

        assertEquals(base, impl);

        final var output = mapper.writeValueAsString(impl);
        assertEquals(input, output);
    }
}
