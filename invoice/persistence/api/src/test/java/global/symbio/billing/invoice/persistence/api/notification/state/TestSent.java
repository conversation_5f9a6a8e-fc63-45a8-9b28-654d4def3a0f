package global.symbio.billing.invoice.persistence.api.notification.state;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import global.symbio.billing.core.persistence.api.DeterministicObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;

import static org.junit.jupiter.api.Assertions.*;

public class TestSent {

    private static final ZonedDateTime EPOCH = ZonedDateTime.ofInstant(Instant.EPOCH, ZoneId.systemDefault());

    private Sent state;

    @BeforeEach
    public void setup() {
        state = new Sent(EPOCH);
    }

    @Test
    @DisplayName("Sent::new rejects null timestamp")
    public void sent_rejects_null_timestamp() {
        assertThrows(NullPointerException.class, () -> new Sent(null));
    }

    @Test
    @DisplayName("Sent is subclass of State")
    public void sent_is_subtype_of_state() {
        assertInstanceOf(Sent.class, state);
        assertInstanceOf(Sent.class, state);
    }

    @Test
    @DisplayName("Sent#TYPE equals SENT and Sent#type equals Sent#TYPE")
    public void sent_type_equals_sent() {
        assertEquals("SENT", Sent.TYPE);
        assertEquals(Sent.TYPE, state.getState());
        assertSame(Sent.TYPE, state.getState());
    }

    @Test
    @DisplayName("Sent#pending returns Pending#getInstance")
    public void sent_pending_returns_singleton_instance() {
        final var pending = state.pending();
        assertEquals(Pending.getInstance(), pending);
        assertSame(Pending.getInstance(), pending);
    }

    @Test
    @DisplayName("Sent#sent returns new instance")
    public void sent_sent_returns_new_instance() {
        final var sent = state.sent(EPOCH);
        assertEquals(state, sent);
        assertNotSame(state, sent);
    }

    @Test
    @DisplayName("Sent state JSON serialisation and deserialisation functions correctly")
    public void sent_json_serialisation_and_deserialisation() throws JsonProcessingException {
        final var mapper = DeterministicObjectMapper.get();
        final var timestamp = mapper.writeValueAsString(ZonedDateTime.now());
        final var input = "{\"state\":\"" + Sent.TYPE + "\",\"timestamp\":" + timestamp + "}";

        final var base = assertInstanceOf(Sent.class, mapper.readValue(input, NotificationState.class));
        final var impl = assertInstanceOf(Sent.class, mapper.readValue(input, Sent.class));

        assertEquals(base, impl);

        final var output = mapper.writeValueAsString(impl);
        assertEquals(input, output);
    }
}
