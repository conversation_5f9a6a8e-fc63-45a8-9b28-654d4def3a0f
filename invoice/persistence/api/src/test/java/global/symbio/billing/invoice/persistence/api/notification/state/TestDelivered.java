package global.symbio.billing.invoice.persistence.api.notification.state;

import com.fasterxml.jackson.core.JsonProcessingException;
import global.symbio.billing.core.persistence.api.DeterministicObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;

import static org.junit.jupiter.api.Assertions.*;

public class TestDelivered {

    private static final ZonedDateTime EPOCH = ZonedDateTime.ofInstant(Instant.EPOCH, ZoneId.systemDefault());

    private Delivered state;

    @BeforeEach
    public void setup() {
        state = new Delivered(EPOCH);
    }

    @Test
    @DisplayName("Delivered::new rejects null timestamp")
    public void delivered_rejects_null_timestamp() {
        assertThrows(NullPointerException.class, () -> new Delivered(null));
    }

    @Test
    @DisplayName("Delivered is subclass of State")
    public void delivered_is_subtype_of_state() {
        assertInstanceOf(Delivered.class, state);
        assertInstanceOf(Delivered.class, state);
    }

    @Test
    @DisplayName("Delivered::TYPE equals DELIVERED and Delivered#type equals Delivered#TYPE")
    public void delivered_type_equals_delivered() {
        assertEquals("DELIVERED", Delivered.TYPE);
        assertEquals(Delivered.TYPE, state.getState());
        assertSame(Delivered.TYPE, state.getState());
    }

    @Test
    @DisplayName("Delivered::pending returns Pending#getInstance")
    public void delivered_pending_returns_singleton_instance() {
        final var pending = state.pending();
        assertEquals(Pending.getInstance(), pending);
        assertSame(Pending.getInstance(), pending);
    }

    @Test
    @DisplayName("Delivered::delivered returns new instance")
    public void delivered_delivered_returns_new_instance() {
        final var delivered = state.delivered(EPOCH);
        assertEquals(state, delivered);
        assertNotSame(state, delivered);
    }

    @Test
    @DisplayName("Delivered state JSON serialisation and deserialisation functions correctly")
    public void delivered_json_serialisation_and_deserialisation() throws JsonProcessingException {
        final var mapper = DeterministicObjectMapper.get();
        final var timestamp = mapper.writeValueAsString(ZonedDateTime.now());
        final var input = "{\"state\":\"" + Delivered.TYPE + "\",\"timestamp\":" + timestamp + "}";

        final var base = assertInstanceOf(Delivered.class, mapper.readValue(input, NotificationState.class));
        final var impl = assertInstanceOf(Delivered.class, mapper.readValue(input, Delivered.class));

        assertEquals(base, impl);

        final var output = mapper.writeValueAsString(impl);
        assertEquals(input, output);
    }
}
