package global.symbio.billing.invoice.persistence.api.notification.state;

import com.fasterxml.jackson.core.JsonProcessingException;
import global.symbio.billing.core.persistence.api.DeterministicObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;

import static org.junit.jupiter.api.Assertions.*;

public class TestFailure {

    private static final ZonedDateTime EPOCH = ZonedDateTime.ofInstant(Instant.EPOCH, ZoneId.systemDefault());

    private Failure state;

    @BeforeEach
    public void setup() {
        state = new Failure(null, EPOCH);
    }

    @Test
    @DisplayName("Failure::new rejects null timestamp")
    public void failure_rejects_null_timestamp() {
        assertThrows(NullPointerException.class, () -> new Failure(null, null));
        assertDoesNotThrow(() -> new Failure(null, EPOCH));
    }

    @Test
    @DisplayName("Failure is subclass of State")
    public void failure_is_subtype_of_state() {
        assertInstanceOf(Failure.class, state);
        assertInstanceOf(NotificationState.class, state);
    }

    @Test
    @DisplayName("Failure::TYPE equals FAILURE and Failure#type equals Failure#TYPE")
    public void failure_type_equals_success() {
        assertEquals("FAILURE", Failure.TYPE);
        assertEquals(Failure.TYPE, state.getState());
        assertSame(Failure.TYPE, state.getState());
    }

    @Test
    @DisplayName("Failure::pending returns Pending#getInstance")
    public void failure_pending_returns_singleton_instance() {
        final var pending = state.pending();
        assertEquals(Pending.getInstance(), pending);
        assertSame(Pending.getInstance(), pending);
    }

    @Test
    @DisplayName("Failure state JSON serialisation and deserialisation functions correctly")
    public void failure_json_serialisation_and_deserialisation() throws JsonProcessingException {
        final var mapper = DeterministicObjectMapper.get();
        final var timestamp = mapper.writeValueAsString(ZonedDateTime.now());
        final var input = "{\"reason\":\"Hello, World!\",\"state\":\"" + Failure.TYPE + "\",\"timestamp\":" + timestamp + "}";

        final var base = assertInstanceOf(Failure.class, mapper.readValue(input, NotificationState.class));
        final var impl = assertInstanceOf(Failure.class, mapper.readValue(input, Failure.class));

        assertEquals(base, impl);

        final var output = mapper.writeValueAsString(impl);
        assertEquals(input, output);
    }
}
