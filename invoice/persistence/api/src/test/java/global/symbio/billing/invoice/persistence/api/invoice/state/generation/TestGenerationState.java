package global.symbio.billing.invoice.persistence.api.invoice.state.generation;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.lang.reflect.Modifier;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class TestGenerationState {

    @Test
    @DisplayName("GenerationState is abstract class.")
    public void state_is_abstract_class() {
        assertTrue(Modifier.isAbstract(GenerationState.class.getModifiers()));
    }

    @Test
    @DisplayName("GenerationState is sealed class.")
    public void state_is_sealed_class() {
        assertTrue(GenerationState.class.isSealed());
    }

    @Test
    @DisplayName("GenerationState only permits Pending, Success and Failure implementations.")
    public void state_only_permits_pending_success_and_failure_subtypes() {
        final var permitted = GenerationState.class.getPermittedSubclasses();
        final var expected = new Class[]{ Pending.class, Success.class, Failure.class };
        assertArrayEquals(expected, permitted);
    }
}
