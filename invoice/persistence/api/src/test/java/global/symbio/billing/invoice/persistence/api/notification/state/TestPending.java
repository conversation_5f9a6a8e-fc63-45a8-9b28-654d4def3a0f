package global.symbio.billing.invoice.persistence.api.notification.state;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import global.symbio.billing.core.persistence.api.DeterministicObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

public class TestPending {

    private Pending state;

    @BeforeEach
    public void setup() {
        state = new Pending();
    }

    @Test
    @DisplayName("Pending is subclass of NotificationState")
    public void pending_is_subtype_of_notification_state() {
        assertInstanceOf(Pending.class,state);
        assertInstanceOf(NotificationState.class, state);
    }

    @Test
    @DisplayName("Pending#TYPE equals PENDING and Pending#type equals Pending#TYPE")
    public void pending_type_equals_pending() {
        assertEquals("PENDING", Pending.TYPE);
        assertEquals(Pending.TYPE, state.getState());
        assertSame(Pending.TYPE, state.getState());
    }

    @Test
    @DisplayName("Pending#pending returns `this` instance")
    public void pending_pending_returns_this_instance() {
        final var pending = state.pending();
        assertEquals(state, pending);
        assertSame(state, pending);
    }

    @Test
    @DisplayName("Pending state JSON serialisation and deserialisation functions correctly")
    public void pending_json_serialisation_and_deserialisation() throws JsonProcessingException {
        final var mapper = DeterministicObjectMapper.get();
        final var input = "{\"state\":\"" + Pending.TYPE + "\"}";

        final var base = assertInstanceOf(Pending.class, mapper.readValue(input, NotificationState.class));
        final var impl = assertInstanceOf(Pending.class, mapper.readValue(input, Pending.class));

        assertEquals(base, impl);

        final var output = mapper.writeValueAsString(impl);
        assertEquals(input, output);
    }
}
