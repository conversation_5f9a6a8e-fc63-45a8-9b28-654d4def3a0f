plugins {
    id 'com.gradle.develocity' version '4.1'
    id 'org.gradle.toolchains.foojay-resolver-convention' version '1.0.0'
}

develocity {
    buildScan {
        final def isCI = System.getenv('CI') != null
        termsOfUseUrl = "https://gradle.com/help/legal-terms-of-use"
        termsOfUseAgree = "yes"
        publishing.onlyIf { true }
        tag (isCI ? 'CI' : 'Local')
        uploadInBackground = !isCI
    }
}

dependencyResolutionManagement {
    repositories {
        mavenCentral()
    }
    versionCatalogs {
        // https://melix.github.io/blog/2022/02/micronaut-version-catalog.html
        create('mn') {
            from("io.micronaut.platform:micronaut-platform:$micronautVersion")
        }
    }
}

rootProject.name = 'billing'

include(
    // Core modules - used by billing project sub-modules
    ':core',
    ':core:annotation',
    ':core:audit',
    ':core:audit:api',
    ':core:audit:spi',
    ':core:audit:spi:pipeline',
    ':core:country',
    ':core:country:api',
    ':core:country:persistence',
    ':core:country:persistence:api',
    ':core:country:persistence:spi',
    ':core:country:persistence:spi:jdbc',
    ':core:currency',
    ':core:currency:api',
    ':core:currency:persistence',
    ':core:currency:persistence:api',
    ':core:currency:persistence:spi',
    ':core:currency:persistence:spi:jdbc',
    ':core:kafka',
    ':core:kafka:api',
    ':core:kafka:model',
    ':core:micronaut:environment',
    ':core:micronaut:http',
    ':core:metrics-mixins',
    ':core:metrics-mixins:all',
    ':core:metrics-mixins:caffeine-cache',
    ':core:metrics-mixins:job',
    ':core:metrics-mixins:job:api',
    ':core:metrics-mixins:pipeline',
    ':core:metrics-mixins:pipeline:spi',
    ':core:metrics-mixins:pipeline:spi:disruptor',
    ':core:metrics-mixins:pipeline:spi:disruptor:consumer',
    ':core:metrics-mixins:pipeline:spi:disruptor:consumer:spi',
    ':core:mixins',
    ':core:payment',
    ':core:payment:api',
    ':core:payment:persistence',
    ':core:payment:persistence:api',
    ':core:payment:persistence:spi',
    ':core:payment:persistence:spi:jdbc',
    ':core:persistence',
    ':core:persistence:api',
    ':core:pipeline',
    ':core:pipeline:api',
    ':core:pipeline:api:engine',
    ':core:pipeline:api:sink',
    ':core:pipeline:spi',
    ':core:pipeline:spi:disruptor',
    ':core:pipeline:spi:disruptor:consumer',
    ':core:pipeline:spi:disruptor:consumer:spi',
    ':core:pipeline:spi:disruptor:consumer:dynamo',
    ':core:pipeline:spi:disruptor:consumer:jdbc',
    ':core:pipeline:spi:disruptor:consumer:jdbc:generic',
    ':core:pipeline:spi:disruptor:consumer:jdbc:hibernate',
    ':core:pipeline:spi:disruptor:consumer:jdbc:postgres',
    ':core:pipeline:spi:disruptor:consumer:metrics',
    ':core:pipeline:spi:disruptor:consumer:kafka',
    ':core:pipeline:spi:disruptor:consumer:validation',
    ':core:services',
    ':core:services:grpc',
    ':core:services:grpc:codec',
    ':core:services:grpc:codec:big-decimal',
    ':core:services:grpc:codec:country',
    ':core:services:grpc:codec:currency',
    ':core:services:grpc:codec:pagination',
    ':core:services:grpc:codec:payment-method',
    ':core:services:grpc:codec:reference',
    ':core:services:grpc:codec:temporal',
    ':core:services:grpc:codec:uuid',
    ':core:services:grpc:invoice',
    ':core:services:grpc:ledger',
    ':core:services:grpc:payment',
    ':core:services:grpc:server',
    ':core:services:rest',
    ':core:services:rest:billing',
    ':core:services:rest:plexus',
    ':core:services:rest:sonar',
    ':core:services:storage',
    ':core:util',
    ':core:util:constants',
    ':core:util:mapper',
    ':core:util:range',
    ':core:util:plexid',
    ':core:util:temporal',
    ':core:util:checksum:luhn',
    ':core:util:sanitization',
    ':core:util:metrics',
    ':core:util:transaction',
    ':core:util:compression',
    ':core:validation',
    ':core:validation:validator',
    ':core:validation:validator:consumer-record',
    ':core:validation:validator:java-bean',
    ':core:validation:validator:json',
    ':core:job',
    ':core:job:api',
    ':core:job:persistence',
    ':core:job:persistence:api',
    ':core:job:persistence:spi',
    ':core:job:persistence:spi:jdbc',
    ':core:exception',
    ':core:exchange',
    ':core:health',
    ':core:i18n',
    ':core:reporting',
    ':core:reporting:api',
    ':core:reporting:api:pipeline',
    ':core:reporting:persistence:api',
    ':core:reporting:persistence:spi',
    ':core:reporting:persistence:spi:jdbc',
    ':core:reference',
    ':core:reference:api',
    ':core:reference:persistence',
    ':core:reference:persistence:api',
    ':core:reference:persistence:spi',
    ':core:reference:persistence:spi:jdbc',
    // E2E modules
    ':e2e',
    // Ledger modules
    ':ledger',
    ':ledger:app',
    ':ledger:persistence',
    ':ledger:persistence:api',
    ':ledger:persistence:spi',
    ':ledger:persistence:spi:jdbc',
    // Invoice modules
    ':invoice',
    ':invoice:app',
    ':invoice:persistence',
    ':invoice:persistence:api',
    ':invoice:persistence:spi',
    ':invoice:persistence:spi:jdbc',
    // Produce Bridge modules
    ':product-bridge',
    ':product-bridge:app',
    ':product-bridge:persistence',
    ':product-bridge:persistence:api',
    ':product-bridge:persistence:spi',
    ':product-bridge:persistence:spi:jdbc',
    // Template modules
//    ':template',
//    ':template:app',
//    ':template:persistence',
//    ':template:persistence:api',
//    ':template:persistence:spi',
//    ':template:persistence:spi:jdbc',
    // Payment modules
    ':payment',
    ':payment:app',
    ':payment:persistence',
    ':payment:persistence:api',
    ':payment:persistence:spi',
)

